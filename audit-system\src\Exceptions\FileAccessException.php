<?php

namespace AuditSystem\Exceptions;

/**
 * Exception thrown when file access operations fail
 */
class FileAccessException extends AuditException
{
    /**
     * Create exception for file not found
     *
     * @param string $filePath Path to the file that was not found
     * @return static
     */
    public static function fileNotFound(string $filePath): self
    {
        return new self("File not found: {$filePath}");
    }

    /**
     * Create exception for file not readable
     *
     * @param string $filePath Path to the file that is not readable
     * @return static
     */
    public static function fileNotReadable(string $filePath): self
    {
        return new self("File is not readable: {$filePath}");
    }

    /**
     * Create exception for directory not accessible
     *
     * @param string $dirPath Path to the directory that is not accessible
     * @return static
     */
    public static function directoryNotAccessible(string $dirPath): self
    {
        return new self("Directory is not accessible: {$dirPath}");
    }

    /**
     * Create exception for file too large
     *
     * @param string $filePath Path to the file that is too large
     * @param int $size File size in bytes
     * @param int $maxSize Maximum allowed size in bytes
     * @return static
     */
    public static function fileTooLarge(string $filePath, int $size, int $maxSize): self
    {
        return new self("File too large: {$filePath} ({$size} bytes, max: {$maxSize} bytes)");
    }
}