<?php

require_once 'vendor/autoload.php';

use AuditSystem\Analyzers\PerformanceAnalyzer;
use AuditSystem\Models\Finding;

echo "Testing PerformanceAnalyzer with Real CMS Code Samples...\n\n";

$analyzer = new PerformanceAnalyzer();

// Test with real CMS-like code patterns
echo "=== Test 1: Article Loading with N+1 Problem ===\n";
$articleCode = '<?php
// Typical article loading pattern that causes N+1 queries
$articles = $pdo->query("SELECT * FROM articles WHERE status = 1 ORDER BY created_at DESC");
foreach ($articles as $article) {
    // N+1 problem: loading comments for each article
    $comments = $pdo->query("SELECT * FROM comments WHERE article_id = " . $article["id"]);
    $article["comments"] = $comments->fetchAll();
    
    // Another N+1: loading category for each article
    $category = $pdo->query("SELECT name FROM categories WHERE id = " . $article["category_id"]);
    $article["category"] = $category->fetch();
    
    // String concatenation in loop
    $output .= "<article>" . $article["title"] . "</article>";
}
?>';

$findings = $analyzer->analyze('public_html/index.php', $articleCode);
echo "Found " . count($findings) . " performance issues:\n";
foreach ($findings as $finding) {
    echo "  - {$finding->severity}: {$finding->description} (Line: {$finding->line})\n";
}

echo "\n=== Test 2: Image Processing Without Optimization ===\n";
$imageCode = '<?php
// Image upload and processing without optimization
if (isset($_FILES["image"])) {
    $uploadedFile = $_FILES["image"]["tmp_name"];
    
    // No size validation
    move_uploaded_file($uploadedFile, "uploads/image.jpg");
    
    // Image processing without WebP support
    $image = imagecreatefromjpeg("uploads/image.jpg");
    
    // Large dimensions without responsive variants
    $resized = imagecopyresampled($dest, $image, 0, 0, 0, 0, 1920, 1080, 3840, 2160);
    
    // JPEG without compression quality
    imagejpeg($resized, "uploads/resized.jpg");
    
    // Memory limit increase instead of optimization
    ini_set("memory_limit", "512M");
}
?>';

$findings = $analyzer->analyze('public_html/image.php', $imageCode);
echo "Found " . count($findings) . " performance issues:\n";
foreach ($findings as $finding) {
    echo "  - {$finding->severity}: {$finding->description} (Line: {$finding->line})\n";
}

echo "\n=== Test 3: Admin Dashboard with Multiple Performance Issues ===\n";
$adminCode = '<?php
// Admin dashboard with multiple performance problems
header("Content-Type: text/html; charset=UTF-8");

// No caching headers
echo "<html><head>";

// Multiple CSS files (should be concatenated)
echo "<link rel=\"stylesheet\" href=\"css/bootstrap.css\">";
echo "<link rel=\"stylesheet\" href=\"css/admin.css\">";
echo "<link rel=\"stylesheet\" href=\"css/dashboard.css\">";
echo "<link rel=\"stylesheet\" href=\"css/responsive.css\">";

// Blocking JavaScript
echo "<script src=\"js/jquery.js\"></script>";
echo "<script src=\"js/bootstrap.js\"></script>";

echo "</head><body>";

// High query count
$users = $pdo->query("SELECT COUNT(*) FROM users");
$articles = $pdo->query("SELECT COUNT(*) FROM articles");
$comments = $pdo->query("SELECT COUNT(*) FROM comments");
$categories = $pdo->query("SELECT COUNT(*) FROM categories");
$tags = $pdo->query("SELECT COUNT(*) FROM tags");
$sessions = $pdo->query("SELECT COUNT(*) FROM sessions");
$logs = $pdo->query("SELECT COUNT(*) FROM logs");
$settings = $pdo->query("SELECT COUNT(*) FROM settings");
$permissions = $pdo->query("SELECT COUNT(*) FROM permissions");
$roles = $pdo->query("SELECT COUNT(*) FROM roles");
$notifications = $pdo->query("SELECT COUNT(*) FROM notifications");
$analytics = $pdo->query("SELECT COUNT(*) FROM analytics");

// API call without caching
$externalStats = file_get_contents("https://api.analytics.com/stats?site=lakofino");

// File operations without caching
$config = file_get_contents("config.json");
$translations = file_get_contents("translations.json");

echo "</body></html>";
?>';

$findings = $analyzer->analyze('public_html/admin/dashboard.php', $adminCode);
echo "Found " . count($findings) . " performance issues:\n";
foreach ($findings as $finding) {
    echo "  - {$finding->severity}: {$finding->description} (Line: {$finding->line})\n";
}

echo "\n=== Test 4: Search Functionality with Performance Problems ===\n";
$searchCode = '<?php
// Search functionality with performance issues
$searchTerm = $_GET["q"];

// Inefficient LIKE query with leading wildcard
$query = "SELECT * FROM articles WHERE title LIKE \'%$searchTerm%\' OR content LIKE \'%$searchTerm%\'";
$results = $pdo->query($query);

// ORDER BY without LIMIT
$popularQuery = "SELECT * FROM articles ORDER BY views DESC";
$popular = $pdo->query($popularQuery);

$output = "";
foreach ($results as $result) {
    // String concatenation in loop
    $output .= "<div class=\"search-result\">";
    $output .= "<h3>" . $result["title"] . "</h3>";
    $output .= "<p>" . substr($result["content"], 0, 200) . "...</p>";
    $output .= "</div>";
}

echo $output;
?>';

$findings = $analyzer->analyze('public_html/search.php', $searchCode);
echo "Found " . count($findings) . " performance issues:\n";
foreach ($findings as $finding) {
    echo "  - {$finding->severity}: {$finding->description} (Line: {$finding->line})\n";
}

echo "\n=== Test 5: Ad System Performance Issues ===\n";
$adCode = '<?php
// Ad system with performance problems
$ads = $pdo->query("SELECT * FROM ads WHERE status = 1");

foreach ($ads as $ad) {
    // N+1 problem: loading impressions for each ad
    $impressions = $pdo->query("SELECT COUNT(*) FROM ad_impressions WHERE ad_id = " . $ad["id"]);
    
    // N+1 problem: loading clicks for each ad
    $clicks = $pdo->query("SELECT COUNT(*) FROM ad_clicks WHERE ad_id = " . $ad["id"]);
    
    // API call without caching for each ad
    $adData = file_get_contents("https://adsense.googleapis.com/v2/accounts/pub-123/adunits/" . $ad["unit_id"]);
    
    // String concatenation in loop
    $adHtml .= "<div class=\"ad-unit\" data-id=\"" . $ad["id"] . "\">";
    $adHtml .= $ad["content"];
    $adHtml .= "</div>";
}

// Memory operations without cleanup
$largeDataSet = $pdo->query("SELECT * FROM ad_impressions")->fetchAll();
foreach ($largeDataSet as $impression) {
    $processedData[] = processImpression($impression);
    // No unset() for memory cleanup
}

echo $adHtml;
?>';

$findings = $analyzer->analyze('public_html/includes/ad_display.php', $adCode);
echo "Found " . count($findings) . " performance issues:\n";
foreach ($findings as $finding) {
    echo "  - {$finding->severity}: {$finding->description} (Line: {$finding->line})\n";
}

echo "\n=== Performance Analysis Summary ===\n";
echo "The PerformanceAnalyzer successfully identified common CMS performance issues:\n";
echo "✓ Database N+1 query problems in article and ad loading\n";
echo "✓ Image processing without optimization (WebP, compression, size validation)\n";
echo "✓ Asset loading inefficiencies (blocking JS, multiple CSS files)\n";
echo "✓ Missing caching for API calls and file operations\n";
echo "✓ Inefficient search queries with leading wildcards\n";
echo "✓ Memory usage issues (string concatenation, missing cleanup)\n";
echo "✓ Priority area detection for admin and ad system files\n";
echo "\nAll tests demonstrate the analyzer can detect real-world performance bottlenecks!\n";