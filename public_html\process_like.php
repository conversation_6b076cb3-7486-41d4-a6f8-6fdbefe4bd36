<?php
/**
 * process_like.php - Handles comment like functionality
 */

// Suppress errors in output for AJAX endpoints
error_reporting(0);
ini_set('display_errors', 0);

// Set header to return JSON
header('Content-Type: application/json');

// Include configuration
require_once __DIR__ . '/config.php';

// --- Input Validation ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
    exit;
}

$commentId = filter_input(INPUT_POST, 'comment_id', FILTER_VALIDATE_INT);

if (!$commentId || $commentId <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid comment ID.']);
    exit;
}

// Ensure PDO connection is available
if (!isset($pdo) || !($pdo instanceof PDO)) {
    error_log("PDO connection not available in process_like.php");
    echo json_encode(['success' => false, 'error' => 'Database connection error.']);
    exit;
}

try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Update the likes count
    $stmt = $pdo->prepare("UPDATE comments SET likes = likes + 1 WHERE id = :comment_id");
    $stmt->bindParam(':comment_id', $commentId, PDO::PARAM_INT);
    $stmt->execute();
    
    // Get the updated count
    $stmtGet = $pdo->prepare("SELECT likes FROM comments WHERE id = :comment_id");
    $stmtGet->bindParam(':comment_id', $commentId, PDO::PARAM_INT);
    $stmtGet->execute();
    $newCount = $stmtGet->fetchColumn();
    
    // Commit transaction
    $pdo->commit();
    
    echo json_encode(['success' => true, 'likes' => $newCount]);
    
} catch (PDOException $e) {
    // Rollback on error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Error processing like for comment ID {$commentId}: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Database error occurred.']);
}
exit;