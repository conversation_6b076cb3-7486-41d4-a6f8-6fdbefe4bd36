<?php

require_once 'vendor/autoload.php';

use AuditSystem\Tests\Services\FindingClassifierTest;

echo "Running FindingClassifier Tests...\n\n";

$test = new FindingClassifierTest();

$methods = get_class_methods(FindingClassifierTest::class);
$testMethods = array_filter($methods, fn($method) => strpos($method, 'test') === 0);

$passed = 0;
$failed = 0;

foreach ($testMethods as $method) {
    echo "Running $method... ";
    try {
        $test->setUp();
        $test->$method();
        echo "PASSED\n";
        $passed++;
    } catch (Exception $e) {
        echo "FAILED: " . $e->getMessage() . "\n";
        $failed++;
    } catch (Error $e) {
        echo "ERROR: " . $e->getMessage() . "\n";
        $failed++;
    }
}

echo "\n";
echo "Results: $passed passed, $failed failed\n";

if ($failed === 0) {
    echo "All tests passed!\n";
} else {
    echo "Some tests failed.\n";
}