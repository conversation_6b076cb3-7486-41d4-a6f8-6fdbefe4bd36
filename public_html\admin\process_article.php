<?php
/**
 * public_html/admin/process_article.php
 * Handles article form submission for create/update.
 *
 * Changelog:
 * - v1.11 (2025-04-19): Implemented "Use Random Author" functionality. Checks for 'use_random_author' POST variable. If set to '1', fetches a random active author ID and uses it instead of the selected author_id.
 * - ... (previous changelogs) ...
 */

// --- START DEBUGGING: Force display errors ---
// error_reporting(E_ALL);
// ini_set('display_errors', 1);
// --- END DEBUGGING ---

// Increase PHP execution time limit for long-running operations
set_time_limit(300); // 5 minutes

require_once '../config.php'; // Includes DB constants, functions.php, session_start() etc.
require_once '../includes/functions.php'; // Explicitly include functions
require_once 'includes/auth_check.php'; // Check if admin is logged in

// --- Helper Function to Establish/Re-establish PDO Connection ---
// (Included directly here for completeness, assuming it might not be in the global scope reliably)
if (!function_exists('getPDOConnection')) {
    function getPDOConnection() {
        // Basic check for constants
        if (!defined('DB_HOST') || !defined('DB_NAME') || !defined('DB_USER') || !defined('DB_PASS') || !defined('DB_CHARSET')) {
            error_log("FATAL: Database configuration constants are missing in getPDOConnection().");
            throw new Exception("Database configuration constants are missing.");
        }
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $options = [ PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC, PDO::ATTR_EMULATE_PREPARES => false, ];
        try {
            return new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (\PDOException $e) {
            error_log("PDO Connection failed in getPDOConnection(): " . $e->getMessage());
            throw new Exception("Database connection failed during operation. Check server logs. PDO Error: " . $e->getMessage());
        }
    }
}

// --- Constants Check ---
$required_constants = [
    'UPLOAD_DIR', 'ALLOWED_MIME_TYPES', 'MAX_FILE_SIZE', 'IMAGE_QUALITY', 'IMAGE_SIZES',
    'SITE_URL', 'DEEPSEEK_API_KEY', 'PROMPTS_COMMENT_RAGEBAIT', 'PROMPTS_COMMENT_QUESTION',
    'PROMPT_GENERATE_USERNAMES_LIST', 'PROMPTS_ANSWER_QUESTION', 'PROMPTS_COMMENT_POSITIVE_ARTICLE',
    'PROMPTS_COMMENT_POSITIVE_SITE', 'PROMPTS_COMMENT_POSITIVE_SHARED'
];
foreach ($required_constants as $const) {
    if (!defined($const) || (is_array(constant($const)) && empty(constant($const))) ) {
         if (session_status() == PHP_SESSION_NONE) session_start();
         $_SESSION['error_message'] = "Critical error: Configuration constant array '{$const}' is missing or empty in config.php.";
         error_log($_SESSION['error_message']);
         header('Location: articles.php');
         exit;
    }
}
// --- Check if required functions exist AFTER explicit include ---
if (!function_exists('generateSlug') || !function_exists('handleImageUpload') || !function_exists('estimateReadingTime') || !function_exists('callDeepSeekAPI') || !function_exists('addComment')) {
     if (session_status() == PHP_SESSION_NONE) session_start();
     $_SESSION['error_message'] = "Critical error: One or more required functions (like generateSlug) were not found. Check includes/functions.php.";
     error_log($_SESSION['error_message']);
     header('Location: articles.php');
     exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') { header('Location: articles.php'); exit; }

// --- Initialize variables ---
$article_id = isset($_POST['article_id']) ? (int)$_POST['article_id'] : null;
$current_article_id = $article_id; // Use a separate variable for the ID being processed
$errors = [];
$comment_generation_status_message = '';
$activePdo = null; $pdoCheck = null; $pdoComment = null; $pdoReply = null; $pdoRandomAuthor = null; // Keep track of connections

// --- Ensure session is started for potential error messages ---
if (session_status() == PHP_SESSION_NONE) { session_start(); }

try {
    // --- Get form data ---
    // (Existing data retrieval - slightly reordered for clarity)
    $current_featured_image_base = trim($_POST['current_featured_image'] ?? '');
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $category_id = !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null;
    $category_ids = isset($_POST['category_ids']) && is_array($_POST['category_ids']) ? array_map('intval', $_POST['category_ids']) : [];
    $selected_author_id = !empty($_POST['author_id']) ? (int)$_POST['author_id'] : null; // Renamed for clarity
    $status = $_POST['status'] ?? 'draft';
    $slug_input = trim($_POST['slug'] ?? '');
    $excerpt = trim($_POST['excerpt'] ?? '');
    $published_at_input = trim($_POST['published_at'] ?? '');
    $tags_string = trim($_POST['tags'] ?? '');
    $meta_title = trim($_POST['meta_title'] ?? '');
    $meta_description = trim($_POST['meta_description'] ?? '');
    $focus_keyword = trim($_POST['focus_keyword'] ?? '');
    $reading_time_input = $_POST['reading_time'] ?? 5;
    $enable_sidebar = isset($_POST['enable_sidebar']) ? (int)$_POST['enable_sidebar'] : 1;
    $show_similar_posts = isset($_POST['show_similar_posts']) ? (int)$_POST['show_similar_posts'] : 1;
    $enable_fb_share = isset($_POST['enable_fb_share']) ? (int)$_POST['enable_fb_share'] : 1;
    $include_in_recommendations = isset($_POST['include_in_recommendations']) ? (int)$_POST['include_in_recommendations'] : 1;
    $custom_recommendations_code = trim($_POST['custom_recommendations_code'] ?? '');
    $enable_loading_trick = isset($_POST['enable_loading_trick']) ? (int)$_POST['enable_loading_trick'] : 1;
    $trick_type = trim($_POST['trick_type'] ?? 'loading');
    $cloak_article_link = isset($_POST['cloak_article_link']) ? (int)$_POST['cloak_article_link'] : 0;
    $enable_adsense = isset($_POST['enable_adsense']) ? (int)$_POST['enable_adsense'] : 1;
    $enable_affiliate_ads = isset($_POST['enable_affiliate_ads']) ? (int)$_POST['enable_affiliate_ads'] : 1;
    $enable_custom_ads = isset($_POST['enable_custom_ads']) ? (int)$_POST['enable_custom_ads'] : 0;
    $custom_css = trim($_POST['custom_css'] ?? '');
    $youtube_url = trim($_POST['youtube_url'] ?? '');
    $enable_auto_linking = isset($_POST['enable_auto_linking']) ? (int)$_POST['enable_auto_linking'] : 1;
    $auto_link_keywords = trim($_POST['auto_link_keywords'] ?? '');
    $generate_comments = isset($_POST['generate_comments']) && $_POST['generate_comments'] == '1';
    $comment_count = isset($_POST['comment_count']) ? intval($_POST['comment_count']) : 5; // Get custom comment count with default of 5
    $use_random_author = isset($_POST['use_random_author']) && $_POST['use_random_author'] == '1'; // <<< Check for the new toggle value
    $featured_image_upload = $_FILES['featured_image_upload'] ?? null;
    $featured_image_url = trim($_POST['featured_image_url'] ?? '');

    // --- Basic Validation ---
    if (empty($title)) { $errors[] = 'Naslov je obavezan.'; }
    if (empty($content)) { $errors[] = 'Sadržaj je obavezan.'; }
    if (!in_array($status, ['draft', 'published', 'scheduled', 'archived'])) { $errors[] = 'Nevažeći status.'; }
    if (!empty($slug_input) && !preg_match('/^[a-z0-9-]+$/', $slug_input)) { $errors[] = 'Slug može sadržavati samo mala slova, brojeve i crtice.'; }
    if (!empty($youtube_url) && !filter_var($youtube_url, FILTER_VALIDATE_URL)) { $errors[] = 'YouTube URL nije validan.'; }
    if (!is_numeric($reading_time_input) || (int)$reading_time_input < 0) { $errors[] = 'Vrijeme čitanja mora biti pozitivan broj.'; }
    $reading_time = (int)$reading_time_input;

    // --- Determine Author ID ---
    $final_author_id = $selected_author_id; // Default to selected author
    if ($use_random_author) {
        try {
            $pdoRandomAuthor = getPDOConnection();
            // Fetch IDs of all *active* authors
            $stmt_authors = $pdoRandomAuthor->query("SELECT id FROM authors WHERE status = 'active'");
            $active_author_ids = $stmt_authors->fetchAll(PDO::FETCH_COLUMN);

            if (!empty($active_author_ids)) {
                // Select a random ID from the list
                $final_author_id = $active_author_ids[array_rand($active_author_ids)];
            } else {
                // Fallback if no active authors found (keep selected or null)
                error_log("Random author requested but no active authors found. Using selected author: " . ($selected_author_id ?? 'None'));
                $final_author_id = $selected_author_id; // Keep the originally selected one as fallback
            }
        } catch (Exception $e) {
            $errors[] = "Greška pri odabiru nasumičnog autora: " . $e->getMessage();
            error_log("Error fetching random author: " . $e->getMessage());
            $final_author_id = $selected_author_id; // Fallback on error
        } finally {
             $pdoRandomAuthor = null; // Close connection
        }
    }
    // If not using random author and none was selected, set to null (or a default admin ID if preferred)
    if (!$use_random_author && $final_author_id === null) {
        // Decide fallback: null or a specific default author ID
         $final_author_id = null; // Or set to your admin user ID e.g., 1
         // Optionally add a warning/error if author is required?
         // $errors[] = 'Autor nije odabran.';
    }


    // --- Published Date Logic ---
    // (Keep existing logic - no changes needed here for random author)
    $published_at = null;
    // ... (rest of published date logic as before) ...
    if ($status === 'scheduled') { if (empty($published_at_input)) { $errors[] = 'Datum objave je obavezan za zakazane članke.'; } else { try { $publishDate = new DateTime($published_at_input); if ($publishDate <= new DateTime()) { $errors[]='Datum objave za zakazane članke mora biti u budućnosti.'; } else { $published_at = $publishDate->format('Y-m-d H:i:s'); }} catch (Exception $e) { $errors[]='Format datuma objave nije validan.'; $published_at = null; } } } elseif ($status === 'published') { if (!empty($published_at_input)) { try { $publishDate = new DateTime($published_at_input); $published_at = $publishDate->format('Y-m-d H:i:s'); } catch (Exception $e) { $errors[]='Format datuma objave nije validan. Postavlja se na trenutni datum.'; $published_at = date('Y-m-d H:i:s'); } } else { $existing_published_at = null; if ($article_id) { try { $pdoCheck = getPDOConnection(); $stmt_check_publish = $pdoCheck->prepare("SELECT published_at FROM articles WHERE id = :id"); $stmt_check_publish->bindParam(':id', $article_id, PDO::PARAM_INT); $stmt_check_publish->execute(); $existing_published_at = $stmt_check_publish->fetchColumn(); } catch (Exception $e) { $errors[] = 'Greška pri provjeri postojećeg datuma objave: ' . $e->getMessage(); error_log("Error checking existing published_at: " . $e->getMessage()); } finally { $pdoCheck = null; } } if (empty($existing_published_at)) { $published_at = date('Y-m-d H:i:s'); } else { $published_at = $existing_published_at; } } } else { $published_at = null; }


    // --- Featured Image Processing ---
    // (Keep existing logic - no changes needed here for random author)
    $featured_image_to_save = null; $image_source = null; if (isset($featured_image_upload['error']) && $featured_image_upload['error'] === UPLOAD_ERR_OK) { if (isset($featured_image_upload['tmp_name']) && is_uploaded_file($featured_image_upload['tmp_name'])) { if ($featured_image_upload['size'] > MAX_FILE_SIZE) { $errors[] = 'Uploadana slika premašuje limit (' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB).'; } else { $finfo = finfo_open(FILEINFO_MIME_TYPE); $mimeType = finfo_file($finfo, $featured_image_upload['tmp_name']); finfo_close($finfo); if (!$mimeType || !in_array($mimeType, ALLOWED_MIME_TYPES)) { $errors[] = 'Nevažeći tip uploadane slike (' . ($mimeType ?: 'unknown') . '). Dozvoljeni: ' . implode(', ', ALLOWED_MIME_TYPES); } else { $image_source = $featured_image_upload; } } } else { $errors[] = 'Greška pri uploadu slike.'; } } elseif (!empty($featured_image_url)) { if (!filter_var($featured_image_url, FILTER_VALIDATE_URL)) { $errors[] = 'URL istaknute slike nije validan.'; } else { $pathInfo = pathinfo(parse_url($featured_image_url, PHP_URL_PATH)); $extension = strtolower($pathInfo['extension'] ?? ''); $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp']; if (!in_array($extension, $allowedExtensions)) { $errors[] = 'URL ne ukazuje na validan tip slike (jpg, png, gif, webp).'; } else { $image_source = $featured_image_url; } } } if ($image_source && empty($errors)) { $imageResult = handleImageUpload($image_source, 'articles'); if (is_array($imageResult) && isset($imageResult['error'])) { $errors[] = "Greška pri obradi slike: " . $imageResult['error']; } elseif (is_string($imageResult)) { $featured_image_to_save = $imageResult; /* TODO: Delete old images */ } } elseif (empty($errors) && $article_id && !empty($current_featured_image_base)) { $featured_image_to_save = $current_featured_image_base; } elseif (empty($errors)) { $featured_image_to_save = null; }


    // --- Generate Slug & Ensure Uniqueness ---
    // (Keep existing logic - no changes needed here for random author)
    if (empty($errors)) { if (!empty($slug_input)) { $slug = $slug_input; } elseif (!empty($title)) { $slug = generateSlug($title); } else { $slug = 'clanak-' . time(); $errors[] = "Ne može se generisati slug jer nedostaje naslov."; } if (empty($errors)) { $original_slug = $slug; $counter = 1; $slug_ok = false; try { $pdoCheck = getPDOConnection(); while (!$slug_ok) { $sql_check_slug = "SELECT id FROM articles WHERE slug = :slug"; $params_check_slug = [':slug' => $slug]; if ($article_id) { $sql_check_slug .= " AND id != :article_id"; $params_check_slug[':article_id'] = $article_id; } $stmt_check_slug = $pdoCheck->prepare($sql_check_slug); $stmt_check_slug->execute($params_check_slug); if ($stmt_check_slug->fetchColumn() === false) { $slug_ok = true; } else { $slug = $original_slug . '-' . $counter; $counter++; if ($counter > 100) { throw new Exception("Neuspješno generisanje jedinstvenog sluga nakon 100 pokušaja."); } } } } catch (Exception $e) { $errors[] = "Greška pri provjeri jedinstvenosti sluga: " . $e->getMessage(); error_log("Error checking slug uniqueness: " . $e->getMessage()); } finally { $pdoCheck = null; } } }


    // --- Calculate Reading Time ---
    // (Keep existing logic - no changes needed here for random author)
    if (empty($errors)) { if ($reading_time <= 5 && !empty($content)) { $reading_time = estimateReadingTime($content); } else { $reading_time = max(1, $reading_time); } }


    // --- Early Exit if Errors Found ---
    if (!empty($errors)) {
        $_SESSION['form_errors'] = $errors;
        $_SESSION['form_data'] = $_POST; // Store form data for repopulation
        $redirect_url = $article_id ? "article_form.php?id=$article_id" : "article_form.php";
        header("Location: $redirect_url");
        exit;
    }

    // ================================================================
    // --- SHORT TRANSACTION: Save Article and Tags ---
    // ================================================================
    $activePdo = getPDOConnection();
    $activePdo->beginTransaction();

    if ($article_id) {
        // --- Update Article ---
        $sql = "UPDATE articles SET
                    title = :title, slug = :slug, content = :content, excerpt = :excerpt,
                    featured_image = :featured_image, category_id = :category_id, author_id = :author_id,
                    status = :status, published_at = :published_at, meta_title = :meta_title,
                    meta_description = :meta_description, focus_keyword = :focus_keyword,
                    reading_time = :reading_time, enable_sidebar = :enable_sidebar,
                    show_similar_posts = :show_similar_posts, enable_fb_share = :enable_fb_share,
                    include_in_recommendations = :include_in_recommendations,
                    custom_recommendations_code = :custom_recommendations_code,
                    enable_loading_trick = :enable_loading_trick, trick_type = :trick_type,
                    cloak_article_link = :cloak_article_link,
                    enable_adsense = :enable_adsense, enable_affiliate_ads = :enable_affiliate_ads,
                    enable_custom_ads = :enable_custom_ads, custom_css = :custom_css,
                    youtube_url = :youtube_url, enable_auto_linking = :enable_auto_linking,
                    auto_link_keywords = :auto_link_keywords, generate_comments = :generate_comments,
                    comment_count = :comment_count, updated_at = NOW()
                WHERE id = :id";
    } else {
        // --- Insert Article ---
        $sql = "INSERT INTO articles (
                    title, slug, content, excerpt, featured_image, category_id, author_id, status, published_at,
                    meta_title, meta_description, focus_keyword, reading_time, enable_sidebar,
                    show_similar_posts, enable_fb_share, include_in_recommendations, custom_recommendations_code,
                    enable_loading_trick, trick_type, cloak_article_link, enable_adsense, enable_affiliate_ads, enable_custom_ads,
                    custom_css, youtube_url, enable_auto_linking, auto_link_keywords, generate_comments, comment_count,
                    created_at, updated_at, views
                ) VALUES (
                    :title, :slug, :content, :excerpt, :featured_image, :category_id, :author_id, :status, :published_at,
                    :meta_title, :meta_description, :focus_keyword, :reading_time, :enable_sidebar,
                    :show_similar_posts, :enable_fb_share, :include_in_recommendations, :custom_recommendations_code,
                    :enable_loading_trick, :trick_type, :cloak_article_link, :enable_adsense, :enable_affiliate_ads, :enable_custom_ads,
                    :custom_css, :youtube_url, :enable_auto_linking, :auto_link_keywords, :generate_comments, :comment_count,
                    NOW(), NOW(), 0
                )";
    }
    $stmt = $activePdo->prepare($sql);

    // Bind ALL parameters... Use $final_author_id instead of $author_id
    $stmt->bindParam(':title', $title, PDO::PARAM_STR);
    $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
    $stmt->bindParam(':content', $content, PDO::PARAM_STR);
    $stmt->bindParam(':excerpt', $excerpt, PDO::PARAM_STR);
    $stmt->bindParam(':featured_image', $featured_image_to_save, $featured_image_to_save === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
    $stmt->bindParam(':category_id', $category_id, $category_id === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
    $stmt->bindParam(':author_id', $final_author_id, $final_author_id === null ? PDO::PARAM_NULL : PDO::PARAM_INT); // <<< Use the final author ID
    $stmt->bindParam(':status', $status, PDO::PARAM_STR);
    $stmt->bindParam(':published_at', $published_at, $published_at === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
    $stmt->bindParam(':meta_title', $meta_title, PDO::PARAM_STR);
    $stmt->bindParam(':meta_description', $meta_description, PDO::PARAM_STR);
    $stmt->bindParam(':focus_keyword', $focus_keyword, PDO::PARAM_STR);
    $stmt->bindParam(':reading_time', $reading_time, PDO::PARAM_INT);
    $stmt->bindParam(':enable_sidebar', $enable_sidebar, PDO::PARAM_INT);
    $stmt->bindParam(':show_similar_posts', $show_similar_posts, PDO::PARAM_INT);
    $stmt->bindParam(':enable_fb_share', $enable_fb_share, PDO::PARAM_INT);
    $stmt->bindParam(':include_in_recommendations', $include_in_recommendations, PDO::PARAM_INT);
    $stmt->bindParam(':custom_recommendations_code', $custom_recommendations_code, PDO::PARAM_STR);
    $stmt->bindParam(':enable_loading_trick', $enable_loading_trick, PDO::PARAM_INT);
    $stmt->bindParam(':trick_type', $trick_type, PDO::PARAM_STR);
    $stmt->bindParam(':cloak_article_link', $cloak_article_link, PDO::PARAM_INT);
    $stmt->bindParam(':enable_adsense', $enable_adsense, PDO::PARAM_INT);
    $stmt->bindParam(':enable_affiliate_ads', $enable_affiliate_ads, PDO::PARAM_INT);
    $stmt->bindParam(':enable_custom_ads', $enable_custom_ads, PDO::PARAM_INT);
    $stmt->bindParam(':custom_css', $custom_css, PDO::PARAM_STR);
    $stmt->bindParam(':youtube_url', $youtube_url, PDO::PARAM_STR);
    $stmt->bindParam(':enable_auto_linking', $enable_auto_linking, PDO::PARAM_INT);
    $stmt->bindParam(':auto_link_keywords', $auto_link_keywords, PDO::PARAM_STR);
    $stmt->bindParam(':generate_comments', $generate_comments, PDO::PARAM_BOOL);
    $stmt->bindParam(':comment_count', $comment_count, PDO::PARAM_INT);

    if ($article_id) {
        $stmt->bindParam(':id', $article_id, PDO::PARAM_INT);
    }

    $stmt->execute();

    if (!$article_id) {
        $current_article_id = $activePdo->lastInsertId(); // Get ID for new article
    }

    // Handle Tags within transaction...
    // (Keep existing tag handling logic)
    $tag_names = array_map('trim', array_filter(explode(',', $tags_string)));
    $tag_ids = [];
    if (!empty($tag_names)) {
        $sql_find_tag = "SELECT id FROM tags WHERE name = :name OR slug = :slug LIMIT 1";
        $stmt_find_tag = $activePdo->prepare($sql_find_tag);
        $sql_insert_tag = "INSERT INTO tags (name, slug) VALUES (:name, :slug)";
        $stmt_insert_tag = $activePdo->prepare($sql_insert_tag);
        foreach ($tag_names as $tag_name) {
            if (empty($tag_name)) continue;
            $tag_slug = generateSlug($tag_name);
            $stmt_find_tag->bindParam(':name', $tag_name, PDO::PARAM_STR);
            $stmt_find_tag->bindParam(':slug', $tag_slug, PDO::PARAM_STR);
            $stmt_find_tag->execute();
            $existing_tag_id = $stmt_find_tag->fetchColumn();
            if ($existing_tag_id) {
                $tag_ids[] = $existing_tag_id;
            } else {
                try {
                    $stmt_insert_tag->bindParam(':name', $tag_name, PDO::PARAM_STR);
                    $stmt_insert_tag->bindParam(':slug', $tag_slug, PDO::PARAM_STR);
                    $stmt_insert_tag->execute();
                    $tag_ids[] = $activePdo->lastInsertId();
                } catch (PDOException $e) {
                    if ($e->getCode() == '23000') { // Handle potential race condition/duplicate entry
                        error_log("Attempted duplicate tag insert (handled quietly): '{$tag_name}'");
                        $stmt_find_tag->execute(); // Re-fetch ID after potential duplicate error
                        $retry_id = $stmt_find_tag->fetchColumn();
                        if ($retry_id) $tag_ids[] = $retry_id;
                    } else {
                        error_log("Failed to insert tag: '{$tag_name}'. SQL Error: " . $e->getMessage());
                        // Decide if you want to throw the exception or just log and continue
                        // throw $e;
                    }
                }
            }
        }
    }
    $unique_tag_ids = array_unique($tag_ids);

    // Delete existing relations and insert new ones
    $sql_delete_tags = "DELETE FROM article_tags WHERE article_id = :article_id";
    $stmt_delete_tags = $activePdo->prepare($sql_delete_tags);
    $stmt_delete_tags->bindParam(':article_id', $current_article_id, PDO::PARAM_INT);
    $stmt_delete_tags->execute();

    if (!empty($unique_tag_ids)) {
        $sql_insert_relation = "INSERT INTO article_tags (article_id, tag_id) VALUES (:article_id, :tag_id)";
        $stmt_insert_relation = $activePdo->prepare($sql_insert_relation);
        $stmt_insert_relation->bindParam(':article_id', $current_article_id, PDO::PARAM_INT);
        foreach ($unique_tag_ids as $tag_id) {
            $stmt_insert_relation->bindParam(':tag_id', $tag_id, PDO::PARAM_INT);
            try {
                $stmt_insert_relation->execute();
            } catch (PDOException $e) {
                if ($e->getCode() !== '23000') { // Ignore duplicate entry errors silently
                     throw $e; // Re-throw other errors
                }
            }
        }
    }

    // Handle Categories within transaction...
    // If no categories selected but primary category is set, add it to the array
    if (empty($category_ids) && $category_id) {
        $category_ids = [$category_id];
    }
    // If categories are selected but no primary category, use the first one
    elseif (!empty($category_ids) && !$category_id) {
        $category_id = $category_ids[0];
    }

    // Delete existing category relationships
    $sql_delete_categories = "DELETE FROM article_categories WHERE article_id = :article_id";
    $stmt_delete_categories = $activePdo->prepare($sql_delete_categories);
    $stmt_delete_categories->bindParam(':article_id', $current_article_id, PDO::PARAM_INT);
    $stmt_delete_categories->execute();

    // Insert new category relationships
    if (!empty($category_ids)) {
        $sql_insert_category = "INSERT INTO article_categories (article_id, category_id) VALUES (:article_id, :category_id)";
        $stmt_insert_category = $activePdo->prepare($sql_insert_category);
        $stmt_insert_category->bindParam(':article_id', $current_article_id, PDO::PARAM_INT);

        foreach ($category_ids as $cat_id) {
            $stmt_insert_category->bindParam(':category_id', $cat_id, PDO::PARAM_INT);
            try {
                $stmt_insert_category->execute();
            } catch (PDOException $e) {
                if ($e->getCode() !== '23000') { // Ignore duplicate entry errors silently
                    throw $e; // Re-throw other errors
                }
            }
        }
    }


    // --- Process Auto Internal Linking ---
    if ($enable_auto_linking && !empty($auto_link_keywords) && $current_article_id > 0) {
        // Include internal_links.php if not already included
        if (!function_exists('processContentWithInternalLinks')) {
            require_once '../includes/internal_links.php';
        }

        $keywords = array_map('trim', explode(',', $auto_link_keywords));

        // Process each keyword
        foreach ($keywords as $keyword) {
            if (empty($keyword)) continue;

            // Check if this keyword already exists as an internal link
            $sql = "SELECT id FROM internal_links WHERE keyword = :keyword AND target_article_id = :article_id";
            $stmt = $activePdo->prepare($sql);
            $stmt->bindParam(':keyword', $keyword, PDO::PARAM_STR);
            $stmt->bindParam(':article_id', $current_article_id, PDO::PARAM_INT);
            $stmt->execute();
            $existingLink = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$existingLink) {
                // Create a new internal link
                $url = "/{$slug}/";
                createInternalLink($activePdo, $keyword, $url, $current_article_id);
            }
        }
    }

    $activePdo->commit(); // Commit article and tag changes
    $activePdo = null; // Release connection

    $_SESSION['success_message'] = $article_id ? 'Članak uspješno ažuriran.' : 'Članak uspješno kreiran.';


    // ================================================================
    // --- SEPARATE LOGIC: Generate Comments (Outside Transaction) ---
    // ================================================================
    // (Keep existing comment generation logic)
    if ($generate_comments && $current_article_id) {
        if (!function_exists('callDeepSeekAPI') || !function_exists('addComment')) {
             error_log("Required functions callDeepSeekAPI or addComment not found for comment generation.");
             $comment_generation_status_message = ' Greška: Nije moguće generisati komentare (nedostaju funkcije).';
        } else {
            $site_name = defined('SITE_NAME') ? SITE_NAME : 'našoj stranici';
            $content_snippet = mb_substr(strip_tags($content), 0, 500); // Limit context length

            // 1. Generate usernames list (requesting 6)
            $fake_users = [];
            try {
                 session_write_close();
                 $names_result = callDeepSeekAPI(PROMPT_GENERATE_USERNAMES_LIST, '');
                 session_start();

                 if ($names_result['success'] && !empty($names_result['text'])) {
                     $generated_names = array_map('trim', explode(',', $names_result['text']));
                     $fake_users = array_filter($generated_names);
                     while (count($fake_users) < 6) { $fake_users[] = "Korisnica" . rand(10, 999); } // Ensure 6 names, use female fallback
                     $fake_users = array_slice($fake_users, 0, 6);
                 } else {
                     throw new Exception("Failed to generate usernames list. API Error: " . ($names_result['error'] ?? 'Unknown API error'));
                 }

                 // 2. Define comment prompt arrays
                 $comment_prompt_configs = [ // Define which prompt array to use for each slot
                     'question' => PROMPTS_COMMENT_QUESTION,
                     'positive_article' => PROMPTS_COMMENT_POSITIVE_ARTICLE,
                     'positive_site' => PROMPTS_COMMENT_POSITIVE_SITE,
                     'positive_shared' => PROMPTS_COMMENT_POSITIVE_SHARED,
                     'ragebait' => PROMPTS_COMMENT_RAGEBAIT, // Define the order/types here
                 ];

                 // Use the custom comment count from the form (default is 5)
                 $custom_comment_count = min(10, max(1, $comment_count)); // Ensure it's between 1 and 10

                 // Shuffle the comment types to get a random selection
                 $comment_types = array_keys($comment_prompt_configs);
                 shuffle($comment_types);

                 // Make sure we have enough comment types (repeat if necessary)
                 while (count($comment_types) < $custom_comment_count) {
                     $comment_types = array_merge($comment_types, $comment_types);
                 }

                 // Take only the number we want to generate
                 $comment_types_to_generate = array_slice($comment_types, 0, $custom_comment_count);

                 // Always ensure we have at least one provocative comment type (question or ragebait)
                 $has_provocative = in_array('question', $comment_types_to_generate) || in_array('ragebait', $comment_types_to_generate);
                 if (!$has_provocative && $custom_comment_count > 0) {
                     // Replace a random comment with a provocative one
                     $provocative_type = (rand(0, 1) == 0) ? 'question' : 'ragebait';
                     $comment_types_to_generate[array_rand($comment_types_to_generate)] = $provocative_type;
                 }

                 $generated_count = 0;
                 $max_comments_to_generate = count($comment_types_to_generate); // Use our random count
                 $question_comment_id = null;
                 $question_text = '';

                 for($i=0; $i < $max_comments_to_generate; $i++) {
                     $pdoComment = null;
                     $type = $comment_types_to_generate[$i];
                     $prompt_array = $comment_prompt_configs[$type];

                     // Randomly select a prompt variation from the array for this type
                     $prompt_template = $prompt_array[array_rand($prompt_array)];

                     // Replace placeholders in the selected prompt
                     $prompt = str_replace(
                         ['%TITLE%', '%CONTENT_SNIPPET%', '%SITE_NAME%'],
                         [$title, $content_snippet, $site_name],
                         $prompt_template
                     );

                     // Use names from index 0 to 4
                     $user_name = $fake_users[$i] ?? "Korisnica {$i}";

                     session_write_close();
                     $api_result = callDeepSeekAPI($prompt, $content_snippet, 0.85, 200); // Slightly increased temp for variety
                     session_start();

                     if ($api_result['success'] && !empty($api_result['text'])) {
                         $comment_text = trim($api_result['text']);
                         try {
                              $pdoComment = getPDOConnection();
                              if (function_exists('addComment')) {
                                 $add_result = addComment($pdoComment, $current_article_id, $user_name, $comment_text, null, 1);
                                 if ($add_result['success']) {
                                     $generated_count++;
                                     if ($type === 'question' && isset($add_result['comment_id'])) {
                                         $question_comment_id = $add_result['comment_id'];
                                         $question_text = $comment_text;
                                     }
                                 } else { error_log("DB Error adding comment type '{$type}' for article {$current_article_id}: " . ($add_result['error'] ?? 'Unknown DB Error')); }
                              } else { error_log("addComment function missing..."); }
                         } catch (Exception $dbE) { error_log("Error connecting/adding comment type '{$type}' for article {$current_article_id}: " . $dbE->getMessage()); }
                         finally { $pdoComment = null; }
                     } else { error_log("API Error generating comment text type '{$type}' for article {$current_article_id}: " . ($api_result['error'] ?? 'Unknown API error')); }
                 } // End for loop generating main comments

                 // --- Generate Reply to Question (with 70% probability) ---
                 if ($question_comment_id !== null && !empty($question_text) && (rand(1, 100) <= 70)) {
                     // Randomly select an answer prompt variation
                     $reply_prompt_template = PROMPTS_ANSWER_QUESTION[array_rand(PROMPTS_ANSWER_QUESTION)];
                     $reply_prompt = str_replace(['%TITLE%', '%QUESTION%', '%CONTENT_SNIPPET%'], [$title, $question_text, $content_snippet], $reply_prompt_template);

                     // Use the 6th username generated
                     $reply_user_name = $fake_users[5] ?? "Anonimna Komentatorka"; // Female fallback
                     $pdoReply = null;

                     session_write_close();
                     $reply_api_result = callDeepSeekAPI($reply_prompt, $content_snippet, 0.75, 300); // Slightly higher temp for answer variety
                     session_start();

                     if ($reply_api_result['success'] && !empty($reply_api_result['text'])) {
                         $reply_text = trim($reply_api_result['text']);
                         try {
                              $pdoReply = getPDOConnection();
                              if (function_exists('addComment')) {
                                 $add_reply_result = addComment($pdoReply, $current_article_id, $reply_user_name, $reply_text, $question_comment_id, 1);
                                 if (!$add_reply_result['success']) { error_log("DB Error adding reply for article {$current_article_id}, question {$question_comment_id}: " . ($add_reply_result['error'] ?? 'Unknown DB Error')); }
                                 else { $comment_generation_status_message .= " + AI odgovor."; }
                              } else { error_log("addComment function missing..."); }
                         } catch (Exception $dbE_reply) { error_log("Error connecting/adding reply for article {$current_article_id}, question {$question_comment_id}: " . $dbE_reply->getMessage()); }
                         finally { $pdoReply = null; }
                     } else { error_log("API Error generating REPLY text for article {$current_article_id}, question {$question_comment_id}: " . ($reply_api_result['error'] ?? 'Unknown API error')); }
                 }
                 // --- End generate reply ---

                 $total_attempted = $max_comments_to_generate; // Only count base comments attempted
                 if ($generated_count > 0) { $comment_generation_status_message .= " Uspješno generisano {$generated_count}/{$total_attempted} AI komentara."; }
                 else { $comment_generation_status_message .= ' Nije uspjelo generisanje AI komentara (provjerite API/DB logove).'; }

            } catch(Exception $nameGenEx) {
                 error_log($nameGenEx->getMessage()); $comment_generation_status_message .= ' Greška pri generisanju imena korisnika, komentari nisu dodani.';
                 if (session_status() == PHP_SESSION_NONE) session_start();
            }
        }
    } // End if generate_comments

    // Append comment status to final success message
    if (isset($_SESSION['success_message'])) {
        $_SESSION['success_message'] .= $comment_generation_status_message;
    }

    // --- Final Redirect on Success ---
    header("Location: article_form.php?id=$current_article_id&success=1");
    exit;

} catch (Exception $e) { // Catch ALL other exceptions
    if (session_status() == PHP_SESSION_NONE) session_start();
    $error_message_for_user = "Došlo je do greške prilikom spremanja članka.";
    error_log("Article Processing Error (Article ID: " . ($current_article_id ?? 'NEW') . "): " . $e->getMessage() . "\nTrace: " . $e->getTraceAsString());

    // Check if transaction was started before attempting rollback
    if (isset($activePdo) && $activePdo instanceof PDO && $activePdo->inTransaction()) {
        try {
            $activePdo->rollBack();
            $error_message_for_user .= " Transakcija poništena.";
        } catch (PDOException $rollbackException) {
            error_log("Rollback failed after primary error: " . $rollbackException->getMessage());
            $error_message_for_user .= " Greška pri poništavanju transakcije.";
        }
    } else {
        error_log("Rollback not attempted or not needed for main transaction.");
    }

    $_SESSION['error_message'] = $error_message_for_user;
    $formDataToKeep = $_POST;
    unset($formDataToKeep['content']); // Don't resend large content
    $_SESSION['form_data'] = $formDataToKeep; // Store other data for repopulation

    $redirect_url = $article_id ? "article_form.php?id=$article_id" : "article_form.php";
    header("Location: $redirect_url");
    exit;

} finally {
    // Ensure connection objects are cleared
    $activePdo = null;
    $pdoCheck = null;
    $pdoComment = null;
    $pdoReply = null;
    $pdoRandomAuthor = null; // Clear random author connection too
}
?>