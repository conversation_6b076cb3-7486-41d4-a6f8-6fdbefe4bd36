<?php

namespace AuditSystem\Models;

use DateTime;

/**
 * Tracks audit progress and state for incremental processing
 */
class AuditProgress
{
    public array $completedFiles;
    public array $pendingFiles;
    public array $findings;
    public DateTime $lastUpdate;
    public string $currentPhase;
    public array $statistics;

    public function __construct()
    {
        $this->completedFiles = [];
        $this->pendingFiles = [];
        $this->findings = [];
        $this->lastUpdate = new DateTime();
        $this->currentPhase = 'initialization';
        $this->statistics = [
            'totalFiles' => 0,
            'processedFiles' => 0,
            'totalFindings' => 0,
            'criticalFindings' => 0,
            'priorityAreaFindings' => 0
        ];
    }

    /**
     * Mark a file as completed
     *
     * @param string $filePath
     * @param Finding[] $fileFindings
     * @return void
     */
    public function markFileCompleted(string $filePath, array $fileFindings): void
    {
        $this->completedFiles[] = $filePath;
        $this->findings[$filePath] = $fileFindings;
        $this->lastUpdate = new DateTime();
        
        // Remove from pending if it exists and re-index array
        $this->pendingFiles = array_values(array_filter($this->pendingFiles, fn($file) => $file !== $filePath));
        
        // Update statistics
        $this->updateStatistics();
    }

    /**
     * Check if a file has been completed
     *
     * @param string $filePath
     * @return bool
     */
    public function isFileCompleted(string $filePath): bool
    {
        return in_array($filePath, $this->completedFiles);
    }

    /**
     * Set the list of files to be processed
     *
     * @param array $files
     * @return void
     */
    public function setPendingFiles(array $files): void
    {
        $this->pendingFiles = array_diff($files, $this->completedFiles);
        $this->statistics['totalFiles'] = count($files);
        $this->updateStatistics();
    }

    /**
     * Update internal statistics
     *
     * @return void
     */
    private function updateStatistics(): void
    {
        $this->statistics['processedFiles'] = count($this->completedFiles);
        
        $totalFindings = 0;
        $criticalFindings = 0;
        $priorityAreaFindings = 0;
        
        foreach ($this->findings as $fileFindings) {
            $totalFindings += count($fileFindings);
            foreach ($fileFindings as $finding) {
                if ($finding->severity === Finding::SEVERITY_CRITICAL) {
                    $criticalFindings++;
                }
                if ($finding->priority === Finding::PRIORITY_AREA) {
                    $priorityAreaFindings++;
                }
            }
        }
        
        $this->statistics['totalFindings'] = $totalFindings;
        $this->statistics['criticalFindings'] = $criticalFindings;
        $this->statistics['priorityAreaFindings'] = $priorityAreaFindings;
    }

    /**
     * Get completion percentage
     *
     * @return float
     */
    public function getCompletionPercentage(): float
    {
        if ($this->statistics['totalFiles'] === 0) {
            return 0.0;
        }
        
        return ($this->statistics['processedFiles'] / $this->statistics['totalFiles']) * 100;
    }

    /**
     * Convert to array for serialization
     *
     * @return array
     */
    public function toArray(): array
    {
        $findingsArray = [];
        foreach ($this->findings as $file => $fileFindings) {
            $findingsArray[$file] = array_map(fn($finding) => $finding->toArray(), $fileFindings);
        }

        return [
            'completedFiles' => $this->completedFiles,
            'pendingFiles' => $this->pendingFiles,
            'findings' => $findingsArray,
            'lastUpdate' => $this->lastUpdate->format('Y-m-d H:i:s'),
            'currentPhase' => $this->currentPhase,
            'statistics' => $this->statistics
        ];
    }

    /**
     * Create from array data
     *
     * @param array $data
     * @return AuditProgress
     */
    public static function fromArray(array $data): AuditProgress
    {
        $progress = new self();
        $progress->completedFiles = $data['completedFiles'] ?? [];
        $progress->pendingFiles = $data['pendingFiles'] ?? [];
        $progress->lastUpdate = new DateTime($data['lastUpdate'] ?? 'now');
        $progress->currentPhase = $data['currentPhase'] ?? 'initialization';
        $progress->statistics = $data['statistics'] ?? [];

        // Reconstruct findings
        if (isset($data['findings'])) {
            foreach ($data['findings'] as $file => $fileFindings) {
                $progress->findings[$file] = array_map(
                    fn($findingData) => Finding::fromArray($findingData),
                    $fileFindings
                );
            }
        }

        return $progress;
    }
}