<?php
// ========================================================================= //
//                     ARTICLE PAGE (article.php)                            //
// ========================================================================= //
// This file displays a single article, fetches related data, handles meta   //
// tags (including Open Graph for social sharing), displays promotional      //
// content (ads/affiliate links), manages comments, and includes specific    //
// features aimed at increasing Facebook engagement.                         //
//                                                                           //
// Changelog:                                                                //
// - v1.1 (2025-04-19): Mod Bonus Article section.                           //
// - v1.2 (2025-04-20): Implemented YT lazy load.                            //
// - v1.3 (2025-04-20): Removed YT placeholder bg color.                     //
// - v1.4 (2025-04-20): Added object-center to YT thumb.                     //
// - v1.5 (2025-04-20): Changed YT thumb positioning to block.               //
// - v1.6 (2025-04-20): Removed my-4/h-full from YT placeholder/img.         //
// - v1.7 (2025-04-20): Replaced aspect-video with pure CSS aspect ratio     //
//                      and absolute positioning for YT placeholder/img.     //
// - v1.8 (2025-04-20): Removed mt-6 class from YT placeholder container.    //
// - v1.9 (2025-04-20): Added specific class to YT thumb img and CSS rule    //
//                      to override potential prose margins (mt/mb: 0).      //
// - v1.10(2025-04-20): Applied YT lazy load processing to Bonus Article.    //
// - v1.11(2025-04-22): Implemented responsive featured image preloading     //
//                      using <picture> and <link rel="preload"> with        //
//                      imagesrcset/imagesizes. Removed previous JS loader.  //
// - v1.12(2025-04-22): Refined 'sizes' attribute for featured image.        //
//                      Added basic mobile detection for ad image sizes.     //
//                      Removed loading=lazy from LCP img.                   //
// - v1.13(2025-04-22): Added function_exists check for isMobileDevice.      //
// - v1.14(2025-04-22): Removed isMobileDevice definition from article.php   //
//                      as it's defined in ad_targeting.php.                 //
// - v1.15(2025-04-22): Removed 'ls' image size completely from generation   //
//                      and output to prevent it from ever loading.          //
// - v1.16(2025-04-22): Prioritized 'xs' as fallback src/dims. Simplified    //
//                      'sizes' attribute further.                           //
// - v1.17(2025-04-22): Implemented conditional preload link based on mobile //
//                      detection to specifically target mobile preloading.  //
// ========================================================================= //

// --- Core Includes & Setup ---
require_once 'config.php'; // Includes functions.php
require_once 'includes/Parsedown.php'; // Markdown parser
// Include ad-related files
require_once 'includes/ad_display.php'; // Handles displaying ads/promos
require_once 'includes/ad_manager.php'; // Manages ad/promo data
require_once 'includes/ad_tracking.php'; // Handles tracking for ads/promos
// --- Assuming ad_targeting.php (with isMobileDevice) is included via one of the above ---

// Instantiate the Parsedown parser
$Parsedown = new Parsedown();
// Optional: Configure Parsedown (e.g., enable safe mode)
// $Parsedown->setSafeMode(true); // <-- IMPORTANT: Enable for security if excerpt content is not fully trusted.
// $Parsedown->setBreaksEnabled(true);

// --- Use Mobile Detection (defined elsewhere, e.g., ad_targeting.php) ---
// Ensure the function exists before calling it, just in case include order changes
if (function_exists('isMobileDevice')) {
    $isMobile = isMobileDevice(); // Global flag for mobile context
} else {
    // Fallback if function isn't loaded - default to non-mobile
    error_log("Warning: isMobileDevice() function not found. Defaulting to non-mobile.");
    $isMobile = false;
}


// Initialize variables
$article = null;
$bonus_article_html = ''; // Variable to hold the bonus article HTML

// --- Fetch Article Data ---
// Get the slug from the URL query parameter
$slug = $_GET['slug'] ?? null;

if (!$slug) {
    // Redirect to homepage if no slug is provided
    header('Location: ' . SITE_URL);
    exit;
}

// Sanitize the slug to prevent XSS
$slug = htmlspecialchars($slug, ENT_QUOTES, 'UTF-8');

// Fetch the article data using the sanitized slug
$article = getArticleBySlug($pdo, $slug);

// Check if AVG time trick is enabled for this article
$useAvgTimeTrick = false;
if ($article && isset($article['enable_loading_trick']) && $article['enable_loading_trick'] && isset($article['trick_type']) && $article['trick_type'] === 'avg_time') {
    $useAvgTimeTrick = true;
}

// --- Handle 404 Not Found ---
if (!$article) {
    http_response_code(404);
    // Set meta tags for the 404 page
    $page_title = '404 - Stranica nije pronađena';
    $meta_description = 'Traženi članak nije pronađen.';
    $og_title = $page_title;
    $og_description = $meta_description;
    // Use default OG image for 404 page
    $ogImageData = getDefaultOgImageData();
    $og_image_url = $ogImageData['url'];
    $og_image_width = $ogImageData['width'];
    $og_image_height = $ogImageData['height'];
    $og_image_alt = $ogImageData['og_alt'];
    $og_url = SITE_URL . '/' . $slug . '/'; // Use the requested URL
    $og_type = 'website'; // OG type for a general error page
    $final_focus_keyword = '';
    $final_tags_string = '';

    // Include header, display 404 message, include footer, and exit
    include 'includes/header.php';
    echo '<div class="text-center py-20">';
    echo '<h1 class="text-4xl font-bold mb-4">404</h1>';
    echo '<p class="text-gray-600">Traženi članak nije pronađen.</p>';
    echo '<a href="' . SITE_URL . '" class="btn mt-6 inline-block">Vrati se na početnu</a>';
    echo '</div>';
    include 'includes/footer.php';
    exit;
}

// --- BEGIN BONUS ARTICLE LOGIC ---
// This logic fetches a random article from the top 5 most viewed
// in the same category and prepares it for display, including its image.
if ($article) { // Only run if the main article was found
    try {
        $current_article_id = $article['id'];
        $current_category_id = $article['category_id'];

        // Prepare statement to fetch top 5 most viewed articles in the same category (excluding the current one)
        // Added 'featured_image' to SELECT list
        $bonus_sql = "SELECT id, title, content, slug, featured_image
                      FROM articles
                      WHERE category_id = :category_id
                        AND id != :current_article_id
                        AND status = 'published'
                      ORDER BY views DESC
                      LIMIT 5";
        $bonus_stmt = $pdo->prepare($bonus_sql);

        if ($bonus_stmt) {
            $bonus_stmt->bindParam(':category_id', $current_category_id, PDO::PARAM_INT);
            $bonus_stmt->bindParam(':current_article_id', $current_article_id, PDO::PARAM_INT);
            $bonus_stmt->execute();
            $potential_bonus_articles = $bonus_stmt->fetchAll(PDO::FETCH_ASSOC);

            // Randomly select one bonus article if any were found
            if (!empty($potential_bonus_articles)) {
                $selected_bonus_article = $potential_bonus_articles[array_rand($potential_bonus_articles)];

                // Generate the URL for the bonus article
                $bonus_article_url = SITE_URL . '/' . htmlspecialchars($selected_bonus_article['slug']) . '/';

                // --- Prepare the Bonus Article HTML ---
                $bonus_article_html .= '<div class="bonus-article-section mt-8 pt-6 border-t border-gray-200 not-prose">';
                $bonus_article_html .= '  <h2 class="text-2xl font-bold mb-4 text-center p-3 rounded-lg shadow" style="color: white; background-color: #ff6481;">Bonus objava odabrana samo za vas:</h2>';

                // Add the featured image if available using our responsive image helper
                if (!empty($selected_bonus_article['featured_image'])) {
                    // Generate responsive image HTML and capture it
                    $bonusImageHtml = getResponsiveImageHtml(
                        $selected_bonus_article['featured_image'],
                        'articles',
                        $selected_bonus_article['title'] ?? 'Bonus članak',
                        [
                            'sizes' => ['xs', 'ss', 'ms'],
                            'isPrimary' => false, // Not a primary image
                            'lazyLoad' => true,   // Lazy load is fine for bonus article
                            'linkUrl' => $bonus_article_url, // Link to the bonus article
                            'containerClass' => 'mb-4 rounded-lg overflow-hidden shadow-sm'
                        ]
                    );

                    // Add the image HTML to the bonus article HTML
                    $bonus_article_html .= $bonusImageHtml;
                }

                // Add the title (linked)
                $bonus_article_html .= '  <h3 class="text-xl font-semibold mb-2 text-center"><a href="' . $bonus_article_url . '" class="text-gray-800 hover:text-white-700">' . htmlspecialchars($selected_bonus_article['title']) . '</a></h3>';

                // --- Process Bonus Article Content ---
                // 1. Parse Markdown
                $bonus_content_html = $Parsedown->text($selected_bonus_article['content']);
                // 2. Apply YouTube lazy loading
                $bonus_content_html = processContentForLazyYoutube($bonus_content_html); // <-- APPLY YT PROCESSING HERE

                // Add the processed content
                $bonus_article_html .= '  <div class="prose max-w-none mt-4">' . $bonus_content_html . '</div>'; // Apply prose styles
                $bonus_article_html .= '</div>'; // Close bonus-article-section
            }
        } else {
            error_log("Failed to prepare bonus article statement for article ID {$article['id']}");
        }

    } catch (PDOException $e) {
        error_log("PDOException fetching bonus article for article ID {$article['id']}: " . $e->getMessage());
    } catch (Exception $e) {
        error_log("Exception fetching bonus article for article ID {$article['id']}: " . $e->getMessage());
    }
}
// --- END BONUS ARTICLE LOGIC ---


// --- Prepare Image Data ---
// We no longer need to prepare image data here since we're using the getResponsiveImageHtml function
// which handles all the image data preparation internally.

// We only need to check if the article has a featured image for preloading
$hasFeaturedImage = !empty($article['featured_image']);

// Define sizes attribute for reference
$lcpImageSizesAttr = "(max-width: 767px) 100vw, 720px";

// Open Graph (OG) image - specifically sized for Facebook sharing (e.g., 1200x630)
$ogImageData = getFeaturedImageUrl($article['featured_image'], 'fb', 'articles', escape($article['title']));
if (!$ogImageData || !$ogImageData['url']) { // Check if OG image data is valid
    // Fallback to default OG image if article has no featured image or fetch failed
    $ogImageData = getDefaultOgImageData();
}

// --- Prepare Meta Data (Crucial for SEO and Social Sharing) ---
$page_title = $article['meta_title'] ?: $article['title']; // Use specific meta title or fallback to article title
$og_title = $article['title']; // OG title should generally be the main article title for clarity on social platforms

// --- Improved Meta Description Generation ---
// Importance for Facebook: The OG description is shown below the title in Facebook shares. It should be concise and engaging.
$generated_description = '';
$description_limit = 160; // Standard limit for meta descriptions

// Prioritize explicit meta description
if (!empty($article['meta_description'])) {
    $text = strip_tags($article['meta_description']); // Remove HTML tags
    $text = preg_replace('/\s+/', ' ', $text); // Normalize whitespace
    $generated_description = trim($text);
    // Truncate if too long
    if (mb_strlen($generated_description) > $description_limit) {
         $generated_description = mb_substr($generated_description, 0, $description_limit - 3) . '...';
    }
}
// Fallback to excerpt if no meta description
elseif (!empty($article['excerpt'])) {
    $text = strip_tags($article['excerpt']);
    $text = preg_replace('/\s+/', ' ', $text);
    $text = trim($text);
    if (mb_strlen($text) > $description_limit) {
        // Try to truncate at the last word boundary
        $truncated_text = mb_substr($text, 0, $description_limit);
        $last_space = mb_strrpos($truncated_text, ' ');
        $generated_description = ($last_space !== false)
            ? mb_substr($truncated_text, 0, $last_space) . '...'
            : mb_substr($text, 0, $description_limit - 3) . '...'; // Force cut if no space found
    } else {
        $generated_description = $text;
    }
}
// Fallback to start of content if no excerpt
elseif (!empty($article['content'])) {
    $text = strip_tags($article['content']); // Remove HTML from content
    $text = preg_replace('/\s+/', ' ', $text); // Normalize whitespace
    $text = trim($text);
    if (mb_strlen($text) > $description_limit) {
        // Try to truncate at the last word boundary
        $truncated_text = mb_substr($text, 0, $description_limit);
        $last_space = mb_strrpos($truncated_text, ' ');
        $generated_description = ($last_space !== false)
            ? mb_substr($truncated_text, 0, $last_space) . '...'
            : mb_substr($text, 0, $description_limit - 3) . '...'; // Force cut
    } else {
        $generated_description = $text;
    }
}

// Final fallback to article title if no description could be generated
if (empty(trim($generated_description))) {
     $generated_description = escape($article['title']);
}

$meta_description = escape($generated_description); // Escape for HTML attribute
$og_description = $meta_description; // Use the same generated description for OG
// --- End Improved Meta Description ---

// --- Set Open Graph (OG) Meta Tags ---
// These tags control how the article appears when shared on social platforms like Facebook.
$og_image_url = $ogImageData['url'];         // URL of the image for the share preview
$og_image_width = $ogImageData['width'];     // Width of the image
$og_image_height = $ogImageData['height'];    // Height of the image
$og_image_alt = $ogImageData['og_alt'];      // Alt text for the image (important for accessibility)
$og_url = SITE_URL . '/' . escape($article['slug']) . '/'; // Canonical URL of the article
$og_type = 'article';                        // Type of content (article, website, etc.)

// --- Fetch Related Data ---
$page_category_slug = $article['category_slug'] ?? null; // Get primary category slug for links/styling
$tags = getArticleTags($pdo, $article['id']); // Get tags associated with the article
$categories = getArticleCategories($pdo, $article['id']); // Get all categories associated with the article
$tags_string = !empty($tags) ? implode(',', array_column($tags, 'name')) : ''; // Comma-separated string for meta keywords (optional)
$final_focus_keyword = escape($article['focus_keyword'] ?? ''); // Primary keyword for the article
$final_tags_string = escape($tags_string); // Escaped tags string

// Fetch related/popular articles for sidebar/recommendations
$popularArticles = getPopularArticles($pdo, 5, $article['id']); // Exclude current article
$recommendedArticles = getPopularArticles($pdo, 6, $article['id']); // Exclude current article

// --- Prepare Context for Promotional Content Targeting ---
// This data helps decide which ads/promos are relevant to the current article.
$promoContext = [
    'page_type' => 'article',                  // Type of page
    'source_page_id' => $article['id'],        // ID of the current article
    'category_id' => $article['category_id'] ?? null, // Category ID
    'tag_ids' => !empty($tags) ? array_column($tags, 'id') : [], // Array of tag IDs
    // 'click_position' will be added dynamically where items are displayed (e.g., 'sidebar_popular_1')
];

// --- Increment View Count ---
// Simple view counter update. Consider more robust tracking for high traffic.
try {
    $updateSql = "UPDATE articles SET views = views + 1 WHERE id = :id";
    $updateStmt = $pdo->prepare($updateSql);
    $updateStmt->bindParam(':id', $article['id'], PDO::PARAM_INT);
    $updateStmt->execute();
} catch (PDOException $e) {
    // Log error if view count update fails, but don't stop page load
    error_log("Failed to update view count for article ID {$article['id']}: " . $e->getMessage());
}

// --- Advanced Preload Strategy for LCP Image using dynamic image processing ---
// Store preload info in variables to reuse for the actual image
$preloadWidth = 0;
$preloadHeight = 0;
$preloadUrl = '';

if ($hasFeaturedImage) {
    // Use a single size for all devices to avoid PageSpeed "properly size images" warning
    // Most content areas display at around 360-400px on mobile and 600-700px on desktop
    // Using 480px as a compromise size that works well on both
    $preloadWidth = 480; // Compromise size that works for both mobile and desktop

    $preloadHeight = round($preloadWidth * (9/16)); // Assuming 16:9 aspect ratio

    // Generate the dynamic image URL
    $originalPath = 'articles/' . $article['featured_image'];
    $preloadUrl = '/image.php?src=' . urlencode($originalPath) .
                 '&w=' . $preloadWidth .
                 '&h=' . $preloadHeight .
                 '&q=70&fit=cover&f=webp'; // WebP format with aggressive compression

    // Generate the preload link
    echo '<link rel="preload" as="image" href="' . escape($preloadUrl) . '" fetchpriority="high">';
}

// --- Include Header ---
// Header is included AFTER all meta variables ($page_title, $meta_description, $og_*, preload links) are set.
include 'includes/header.php';

// --- Prepare Author and Category Data for Display ---
$authorName = $article['author_name'] ?? 'Nepoznat autor';
$authorAvatarData = getFeaturedImageUrl($article['author_avatar'], 'ss', 'articles', escape($authorName)); // 'ss' for small square avatar
$authorAvatarUrl = $authorAvatarData['url'] ?? null; // Ensure it's null if not found
$categoryName = $article['category_name'] ?? null;

// Determine category color class based on slug hash for visual variety
$categoryColorClass = 'tag-default';
if ($page_category_slug) {
    $catHash = crc32($page_category_slug);
    // Define available color classes for categories
    $catColorClasses = ['tag-food', 'tag-health', 'tag-lifestyle', 'tag-technology', 'tag-business', 'tag-travel', 'tag-fashion'];
    $catColorIndex = abs($catHash) % count($catColorClasses);
    $categoryColorClass = $catColorClasses[$catColorIndex] ?? 'tag-default'; // Fallback to default
}

// Define tag styles (icon and color) for specific slugs
$tagStyles = [
    // Example tag styles (add more as needed)
    'recepti' => ['icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" /></svg>', 'color' => 'tag-food'],
    'prirodni-lijekovi' => ['icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" /></svg>', 'color' => 'tag-health'],
    'tehnologija' => ['icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>', 'color' => 'tag-technology'],
    'seo' => ['icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>', 'color' => 'tag-business'],
    'optimizacija' => ['icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>', 'color' => 'tag-technology'],
];
// Generic icon for tags without a specific style defined
$genericTagIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M7 7h.01M7 3h5a2 2 0 012 2v1m-2-3h4a2 2 0 012 2v10a2 2 0 01-2 2H7a2 2 0 01-2-2V5a2 2 0 012-2m0 5h.01" /></svg>';

// --- Helper Function: Render Sidebar Item (Article or Promo) ---
// Renders a single item in the "Popular Articles" sidebar section, handling both actual articles and promotional items.
function renderSidebarItemWithContext($itemContext) {
    global $pdo, $isMobile; // Access global PDO object and mobile flag
    $isPromo = $itemContext['is_promo']; // Flag indicating if the item is a promotion
    $item = $itemContext['item_data'];   // The data for the article or promo

    $itemImageData = null; // Initialize
    $itemImageUrl = null;
    $itemImageAlt = 'Placeholder';
    $itemImageWidth = 64; // Default placeholder size
    $itemImageHeight = 64; // Default placeholder size

    if ($isPromo) {
        // --- Affiliate Promo Data ---
        $itemTitle = $item['title'] ?? "Sponzorirani članak"; // Fallback title
        $itemTrackingUrl = generateTrackingUrl($item['id'], 'affiliate', $item['external_url'], $itemContext);
        $itemUrl = escape($itemTrackingUrl);
        // Choose ad image size based on device context
        $adImageSize = $isMobile ? 'xs' : 'ss'; // Use 'xs' for mobile, 'ss' for desktop sidebar ads
        if (!empty($item['featured_image'])) {
             $itemImageData = getFeaturedImageUrl($item['featured_image'], $adImageSize, 'ads', escape($itemTitle));
        }
        // Use fetched data or keep placeholder defaults
        $itemImageUrl = $itemImageData['url'] ?? null;
        $itemImageAlt = $itemImageData['alt'] ?? escape($itemTitle);
        $itemImageWidth = $itemImageData['width'] ?? 64;
        $itemImageHeight = $itemImageData['height'] ?? 64;

        $itemDate = $item['published_at'] ?? date('Y-m-d H:i:s');
        $itemReadingTime = $item['reading_time'] ?? null;
        $targetAttribute = !empty($item['open_in_new_tab']) ? 'target="_blank" rel="noopener noreferrer sponsored"' : 'rel="sponsored"';
        $showSponsoredLabel = !empty($item['show_sponsored_label']);
    } else {
        // --- Actual Article Data ---
        $itemTitle = $item['title'];
        $itemUrl = SITE_URL . '/' . escape($item['slug']) . '/';
         // We no longer need to prepare image data here since we're using the getResponsiveImageHtml function
        // which handles all the image data preparation internally.
        // No need to prepare image data anymore, as we're using getResponsiveImageHtml

        $itemDate = $item['published_at'] ?? $item['created_at'];
        $itemReadingTime = $item['reading_time'] ?? null;
        $targetAttribute = '';
        $showSponsoredLabel = false;
    }
    ?>
    <div class="flex gap-3">
        <?php if (!empty($item['featured_image'])): ?>
            <?php
            // Use our responsive image helper for sidebar thumbnails
            echo getResponsiveImageHtml(
                $item['featured_image'],
                $isPromo ? 'ads' : 'articles',
                escape($itemTitle),
                [
                    'sizes' => ['xs'], // Only use the smallest size for sidebar thumbnails
                    'isPrimary' => false,
                    'lazyLoad' => true,
                    'linkUrl' => $itemUrl,
                    'containerClass' => 'flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden block bg-gray-200',
                    'imgClass' => 'w-full h-full object-cover rounded-lg',
                    'usePicture' => false // Simple img with srcset is sufficient here
                ]
            );
            ?>
        <?php else: // No image available, show placeholder ?>
            <a href="<?php echo $itemUrl; ?>" <?php echo $targetAttribute; ?> class="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden block bg-gray-200">
                <div class="w-full h-full flex items-center justify-center bg-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                </div>
            </a>
        <?php endif; ?>
        <div class="min-w-0">
            <h4 class="font-montserrat font-semibold text-sm line-clamp-2">
                <a href="<?php echo $itemUrl; ?>" <?php echo $targetAttribute; ?> class="hover:text-primary cursor-pointer text-dark-contrast"><?php echo escape($itemTitle); ?></a>
                <?php if ($isPromo && $showSponsoredLabel): // Display "Sponsored" label if applicable ?>
                <span class="inline-block text-xs text-gray-500 ml-1">• Sponzorirano</span>
                <?php endif; ?>
            </h4>
            <p class="text-xs text-gray-500 mt-1">
                <?php echo formatDate($itemDate); // Format date nicely ?>
            </p>
        </div>
    </div>
    <?php
}

// --- Helper Function: Render Recommended Item (Article or Promo) ---
// Renders a single card in the "Recommended for You" section, handling both articles and promos.
function renderRecommendedItemWithContext($itemContext) {
    global $pdo, $isMobile; // Access global PDO and mobile flag
    $isPromo = $itemContext['is_promo']; // Is it a promotion?
    $item = $itemContext['item_data'];   // Article or promo data

    // No need to initialize image data variables as we're using getResponsiveImageHtml

    if ($isPromo) {
        // --- Affiliate Promo Data ---
        $itemTitle = $item['title'] ?? 'Preporučeni članak';
        $itemTrackingUrl = generateTrackingUrl($item['id'], 'affiliate', $item['external_url'], $itemContext);
        $itemUrl = escape($itemTrackingUrl);
        // We no longer need to prepare image data here since we're using the getResponsiveImageHtml function
        // which handles all the image data preparation internally.

        $itemDate = $item['published_at'] ?? date('Y-m-d H:i:s');
        $itemReadingTime = $item['reading_time'] ?? null;
        $targetAttribute = !empty($item['open_in_new_tab']) ? 'target="_blank" rel="noopener noreferrer sponsored"' : 'rel="sponsored"';
        $showSponsoredLabel = !empty($item['show_sponsored_label']);
    } else {
        // --- Actual Article Data ---
        $itemTitle = $item['title'];
        $itemUrl = SITE_URL . '/' . escape($item['slug']) . '/';
        // We no longer need to prepare image data here since we're using the getResponsiveImageHtml function
        // which handles all the image data preparation internally.

        $itemDate = $item['published_at'] ?? $item['created_at'];
        $itemReadingTime = $item['reading_time'] ?? null;
        $targetAttribute = '';
        $showSponsoredLabel = false;
    }
    ?>
    <a href="<?php echo $itemUrl; ?>" <?php echo $targetAttribute; ?> class="card overflow-hidden block hover:shadow-lg transition-shadow">
        <?php if (!empty($item['featured_image'])): ?>
            <?php
            // Use our responsive image helper for recommended article cards
            echo getResponsiveImageHtml(
                $item['featured_image'],
                $isPromo ? 'ads' : 'articles',
                escape($itemTitle),
                [
                    'sizes' => ['xs', 'ss'], // Use xs and ss sizes for recommended articles
                    'isPrimary' => false,
                    'lazyLoad' => true,
                    'containerClass' => 'h-48 relative overflow-hidden bg-gray-200',
                    'imgClass' => 'w-full h-full object-cover',
                    'sizesAttr' => '(max-width: 767px) 100vw, 350px',
                    'usePicture' => true // Use picture element for better control
                ]
            );
            ?>
        <?php else: // No image available, show placeholder ?>
            <div class="h-48 relative overflow-hidden bg-gray-200"> <?php // Fixed height for image container ?>
                <div class="w-full h-full flex items-center justify-center bg-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                </div>
            </div>
        <?php endif; ?>
        <div class="p-4">
            <h4 class="font-montserrat font-bold text-lg mb-2 line-clamp-2 hover:text-primary transition-colors"><?php echo escape($itemTitle); ?></h4>
             <?php if ($isPromo && $showSponsoredLabel): // Optional "Sponsored" label ?>
                 <span class="inline-block text-xs text-gray-500 mb-2">• Sponzorirano</span>
             <?php endif; ?>
            <div class="flex justify-between items-center text-xs text-gray-500">
                <span><?php echo formatDate($itemDate); ?></span>
                <?php if (!empty($itemReadingTime)): ?>
                <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <?php echo escape($itemReadingTime); ?> min
                </span>
                <?php endif; ?>
            </div>
        </div>
    </a>
    <?php
}

// --- Function to process content for lazy YouTube embeds ---
function processContentForLazyYoutube($content) {
    // Regex to find YouTube iframes
    $pattern = '/<iframe[^>]*src="https:\/\/www\.youtube(?:-nocookie)?\.com\/embed\/([a-zA-Z0-9_-]+)[^>]*><\/iframe>/i';

    return preg_replace_callback($pattern, function ($matches) {
        $videoId = $matches[1]; // Extract video ID
        // Use a high-quality thumbnail (hqdefault), maxresdefault might not always exist
        $thumbnailUrl = "https://img.youtube.com/vi/{$videoId}/hqdefault.jpg";

        // Generate the placeholder HTML - Using specific CSS classes now
        // Container has Tailwind classes for styling + yt-lazy-placeholder-container for aspect ratio
        $placeholderHtml = '<div class="yt-lazy-placeholder-container relative cursor-pointer group rounded-lg overflow-hidden shadow-md mb-4" data-youtube-id="' . $videoId . '">'; // Removed mt-6
        // Thumbnail image - Added yt-lazy-thumb-image class
        $placeholderHtml .= '<img src="' . escape($thumbnailUrl) . '" alt="YouTube video thumbnail" loading="lazy" class="yt-lazy-thumb-image transition-opacity duration-300 opacity-80 group-hover:opacity-100">';
        // Play button overlay - Still absolute over the container
        $placeholderHtml .= '<div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300">';
        $placeholderHtml .= '<svg class="w-16 h-16 md:w-20 md:h-20 text-white opacity-80 group-hover:opacity-100 transition-opacity duration-300 drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg>';
        $placeholderHtml .= '</div>';
        $placeholderHtml .= '</div>';

        return $placeholderHtml;
    }, $content);
}

?>

<?php // ================== START PAGE LAYOUT ================== ?>
<div class="flex flex-col md:flex-row gap-8"> <?php // Main layout: Sidebar + Content ?>

    <?php // --- Sidebar (Order 2 on mobile, Order 1 on desktop) --- ?>
    <div class="md:w-1/4 order-2 md:order-1">
        <div class="sidebar-content md:sticky md:top-20"> <?php // Sticky sidebar on larger screens ?>

            <?php // --- Popular Articles Section --- ?>
            <?php if (!empty($popularArticles)): ?>
            <div class="card p-4 mb-4">
                <h3 class="font-montserrat font-extrabold text-lg mb-4">Popularni članci</h3>
                <div class="space-y-4">
                    <?php
                    // Get affiliate promos specifically for the 'sidebar_popular' placement
                    $sidebarPopularPromos = getActiveAdsForPlacement($pdo, 'sidebar_popular', $promoContext, 2); // Fetch up to 2 promos
                    $popCounter = 0; // Counter for item position
                    $promoIndex = 0; // Index for accessing fetched promos
                    $maxPromos = count($sidebarPopularPromos);

                    // Loop through popular articles and intersperse promos
                    foreach ($popularArticles as $index => $popArticle):
                        $popCounter++;
                        // Define positions where promos should be inserted (e.g., after 1st and 3rd article)
                        $isPromoPosition = ($popCounter == 2 || $popCounter == 4);
                        $canInsertPromo = $isPromoPosition && $promoIndex < $maxPromos && isset($sidebarPopularPromos[$promoIndex]);

                        if ($canInsertPromo) {
                            // --- Render Affiliate Promo ---
                            $promoData = $sidebarPopularPromos[$promoIndex];
                            $promoItemContext = $promoContext; // Start with base page context
                            $promoItemContext['current_placement'] = 'sidebar_popular'; // Specify placement
                            $promoItemContext['click_position'] = 'sidebar_popular_' . $popCounter; // Specify position for tracking
                            $promoItemContext['item_data'] = $promoData; // Add promo data
                            $promoItemContext['is_promo'] = true; // Mark as promo
                            renderSidebarItemWithContext($promoItemContext); // Use helper function
                            $promoIndex++; // Move to the next promo
                        } else {
                            // --- Render Regular Article ---
                            $articleItemContext = $promoContext;
                            $articleItemContext['item_data'] = $popArticle; // Add article data
                            $articleItemContext['is_promo'] = false; // Mark as not a promo
                            renderSidebarItemWithContext($articleItemContext); // Use helper function
                        }
                    endforeach;
                    ?>
                </div>
            </div>
             <?php endif; ?>

            <?php // --- Middle Sidebar Ad/Promo Slot --- ?>
            <?php
            // Display ad/promo for the 'sidebar_middle' placement
            echo displayAdsForPlacement($pdo, 'sidebar_middle', $promoContext, 1, 1); // Display 1 item, require 1
            ?>

            <?php // --- Bottom Sidebar Ad/Promo Slot --- ?>
            <?php
            // Display ad/promo for the 'sidebar_bottom' placement (e.g., AdSense)
            echo displayAdsForPlacement($pdo, 'sidebar_bottom', $promoContext, 1, 1);
            ?>


        </div> <?php // End .sidebar-content ?>
    </div> <?php // End Sidebar Column ?>

    <?php // --- Main Content Column (Order 1 on mobile, Order 2 on desktop) --- ?>
    <div class="md:w-3/4 order-1 md:order-2">
        <div class="card p-6 md:p-8"> <?php // Main article card ?>

            <?php // --- Article Header --- ?>
            <div class="mb-8">
                <?php // Display Category Links ?>
                <div class="mb-3 flex flex-wrap gap-2">
                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $category):
                            // Determine category color class based on slug hash for visual variety
                            $catColorClass = 'tag-default';
                            if (!empty($category['slug'])) {
                                $catHash = crc32($category['slug']);
                                // Define available color classes for categories
                                $catColorClasses = ['tag-food', 'tag-health', 'tag-lifestyle', 'tag-technology', 'tag-business', 'tag-travel', 'tag-fashion'];
                                $catColorIndex = abs($catHash) % count($catColorClasses);
                                $catColorClass = $catColorClasses[$catColorIndex] ?? 'tag-default'; // Fallback to default
                            }
                        ?>
                        <a href="/category/<?php echo escape($category['slug']); ?>/" class="<?php echo $catColorClass; ?> inline-block text-sm px-3 py-1 rounded-full transition-colors">
                            <?php echo escape($category['name']); ?>
                        </a>
                        <?php endforeach; ?>
                    <?php elseif ($categoryName && $page_category_slug): // Fallback to primary category if no categories found ?>
                        <a href="/category/<?php echo escape($page_category_slug); ?>/" class="<?php echo $categoryColorClass; ?> inline-block text-sm px-3 py-1 rounded-full transition-colors">
                            <?php echo escape($categoryName); ?>
                        </a>
                    <?php endif; ?>
                </div>

                <?php // Article Title ?>
                <h1 class="text-3xl md:text-4xl font-montserrat font-extrabold mb-2 leading-tight text-primary">
                    <?php echo escape($article['title']); ?>
                </h1>

                <?php // Display Styled Excerpt (Parsed with Parsedown) ?>
                <?php if (!empty($article['excerpt'])): ?>
                    <blockquote class="mt-3 mb-5 pl-4 pr-2 py-2 border-l-4 border-primary bg-gray-50 rounded-r-lg text-base md:text-lg italic text-gray-700 leading-relaxed excerpt-blockquote">
                        <?php
                            // Use Parsedown->text() to parse Markdown in the excerpt.
                            echo $Parsedown->text($article['excerpt']);
                        ?>
                    </blockquote>
                <?php endif; ?>

                <?php // Author, Date, Reading Time Meta Box ?>
                <div class="p-4 bg-gray-50 rounded-xl border border-border shadow-sm my-4">
                    <div class="flex flex-wrap justify-between items-center text-sm text-gray-500">
                        <?php // Author Info ?>
                        <div class="flex items-center mb-2 md:mb-0">
                            <div class="w-10 h-10 rounded-full overflow-hidden mr-3 bg-gray-200 flex items-center justify-center">
                                <?php if ($authorAvatarUrl): ?>
                                    <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo escape($authorAvatarData['alt']); ?>" class="w-full h-full object-cover" loading="lazy" width="40" height="40" />
                                <?php else: // Placeholder avatar ?>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                                <?php endif; ?>
                            </div>
                            <div>
                                <span class="block text-sm font-montserrat font-semibold text-gray-800"><?php echo escape($authorName); ?></span>
                            </div>
                        </div>
                        <?php // Date & Reading Time ?>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center"> <?php // Date ?>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                                <span><?php echo formatDate($article['published_at'] ?? $article['created_at']); ?></span>
                            </div>
                             <?php if (!empty($article['reading_time'])): ?>
                            <div class="flex items-center"> <?php // Reading Time ?>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                                <span><?php echo escape($article['reading_time']); ?> min čitanja</span>
                            </div>
                             <?php endif; ?>
                        </div>
                    </div>
                </div>

                <?php // --- START: Featured Image using preloaded image --- ?>
                <?php if (!empty($article['featured_image'])): ?>
                    <?php if (!empty($preloadUrl)): // Use the preloaded image ?>
                        <div class="mb-8 rounded-xl overflow-hidden flex justify-center">
                            <div style="max-width: <?php echo $preloadWidth; ?>px; width: 100%;">
                                <img src="<?php echo escape($preloadUrl); ?>"
                                     alt="<?php echo escape($article['title']); ?>"
                                     width="<?php echo $preloadWidth; ?>"
                                     height="<?php echo $preloadHeight; ?>"
                                     class="w-full h-auto object-cover rounded-xl"
                                     fetchpriority="high">
                            </div>
                        </div>
                    <?php else: // Fallback to responsive image if preload failed ?>
                        <?php
                        // Use our helper function to generate responsive image HTML
                        echo getResponsiveImageHtml(
                            $article['featured_image'],
                            'articles',
                            escape($article['title']),
                            [
                                'widths' => [360], // Single width for all devices
                                'isPrimary' => true, // This is the LCP image
                                'lazyLoad' => false, // Don't lazy load LCP image
                                'containerClass' => 'mb-8 rounded-xl overflow-hidden bg-gray-100',
                                'sizesAttr' => '100vw',
                                'quality' => 70 // Aggressive compression
                            ]
                        );
                        ?>
                    <?php endif; ?>
                <?php else: // Placeholder if no featured image ?>
                    <div class="mb-8 rounded-xl overflow-hidden bg-gray-200 flex items-center justify-center h-[250px] md:h-[450px]">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                    </div>
                <?php endif; ?>
                 <?php // --- END: Featured Image --- ?>

            </div> <?php // End Article Header ?>

            <?php // --- Custom CSS for Ad/Promo Margins & YouTube --- ?>
            <style>
            /* Ensure consistent spacing around promotional blocks */
            .block-type-affiliate, .block-type-adsense { /* Target promo block containers */
                margin-top: 1.5rem !important;
                margin-bottom: 1.5rem !important;
            }
            .block-location-article_beginning,
            .block-location-after_content,
            .block-location-article_bottom_banner {
                 /* Additional spacing adjustments if needed by placement */
            }
            /* Ensure spacing for items injected directly into content */
             .in-article-item-container { /* Wrapper for injected items */
                 margin-top: 2rem !important;
                 margin-bottom: 2rem !important;
                 padding-top: 1rem; /* Add some padding if needed */
                 padding-bottom: 1rem;
                 border-top: 1px solid #eee; /* Optional separator */
                 border-bottom: 1px solid #eee;
             }
             /* Styling for Parsedown content within the excerpt blockquote */
             .excerpt-blockquote p:first-of-type { margin-top: 0; }
             .excerpt-blockquote p:last-of-type { margin-bottom: 0; }

             /* Styling for Bonus Article Section */
             .bonus-article-section h2 { /* Style the main bonus title */ }
             .bonus-article-section h3 { /* Style the linked bonus article title */ }
             .bonus-article-section .prose { /* Ensure bonus content has prose styles */ }
             .bonus-article-section img {
                border-radius: 0.5rem; /* Example: Add rounded corners */
                box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Example: Add subtle shadow */
             }

             /* --- YouTube Responsive & Lazy Load Styles --- */
             /* Container for responsive iframe (maintains aspect ratio) */
             .youtube-iframe-container {
                position: relative;
                width: 100%;
                padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
                height: 0;
                overflow: hidden;
                border-radius: 0.5rem; /* Rounded corners */
                box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Subtle shadow */
                margin-bottom: 1rem; /* Spacing below only */
                margin-top: 1.5rem; /* Spacing above */
             }
             .youtube-iframe-container iframe {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border: 0;
             }
             /* Placeholder container using CSS for aspect ratio */
             .yt-lazy-placeholder-container {
                position: relative; /* Needed for absolute children */
                display: block; /* Ensure it takes block layout */
                width: 100%;
                padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
                height: 0; /* Collapse height, padding provides it */
                overflow: hidden; /* Ensure children are clipped */
                background-color: #eee; /* Fallback background */
                /* Tailwind classes used in PHP: cursor-pointer group rounded-lg shadow-md mb-4 */
             }
             .yt-lazy-placeholder-container img.yt-lazy-thumb-image { /* Target specific image class */
                position: absolute; /* Position relative to container */
                top: 0;
                left: 0;
                width: 100%; /* Fill container width */
                height: 100%; /* Fill container height */
                object-fit: cover; /* Cover the area, cropping if needed */
                object-position: center; /* Center the image content */
                /* Tailwind classes used in PHP: transition-opacity duration-300 opacity-80 group-hover:opacity-100 */
             }
             /* Override prose margin specifically for the YT thumbnail image */
             .article-content .yt-lazy-placeholder-container .yt-lazy-thumb-image {
                 margin-top: 0;
                 margin-bottom: 0;
             }
             /* --- End YouTube Styles --- */

            </style>

            <?php
            // --- Display Promo Unit at Article Beginning ---
            // Placement: Immediately after the header/featured image.
            echo displayAdsForPlacement($pdo, 'article_beginning', $promoContext, 1, 1);
            ?>

            <?php // --- Article Content --- ?>
            <div class="article-content prose prose-lg max-w-none prose-headings:font-montserrat prose-headings:text-dark-contrast prose-a:text-primary hover:prose-a:text-primary/80 prose-img:rounded-xl prose-img:shadow-sm">
                <?php
                    // Include internal_links.php if not already included
                    if (!function_exists('processContentWithInternalLinks')) {
                        require_once 'includes/internal_links.php';
                    }

                    // 1. Parse Markdown content to HTML using Parsedown
                    $articleHtmlContent = $Parsedown->text($article['content']);

                    // 2. Process the HTML for lazy YouTube embeds
                    $articleHtmlContent = processContentForLazyYoutube($articleHtmlContent);

                    // 3. Process the HTML for internal links if enabled for this article
                    if ($article['enable_auto_linking'] ?? true) {
                        $articleHtmlContent = processContentWithInternalLinks($pdo, $articleHtmlContent);
                    }

                    // 4. Inject In-Content Promotional Items
                    $contentWithPromos = injectAdsIntoContent($articleHtmlContent, $pdo, $promoContext);

                    // 5. Output the final article content
                    echo $contentWithPromos;

                    // 6. DISPLAY THE BONUS ARTICLE HTML
                    // This will output the bonus article section (with image if available)
                    // Note: Bonus article content was already processed for YT embeds where $bonus_article_html was built
                    echo $bonus_article_html;
                 ?>
            </div> <?php // End .article-content ?>

            <?php // ================== COMMENTS SECTION ================== ?>
            <?php // Importance for Facebook: Fosters "Meaningful Interaction" which Facebook's algorithm values. ?>
            <?php
            // Fetch initial set of comments for the article
            $commentsData = fetchComments($pdo, $article['id'], 5); // Fetch first 5 top-level comments
            $comments = $commentsData['comments'];
            $totalComments = $commentsData['total'];
            $commentsPerPage = 5; // Number of comments to load per page/click
            ?>
            <div class="mt-6 md:mt-8 p-4 md:p-6 bg-gradient-to-r from-white to-gray-50 rounded-xl border border-border shadow-md not-prose" id="comments-section">
                <?php // Comments Header ?>
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mb-4 md:mb-6">
                    <h3 class="text-lg md:text-xl font-montserrat font-extrabold text-primary flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        Komentari <span class="text-sm font-normal text-gray-500 ml-2">(<span id="comment-count"><?php echo $totalComments; ?></span>)</span> <?php // Display total comment count (Social Proof) ?>
                    </h3>
                    <?php // Display "Active conversation" indicator only if there are comments ?>
                    <?php if ($totalComments > 0): ?>
                    <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full font-medium flex items-center self-start sm:self-center">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></span>
                        Aktivni razgovor <?php // Social Proof Signal ?>
                    </span>
                    <?php endif; ?>
                </div>

                <?php // Comments List Container ?>
                <div class="space-y-4 md:space-y-6 mb-6 md:mb-8" id="comments-list">
                    <?php if (empty($comments)): ?>
                        <p id="no-comments-message" class="text-center text-gray-500 italic">Budite prvi koji će ostaviti komentar!</p>
                    <?php else: ?>
                        <?php echo renderComments($comments); // Render initial comments using helper function ?>
                    <?php endif; ?>
                </div>

                <?php // "Load More" Button (shown if more comments exist) ?>
                <?php if ($totalComments > $commentsPerPage): ?>
                <div class="flex justify-center my-4 md:my-6" id="load-more-container">
                    <button id="load-more-comments" data-article-id="<?php echo $article['id']; ?>" data-offset="<?php echo $commentsPerPage; ?>" class="text-primary font-medium text-sm hover:underline flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                        Učitaj još (<span id="remaining-count"><?php echo $totalComments - count($comments); ?></span>) <?php // Show remaining count ?>
                    </button>
                    <span id="loading-comments-indicator" class="text-gray-500 italic hidden">Učitavanje...</span> <?php // Loading indicator for AJAX ?>
                </div>
                <?php endif; ?>

                <?php // Comment Submission Form ?>
                <div class="mt-4 bg-white p-4 md:p-5 rounded-lg border border-primary/10 shadow-sm">
                    <h4 class="font-montserrat font-bold text-base md:text-lg mb-2 text-gray-800 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 mr-1.5 md:mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Ostavite komentar <?php // Clear Call to Action ?>
                    </h4>

                    <?php // Encouragement Message ?>
                    <div class="mb-3 md:mb-4 p-2 md:p-3 bg-blue-50 rounded-lg text-xs md:text-sm text-gray-600">
                        <p class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 mr-1.5 md:mr-2 text-blue-500 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>
                                <strong>Vaše mišljenje je važno!</strong> Pridružite se razgovoru i podijelite svoje iskustvo. <?php // Prompting interaction ?>
                            </span>
                        </p>
                    </div>

                    <?php // The actual form (requires JS for submission handling via AJAX) ?>
                    <form id="comment-form" class="space-y-3 md:space-y-4">
                         <input type="hidden" name="action" value="add_comment"> <?php // Action for AJAX handler ?>
                         <input type="hidden" name="article_id" value="<?php echo $article['id']; ?>"> <?php // Associate comment with article ?>
                         <input type="hidden" name="parent_id" value=""> <?php // Set by JS for replies ?>

                        <?php // Form Status Messages (populated by JS) ?>
                        <div id="comment-form-success" class="hidden bg-green-100 border border-green-300 text-green-800 px-3 py-2 rounded text-xs md:text-sm">Komentar uspješno poslan!</div>
                        <div id="comment-form-error" class="hidden bg-red-100 border border-red-300 text-red-700 px-3 py-2 rounded text-xs md:text-sm"></div>

                        <?php // Name Input ?>
                        <div>
                            <label for="comment-name" class="block text-xs md:text-sm font-medium text-gray-700 mb-1">Ime ili nadimak *</label>
                            <input type="text" id="comment-name" name="name" required
                                   placeholder="Npr. Ana, Marko K...."
                                   class="w-full border border-gray-200 rounded-lg p-2 md:p-3 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 text-sm">
                            <div class="text-xs text-gray-500 mt-1">Koristite nadimak ako želite ostati anonimni</div>
                        </div>
                        <?php // Comment Text Area ?>
                        <div>
                            <label for="comment-text" class="block text-xs md:text-sm font-medium text-gray-700 mb-1">Vaš komentar *</label>
                            <textarea id="comment-text" name="comment" rows="3" required
                                class="w-full border border-gray-200 rounded-lg p-2 md:p-3 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 text-sm"
                                placeholder="Podijelite svoje mišljenje, pitanje ili iskustvo..."></textarea>
                        </div>
                        <?php // Submit Button ?>
                        <div class="flex justify-end items-center mt-3 md:mt-4">
                            <button type="submit" id="submit-comment-button"
                                class="btn px-4 py-2 md:px-6 font-montserrat font-semibold bg-primary text-white hover:bg-primary/90 transition-all duration-300 rounded-lg flex items-center text-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 md:h-4 md:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                </svg>
                                Pošalji komentar
                            </button>
                        </div>
                    </form>
                </div> <?php // End Comment Form Container ?>
            </div> <?php // End Comments Section ?>

            <?php
            // --- Helper Function: Render Comments Recursively ---
            // Renders a list of comments and their nested replies.
            function renderComments(array $comments): string {
                $html = '';
                foreach ($comments as $comment) {
                    $html .= '<div class="comment-item p-3 md:p-4 bg-white rounded-lg border border-gray-100 shadow-sm transition-all hover:border-primary/20" id="comment-' . $comment['id'] . '">';
                    $html .= '  <div class="flex items-start gap-2 md:gap-3">';

                    // Generate simple avatar with initials and varied background color
                    $initials = '';
                    $nameParts = explode(' ', trim($comment['user_name']));
                    if (!empty($nameParts[0])) $initials .= mb_strtoupper(mb_substr($nameParts[0], 0, 1));
                    if (count($nameParts) > 1 && !empty($nameParts[count($nameParts)-1])) $initials .= mb_strtoupper(mb_substr($nameParts[count($nameParts)-1], 0, 1));
                    if (empty($initials)) $initials = '?';
                    $colorIndex = abs(crc32($comment['user_name'])) % 5; // Simple hash for color variation
                    $avatarColors = ['from-blue-500 to-purple-500', 'from-pink-500 to-red-500', 'from-green-400 to-blue-500', 'from-yellow-400 to-orange-500', 'from-indigo-500 to-violet-500'];
                    $avatarClass = $avatarColors[$colorIndex];

                    $html .= '    <div class="w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-r ' . $avatarClass . ' flex items-center justify-center text-white font-bold text-xs md:text-sm flex-shrink-0">';
                    $html .= escape($initials);
                    $html .= '    </div>';
                    $html .= '    <div class="flex-grow min-w-0">'; // Ensure comment text wraps
                    $html .= '      <div class="flex flex-wrap items-start justify-between gap-1">'; // User name and time
                    $html .= '        <h4 class="font-montserrat font-semibold text-sm md:text-base text-gray-800">' . escape($comment['user_name']) . '</h4>';
                    $html .= '        <span class="text-xs text-gray-500">' . formatCommentDate($comment['created_at']) . '</span>'; // Display date only
                    $html .= '      </div>';
                    $html .= '      <p class="mt-1 md:mt-2 text-sm text-gray-700 break-words">' . nl2br(escape($comment['comment_text'])) . '</p>'; // Display comment text, preserve line breaks
                    $html .= '      <div class="flex items-center mt-2 md:mt-3 space-x-3 md:space-x-4">'; // Action buttons (Like, Reply)
                    // Like Button (Requires JS for functionality)
                    $html .= '        <button data-comment-id="' . $comment['id'] . '" class="like-button text-xs text-gray-500 hover:text-primary flex items-center transition-colors">';
                    $html .= '          <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 md:h-4 md:w-4 mr-1 like-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" /></svg>';
                    $html .= '          (<span class="like-count">' . ($comment['likes'] ?? 0) . '</span>)'; // Display like count
                    $html .= '        </button>';
                    // Reply Button (Requires JS to move focus/set parent_id in form)
                    $html .= '        <button data-comment-id="' . $comment['id'] . '" data-user-name="' . escape($comment['user_name']) . '" class="reply-button text-xs text-gray-500 hover:text-primary flex items-center transition-colors">';
                    $html .= '          <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 md:h-4 md:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" /></svg>';
                    $html .= '          Odgovori';
                    $html .= '        </button>';
                    $html .= '      </div>';

                    // --- Render Replies Recursively ---
                    if (!empty($comment['replies'])) {
                        $html .= '<div class="mt-3 md:mt-4 pl-3 md:pl-6 border-l-2 border-gray-100 space-y-3 md:space-y-4">'; // Indented container for replies
                        $html .= renderComments($comment['replies']); // Recursive call for nested replies
                        $html .= '</div>';
                    }
                    // --- End Replies ---

                    $html .= '    </div>'; // End flex-grow
                    $html .= '  </div>'; // End flex
                    $html .= '</div>'; // End comment-item
                }
                return $html;
            }

            // This function has been moved to includes/functions.php
            // We now use formatCommentDate() instead of timeAgo() to display only the date without time
            ?>
            <?php // ================== END COMMENTS SECTION ================== ?>


            <?php // ================== IDENTITY-BASED SHARING BLOCK ================== ?>
            <?php // Importance for Facebook: Uses psychological triggers (identity signaling, social proof) to encourage sharing. ?>
            <?php if ($article['enable_fb_share']): // Check if sharing is enabled for this article ?>
            <div class="mt-6 md:mt-8 not-prose"> <?php // not-prose prevents Tailwind typography styles from applying here ?>
                <div class="p-4 md:p-5 bg-gradient-to-r from-fuchsia-50 to-violet-50 rounded-xl border border-purple-100 shadow-sm">
                    <div class="mb-3 md:mb-4 text-center">
                        <?php // Headline using Identity Signaling ?>
                        <h3 class="font-bold text-lg md:text-xl text-gray-800 mb-1 md:mb-2">
                            Kakva si zapravo prijateljica?
                        </h3>
                        <?php // Supporting text with a statistic (Social Proof/Benefit) ?>
                        <p class="text-xs md:text-sm text-gray-600 mb-3 md:mb-4 max-w-md mx-auto">
                            Prijatelji koji dijele vrijedne informacije imaju 3.2x jače veze. Pokaži da misliš na svoje prijateljice.
                        </p>

                        <?php // Visual representation of Identities ?>
                        <div class="flex flex-col items-center py-2 md:py-3">
                            <?php // Positive Identity (Desired State) ?>
                            <div class="w-full max-w-md p-3 md:p-4 bg-white rounded-xl shadow-sm border border-purple-100 mb-2 md:mb-3">
                                <div class="flex items-center space-x-2 md:space-x-3 pb-2 md:pb-3 border-b border-gray-100">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 md:h-8 md:w-8 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                    </svg>
                                    <div>
                                        <h4 class="font-bold text-base md:text-lg text-gray-900">Empatična prijateljica</h4>
                                        <p class="text-xs text-gray-500">93% žena želi biti ovakva prijateljica</p> <?php // Social Proof ?>
                                    </div>
                                </div>
                                <ul class="mt-2 md:mt-3 space-y-1.5 md:space-y-2"> <?php // Traits of the positive identity ?>
                                    <li class="flex items-start space-x-1.5 md:space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 text-purple-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                                        <span class="text-xs md:text-sm text-gray-700">Misli na potrebe drugih</span>
                                    </li>
                                    <li class="flex items-start space-x-1.5 md:space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 text-purple-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                                        <span class="text-xs md:text-sm text-gray-700">Dijeli korisne informacije</span> <?php // Connects trait to sharing ?>
                                    </li>
                                    <li class="flex items-start space-x-1.5 md:space-x-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 text-purple-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>
                                        <span class="text-xs md:text-sm text-gray-700">Brine o dobrobiti prijatelja</span>
                                    </li>
                                </ul>
                            </div>

                            <?php // Contrasting Identity (Undesired State) ?>
                            <div class="w-full max-w-md p-3 md:p-4 bg-white rounded-xl shadow-sm border border-purple-100 opacity-70"> <?php // Reduced opacity for contrast ?>
                                <div class="flex items-center space-x-2 md:space-x-3 pb-2 md:pb-3 border-b border-gray-100">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 md:h-8 md:w-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20"> <?php // Gray icon ?>
                                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                                    </svg>
                                    <div>
                                        <h4 class="font-bold text-base md:text-lg text-gray-700">Zaboravna prijateljica</h4>
                                        <p class="text-xs text-gray-500">Ne misli na potrebe svojih prijateljica</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php // Sharing Buttons (Calls to Action) ?>
                    <div class="grid grid-cols-1 gap-2 md:gap-3 mb-3 md:mb-4">
                        <?php // Facebook Share Button ?>
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode($og_url); ?>" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center px-4 py-2.5 md:py-3 rounded-lg border border-blue-300 bg-blue-600 text-white hover:bg-blue-700 transition-all duration-300 shadow-sm text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                            </svg>
                            <span class="font-medium">Podijeli na Facebook</span> <?php // Clear action text ?>
                        </a>

                         <?php // Facebook Messenger Send Button ?>
                        <?php // IMPORTANT: Replace YOUR_FACEBOOK_APP_ID with your actual Facebook App ID for the Send dialog to work correctly. ?>
                        <?php $fbAppId = defined('FB_APP_ID') && FB_APP_ID ? FB_APP_ID : 'YOUR_FACEBOOK_APP_ID'; // Use defined constant or placeholder ?>
                        <a href="https://www.facebook.com/dialog/send?app_id=<?php echo $fbAppId; ?>&link=<?php echo urlencode($og_url); ?>&redirect_uri=<?php echo urlencode($og_url); ?>" target="_blank" rel="noopener noreferrer" class="flex items-center justify-center px-4 py-2.5 md:py-3 rounded-lg border border-blue-300 bg-blue-50 hover:bg-blue-100 text-blue-700 transition-all duration-300 shadow-sm text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0C5.373 0 0 4.974 0 11.111c0 3.498 1.744 6.617 4.47 8.652v4.237l4.086-2.242c1.09.301 2.246.464 3.444.464 6.627 0 12-4.974 12-11.111C24 4.974 18.627 0 12 0zm1.193 14.963l-3.056-3.259-5.963 3.259 6.559-6.963 3.13 3.259 5.889-3.259-6.559 6.963z"/>
                            </svg>
                            <span class="font-medium">Pošalji u Messenger</span>
                        </a>

                        <?php // Viber Share Button ?>
                        <a href="viber://forward?text=<?php echo urlencode('Mislim da bi te ovo moglo zanimati: ' . $og_title . ' ' . $og_url); ?>" class="flex items-center justify-center px-4 py-2.5 md:py-3 rounded-lg border border-purple-300 bg-purple-600 text-white hover:bg-purple-700 transition-all duration-300 shadow-sm text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 md:h-5 md:w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.5.5C6.2.5 1.1 5.3 1 11.2c0 2 .6 4 1.7 5.5-.1.6-.3 1.5-.7 2.3-.1.3.1.6.4.6 1.8-.2 3.1-.7 3.9-1 2.7 1.4 6.1 1.6 9 .7 4.2-1.3 7-5.1 7.1-9.3C22.6 5.4 18.1.5 12.5.5zm0 20.1c-2.1 0-4.2-.4-6.2-1.4-.3-.2-.7-.1-1 0-.5.2-1.2.5-1.9.7.3-.7.5-1.3.6-1.9.1-.3 0-.6-.2-.8C2.8 15.8 2 13.6 2 11.3 2 5.8 6.8 1.5 12.5 1.5c5.5 0 10 4.1 10.1 9.1 0 5-4.7 9.9-10.1 10zM7.9 7.4c.3 0 .5.1.6.5.2.5.6 1.4 1.2 2.3.1.2.1.4 0 .5l-.3.4c-.1.1-.1.3 0 .4.6 1 1.5 1.9 2.6 2.5.1.1.3.1.4 0l.4-.3c.2-.1.4-.1.5 0 .9.6 1.9 1 2.4 1.2.3.1.5.4.4.7-.1.5-.6 1.4-1.2 1.7-.2.1-.4.1-.7.1-1.6-.3-3.4-1.3-4.9-2.8-1.3-1.3-2.3-3-2.7-4.5 0-.2 0-.5.1-.7.4-.6 1.2-1.1 1.7-1.2h0z"/>
                            </svg>
                            <span class="font-medium">Podijeli na Viber</span>
                        </a>
                    </div>

                    <?php // Copy Link Button (Requires JS) ?>
                    <div class="text-center">
                        <button onclick="copyArticleLink(this, '<?php echo $og_url; ?>')" class="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 rounded-full border border-gray-200 hover:bg-gray-50 transition-all duration-300 text-gray-600 hover:text-purple-600 hover:border-purple-300 no-underline text-xs md:text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 md:h-4 md:w-4 mr-1 md:mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                            <span class="copy-link-text">Kopiraj link</span> <?php // Text changes on click via JS ?>
                        </button>
                    </div>

                    <?php // Final Social Proof Element ?>
                    <div class="mt-3 md:mt-4 text-center text-xs text-gray-500 flex justify-center">
                        <span>💜 87% čitatelja dijeli ovaj članak s prijateljicama 💜</span> <?php // Reinforces sharing behavior ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <?php // ================== END IDENTITY-BASED SHARING BLOCK ================== ?>


            <?php // --- Article Tags (Below Sharing Block) --- ?>
            <?php if (!empty($tags)): ?>
            <div class="mt-6 not-prose">
                <div class="tag-container p-4 bg-gray-50 rounded-xl border border-border shadow-sm"> <?php // Container for tags ?>
                    <span class="font-semibold mr-2">Oznake:</span> <?php // Label ?>
                    <?php foreach ($tags as $tag):
                        $tagStyle = $tagStyles[$tag['slug']] ?? null;
                        $tagIcon = $tagStyle['icon'] ?? $genericTagIcon;
                        $tagColorClass = $tagStyle['color'] ?? 'tag-default';
                    ?>
                    <a href="/tag/<?php echo escape($tag['slug']); ?>/" class="<?php echo $tagColorClass; ?> flex items-center text-sm px-3 py-1 rounded-full transition-colors">
                         <?php echo $tagIcon; ?>
                        <?php echo escape($tag['name']); ?>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <?php
            // --- Display Promo Unit After Content/Tags ---
            echo displayAdsForPlacement($pdo, 'after_content', $promoContext, 1, 1);
            ?>

            <?php // --- Author Bio Box --- ?>
            <?php $authorBio = $article['author_bio'] ?? null; ?>
            <?php if (!empty($authorBio)): ?>
            <div class="mt-8 p-4 bg-white rounded-xl border border-border shadow-sm not-prose">
                <div class="flex flex-col sm:flex-row items-center sm:items-start gap-4">
                    <?php // Author Avatar ?>
                    <div class="w-20 h-20 rounded-full overflow-hidden flex-shrink-0 bg-gray-200 flex items-center justify-center">
                         <?php if ($authorAvatarUrl): ?>
                             <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo escape($authorAvatarData['alt']); ?>" class="w-full h-full object-cover" loading="lazy" width="80" height="80" />
                         <?php else: ?>
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                         <?php endif; ?>
                    </div>
                    <?php // Author Details ?>
                    <div>
                        <h3 class="font-montserrat font-bold text-lg mb-2">O autoru</h3>
                        <h4 class="font-montserrat font-semibold mb-2"><?php echo escape($authorName); ?></h4>
                        <p class="text-sm text-gray-600 mb-3"><?php echo escape($authorBio); ?></p>
                        <?php // Add link to author page if available ?>
                        <?php /*
                        if (!empty($article['author_slug'])) {
                            echo '<a href="/author/' . escape($article['author_slug']) . '/" class="text-primary text-sm font-medium hover:underline">Više članaka autora</a>';
                        }
                        */ ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

        </div> <?php // End Main Article .card ?>

        <?php // --- Recommended Articles Section --- ?>
        <?php // Importance for Facebook: Keeps users engaged on site, potentially leading to more shares later. ?>
        <?php if ($article['include_in_recommendations'] && !empty($recommendedArticles)): ?>
        <div class="mt-8 card p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-montserrat font-extrabold">Preporučeno za vas</h3>
                <a href="<?php echo SITE_URL; ?>" class="text-primary text-sm font-medium hover:underline">Vidi sve</a> <?php // Link to homepage or relevant category ?>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <?php // Grid layout for recommendations ?>
                 <?php
                 // Get affiliate promos for the 'recommended' placement
                 $recommendedPromos = getActiveAdsForPlacement($pdo, 'recommended', $promoContext, 3); // Fetch up to 3 promos

                 // --- Combine Articles and Promos ---
                 $displayItems = [];
                 // Add actual articles (limit to e.g., 3-4 to make space for promos)
                 $articleCount = min(count($recommendedArticles), 3);
                 for ($i = 0; $i < $articleCount; $i++) {
                     $displayItems[] = ['type' => 'article', 'data' => $recommendedArticles[$i]];
                 }
                 // Add fetched promos
                 foreach ($recommendedPromos as $promo) {
                     // Structure promo data similarly to article data for consistent rendering
                     $articleLikePromo = [
                         'id' => $promo['id'] ?? 0,
                         'title' => $promo['title'] ?? 'Preporučeni članak',
                         'slug' => '', // Promos don't have slugs
                         'featured_image' => $promo['featured_image'] ?? '',
                         'published_at' => $promo['published_at'] ?? date('Y-m-d H:i:s'), // Use promo date or fallback
                         'reading_time' => $promo['reading_time'] ?? rand(2, 5), // Optional random time
                         'external_url' => $promo['external_url'] ?? '#', // Link for the promo
                         'open_in_new_tab' => $promo['open_in_new_tab'] ?? 1, // Should it open in new tab?
                         'show_sponsored_label' => $promo['show_sponsored_label'] ?? 1, // Show "Sponsored"?
                     ];
                     $displayItems[] = ['type' => 'promo', 'data' => $articleLikePromo];
                 }

                 // Shuffle the combined list for variety
                 shuffle($displayItems);

                 // Limit the total number of items displayed (e.g., 6)
                 $maxItems = min(count($displayItems), 6);

                 // Render the items using the helper function
                 for ($i = 0; $i < $maxItems; $i++) {
                     $item = $displayItems[$i];
                     $itemContext = $promoContext; // Start with base context
                     $itemContext['item_data'] = $item['data']; // Add item data
                     $itemContext['is_promo'] = $item['type'] === 'promo'; // Mark if promo

                     // Add specific placement/position if it's a promo
                     if ($itemContext['is_promo']) {
                         $itemContext['current_placement'] = 'recommended';
                         $itemContext['click_position'] = 'recommended_' . ($i + 1);
                     }
                     renderRecommendedItemWithContext($itemContext); // Use the card rendering function
                 }
                 ?>
            </div>
        </div>
        <?php endif; ?>

        <?php // --- Bottom Banner Ad/Promo Slot --- ?>
        <div class="mt-8 px-0 py-0 bg-transparent text-center"> <?php // Container for a full-width banner ?>
            <?php
            // Display ad/promo for the 'article_bottom_banner' placement
            echo displayAdsForPlacement($pdo, 'article_bottom_banner', $promoContext, 1, 1);
            ?>
        </div>

    </div> <?php // End Main Content Column ?>

</div> <?php // End Main Flex Container ?>

<?php // --- JavaScript for Interactions --- ?>
<script>
// Function to copy the article link to the clipboard
function copyArticleLink(buttonElement, url) {
    const textSpan = buttonElement.querySelector('.copy-link-text');
    if (!textSpan) return; // Safety check

    const originalText = textSpan.textContent; // Store original button text

    // Check if Clipboard API is available
    if (!navigator.clipboard) {
        textSpan.textContent = 'Greška!'; // Indicate error
        setTimeout(() => { if(textSpan) textSpan.textContent = originalText; }, 2000); // Revert after 2s
        return;
    }

    // Try to write the URL to the clipboard
    navigator.clipboard.writeText(url).then(() => {
        if(textSpan) textSpan.textContent = 'Kopirano!'; // Indicate success
        setTimeout(() => { if(textSpan) textSpan.textContent = originalText; }, 2000); // Revert after 2s
    }).catch(err => {
        console.error('Failed to copy link: ', err); // Log error
        if(textSpan) textSpan.textContent = 'Greška!'; // Indicate error
        setTimeout(() => { if(textSpan) textSpan.textContent = originalText; }, 2000); // Revert after 2s
    });
}

// --- YouTube Lazy Load Functionality ---
document.addEventListener('DOMContentLoaded', () => {
    // Select placeholders by specific class OR data attribute for robustness
    const youtubePlaceholders = document.querySelectorAll('.yt-lazy-placeholder-container[data-youtube-id]');

    youtubePlaceholders.forEach(placeholder => {
        placeholder.addEventListener('click', () => {
            const videoId = placeholder.getAttribute('data-youtube-id');
            if (videoId) {
                // Create the iframe container
                const iframeContainer = document.createElement('div');
                iframeContainer.className = 'youtube-iframe-container'; // Apply responsive styles

                // Create the iframe
                const iframe = document.createElement('iframe');
                // Use youtube-nocookie.com for enhanced privacy if needed
                iframe.setAttribute('src', `https://www.youtube-nocookie.com/embed/${videoId}?autoplay=1&rel=0`); // Autoplay added, rel=0 prevents related videos from same channel
                iframe.setAttribute('frameborder', '0');
                iframe.setAttribute('allow', 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share');
                iframe.setAttribute('allowfullscreen', '');
                // No need for width/height attributes here as CSS handles it

                // Append iframe to container
                iframeContainer.appendChild(iframe);

                // Replace placeholder with the iframe container
                placeholder.parentNode.replaceChild(iframeContainer, placeholder);
            }
        });
    });
});


// --- Additional JavaScript Notes ---
// 1. Comments AJAX: Ensure you have JavaScript functions to handle:
//    - Submitting the #comment-form via AJAX (POST to a backend handler like ajax_handler.php).
//    - Handling responses (success/error messages, appending new comments).
//    - Handling clicks on .reply-button (setting the parent_id hidden input, maybe focusing the form).
//    - Handling clicks on .like-button (sending AJAX request to update likes, updating the .like-count).
//    - Handling clicks on #load-more-comments (sending AJAX GET request, appending results, updating offset).
// 2. Ad/Promo Tracking: Implement JavaScript (e.g., using Intersection Observer) to accurately track impressions for ads/promos when they become visible on the screen. Send tracking data via AJAX.
// 3. Ensure all JS targets the correct element IDs and classes used in this PHP file.

</script>

<?php // --- Include AVG Time Trick JavaScript if enabled --- ?>
<?php if ($useAvgTimeTrick): ?>
<script src="<?php echo SITE_URL; ?>/js/avg-time-trick.js"></script>
<?php endif; ?>

<?php // --- Include Footer --- ?>
<?php include 'includes/footer.php'; ?>