# Audit System Usage Guidelines

## Quick Start

The Lako & Fino CMS Audit System is now fully integrated and ready for production use. This guide provides practical usage guidelines for getting the most out of the system.

## Basic Usage

### 1. Run a Complete Audit

```bash
# Basic audit of the CMS
php bin/audit.php

# Audit with verbose output
php bin/audit.php --verbose

# Quiet mode for automated scripts
php bin/audit.php --quiet
```

### 2. Check System Status

```bash
# Validate system configuration
php bin/audit.php validate

# Check current audit status
php bin/audit.php status

# View system configuration
php bin/audit.php config
```

### 3. Resume Interrupted Audits

```bash
# Resume from last checkpoint
php bin/audit.php resume

# Watch progress in real-time
php bin/audit.php watch
```

## Advanced Usage

### Custom Configuration

```bash
# Use custom configuration file
php bin/audit.php --config=custom-audit.json

# Audit specific directory
php bin/audit.php --target=/path/to/cms

# Custom report directory
php bin/audit.php --report-dir=/custom/reports
```

### Performance Tuning

```bash
# For large codebases (increase timeout and memory)
php -d memory_limit=512M bin/audit.php --timeout=1200

# For quick scans (smaller files only)
php bin/audit.php --max-file-size=102400 --timeout=60

# Clear logs before starting
php bin/audit.php --clear-logs
```

## Understanding Reports

### Report Formats

The system generates three types of reports:

1. **Markdown (.md)** - Human-readable detailed report
2. **JSON (.json)** - Machine-readable structured data  
3. **HTML (.html)** - Web-viewable formatted report

### Priority Levels

- **Critical** - Security vulnerabilities requiring immediate attention
- **High** - Important issues that should be addressed soon
- **Medium** - Code quality improvements
- **Low** - Minor optimizations and suggestions

### Priority Areas

Files are classified as:
- **PRIORITY_AREA** - Critical system files (config, admin, security)
- **NON_PRIORITY** - General application files

## Common Workflows

### 1. Initial Security Assessment

```bash
# Run comprehensive audit
php bin/audit.php --verbose

# Review critical and high-priority findings first
# Focus on files marked as PRIORITY_AREA
# Address security vulnerabilities immediately
```

### 2. Regular Maintenance Audits

```bash
# Weekly audit with comparison
php bin/audit.php --clear-logs

# Review new findings since last audit
# Track progress on previously identified issues
```

### 3. Pre-Deployment Validation

```bash
# Quick validation before deployment
php bin/audit.php --timeout=300 --max-file-size=1048576

# Ensure no new critical issues introduced
# Verify security configurations are correct
```

### 4. Performance Optimization

```bash
# Focus on performance issues
php bin/audit.php --verbose

# Review performance findings in reports
# Implement recommended optimizations
# Re-run audit to validate improvements
```

## Interpreting Results

### Audit Summary Example

```
📁 Files Analyzed: 84
🔍 Total Findings: 7234
🚨 Critical Issues: 0
⚠️  High Priority: 114
📊 Medium Priority: 5580
ℹ️  Low Priority: 1540
🎯 Priority Area Issues: 1685
```

### Action Priority

1. **Address Critical Issues First** - Security vulnerabilities
2. **Review High Priority** - Important functionality issues
3. **Plan Medium Priority** - Code quality improvements
4. **Consider Low Priority** - Minor optimizations

### Finding Categories

- **Security** - SQL injection, XSS, authentication issues
- **Performance** - Database queries, caching, asset loading
- **Quality** - Code complexity, naming conventions, duplication
- **Architecture** - MVC adherence, dependency management
- **Configuration** - Security settings, performance tuning

## Best Practices

### Regular Auditing

- **Schedule Weekly Audits** - Catch issues early
- **Monitor Trends** - Track improvement over time
- **Document Changes** - Keep record of fixes implemented
- **Validate Fixes** - Re-run audit after making changes

### Security Focus

- **Prioritize Security** - Address vulnerabilities immediately
- **Review Configurations** - Check config.php and .htaccess regularly
- **Validate Input Handling** - Ensure proper sanitization
- **Monitor Authentication** - Check session management

### Performance Monitoring

- **Database Optimization** - Address N+1 queries and missing indexes
- **Asset Management** - Optimize CSS/JS loading
- **Caching Implementation** - Add appropriate caching layers
- **Image Optimization** - Ensure responsive image delivery

### Code Quality

- **Maintain Standards** - Follow consistent naming conventions
- **Reduce Complexity** - Break down large functions
- **Eliminate Duplication** - Refactor repeated code
- **Improve Architecture** - Enhance MVC structure

## Troubleshooting

### Common Issues

#### Memory Limit Exceeded
```bash
# Increase PHP memory limit
php -d memory_limit=512M bin/audit.php
```

#### Slow Performance
```bash
# Reduce file size limit and timeout
php bin/audit.php --max-file-size=102400 --timeout=60
```

#### Permission Errors
```bash
# Fix directory permissions
chmod 755 data reports logs
```

#### Target Directory Not Found
```bash
# Verify and update target path
php bin/audit.php --target=/correct/path/to/cms
```

### Debug Information

```bash
# Enable verbose logging
php bin/audit.php --verbose

# Check log files
tail -f logs/audit.log
tail -f logs/error.log
tail -f logs/performance.log
```

### System Health

```bash
# Comprehensive system validation
php bin/audit.php validate

# Check system status
php bin/audit.php status

# Run integration validation
php validate_integration.php
```

## Integration with Development Workflow

### Git Hooks

Add audit checks to your Git workflow:

```bash
# Pre-commit hook
#!/bin/bash
php audit-system/bin/audit.php --quiet --timeout=60
if [ $? -ne 0 ]; then
    echo "Audit failed - commit rejected"
    exit 1
fi
```

### CI/CD Pipeline

```yaml
# Example CI configuration
audit:
  script:
    - cd audit-system
    - php bin/audit.php --quiet --timeout=300
    - php validate_integration.php
  artifacts:
    reports:
      - audit-system/reports/
```

### Automated Monitoring

```bash
# Cron job for regular audits
0 2 * * 1 cd /path/to/cms/audit-system && php bin/audit.php --quiet
```

## Testing and Validation

### PHPUnit Testing

The system includes comprehensive PHPUnit tests:

```bash
# Run all tests
php -d extension=mbstring phpunit.phar

# Run integration tests
php -d extension=mbstring phpunit.phar tests/Integration/

# Run specific test
php -d extension=mbstring phpunit.phar --filter testSystemInitialization tests/Integration/CompleteSystemIntegrationTest.php

# Test suite runner
php run_all_tests.php
```

### System Validation

```bash
# Quick integration validation
php validate_integration.php

# Complete system demonstration
php run_complete_integration.php
```

## Support and Maintenance

### System Updates

1. **Update Dependencies**
   ```bash
   composer update
   ```

2. **Validate Configuration**
   ```bash
   php bin/audit.php validate
   ```

3. **Test Integration**
   ```bash
   php validate_integration.php
   ```

### Performance Monitoring

- Monitor audit execution time
- Track memory usage patterns
- Optimize batch sizes for your environment
- Adjust timeouts based on codebase size

### Configuration Management

- Keep audit.json under version control
- Document configuration changes
- Test configuration changes before deployment
- Maintain separate configs for different environments

## Getting Help

1. **Check Documentation** - Review this guide and system documentation
2. **Validate System** - Run `php bin/audit.php validate`
3. **Check Logs** - Review audit.log and error.log files
4. **Test Integration** - Run `php validate_integration.php`
5. **Review Configuration** - Verify audit.json settings

## Conclusion

The integrated audit system provides comprehensive analysis of your CMS codebase with focus on security, performance, and code quality. Regular use of this tool will help maintain and improve your application's overall health and security posture.

Remember to:
- Run audits regularly
- Address critical issues immediately
- Track improvements over time
- Keep the system updated
- Monitor performance and adjust settings as needed

The system is designed to grow with your codebase and provide ongoing value through automated analysis and actionable recommendations.