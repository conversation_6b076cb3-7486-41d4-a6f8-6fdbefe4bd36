<?php

namespace AuditSystem\Exceptions;

/**
 * Exception thrown when file analysis operations fail
 */
class AnalysisException extends AuditException
{
    /**
     * Create exception for analyzer failure
     *
     * @param string $analyzerClass Class name of the analyzer that failed
     * @param string $filePath Path to the file being analyzed
     * @param string $reason Reason for the failure
     * @return static
     */
    public static function analyzerFailed(string $analyzerClass, string $filePath, string $reason): self
    {
        return new self("Analyzer {$analyzerClass} failed for file {$filePath}: {$reason}");
    }

    /**
     * Create exception for invalid file content
     *
     * @param string $filePath Path to the file with invalid content
     * @param string $expectedType Expected file type or format
     * @return static
     */
    public static function invalidFileContent(string $filePath, string $expectedType): self
    {
        return new self("Invalid file content in {$filePath}, expected {$expectedType}");
    }

    /**
     * Create exception for parsing failure
     *
     * @param string $filePath Path to the file that failed to parse
     * @param string $parserType Type of parser that failed
     * @return static
     */
    public static function parsingFailed(string $filePath, string $parserType): self
    {
        return new self("Failed to parse {$filePath} with {$parserType} parser");
    }

    /**
     * Create exception for timeout during analysis
     *
     * @param string $filePath Path to the file being analyzed when timeout occurred
     * @param int $timeout Timeout value in seconds
     * @return static
     */
    public static function analysisTimeout(string $filePath, int $timeout): self
    {
        return new self("Analysis of {$filePath} timed out after {$timeout} seconds");
    }
}