/**
 * reward-popup.js - Engagement Trick for Articles
 * 
 * This script creates a popup that appears when the user has scrolled 70% of the article.
 * It shows a message about qualifying for a reward and a countdown timer.
 * When clicked, it shows a popup with instructions to message the Facebook page.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Configuration
    const scrollThreshold = 0.7; // Show popup after 70% of article scrolled
    const countdownSeconds = 60; // Countdown time in seconds
    
    // Elements
    const articleContent = document.querySelector('.article-content');
    if (!articleContent) return; // Exit if no article content found
    
    // Create popup elements
    const popupBanner = document.createElement('div');
    popupBanner.className = 'reward-popup-banner';
    popupBanner.innerHTML = `
        <div class="reward-popup-content">
            <div class="reward-popup-icon">🎁</div>
            <div class="reward-popup-text">
                <div class="reward-popup-title">Kvalifikovali ste se za nagradnu igru!</div>
                <div class="reward-popup-subtitle">Učitavanje uputa za nagradnu igru za <span class="reward-popup-countdown">60</span> sekundi...</div>
            </div>
            <div class="reward-popup-button">Klikni ovdje</div>
        </div>
    `;
    
    // Create confirmation popup
    const confirmationPopup = document.createElement('div');
    confirmationPopup.className = 'reward-confirmation-popup';
    confirmationPopup.innerHTML = `
        <div class="reward-confirmation-content">
            <div class="reward-confirmation-close">&times;</div>
            <div class="reward-confirmation-icon">🎉</div>
            <h3 class="reward-confirmation-title">Čestitamo!</h3>
            <p class="reward-confirmation-text">
                Uspješno ste se kvalifikovali za našu nagradnu igru. Da biste potvrdili svoje učešće, 
                pošaljite poruku našoj Facebook stranici sa tekstom "NAGRADA" i link na ovaj članak.
            </p>
            <a href="https://www.facebook.com/mercislike.art" target="_blank" class="reward-confirmation-button">
                Pošalji poruku na Facebook
            </a>
            <p class="reward-confirmation-note">
                * Nagrade se dodjeljuju nasumično odabranim učesnicima koji ispune sve uslove.
            </p>
        </div>
    `;
    
    // Add styles
    const style = document.createElement('style');
    style.textContent = `
        .reward-popup-banner {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 90%;
            max-width: 480px;
            background: linear-gradient(135deg, #ff6481 0%, #ff8c66 100%);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(255, 100, 129, 0.3);
            z-index: 1000;
            opacity: 0;
            transition: all 0.3s ease;
            transform: translateX(-50%) translateY(100px);
            overflow: hidden;
        }
        
        .reward-popup-banner.show {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        
        .reward-popup-content {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: white;
        }
        
        .reward-popup-icon {
            font-size: 28px;
            margin-right: 15px;
            animation: pulse 2s infinite;
        }
        
        .reward-popup-text {
            flex: 1;
        }
        
        .reward-popup-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 4px;
        }
        
        .reward-popup-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .reward-popup-countdown {
            font-weight: bold;
        }
        
        .reward-popup-button {
            background-color: white;
            color: #ff6481;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        
        .reward-popup-button:hover {
            background-color: #fff4f5;
            transform: scale(1.05);
        }
        
        .reward-confirmation-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .reward-confirmation-popup.show {
            opacity: 1;
            visibility: visible;
        }
        
        .reward-confirmation-content {
            background-color: white;
            border-radius: 12px;
            padding: 30px;
            max-width: 90%;
            width: 400px;
            text-align: center;
            position: relative;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
        
        .reward-confirmation-close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #999;
            transition: color 0.2s ease;
        }
        
        .reward-confirmation-close:hover {
            color: #333;
        }
        
        .reward-confirmation-icon {
            font-size: 48px;
            margin-bottom: 15px;
            animation: bounce 1s ease infinite;
        }
        
        .reward-confirmation-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .reward-confirmation-text {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 20px;
            color: #555;
        }
        
        .reward-confirmation-button {
            display: inline-block;
            background: linear-gradient(135deg, #4267B2 0%, #3b5998 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            text-decoration: none;
            margin-bottom: 15px;
            transition: all 0.2s ease;
        }
        
        .reward-confirmation-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(59, 89, 152, 0.3);
        }
        
        .reward-confirmation-note {
            font-size: 12px;
            color: #999;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
    `;
    
    // Add elements to the DOM
    document.head.appendChild(style);
    document.body.appendChild(popupBanner);
    document.body.appendChild(confirmationPopup);
    
    // Variables
    let popupShown = false;
    let countdownInterval;
    let secondsLeft = countdownSeconds;
    const countdownElement = popupBanner.querySelector('.reward-popup-countdown');
    
    // Function to check scroll position
    function checkScrollPosition() {
        if (popupShown) return;
        
        const articleHeight = articleContent.offsetHeight;
        const scrollPosition = window.scrollY + window.innerHeight;
        const articleTop = getElementOffset(articleContent).top;
        const scrolledAmount = scrollPosition - articleTop;
        
        // Show popup when user has scrolled 70% of the article
        if (scrolledAmount > articleHeight * scrollThreshold) {
            showPopup();
        }
    }
    
    // Function to get element's offset from top of page
    function getElementOffset(element) {
        const rect = element.getBoundingClientRect();
        return {
            top: rect.top + window.scrollY,
            left: rect.left + window.scrollX
        };
    }
    
    // Function to show popup
    function showPopup() {
        popupShown = true;
        popupBanner.classList.add('show');
        
        // Start countdown
        countdownInterval = setInterval(updateCountdown, 1000);
    }
    
    // Function to update countdown
    function updateCountdown() {
        secondsLeft--;
        countdownElement.textContent = secondsLeft;
        
        if (secondsLeft <= 0) {
            clearInterval(countdownInterval);
            // You could automatically show the confirmation here
            // or leave it to user click
        }
    }
    
    // Event listeners
    window.addEventListener('scroll', checkScrollPosition);
    
    // Click on popup banner
    popupBanner.addEventListener('click', function() {
        confirmationPopup.classList.add('show');
        popupBanner.classList.remove('show');
        
        // Clear countdown if it's still running
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }
    });
    
    // Close confirmation popup
    const closeButton = confirmationPopup.querySelector('.reward-confirmation-close');
    closeButton.addEventListener('click', function(e) {
        e.stopPropagation();
        confirmationPopup.classList.remove('show');
    });
    
    // Prevent closing when clicking inside the confirmation content
    const confirmationContent = confirmationPopup.querySelector('.reward-confirmation-content');
    confirmationContent.addEventListener('click', function(e) {
        e.stopPropagation();
    });
    
    // Close confirmation when clicking outside
    confirmationPopup.addEventListener('click', function() {
        confirmationPopup.classList.remove('show');
    });
    
    // Check scroll position on page load (in case user has already scrolled)
    setTimeout(checkScrollPosition, 1000);
});
