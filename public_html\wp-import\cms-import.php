<?php
/**
 * Custom CMS Import Script with User Interface
 * 
 * This script provides a web interface for importing WordPress data into your custom CMS.
 * It allows testing imports, viewing previews, and controlled batch importing.
 * 
 * Place this file in the wp-import directory and access via browser.
 */

// Get the parent directory (website root) for config inclusion
$rootDir = dirname(__DIR__);

// Include your CMS configuration file from the parent directory
require_once $rootDir . '/config.php';
require_once $rootDir . '/includes/functions.php';

// Configuration - adjust paths to be relative to the script location
$import_directory = __DIR__; // Current directory (wp-import)
$import_file = $import_directory . '/wp-export.json';
$images_source_dir = $import_directory . '/images';
$images_target_dir = $_SERVER['DOCUMENT_ROOT'] . UPLOAD_DIR . '/articles';
$log_file = $import_directory . '/import-log.txt';

// Create log file if it doesn't exist
if (!file_exists($log_file)) {
    file_put_contents($log_file, date('Y-m-d H:i:s') . " Import log created\n");
}

// Function to write to log file
function log_message($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
    return "[$timestamp] $message";
}

// Function to create PDO connection (uses your CMS's connection details from config.php)
function getPDOConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        return new PDO($dsn, DB_USER, DB_PASS, $options);
    } catch (\PDOException $e) {
        log_message("Database connection failed: " . $e->getMessage());
        die("Database connection failed: " . $e->getMessage());
    }
}

// Process image using the CMS handleImageUpload function
function processImage($source_path, $target_subdir = 'articles') {
    global $rootDir;
    
    if (!file_exists($source_path)) {
        log_message("Image file not found: {$source_path}");
        return ['error' => 'Image file not found'];
    }
    
    try {
        // Log what we're trying to process
        log_message("Processing image: {$source_path}");
        
        // Get image file info
        $file_size = filesize($source_path);
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $source_path);
        finfo_close($finfo);
        
        log_message("Image size: {$file_size} bytes, MIME type: {$mime_type}");
        
        // Check if image type is valid
        $allowed_mime_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($mime_type, $allowed_mime_types)) {
            log_message("Invalid image type: {$mime_type}");
            return ['error' => 'Invalid image type: ' . $mime_type];
        }
        
        // Use your CMS's handleImageUpload function
        $result = handleImageUpload($source_path, $target_subdir);
        
        log_message("Image processing result: " . (is_string($result) ? "Success - {$result}" : "Error - " . ($result['error'] ?? 'Unknown error')));
        
        return $result;
    } catch (Exception $e) {
        log_message("Exception processing image: " . $e->getMessage());
        return ['error' => 'Exception: ' . $e->getMessage()];
    }
}

// Get table schema information to avoid column errors
function getTableSchema($pdo, $tableName) {
    try {
        $stmt = $pdo->prepare("DESCRIBE `$tableName`");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        return $columns;
    } catch (PDOException $e) {
        log_message("Error fetching schema for table $tableName: " . $e->getMessage());
        return [];
    }
}

// Function to check if WordPress export file exists
function checkExportFile() {
    global $import_file;
    return file_exists($import_file);
}

// Function to get post count from the export file
function getExportPostCount() {
    global $import_file;
    if (!file_exists($import_file)) {
        return 0;
    }
    $import_data = json_decode(file_get_contents($import_file), true);
    return is_array($import_data) ? count($import_data) : 0;
}

// Function to read import data from a specific index range
function getImportData($startIndex = 0, $count = null) {
    global $import_file;
    if (!file_exists($import_file)) {
        return [];
    }
    
    $import_data = json_decode(file_get_contents($import_file), true);
    if (!is_array($import_data)) {
        return [];
    }
    
    if ($count !== null) {
        return array_slice($import_data, $startIndex, $count);
    } else {
        return array_slice($import_data, $startIndex);
    }
}

// Process the actual import
function processImport($posts, $isDryRun = false) {
    global $images_source_dir, $images_target_dir, $rootDir;
    
    // Create images directory if it doesn't exist
    if (!$isDryRun && !file_exists($images_target_dir)) {
        if (!mkdir($images_target_dir, 0755, true)) {
            log_message("Error: Could not create images directory at {$images_target_dir}");
            return [
                'success' => false,
                'message' => "Error: Could not create images directory at {$images_target_dir}"
            ];
        }
    }
    
    $pdo = getPDOConnection();
    
    // Get table schemas to avoid column errors
    $categoryColumns = getTableSchema($pdo, 'categories');
    $authorColumns = getTableSchema($pdo, 'authors');
    $tagColumns = getTableSchema($pdo, 'tags');
    $articleColumns = getTableSchema($pdo, 'articles');
    
    log_message("Category columns: " . implode(", ", $categoryColumns));
    log_message("Author columns: " . implode(", ", $authorColumns));
    log_message("Tag columns: " . implode(", ", $tagColumns));
    log_message("Article columns: " . implode(", ", $articleColumns));
    
    // Prepare counters
    $stats = [
        'total' => count($posts),
        'inserted' => 0,
        'skipped' => 0,
        'errors' => 0,
        'details' => [] // For storing detailed results
    ];
    
    // Handle category mapping
    $category_map = [];
    $categoryStmt = $pdo->prepare("SELECT id, slug FROM categories WHERE slug = :slug LIMIT 1");
    
    // Build category insert SQL based on actual columns
    $categoryInsertSql = "INSERT INTO categories (name, slug";
    $categoryInsertValues = "(:name, :slug";
    
    if (in_array('created_at', $categoryColumns)) {
        $categoryInsertSql .= ", created_at";
        $categoryInsertValues .= ", NOW()";
    }
    
    $categoryInsertSql .= ") VALUES " . $categoryInsertValues . ")";
    $insertCategoryStmt = $pdo->prepare($categoryInsertSql);
    
    // Handle tag mapping
    $tag_map = [];
    $tagStmt = $pdo->prepare("SELECT id, name FROM tags WHERE name = :name OR slug = :slug LIMIT 1");
    
    // Build tag insert SQL based on actual columns
    $tagInsertSql = "INSERT INTO tags (name, slug) VALUES (:name, :slug)";
    $insertTagStmt = $pdo->prepare($tagInsertSql);
    
    // Handle authors
    $author_map = [];
    $authorStmt = $pdo->prepare("SELECT id, email FROM authors WHERE name = :name LIMIT 1");
    
    // Build author insert SQL based on actual columns
    $authorInsertSql = "INSERT INTO authors (name, email";
    $authorInsertValues = "(:name, :email";
    
    if (in_array('created_at', $authorColumns)) {
        $authorInsertSql .= ", created_at";
        $authorInsertValues .= ", NOW()";
    }
    
    if (in_array('status', $authorColumns)) {
        $authorInsertSql .= ", status";
        $authorInsertValues .= ", 'active'";
    }
    
    $authorInsertSql .= ") VALUES " . $authorInsertValues . ")";
    $insertAuthorStmt = $pdo->prepare($authorInsertSql);
    
    // Check if article with slug already exists
    $checkSlugStmt = $pdo->prepare("SELECT id FROM articles WHERE slug = :slug LIMIT 1");
    
    // Build article insert SQL based on actual columns
    $articleInsertSql = "INSERT INTO articles (
        title, slug, content";
    $articleInsertValues = "
        :title, :slug, :content";
    
    // Add optional columns based on schema
    $optionalColumns = [
        'excerpt' => ':excerpt',
        'featured_image' => ':featured_image',
        'category_id' => ':category_id',
        'author_id' => ':author_id',
        'status' => "'published'",
        'published_at' => ':published_at',
        'meta_title' => ':meta_title',
        'meta_description' => ':meta_description',
        'reading_time' => ':reading_time',
        'enable_sidebar' => '1',
        'show_similar_posts' => '1',
        'enable_fb_share' => '1',
        'include_in_recommendations' => '1',
        'enable_loading_trick' => '1',
        'enable_adsense' => '1',
        'enable_affiliate_ads' => '1',
        'created_at' => ':created_at',
        'updated_at' => ':updated_at',
        'views' => '0'
    ];
    
    foreach ($optionalColumns as $column => $value) {
        if (in_array($column, $articleColumns)) {
            $articleInsertSql .= ", $column";
            $articleInsertValues .= ", $value";
        }
    }
    
    $articleInsertSql .= ") VALUES (" . $articleInsertValues . ")";
    $insertArticleStmt = $pdo->prepare($articleInsertSql);
    
    // Associate tags with article statement
    $insertArticleTagStmt = $pdo->prepare("INSERT INTO article_tags (article_id, tag_id) VALUES (:article_id, :tag_id)");
    
    // Start a transaction if not in dry run mode
    if (!$isDryRun) {
        $pdo->beginTransaction();
    }
    
    try {
        foreach ($posts as $index => $post) {
            $result = [
                'title' => $post['title'],
                'slug' => $post['slug'],
                'status' => 'pending',
                'messages' => []
            ];
            
            // Add log message for processing
            $result['messages'][] = "Processing post: {$post['title']}";
            log_message("Processing post: {$post['title']}");
            
            // Check if post with same slug already exists
            $checkSlugStmt->bindParam(':slug', $post['slug'], PDO::PARAM_STR);
            $checkSlugStmt->execute();
            
            if ($checkSlugStmt->fetch()) {
                $result['status'] = 'skipped';
                $result['messages'][] = "Skipping: Post with slug '{$post['slug']}' already exists";
                $stats['skipped']++;
                $stats['details'][] = $result;
                continue;
            }
            
            // Process featured image if exists
            $featured_image_filename = null;
            if (!empty($post['featured_image'])) {
                $source_image = $images_source_dir . '/' . $post['featured_image'];
                
                if (file_exists($source_image)) {
                    if (!$isDryRun) {
                        // Process the image using our custom function
                        $image_result = processImage($source_image, 'articles');
                        
                        if (is_string($image_result)) {
                            $featured_image_filename = $image_result;
                            $result['messages'][] = "Featured image processed: {$featured_image_filename}";
                        } else {
                            $error_msg = "Warning: Failed to process featured image: " . ($image_result['error'] ?? 'Unknown error');
                            $result['messages'][] = $error_msg;
                        }
                    } else {
                        $result['messages'][] = "[DRY RUN] Would process featured image: {$post['featured_image']}";
                    }
                } else {
                    $result['messages'][] = "Warning: Featured image not found at {$source_image}";
                }
            }
            
            // Handle category
            $category_id = null;
            if (!empty($post['category'])) {
                $category_slug = $post['category']['slug'];
                
                // Check if we've already processed this category
                if (isset($category_map[$category_slug])) {
                    $category_id = $category_map[$category_slug];
                    $result['messages'][] = "Using existing category mapping: {$post['category']['name']} (ID: {$category_id})";
                } else {
                    // Check if category exists
                    $categoryStmt->bindParam(':slug', $category_slug, PDO::PARAM_STR);
                    $categoryStmt->execute();
                    $existing_category = $categoryStmt->fetch();
                    
                    if ($existing_category) {
                        $category_id = $existing_category['id'];
                        $result['messages'][] = "Using existing category: {$post['category']['name']} (ID: {$category_id})";
                    } else {
                        if (!$isDryRun) {
                            // Create new category
                            $insertCategoryStmt->bindParam(':name', $post['category']['name'], PDO::PARAM_STR);
                            $insertCategoryStmt->bindParam(':slug', $category_slug, PDO::PARAM_STR);
                            $insertCategoryStmt->execute();
                            $category_id = $pdo->lastInsertId();
                        }
                        $result['messages'][] = ($isDryRun ? "[DRY RUN] " : "") . "Created new category: {$post['category']['name']}" . 
                                               (!$isDryRun ? " (ID: {$category_id})" : "");
                    }
                    
                    // Cache category ID for future use
                    if (!$isDryRun) {
                        $category_map[$category_slug] = $category_id;
                    }
                }
            }
            
            // Handle author
            $author_id = null;
            if (!empty($post['author']['name'])) {
                $author_name = $post['author']['name'];
                
                // Check if we've already processed this author
                if (isset($author_map[$author_name])) {
                    $author_id = $author_map[$author_name];
                    $result['messages'][] = "Using existing author mapping: {$author_name} (ID: {$author_id})";
                } else {
                    // Check if author exists
                    $authorStmt->bindParam(':name', $author_name, PDO::PARAM_STR);
                    $authorStmt->execute();
                    $existing_author = $authorStmt->fetch();
                    
                    if ($existing_author) {
                        $author_id = $existing_author['id'];
                        $result['messages'][] = "Using existing author: {$author_name} (ID: {$author_id})";
                    } else {
                        if (!$isDryRun) {
                            // Create new author with a generated email
                            $author_email = strtolower(str_replace(' ', '.', $author_name)) . '@example.com';
                            $insertAuthorStmt->bindParam(':name', $author_name, PDO::PARAM_STR);
                            $insertAuthorStmt->bindParam(':email', $author_email, PDO::PARAM_STR);
                            $insertAuthorStmt->execute();
                            $author_id = $pdo->lastInsertId();
                        }
                        $result['messages'][] = ($isDryRun ? "[DRY RUN] " : "") . "Created new author: {$author_name}" . 
                                               (!$isDryRun ? " (ID: {$author_id})" : "");
                    }
                    
                    // Cache author ID for future use
                    if (!$isDryRun) {
                        $author_map[$author_name] = $author_id;
                    }
                }
            }
            
            // Prepare article data
            $published_at = !empty($post['date']) ? $post['date'] : date('Y-m-d H:i:s');
            $updated_at = !empty($post['modified']) ? $post['modified'] : $published_at;
            $created_at = $published_at;
            
            // Calculate reading time
            $reading_time = estimateReadingTime($post['content']);
            
            if (!$isDryRun) {
                // Insert article - bind basic parameters always present
                $insertArticleStmt->bindParam(':title', $post['title'], PDO::PARAM_STR);
                $insertArticleStmt->bindParam(':slug', $post['slug'], PDO::PARAM_STR);
                $insertArticleStmt->bindParam(':content', $post['content'], PDO::PARAM_STR);
                
                // Bind optional parameters based on schema
                if (in_array('excerpt', $articleColumns)) {
                    $insertArticleStmt->bindParam(':excerpt', $post['excerpt'], PDO::PARAM_STR);
                }
                
                if (in_array('featured_image', $articleColumns)) {
                    $insertArticleStmt->bindParam(':featured_image', $featured_image_filename, $featured_image_filename === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
                }
                
                if (in_array('category_id', $articleColumns)) {
                    $insertArticleStmt->bindParam(':category_id', $category_id, $category_id === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
                }
                
                if (in_array('author_id', $articleColumns)) {
                    $insertArticleStmt->bindParam(':author_id', $author_id, $author_id === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
                }
                
                if (in_array('published_at', $articleColumns)) {
                    $insertArticleStmt->bindParam(':published_at', $published_at, PDO::PARAM_STR);
                }
                
                if (in_array('meta_title', $articleColumns)) {
                    $insertArticleStmt->bindParam(':meta_title', $post['title'], PDO::PARAM_STR);
                }
                
                if (in_array('meta_description', $articleColumns)) {
                    $insertArticleStmt->bindParam(':meta_description', $post['excerpt'], PDO::PARAM_STR);
                }
                
                if (in_array('reading_time', $articleColumns)) {
                    $insertArticleStmt->bindParam(':reading_time', $reading_time, PDO::PARAM_INT);
                }
                
                if (in_array('created_at', $articleColumns)) {
                    $insertArticleStmt->bindParam(':created_at', $created_at, PDO::PARAM_STR);
                }
                
                if (in_array('updated_at', $articleColumns)) {
                    $insertArticleStmt->bindParam(':updated_at', $updated_at, PDO::PARAM_STR);
                }
                
                $insertArticleStmt->execute();
                $article_id = $pdo->lastInsertId();
                $result['id'] = $article_id;
                $result['messages'][] = "Article inserted with ID: {$article_id}";
            } else {
                $result['messages'][] = "[DRY RUN] Would insert article: {$post['title']}";
                $article_id = 0; // Dummy ID for dry run
            }
            
            // Process tags
            if (!empty($post['tags']) && is_array($post['tags'])) {
                foreach ($post['tags'] as $tag_name) {
                    $tag_id = null;
                    $tag_slug = generateSlug($tag_name);
                    
                    // Check if we've already processed this tag
                    if (isset($tag_map[$tag_name])) {
                        $tag_id = $tag_map[$tag_name];
                        $result['messages'][] = "Using existing tag mapping: {$tag_name} (ID: {$tag_id})";
                    } else {
                        // Check if tag exists
                        $tagStmt->bindParam(':name', $tag_name, PDO::PARAM_STR);
                        $tagStmt->bindParam(':slug', $tag_slug, PDO::PARAM_STR);
                        $tagStmt->execute();
                        $existing_tag = $tagStmt->fetch();
                        
                        if ($existing_tag) {
                            $tag_id = $existing_tag['id'];
                            $result['messages'][] = "Using existing tag: {$tag_name} (ID: {$tag_id})";
                        } else {
                            if (!$isDryRun) {
                                // Create new tag
                                $insertTagStmt->bindParam(':name', $tag_name, PDO::PARAM_STR);
                                $insertTagStmt->bindParam(':slug', $tag_slug, PDO::PARAM_STR);
                                $insertTagStmt->execute();
                                $tag_id = $pdo->lastInsertId();
                            }
                            $result['messages'][] = ($isDryRun ? "[DRY RUN] " : "") . "Created new tag: {$tag_name}" .
                                                   (!$isDryRun ? " (ID: {$tag_id})" : "");
                        }
                        
                        // Cache tag ID for future use
                        if (!$isDryRun) {
                            $tag_map[$tag_name] = $tag_id;
                        }
                    }
                    
                    if (!$isDryRun && $article_id > 0) {
                        // Associate tag with article
                        $insertArticleTagStmt->bindParam(':article_id', $article_id, PDO::PARAM_INT);
                        $insertArticleTagStmt->bindParam(':tag_id', $tag_id, PDO::PARAM_INT);
                        
                        try {
                            $insertArticleTagStmt->execute();
                            $result['messages'][] = "Associated tag '{$tag_name}' with article";
                        } catch (PDOException $e) {
                            // Ignore duplicate entry errors for tags
                            if ($e->getCode() !== '23000') {
                                throw $e;
                            }
                        }
                    } else {
                        $result['messages'][] = "[DRY RUN] Would associate tag '{$tag_name}' with article";
                    }
                }
            }
            
            $result['status'] = 'success';
            $stats['inserted']++;
            $stats['details'][] = $result;
        }
        
        // Commit transaction if not in dry run mode
        if (!$isDryRun) {
            $pdo->commit();
        }
        
        return [
            'success' => true,
            'isDryRun' => $isDryRun,
            'stats' => $stats
        ];
        
    } catch (Exception $e) {
        // Rollback transaction if not in dry run mode
        if (!$isDryRun) {
            $pdo->rollBack();
        }
        
        $error_message = "Error: " . $e->getMessage();
        log_message($error_message);
        
        return [
            'success' => false,
            'isDryRun' => $isDryRun,
            'message' => $error_message,
            'stats' => $stats
        ];
    }
}

// Handle form submission
$result = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Set high execution time for large imports
    set_time_limit(300);
    
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'test_single':
                // Test import a single post
                $postIndex = isset($_POST['post_index']) ? (int)$_POST['post_index'] : 0;
                $isDryRun = isset($_POST['dry_run']) && $_POST['dry_run'] == '1';
                $posts = getImportData($postIndex, 1);
                if (!empty($posts)) {
                    $result = processImport($posts, $isDryRun);
                } else {
                    $result = [
                        'success' => false,
                        'message' => "No post found at index {$postIndex}"
                    ];
                }
                break;
                
            case 'test_batch':
                // Test import a batch of posts
                $startIndex = isset($_POST['start_index']) ? (int)$_POST['start_index'] : 0;
                $batchSize = isset($_POST['batch_size']) ? (int)$_POST['batch_size'] : 5;
                $isDryRun = isset($_POST['dry_run']) && $_POST['dry_run'] == '1';
                $posts = getImportData($startIndex, $batchSize);
                if (!empty($posts)) {
                    $result = processImport($posts, $isDryRun);
                } else {
                    $result = [
                        'success' => false,
                        'message' => "No posts found starting at index {$startIndex}"
                    ];
                }
                break;
                
            case 'import_all':
                // Import all posts
                $isDryRun = isset($_POST['dry_run']) && $_POST['dry_run'] == '1';
                $posts = getImportData();
                if (!empty($posts)) {
                    $result = processImport($posts, $isDryRun);
                } else {
                    $result = [
                        'success' => false,
                        'message' => "No posts found in import file"
                    ];
                }
                break;
                
            case 'preview_post':
                // Just preview a post without importing
                $postIndex = isset($_POST['post_index']) ? (int)$_POST['post_index'] : 0;
                $posts = getImportData($postIndex, 1);
                if (!empty($posts)) {
                    $result = [
                        'success' => true,
                        'preview' => $posts[0]
                    ];
                } else {
                    $result = [
                        'success' => false,
                        'message' => "No post found at index {$postIndex}"
                    ];
                }
                break;
                
            case 'check_database':
                // Check database schema
                try {
                    $pdo = getPDOConnection();
                    $categoryColumns = getTableSchema($pdo, 'categories');
                    $authorColumns = getTableSchema($pdo, 'authors');
                    $tagColumns = getTableSchema($pdo, 'tags');
                    $articleColumns = getTableSchema($pdo, 'articles');
                    
                    $result = [
                        'success' => true,
                        'schema' => [
                            'categories' => $categoryColumns,
                            'authors' => $authorColumns,
                            'tags' => $tagColumns,
                            'articles' => $articleColumns
                        ]
                    ];
                } catch (Exception $e) {
                    $result = [
                        'success' => false,
                        'message' => "Database schema check failed: " . $e->getMessage()
                    ];
                }
                break;
                
            case 'test_image':
                // Test image processing
                $imageFile = isset($_POST['image_file']) ? $_POST['image_file'] : '';
                if (!empty($imageFile)) {
                    $source_path = $images_source_dir . '/' . $imageFile;
                    if (file_exists($source_path)) {
                        $image_result = processImage($source_path, 'articles');
                        $result = [
                            'success' => is_string($image_result),
                            'message' => is_string($image_result) ? 
                                "Image processed successfully: {$image_result}" : 
                                "Image processing failed: " . ($image_result['error'] ?? 'Unknown error')
                        ];
                    } else {
                        $result = [
                            'success' => false,
                            'message' => "Image file not found: {$source_path}"
                        ];
                    }
                } else {
                    $result = [
                        'success' => false,
                        'message' => "No image file specified"
                    ];
                }
                break;
        }
    }
}

// Get list of available image files
$availableImages = [];
if (file_exists($images_source_dir)) {
    $files = scandir($images_source_dir);
    foreach ($files as $file) {
        if (in_array(pathinfo($file, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
            $availableImages[] = $file;
        }
    }
}

// Display server path information for debugging
$pathInfo = [
    'script_path' => __FILE__,
    'script_dir' => __DIR__,
    'root_dir' => $rootDir,
    'import_file' => $import_file,
    'images_source_dir' => $images_source_dir,
    'document_root' => $_SERVER['DOCUMENT_ROOT'],
    'images_target_dir' => $images_target_dir
];

// Check if export file exists and get post count
$fileExists = checkExportFile();
$postCount = getExportPostCount();

// HTML Header
$pageTitle = "WordPress Import Tool";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <style>
        /* Basic styling */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 4px;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.warning {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
        }
        .status.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .panel {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .panel-title {
            margin-top: 0;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="number"],
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        button.secondary {
            background-color: #2196F3;
        }
        button.secondary:hover {
            background-color: #0b7dda;
        }
        button.warning {
            background-color: #ff9800;
        }
        button.warning:hover {
            background-color: #e68a00;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        pre {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            overflow: auto;
            max-height: 400px;
        }
        .checkbox-container {
            margin-top: 10px;
        }
        .result-container {
            margin-top: 20px;
        }
        .post-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            padding: 10px;
        }
        .post-item h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .post-item .status-tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .post-item .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .post-item .status-skipped {
            background-color: #fff3cd;
            color: #856404;
        }
        .post-item .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .post-item .messages {
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-size: 14px;
        }
        .post-item .message {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .post-item .message:last-child {
            border-bottom: none;
        }
        .preview-container {
            margin-top: 20px;
        }
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            border-radius: 4px 4px 0 0;
        }
        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: 0.3s;
            color: #333;
        }
        .tab button:hover {
            background-color: #ddd;
        }
        .tab button.active {
            background-color: #ccc;
        }
        .tabcontent {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 4px 4px;
        }
        .show {
            display: block;
        }
        .debug-info {
            margin-top: 30px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .debug-info summary {
            cursor: pointer;
            font-weight: bold;
            color: #2c3e50;
        }
        .debug-info table {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1><?php echo $pageTitle; ?></h1>
    
    <?php if (!$fileExists): ?>
    <div class="status warning">
        <strong>Warning:</strong> WordPress export file not found at <code><?php echo $import_file; ?></code>
        <p>Please upload your WordPress export file to the <code><?php echo $import_directory; ?></code> directory.</p>
    </div>
    <?php else: ?>
    <div class="status info">
        <strong>Export file found!</strong> Contains <?php echo $postCount; ?> posts.
    </div>
    <?php endif; ?>
    
    <?php if ($result && isset($result['message'])): ?>
    <div class="status <?php echo $result['success'] ? 'success' : 'error'; ?>">
        <?php echo $result['message']; ?>
    </div>
    <?php endif; ?>
    
    <?php if ($fileExists): ?>
    <div class="tab">
        <button class="tablink active" onclick="openTab(event, 'TestImageTab')">Test Image</button>
        <button class="tablink" onclick="openTab(event, 'DbCheckTab')">Database Check</button>
        <button class="tablink" onclick="openTab(event, 'PreviewTab')">Preview</button>
        <button class="tablink" onclick="openTab(event, 'TestSingleTab')">Test Single Post</button>
        <button class="tablink" onclick="openTab(event, 'TestBatchTab')">Test Batch</button>
        <button class="tablink" onclick="openTab(event, 'ImportAllTab')">Import All</button>
    </div>
    
    <!-- Test Image Tab -->
    <div id="TestImageTab" class="tabcontent show">
        <h2 class="panel-title">Test Image Processing</h2>
        <p>Test the image processing functionality to ensure images are correctly imported.</p>
        
        <form method="post" action="">
            <input type="hidden" name="action" value="test_image">
            
            <div class="form-group">
                <label for="image-file">Select an image to test:</label>
                <select id="image-file" name="image_file" required>
                    <option value="">-- Select an image --</option>
                    <?php foreach ($availableImages as $image): ?>
                    <option value="<?php echo htmlspecialchars($image); ?>"><?php echo htmlspecialchars($image); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <button type="submit">Test Image Processing</button>
        </form>
        
        <div class="preview-container">
            <h3>Available Images:</h3>
            <div class="panel">
                <p>Found <?php echo count($availableImages); ?> images in the import directory.</p>
                <?php if (!empty($availableImages)): ?>
                <ul>
                    <?php foreach ($availableImages as $image): ?>
                    <li><?php echo htmlspecialchars($image); ?></li>
                    <?php endforeach; ?>
                </ul>
                <?php else: ?>
                <p>No images found in import directory.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Database Check Tab -->
    <div id="DbCheckTab" class="tabcontent">
        <h2 class="panel-title">Database Schema Check</h2>
        <p>Check your database schema to ensure compatibility with the import process.</p>
        
        <form method="post" action="">
            <input type="hidden" name="action" value="check_database">
            <button type="submit">Check Database Schema</button>
        </form>
        
        <?php if ($result && isset($result['schema'])): ?>
        <div class="preview-container">
            <h3>Database Schema:</h3>
            <div class="panel">
                <h4>Categories Table Columns:</h4>
                <pre><?php echo implode(", ", $result['schema']['categories']); ?></pre>
                
                <h4>Authors Table Columns:</h4>
                <pre><?php echo implode(", ", $result['schema']['authors']); ?></pre>
                
                <h4>Tags Table Columns:</h4>
                <pre><?php echo implode(", ", $result['schema']['tags']); ?></pre>
                
                <h4>Articles Table Columns:</h4>
                <pre><?php echo implode(", ", $result['schema']['articles']); ?></pre>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Preview Tab -->
    <div id="PreviewTab" class="tabcontent">
        <h2 class="panel-title">Preview WordPress Post</h2>
        <p>Preview a post before importing to verify data.</p>
        
        <form method="post" action="">
            <input type="hidden" name="action" value="preview_post">
            
            <div class="form-group">
                <label for="post-index">Post Index (0 to <?php echo $postCount - 1; ?>):</label>
                <input type="number" id="post-index" name="post_index" min="0" max="<?php echo $postCount - 1; ?>" value="0" required>
            </div>
            
            <button type="submit">Preview Post</button>
        </form>
        
        <?php if ($result && isset($result['preview'])): ?>
        <div class="preview-container">
            <h3>Post Preview:</h3>
            <div class="panel">
                <h3><?php echo htmlspecialchars($result['preview']['title']); ?></h3>
                <p><strong>Slug:</strong> <?php echo htmlspecialchars($result['preview']['slug']); ?></p>
                <p><strong>Date:</strong> <?php echo htmlspecialchars($result['preview']['date']); ?></p>
                
                <?php if (!empty($result['preview']['category'])): ?>
                <p><strong>Category:</strong> <?php echo htmlspecialchars($result['preview']['category']['name']); ?> (<?php echo htmlspecialchars($result['preview']['category']['slug']); ?>)</p>
                <?php endif; ?>
                
                <?php if (!empty($result['preview']['author'])): ?>
                <p><strong>Author:</strong> <?php echo htmlspecialchars($result['preview']['author']['name']); ?></p>
                <?php endif; ?>
                
                <?php if (!empty($result['preview']['tags'])): ?>
                <p><strong>Tags:</strong> <?php echo htmlspecialchars(implode(', ', $result['preview']['tags'])); ?></p>
                <?php endif; ?>
                
                <?php if (!empty($result['preview']['featured_image'])): ?>
                <p><strong>Featured Image:</strong> <?php echo htmlspecialchars($result['preview']['featured_image']); ?></p>
                <img src="<?php echo 'images/' . htmlspecialchars($result['preview']['featured_image']); ?>" alt="Preview" style="max-width: 300px; max-height: 300px;">
                <?php endif; ?>
                
                <p><strong>Excerpt:</strong></p>
                <pre><?php echo htmlspecialchars($result['preview']['excerpt']); ?></pre>
                
                <p><strong>Content:</strong></p>
                <pre><?php echo htmlspecialchars(substr($result['preview']['content'], 0, 500)); ?>... (truncated)</pre>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Test Single Post Tab -->
    <div id="TestSingleTab" class="tabcontent">
        <h2 class="panel-title">Test Single Post Import</h2>
        <p>Import a single post to test the process. Use dry run mode to see what would happen without making changes.</p>
        
        <form method="post" action="">
            <input type="hidden" name="action" value="test_single">
            
            <div class="form-group">
                <label for="single-post-index">Post Index (0 to <?php echo $postCount - 1; ?>):</label>
                <input type="number" id="single-post-index" name="post_index" min="0" max="<?php echo $postCount - 1; ?>" value="0" required>
            </div>
            
            <div class="checkbox-container">
                <label>
                    <input type="checkbox" name="dry_run" value="1" checked>
                    Dry Run (won't make actual changes)
                </label>
            </div>
            
            <button type="submit">Test Import Single Post</button>
        </form>
    </div>
    
    <!-- Test Batch Tab -->
    <div id="TestBatchTab" class="tabcontent">
        <h2 class="panel-title">Test Batch Import</h2>
        <p>Import a batch of posts to test the process. Use dry run mode to see what would happen without making changes.</p>
        
        <form method="post" action="">
            <input type="hidden" name="action" value="test_batch">
            
            <div class="form-group">
                <label for="start-index">Start Index (0 to <?php echo $postCount - 1; ?>):</label>
                <input type="number" id="start-index" name="start_index" min="0" max="<?php echo $postCount - 1; ?>" value="0" required>
            </div>
            
            <div class="form-group">
                <label for="batch-size">Batch Size (1 to 20):</label>
                <input type="number" id="batch-size" name="batch_size" min="1" max="20" value="5" required>
            </div>
            
            <div class="checkbox-container">
                <label>
                    <input type="checkbox" name="dry_run" value="1" checked>
                    Dry Run (won't make actual changes)
                </label>
            </div>
            
            <button type="submit">Test Batch Import</button>
        </form>
    </div>
    
    <!-- Import All Tab -->
    <div id="ImportAllTab" class="tabcontent">
        <h2 class="panel-title">Import All Posts</h2>
        <p>Import all <?php echo $postCount; ?> posts. Use dry run mode first to see what would happen without making changes.</p>
        
        <form method="post" action="">
            <input type="hidden" name="action" value="import_all">
            
            <div class="checkbox-container">
                <label>
                    <input type="checkbox" name="dry_run" value="1" checked>
                    Dry Run (won't make actual changes)
                </label>
            </div>
            
            <button type="submit" class="warning">Import All Posts</button>
        </form>
    </div>
    <?php endif; ?>
    
    <!-- Results Section -->
    <?php if ($result && isset($result['stats'])): ?>
    <div class="result-container">
        <h2>Import Results <?php echo isset($result['isDryRun']) && $result['isDryRun'] ? '(DRY RUN)' : ''; ?></h2>
        
        <div class="panel">
            <h3 class="panel-title">Summary</h3>
            <p><strong>Total Posts:</strong> <?php echo $result['stats']['total']; ?></p>
            <p><strong>Inserted:</strong> <?php echo $result['stats']['inserted']; ?></p>
            <p><strong>Skipped:</strong> <?php echo $result['stats']['skipped']; ?></p>
            <p><strong>Errors:</strong> <?php echo $result['stats']['errors']; ?></p>
        </div>
        
        <h3>Detailed Results</h3>
        
        <?php if (!empty($result['stats']['details'])): ?>
            <?php foreach ($result['stats']['details'] as $detail): ?>
            <div class="post-item">
                <h3>
                    <?php echo htmlspecialchars($detail['title']); ?>
                    <span class="status-tag status-<?php echo $detail['status']; ?>">
                        <?php echo ucfirst($detail['status']); ?>
                    </span>
                </h3>
                <p><strong>Slug:</strong> <?php echo htmlspecialchars($detail['slug']); ?></p>
                
                <div class="messages">
                    <?php foreach ($detail['messages'] as $message): ?>
                    <div class="message"><?php echo htmlspecialchars($message); ?></div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    <?php endif; ?>
    
    <!-- Debug Information -->
    <details class="debug-info">
        <summary>Debug Information</summary>
        <table>
            <tr>
                <th>Path</th>
                <th>Value</th>
            </tr>
            <?php foreach ($pathInfo as $key => $value): ?>
            <tr>
                <td><?php echo htmlspecialchars($key); ?></td>
                <td><?php echo htmlspecialchars($value); ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
        
        <h4>Import Log Tail (Last 10 Lines):</h4>
        <pre><?php
            if (file_exists($log_file)) {
                $log_content = file($log_file);
                $last_lines = array_slice($log_content, -10);
                echo htmlspecialchars(implode('', $last_lines));
            } else {
                echo "No log file found";
            }
        ?></pre>
    </details>
    
    <script>
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            
            // Hide all tab content
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            
            // Remove active class from all tab buttons
            tablinks = document.getElementsByClassName("tablink");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            
            // Show the selected tab and add active class to the button
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>