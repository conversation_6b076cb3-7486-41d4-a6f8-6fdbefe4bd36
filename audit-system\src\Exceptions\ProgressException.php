<?php

namespace AuditSystem\Exceptions;

/**
 * Exception thrown when progress tracking operations fail
 */
class ProgressException extends AuditException
{
    /**
     * Create exception for progress file corruption
     *
     * @param string $progressFile Path to the corrupted progress file
     * @return static
     */
    public static function progressFileCorrupted(string $progressFile): self
    {
        return new self("Progress file is corrupted or invalid: {$progressFile}");
    }

    /**
     * Create exception for progress backup failure
     *
     * @param string $backupPath Path where backup failed
     * @param string $reason Reason for backup failure
     * @return static
     */
    public static function backupFailed(string $backupPath, string $reason): self
    {
        return new self("Failed to create progress backup at {$backupPath}: {$reason}");
    }

    /**
     * Create exception for progress restoration failure
     *
     * @param string $backupPath Path to backup that failed to restore
     * @return static
     */
    public static function restoreFailed(string $backupPath): self
    {
        return new self("Failed to restore progress from backup: {$backupPath}");
    }

    /**
     * Create exception for concurrent access conflict
     *
     * @return static
     */
    public static function concurrentAccess(): self
    {
        return new self("Concurrent access to progress file detected. Another audit may be running.");
    }

    /**
     * Create exception for invalid progress state
     *
     * @param string $state Invalid state description
     * @return static
     */
    public static function invalidState(string $state): self
    {
        return new self("Invalid progress state: {$state}");
    }
}