<?php
/**
 * Sitemap Generator
 * 
 * This script generates a sitemap.xml file for the website.
 * It includes all published articles, categories, and static pages.
 * 
 * Usage:
 * - Access directly to generate the sitemap: /sitemap.php
 * - Set up a cron job to run this script periodically
 */

// Include configuration
require_once 'config.php';

// Set content type to XML
header('Content-Type: application/xml; charset=utf-8');

// Start XML output
echo '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;

try {
    // Add homepage
    echo '<url>' . PHP_EOL;
    echo '  <loc>' . SITE_URL . '/</loc>' . PHP_EOL;
    echo '  <changefreq>daily</changefreq>' . PHP_EOL;
    echo '  <priority>1.0</priority>' . PHP_EOL;
    echo '</url>' . PHP_EOL;

    // Add static pages
    $staticPages = [
        'search' => 'weekly',
        'popularno' => 'daily',
        '404' => 'yearly'
    ];

    foreach ($staticPages as $page => $changefreq) {
        echo '<url>' . PHP_EOL;
        echo '  <loc>' . SITE_URL . '/' . $page . '.php</loc>' . PHP_EOL;
        echo '  <changefreq>' . $changefreq . '</changefreq>' . PHP_EOL;
        echo '  <priority>0.7</priority>' . PHP_EOL;
        echo '</url>' . PHP_EOL;
    }

    // Add categories
    $stmt = $pdo->query("SELECT slug FROM categories WHERE status = 'active'");
    while ($category = $stmt->fetch()) {
        echo '<url>' . PHP_EOL;
        echo '  <loc>' . SITE_URL . '/category/' . htmlspecialchars($category['slug']) . '/</loc>' . PHP_EOL;
        echo '  <changefreq>weekly</changefreq>' . PHP_EOL;
        echo '  <priority>0.8</priority>' . PHP_EOL;
        echo '</url>' . PHP_EOL;
    }

    // Add articles
    $stmt = $pdo->query("SELECT slug, updated_at FROM articles WHERE status = 'published' AND (published_at IS NULL OR published_at <= NOW()) ORDER BY COALESCE(published_at, created_at) DESC");
    while ($article = $stmt->fetch()) {
        echo '<url>' . PHP_EOL;
        echo '  <loc>' . SITE_URL . '/' . htmlspecialchars($article['slug']) . '/</loc>' . PHP_EOL;
        
        // Add lastmod if available
        if (!empty($article['updated_at'])) {
            $lastmod = date('Y-m-d', strtotime($article['updated_at']));
            echo '  <lastmod>' . $lastmod . '</lastmod>' . PHP_EOL;
        }
        
        echo '  <changefreq>monthly</changefreq>' . PHP_EOL;
        echo '  <priority>0.6</priority>' . PHP_EOL;
        echo '</url>' . PHP_EOL;
    }

} catch (Exception $e) {
    // Log error but continue
    error_log("Error generating sitemap: " . $e->getMessage());
    
    // Add a comment in the XML about the error
    echo '<!-- Error generating complete sitemap: ' . htmlspecialchars($e->getMessage()) . ' -->' . PHP_EOL;
}

// End XML output
echo '</urlset>';

// Optional: Save the sitemap to a file
$saveToDisk = true;
if ($saveToDisk) {
    try {
        $sitemapContent = ob_get_contents();
        file_put_contents('sitemap.xml', $sitemapContent);
    } catch (Exception $e) {
        error_log("Error saving sitemap to disk: " . $e->getMessage());
    }
}
