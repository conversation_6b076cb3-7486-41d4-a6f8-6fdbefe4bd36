<?php

namespace AuditSystem\Tests\Analyzers;

use PHPUnit\Framework\TestCase;
use AuditSystem\Analyzers\PHPAnalyzer;
use AuditSystem\Models\Finding;

/**
 * Unit tests for PHPAnalyzer
 */
class PHPAnalyzerTest extends TestCase
{
    private PHPAnalyzer $analyzer;

    protected function setUp(): void
    {
        $this->analyzer = new PHPAnalyzer();
    }

    public function testGetSupportedFileTypes(): void
    {
        $this->assertEquals(['php'], $this->analyzer->getSupportedFileTypes());
    }

    public function testGetName(): void
    {
        $this->assertEquals('PHP Code Quality Analyzer', $this->analyzer->getName());
    }

    public function testNamingConventions(): void
    {
        $content = '<?php
class badClassName {
    function BadFunctionName() {
        $BadVariableName = "test";
        const badConstant = "value";
    }
}';

        $findings = $this->analyzer->analyze('test.php', $content);
        
        // Should find 4 naming convention violations
        $namingFindings = array_filter($findings, fn($f) => $f->type === Finding::TYPE_QUALITY && 
            strpos($f->description, 'should use') !== false);
        
        $this->assertGreaterThanOrEqual(3, count($namingFindings));
        
        // Check specific violations
        $descriptions = array_map(fn($f) => $f->description, $namingFindings);
        $this->assertContains("Class name 'badClassName' should use PascalCase convention", $descriptions);
        $this->assertContains("Function name 'BadFunctionName' should use camelCase or snake_case convention", $descriptions);
        $this->assertContains("Variable name '\$BadVariableName' should use camelCase or snake_case convention", $descriptions);
    }

    public function testComplexityChecks(): void
    {
        $content = '<?php
function complexFunction($a, $b, $c, $d, $e, $f) {
    if ($a > 0) {
        if ($b > 0) {
            if ($c > 0) {
                if ($d > 0) {
                    if ($e > 0) {
                        if ($f > 0) {
                            for ($i = 0; $i < 10; $i++) {
                                while ($i < 5) {
                                    foreach ($array as $item) {
                                        switch ($item) {
                                            case 1:
                                                echo "one";
                                                break;
                                            case 2:
                                                echo "two";
                                                break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}';

        $findings = $this->analyzer->analyze('test.php', $content);
        
        // Should find parameter count and complexity violations
        $complexityFindings = array_filter($findings, fn($f) => 
            strpos($f->description, 'too many parameters') !== false ||
            strpos($f->description, 'cyclomatic complexity') !== false
        );
        
        $this->assertGreaterThanOrEqual(2, count($complexityFindings));
    }

    public function testLongFunction(): void
    {
        // Create a function with more than 50 lines
        $lines = ['<?php', 'function longFunction() {'];
        for ($i = 0; $i < 60; $i++) {
            $lines[] = "    echo \"Line $i\";";
        }
        $lines[] = '}';
        
        $content = implode("\n", $lines);
        $findings = $this->analyzer->analyze('test.php', $content);
        
        $longFunctionFindings = array_filter($findings, fn($f) => 
            strpos($f->description, 'too long') !== false
        );
        
        $this->assertGreaterThanOrEqual(1, count($longFunctionFindings));
    }

    public function testCodeDuplication(): void
    {
        $content = '<?php
function test1() {
    $result = calculateSomething();
    $processed = processResult($result);
    $final = finalizeData($processed);
    return $final;
}

function test2() {
    $result = calculateSomething();
    $processed = processResult($result);
    $final = finalizeData($processed);
    return $final;
}

$message = "This is a long string that appears multiple times";
echo $message;
$another = "This is a long string that appears multiple times";
$third = "This is a long string that appears multiple times";
';

        $findings = $this->analyzer->analyze('test.php', $content);
        
        $duplicationFindings = array_filter($findings, fn($f) => 
            strpos($f->description, 'duplication') !== false ||
            strpos($f->description, 'Repeated string') !== false
        );
        
        $this->assertGreaterThanOrEqual(1, count($duplicationFindings));
    }

    public function testArchitecturePatterns(): void
    {
        $content = '<?php
function badFunction() {
    global $database;
    $data = $_POST["data"];
    
    $query = "SELECT * FROM users WHERE id = " . $data;
    $result = mysql_query($query);
    
    echo "<html><body>";
    echo "<h1>Results</h1>";
    while ($row = mysql_fetch_array($result)) {
        echo "<p>" . $row["name"] . "</p>";
    }
    echo "</body></html>";
}

include $_GET["file"] . ".php";
';

        $findings = $this->analyzer->analyze('test.php', $content);
        
        $architectureFindings = array_filter($findings, fn($f) => 
            $f->type === Finding::TYPE_ARCHITECTURE
        );
        
        $this->assertGreaterThanOrEqual(2, count($architectureFindings));
        
        // Check for specific architecture violations
        $descriptions = array_map(fn($f) => $f->description, $architectureFindings);
        $this->assertTrue(in_array('Global variable usage detected', $descriptions) ||
                         in_array('Dynamic include/require path detected', $descriptions));
    }

    public function testMaintainabilityChecks(): void
    {
        $content = '<?php
// TODO: Fix this later
function undocumentedFunction() {
    @file_get_contents("somefile.txt"); // FIXME: Handle errors properly
    
    // Using deprecated function
    $result = mysql_connect("localhost", "user", "pass");
    
    return $result;
}

class UndocumentedClass {
    public function method() {
        return "test";
    }
}
';

        $findings = $this->analyzer->analyze('test.php', $content);
        
        $maintainabilityFindings = array_filter($findings, fn($f) => 
            strpos($f->description, 'lacks documentation') !== false ||
            strpos($f->description, 'Technical debt') !== false ||
            strpos($f->description, 'Error suppression') !== false ||
            strpos($f->description, 'Deprecated function') !== false
        );
        
        $this->assertGreaterThanOrEqual(4, count($maintainabilityFindings));
    }

    public function testPriorityAreaDetection(): void
    {
        $priorityContent = '<?php class badClassName {}';
        $nonPriorityContent = '<?php class badClassName {}';
        
        $priorityFindings = $this->analyzer->analyze('admin/test.php', $priorityContent);
        $nonPriorityFindings = $this->analyzer->analyze('other/test.php', $nonPriorityContent);
        
        $this->assertGreaterThan(0, count($priorityFindings));
        $this->assertGreaterThan(0, count($nonPriorityFindings));
        
        // Priority area files should be marked as PRIORITY_AREA
        $this->assertEquals(Finding::PRIORITY_AREA, $priorityFindings[0]->priority);
        $this->assertEquals(Finding::NON_PRIORITY, $nonPriorityFindings[0]->priority);
    }

    public function testCleanCodeProducesNoFindings(): void
    {
        $content = '<?php
/**
 * Well-documented class following best practices
 */
class GoodExample
{
    private string $property;

    /**
     * Constructor with proper documentation
     * @param string $property The property value
     */
    public function __construct(string $property)
    {
        $this->property = $property;
    }

    /**
     * Simple method with clear purpose
     * @return string The property value
     */
    public function getProperty(): string
    {
        return $this->property;
    }
}
';

        $findings = $this->analyzer->analyze('test.php', $content);
        
        // Clean code should produce minimal or no findings
        $this->assertLessThan(2, count($findings));
    }

    public function testConfigurableOptions(): void
    {
        $config = [
            'max_function_length' => 5,
            'max_parameters' => 2,
            'max_cyclomatic_complexity' => 2
        ];
        
        $analyzer = new PHPAnalyzer($config);
        
        $content = '<?php
function testFunction($a, $b, $c) {
    echo "line 1";
    echo "line 2";
    echo "line 3";
    echo "line 4";
    echo "line 5";
    echo "line 6";
    echo "line 7";
}';

        $findings = $analyzer->analyze('test.php', $content);
        
        // Should find violations based on stricter config
        $parameterFindings = array_filter($findings, fn($f) => 
            strpos($f->description, 'too many parameters') !== false
        );
        $lengthFindings = array_filter($findings, fn($f) => 
            strpos($f->description, 'too long') !== false
        );
        
        $this->assertGreaterThanOrEqual(1, count($parameterFindings));
        $this->assertGreaterThanOrEqual(1, count($lengthFindings));
    }
}