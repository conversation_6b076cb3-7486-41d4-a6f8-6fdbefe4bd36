<?php

namespace AuditSystem\Analyzers;

use AuditSystem\Interfaces\AnalyzerInterface;
use AuditSystem\Models\Finding;

/**
 * Comprehensive performance analyzer for identifying bottlenecks and optimization opportunities
 */
class PerformanceAnalyzer implements AnalyzerInterface
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'check_database_queries' => true,
            'check_asset_loading' => true,
            'check_caching' => true,
            'check_image_processing' => true,
            'check_memory_usage' => true,
            'max_queries_per_page' => 10,
            'max_file_size_mb' => 2
        ], $config);
    }

    /**
     * Analyze a file and return findings
     *
     * @param string $filePath Path to the file to analyze
     * @param string $content File content to analyze
     * @return Finding[] Array of findings discovered in the file
     */
    public function analyze(string $filePath, string $content): array
    {
        $findings = [];

        if ($this->config['check_database_queries']) {
            $findings = array_merge($findings, $this->checkDatabaseQueries($filePath, $content));
        }

        if ($this->config['check_asset_loading']) {
            $findings = array_merge($findings, $this->checkAssetLoading($filePath, $content));
        }

        if ($this->config['check_caching']) {
            $findings = array_merge($findings, $this->checkCaching($filePath, $content));
        }

        if ($this->config['check_image_processing']) {
            $findings = array_merge($findings, $this->checkImageProcessing($filePath, $content));
        }

        if ($this->config['check_memory_usage']) {
            $findings = array_merge($findings, $this->checkMemoryUsage($filePath, $content));
        }

        return $findings;
    }

    /**
     * Compatibility wrapper for tests expecting analyzeFile($filePath)
     */
    public function analyzeFile(string $filePath): array
    {
        $content = @file_get_contents($filePath) ?: '';
        return $this->analyze($filePath, $content);
    }

    /**
     * Check for database query performance issues including N+1 problems
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkDatabaseQueries(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);
        $queryCount = 0;
        $queriesInLoop = [];

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++; // 1-based line numbers

            // Count total queries in file
            if (preg_match('/(query|prepare|exec)\s*\(/', $line)) {
                $queryCount++;
            }

            // Check for queries inside loops (N+1 problem)
            if (preg_match('/(for|foreach|while)\s*\(/', $line)) {
                // Look for queries in the next 20 lines (approximate loop body)
                for ($i = $lineNumber; $i < min($lineNumber + 20, count($lines)); $i++) {
                    if (preg_match('/(query|prepare|exec)\s*\(/', $lines[$i])) {
                        $findings[] = new Finding(
                            $filePath,
                            $i + 1,
                            Finding::TYPE_PERFORMANCE,
                            Finding::SEVERITY_HIGH,
                            $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                            'N+1 query problem: Database query inside loop',
                            'Move query outside loop and use batch processing or JOIN operations',
                            trim($lines[$i]),
                            ['https://stackoverflow.com/questions/97197/what-is-the-n1-selects-problem-in-orm-object-relational-mapping']
                        );
                        break; // Only report once per loop
                    }
                }
            }

            // Check for missing indexes (SELECT without WHERE optimization)
            if (preg_match('/SELECT.*?FROM\s+(\w+)(?!.*WHERE)/i', $line, $matches)) {
                $tableName = $matches[1];
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Query without WHERE clause on table '$tableName' may cause full table scan",
                    'Add appropriate WHERE conditions and ensure proper indexing',
                    trim($line),
                    ['https://dev.mysql.com/doc/refman/8.0/en/optimization-indexes.html']
                );
            }

            // Check for SELECT * queries
            if (preg_match('/SELECT\s+\*\s+FROM/i', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'SELECT * query retrieves unnecessary columns',
                    'Specify only required columns to reduce data transfer and memory usage',
                    trim($line),
                    ['https://dev.mysql.com/doc/refman/8.0/en/select-optimization.html']
                );
            }

            // Check for inefficient LIKE patterns
            if (preg_match('/LIKE\s+["\']%.*?%["\']/i', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'LIKE query with leading wildcard prevents index usage',
                    'Consider full-text search or restructure query to avoid leading wildcards',
                    trim($line),
                    ['https://dev.mysql.com/doc/refman/8.0/en/fulltext-search.html']
                );
            }

            // Check for ORDER BY without LIMIT
            if (preg_match('/ORDER\s+BY.*?(?!.*LIMIT)/i', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'ORDER BY without LIMIT may sort unnecessary rows',
                    'Add LIMIT clause if you only need a subset of results',
                    trim($line),
                    ['https://dev.mysql.com/doc/refman/8.0/en/limit-optimization.html']
                );
            }
        }

        // Check total query count per file
        if ($queryCount > $this->config['max_queries_per_page']) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_PERFORMANCE,
                Finding::SEVERITY_HIGH,
                $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                "High query count: $queryCount queries in single file (>{$this->config['max_queries_per_page']})",
                'Consider query optimization, caching, or breaking into multiple requests',
                "File contains $queryCount database queries",
                ['https://dev.mysql.com/doc/refman/8.0/en/optimization.html']
            );
        }

        return $findings;
    }

    /**
     * Check asset loading efficiency
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkAssetLoading(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Check for blocking CSS/JS in head
            if (preg_match('/<script.*?src=.*?(?!async|defer)/i', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Blocking JavaScript without async/defer attributes',
                    'Add async or defer attributes to prevent render blocking',
                    trim($line),
                    ['https://web.dev/efficiently-load-third-party-javascript/']
                );
            }

            // Check for multiple CSS files (should be concatenated)
            if (preg_match_all('/<link.*?rel=["\']stylesheet["\'].*?href=/i', $content, $matches)) {
                if (count($matches[0]) > 3) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        'Multiple CSS files detected (' . count($matches[0]) . ' files)',
                        'Concatenate and minify CSS files to reduce HTTP requests',
                        count($matches[0]) . ' separate CSS files found',
                        ['https://web.dev/reduce-network-payloads-using-text-compression/']
                    );
                    break; // Only report once per file
                }
            }

            // Check for inline styles (should be in CSS files)
            if (preg_match('/style\s*=\s*["\'][^"\']{50,}["\']/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_LOW,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Large inline styles detected',
                    'Move styles to external CSS file for better caching and maintainability',
                    trim(substr($line, 0, 100)) . '...',
                    ['https://web.dev/extract-critical-css/']
                );
            }

            // Check for images without lazy loading
            if (preg_match('/<img.*?src=.*?(?!loading=["\']lazy["\'])/i', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_LOW,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Image without lazy loading attribute',
                    'Add loading="lazy" attribute to images below the fold',
                    trim($line),
                    ['https://web.dev/lazy-loading-images/']
                );
            }

            // Check for missing preload for critical resources
            if (preg_match('/<link.*?href=["\'][^"\']*\.(css|js)["\'].*?(?!rel=["\']preload["\'])/i', $line)) {
                if (strpos($line, 'critical') !== false || strpos($line, 'main') !== false) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_LOW,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        'Critical resource without preload hint',
                        'Add rel="preload" for critical CSS/JS resources',
                        trim($line),
                        ['https://web.dev/preload-critical-assets/']
                    );
                }
            }
        }

        return $findings;
    }

    /**
     * Check caching implementation
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkCaching(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);
        $hasExpensiveOperation = false;
        $hasCaching = false;

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Detect expensive operations that should be cached
            if (preg_match('/(file_get_contents|curl_exec|query|SELECT|API|remote)/i', $line)) {
                $hasExpensiveOperation = true;
            }

            // Detect caching mechanisms
            if (preg_match('/(cache|memcache|redis|apc_|opcache)/i', $line)) {
                $hasCaching = true;
            }

            // Check for repeated file operations
            if (preg_match('/file_get_contents\s*\(["\'][^"\']+["\']/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'File read operation without caching',
                    'Implement file caching to avoid repeated disk I/O',
                    trim($line),
                    ['https://www.php.net/manual/en/book.apc.php']
                );
            }

            // Check for API calls without caching
            if (preg_match('/(curl_exec|file_get_contents.*?http)/i', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'External API call without caching mechanism',
                    'Implement response caching to reduce external API dependencies',
                    trim($line),
                    ['https://www.php.net/manual/en/book.memcached.php']
                );
            }

            // Check for missing HTTP cache headers
            if (preg_match('/header\s*\(\s*["\']Content-Type/', $line)) {
                // Look for cache headers in surrounding lines
                $hasCacheHeaders = false;
                for ($i = max(0, $lineNumber - 5); $i < min(count($lines), $lineNumber + 5); $i++) {
                    if (preg_match('/(Cache-Control|Expires|ETag|Last-Modified)/i', $lines[$i])) {
                        $hasCacheHeaders = true;
                        break;
                    }
                }

                if (!$hasCacheHeaders) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        'Missing HTTP cache headers',
                        'Add Cache-Control, Expires, or ETag headers for better browser caching',
                        trim($line),
                        ['https://web.dev/http-cache/']
                    );
                }
            }
        }

        // Check if expensive operations exist without caching
        if ($hasExpensiveOperation && !$hasCaching) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_PERFORMANCE,
                Finding::SEVERITY_HIGH,
                $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                'Expensive operations detected without caching implementation',
                'Implement caching layer (Redis, Memcached, or file-based) for expensive operations',
                'File contains database queries or API calls without caching',
                ['https://redis.io/', 'https://memcached.org/']
            );
        }

        return $findings;
    }

    /**
     * Check image processing optimization
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkImageProcessing(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Check for image processing without optimization
            if (preg_match('/(imagecreatefrom|getimagesize|imagecopyresampled)/i', $line)) {
                // Look for WebP support in surrounding lines
                $hasWebPSupport = false;
                for ($i = max(0, $lineNumber - 10); $i < min(count($lines), $lineNumber + 10); $i++) {
                    if (preg_match('/webp/i', $lines[$i])) {
                        $hasWebPSupport = true;
                        break;
                    }
                }

                if (!$hasWebPSupport) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        'Image processing without WebP format support',
                        'Add WebP format support for better compression and performance',
                        trim($line),
                        ['https://developers.google.com/speed/webp']
                    );
                }
            }

            // Check for missing responsive image generation
            if (preg_match('/imagecopyresampled.*?(\d+).*?(\d+)/', $line, $matches)) {
                $width = (int)$matches[1];
                $height = (int)$matches[2];
                
                if ($width > 1200 || $height > 1200) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Large image dimensions ({$width}x{$height}) without responsive variants",
                        'Generate multiple image sizes for responsive delivery',
                        trim($line),
                        ['https://web.dev/serve-responsive-images/']
                    );
                }
            }

            // Check for image uploads without size limits
            if (preg_match('/move_uploaded_file.*?\.(jpg|jpeg|png|gif)/i', $line)) {
                // Look for size validation in surrounding lines
                $hasSizeValidation = false;
                for ($i = max(0, $lineNumber - 10); $i < min(count($lines), $lineNumber + 5); $i++) {
                    if (preg_match('/(filesize|size|MAX_FILE_SIZE)/i', $lines[$i])) {
                        $hasSizeValidation = true;
                        break;
                    }
                }

                if (!$hasSizeValidation) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_HIGH,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        'Image upload without size validation',
                        'Add file size limits to prevent large uploads affecting performance',
                        trim($line),
                        ['https://www.php.net/manual/en/features.file-upload.php']
                    );
                }
            }

            // Check for missing image compression
            if (preg_match('/imagejpeg\s*\([^,)]+\)/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_LOW,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'JPEG output without compression quality setting',
                    'Add quality parameter to imagejpeg() for optimal file size',
                    trim($line),
                    ['https://www.php.net/manual/en/function.imagejpeg.php']
                );
            }
        }

        return $findings;
    }

    /**
     * Check memory usage patterns
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkMemoryUsage(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Check for large array operations
            if (preg_match('/array_merge.*?array_merge/i', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Multiple array_merge operations can be memory intensive',
                    'Consider using array_merge with multiple arrays or array spread operator',
                    trim($line),
                    ['https://www.php.net/manual/en/function.array-merge.php']
                );
            }

            // Check for file_get_contents on large files
            if (preg_match('/file_get_contents\s*\([^)]+\)(?!.*LOCK_SH)/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'file_get_contents() loads entire file into memory',
                    'For large files, use fopen/fread with streaming or file_get_contents with context',
                    trim($line),
                    ['https://www.php.net/manual/en/function.file-get-contents.php']
                );
            }

            // Check for unset() usage for memory cleanup
            if (preg_match('/(foreach|for|while).*?\{/', $line)) {
                // Look for unset in loop body
                $hasUnset = false;
                for ($i = $lineNumber; $i < min(count($lines), $lineNumber + 20); $i++) {
                    if (preg_match('/unset\s*\(/', $lines[$i])) {
                        $hasUnset = true;
                        break;
                    }
                    if (preg_match('/^\s*}/', $lines[$i])) {
                        break; // End of loop
                    }
                }

                if (!$hasUnset && preg_match('/\$\w+\[\]/', $line)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_LOW,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        'Loop with array operations without memory cleanup',
                        'Consider using unset() for large variables in loops to free memory',
                        trim($line),
                        ['https://www.php.net/manual/en/function.unset.php']
                    );
                }
            }

            // Check for string concatenation in loops
            if (preg_match('/(foreach|for|while)/', $line)) {
                for ($i = $lineNumber; $i < min(count($lines), $lineNumber + 15); $i++) {
                    if (preg_match('/\$\w+\s*\.=/', $lines[$i])) {
                        $findings[] = new Finding(
                            $filePath,
                            $i + 1,
                            Finding::TYPE_PERFORMANCE,
                            Finding::SEVERITY_MEDIUM,
                            $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                            'String concatenation in loop can cause memory issues',
                            'Use array and implode() for better memory efficiency',
                            trim($lines[$i]),
                            ['https://www.php.net/manual/en/language.operators.string.php']
                        );
                        break;
                    }
                    if (preg_match('/^\s*}/', $lines[$i])) {
                        break;
                    }
                }
            }

            // Check for memory_limit modifications
            if (preg_match('/ini_set\s*\(\s*["\']memory_limit["\']/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Memory limit modification detected',
                    'Optimize code instead of increasing memory limit, or handle in server configuration',
                    trim($line),
                    ['https://www.php.net/manual/en/ini.core.php#ini.memory-limit']
                );
            }
        }

        return $findings;
    }

    /**
     * Get the types of files this analyzer can handle
     *
     * @return string[] Array of file extensions or patterns this analyzer supports
     */
    public function getSupportedFileTypes(): array
    {
        return ['php', 'html', 'htm', 'css', 'js'];
    }

    /**
     * Get the analyzer name for identification
     *
     * @return string Name of the analyzer
     */
    public function getName(): string
    {
        return 'Performance Analyzer';
    }

    /**
     * Check if file is in priority area
     *
     * @param string $filePath
     * @return bool
     */
    private function isPriorityArea(string $filePath): bool
    {
        $priorityPatterns = [
            'admin/',
            'includes/',
            'config.php',
            'ad_',
            'smrsaj',
            'image.php',
            'process_',
            'index.php',
            'article.php'
        ];

        foreach ($priorityPatterns as $pattern) {
            if (strpos($filePath, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }
}