/**
 * ads.js - Client-side Ad Management
 *
 * Handles lazy loading, viewability tracking, and ad functionality.
 * v1.1: Modified recordAdImpression to use fetch POST and send context.
 */

// Initialize ad tracking when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize intersection observer for all ad units
    initLazyLoadAds();
    // Track all visible ads on page load (for ads that don't need lazy loading)
    trackInitialAdImpressions();
});

/**
 * Initializes intersection observer for lazy loading ads and impression tracking
 */
function initLazyLoadAds() {
    // Check if IntersectionObserver is supported
    if ('IntersectionObserver' in window) {
        const options = {
            root: null, // Use viewport as root
            rootMargin: '0px', // No margin
            threshold: 0.5 // 50% visible
        };

        const observer = new IntersectionObserver((entries, observerInstance) => { // Pass observer to callback
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const adUnit = entry.target;

                    // Example: Load the ad if it's lazy loaded
                    // if (adUnit.classList.contains('ad-lazy-load')) {
                    //     loadAd(adUnit);
                    // }

                    // Record impression if not already recorded
                    // Use a flag to prevent multiple records for the same view instance
                    if (adUnit.dataset.impressionRecorded !== 'true') {
                        recordAdImpression(adUnit); // Call the updated function
                        adUnit.dataset.impressionRecorded = 'true'; // Mark as recorded
                    }

                    // Unobserve after recording to prevent re-triggering on scroll
                    observerInstance.unobserve(adUnit);
                }
            });
        }, options);

        document.querySelectorAll('.ad-unit').forEach(adUnit => {
            // Ensure the dataset attribute exists for logic check
            if (adUnit.dataset.impressionRecorded === undefined) {
                 adUnit.dataset.impressionRecorded = 'false';
            }
            observer.observe(adUnit);
        });
    } else {
        // Fallback for browsers that don't support IntersectionObserver
        console.warn('IntersectionObserver not supported. Recording impressions for all ads on load.');
        document.querySelectorAll('.ad-unit').forEach(adUnit => {
             if (adUnit.dataset.impressionRecorded !== 'true') {
                recordAdImpression(adUnit);
                adUnit.dataset.impressionRecorded = 'true';
             }
        });
    }
}

/**
 * Loads an ad that has been marked for lazy loading (Example function)
 * @param {HTMLElement} adUnit - The ad unit DOM element
 */
// function loadAd(adUnit) {
//     adUnit.classList.remove('ad-lazy-load');
//     const template = adUnit.querySelector('template[data-ad-content]');
//     if (template) {
//         adUnit.innerHTML = template.innerHTML; // Replace placeholder with actual content
//         executeScripts(adUnit); // Execute any scripts within the ad
//     }
// }

/**
 * Records an ad impression by sending a POST request to the server.
 * @param {HTMLElement} adUnit - The ad unit DOM element
 */
function recordAdImpression(adUnit) {
    // Get ad ID, type, and placement from data attributes
    const adId = adUnit.dataset.adId;
    const adType = adUnit.dataset.adType;
    const placement = adUnit.dataset.adPlacement || 'unknown'; // Get placement

    if (!adId || !adType) {
        console.warn('Ad unit missing data-ad-id or data-ad-type for impression tracking.', adUnit);
        return;
    }

    // --- Get Page Context from Body Data Attributes ---
    const bodyData = document.body.dataset;
    const pageType = bodyData.pageType || 'unknown';
    const pageId = bodyData.pageId || null; // Will be null if attribute is empty or missing
    // ------------------------------------------------

    console.log(`Recording impression for Ad ID: ${adId}, Type: ${adType}, Placement: ${placement}, Page Type: ${pageType}, Page ID: ${pageId}`);

    // Prepare data to send using FormData
    const formData = new FormData();
    formData.append('ad_id', adId);
    formData.append('ad_type', adType);
    formData.append('placement', placement);
    formData.append('page_type', pageType);
    // Only append page_id if it has a value
    if (pageId !== null && pageId !== '') {
        formData.append('page_id', pageId);
    }

    // Use fetch to send a POST request
    fetch('/record_impression.php', { // Ensure this path is correct
        method: 'POST',
        body: formData,
        // Keepalive can help ensure the request finishes even if the user navigates away quickly
        // Use with caution, browser support varies, and it has limitations.
        // keepalive: true,
    })
    .then(response => {
        if (!response.ok) {
            // Log error but don't necessarily stop JS execution
            console.error(`Impression recording failed for Ad ID ${adId}. Status: ${response.status}`);
        }
        // No need to process the response body (it's just a GIF)
    })
    .catch(error => {
        // Log network errors or other issues
        console.error(`Error sending impression tracking request for Ad ID ${adId}:`, error);
    });
}


/**
 * Executes any script tags found in dynamically loaded ad content (Example function)
 * @param {HTMLElement} container - The container element with scripts to execute
 */
// function executeScripts(container) {
//     const scripts = container.querySelectorAll('script');
//     scripts.forEach(oldScript => {
//         const newScript = document.createElement('script');
//         Array.from(oldScript.attributes).forEach(attr => {
//             newScript.setAttribute(attr.name, attr.value);
//         });
//         newScript.appendChild(document.createTextNode(oldScript.innerHTML));
//         oldScript.parentNode.replaceChild(newScript, oldScript);
//     });
// }

/**
 * Tracks initial ad impressions for ads that are already visible on page load
 * (e.g., ads not lazy-loaded or above the fold).
 */
function trackInitialAdImpressions() {
    document.querySelectorAll('.ad-unit:not(.ad-lazy-load)').forEach(adUnit => {
        // Check if ad is actually in viewport (simple check)
        const rect = adUnit.getBoundingClientRect();
        const isVisible = (
            rect.top < window.innerHeight && rect.bottom >= 0 &&
            rect.left < window.innerWidth && rect.right >= 0 &&
            rect.width > 0 && rect.height > 0
        );

        // Record impression if visible and not already recorded
        if (isVisible && adUnit.dataset.impressionRecorded !== 'true') {
            recordAdImpression(adUnit);
            adUnit.dataset.impressionRecorded = 'true';
        }
    });
}

/**
 * Refreshes AdSense ads after a specified interval (Example function)
 * @param {number} refreshInterval - Refresh interval in milliseconds
 */
function initAdRefresh(refreshInterval) {
    if (!refreshInterval || refreshInterval <= 0) return;

    // Find all ad units that support refreshing
    document.querySelectorAll('.ad-unit.ad-refreshable').forEach(adUnit => {
        // Set up a timeout to refresh the ad
        setTimeout(() => {
            // Implementation for AdSense ads
            if (adUnit.classList.contains('ad-type-adsense') && window.adsbygoogle) {
                // Remove old ad
                while (adUnit.firstChild) {
                    adUnit.removeChild(adUnit.firstChild);
                }

                // Create new ad slot
                const adSlot = document.createElement('ins');
                adSlot.className = 'adsbygoogle';
                // Make sure these data attributes exist on the original adUnit div
                adSlot.setAttribute('data-ad-client', adUnit.dataset.adClient || '');
                adSlot.setAttribute('data-ad-slot', adUnit.dataset.adSlot || '');
                adSlot.setAttribute('data-ad-format', adUnit.dataset.adFormat || 'auto');
                adUnit.appendChild(adSlot);

                // Push to adsbygoogle for rendering
                try {
                     (adsbygoogle = window.adsbygoogle || []).push({});
                     console.log(`Refreshed AdSense unit: ${adUnit.id}`);
                } catch (e) {
                    console.error(`Error refreshing AdSense unit ${adUnit.id}:`, e);
                }
            }
            // Add logic for other ad types if needed
        }, refreshInterval);
    });
}

// Example Usage (call this from your main page template if needed)
// initAdRefresh(30000); // Refresh ads every 30 seconds
