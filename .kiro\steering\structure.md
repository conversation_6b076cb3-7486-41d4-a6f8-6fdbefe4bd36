# Project Structure

## Root Directory Layout

```
public_html/                 # Web root directory
├── config.php              # Main configuration file
├── index.php               # Homepage
├── article.php             # Single article display
├── category.php            # Category listing page
├── search.php              # Search functionality
├── sitemap.php/.xml        # SEO sitemaps
├── robots.txt              # Search engine directives
├── .htaccess               # Apache configuration
└── lakofino_cms.sql        # Database schema
```

## Core Directories

### `/includes/` - Shared Components
- `functions.php` - Core utility functions
- `header.php` / `footer.php` - Page templates
- `security.php` - Security helpers
- `ad_*.php` - Advertisement management
- `facebook-integration.php` - Social media integration
- `Parsedown.php` - Markdown parser

### `/admin/` - Backend Management
- `index.php` - Admin dashboard
- `articles.php` - Article management
- `advertising.php` - Ad management
- `analytics.php` - Site analytics
- `settings.php` - System configuration
- `includes/` - Admin-specific includes

### `/assets/` - Static Resources
- `/css/` - Stylesheets
- `/js/` - JavaScript files
- `/images/` - Static images
- `/uploads/` - User-uploaded content

## File Naming Conventions

### PHP Files
- **Pages**: `snake_case.php` (e.g., `privacy-policy.php`)
- **Includes**: `snake_case.php` (e.g., `ad_display.php`)
- **Processing**: `process_*.php` (e.g., `process_comment.php`)

### Database Tables
- **Prefix**: None used
- **Naming**: `snake_case` (e.g., `articles`, `adsense_units`)

### CSS Classes
- **Framework**: Tailwind CSS utility classes
- **Custom**: `kebab-case` (e.g., `article-card-mobile`)

## Key Architecture Patterns

### MVC-like Structure
- **Models**: Database functions in `functions.php`
- **Views**: Template files (`header.php`, `footer.php`)
- **Controllers**: Page files (`index.php`, `article.php`)

### Security Patterns
- PDO prepared statements for database queries
- `escape()` function for HTML output
- CSRF protection in forms
- Input sanitization and validation

### Image Handling
- Dynamic image processing via `image.php`
- Responsive image generation with multiple sizes
- WebP format with fallbacks
- Lazy loading implementation

### Content Management
- Markdown content with Parsedown
- SEO optimization with meta tags
- Social media integration (Facebook, Open Graph)
- Comment system with moderation