<?php
/**
 * Image Helper Functions
 * 
 * This file contains functions to help process WordPress images for import.
 * Place this file in the same directory as cms-import.php
 */

// Get the parent directory (website root) for config inclusion
$rootDir = dirname(__DIR__);

// Include your CMS configuration files
require_once $rootDir . '/config.php';
require_once $rootDir . '/includes/functions.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Check for valid request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') { 
    echo json_encode(['success' => false, 'error' => 'Invalid request method']); 
    exit; 
}

// Check for required parameters
if (empty($_POST['action'])) { 
    echo json_encode(['success' => false, 'error' => 'Missing action parameter']); 
    exit; 
}

// Process source image path directly
if ($_POST['action'] === 'process_image') {
    if (empty($_POST['image_path'])) {
        echo json_encode(['success' => false, 'error' => 'Missing image path']);
        exit;
    }
    
    $imagePath = trim($_POST['image_path']);
    $targetDir = !empty($_POST['target_dir']) ? trim($_POST['target_dir']) : 'articles';
    
    // Validate the path exists
    if (!file_exists($imagePath)) {
        echo json_encode(['success' => false, 'error' => 'Image file not found at: ' . $imagePath]);
        exit;
    }
    
    // Get file information
    $fileSize = filesize($imagePath);
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $imagePath);
    finfo_close($finfo);
    
    // Prepare result object
    $result = [
        'original_path' => $imagePath,
        'file_size' => $fileSize,
        'mime_type' => $mimeType
    ];
    
    // Process the image directly using handleImageUpload
    try {
        $uploadResult = handleImageUpload($imagePath, $targetDir);
        
        if (is_array($uploadResult) && isset($uploadResult['error'])) {
            // Error during upload
            $result['success'] = false;
            $result['error'] = $uploadResult['error'];
        } elseif (is_string($uploadResult)) {
            // Success - get image details
            $result['success'] = true;
            $result['base_filename'] = $uploadResult;
            
            // Get URLs for different sizes
            $imageData = [];
            foreach (['ls', 'ms', 'ss', 'fb'] as $size) {
                $sizeData = getFeaturedImageUrl($uploadResult, $size, $targetDir);
                if (!empty($sizeData['url'])) {
                    $imageData[$size] = [
                        'url' => $sizeData['url'],
                        'width' => $sizeData['width'],
                        'height' => $sizeData['height']
                    ];
                }
            }
            
            $result['sizes'] = $imageData;
        } else {
            // Unknown result format
            $result['success'] = false;
            $result['error'] = 'Unknown result format from handleImageUpload';
        }
    } catch (Exception $e) {
        $result['success'] = false;
        $result['error'] = 'Exception: ' . $e->getMessage();
    }
    
    echo json_encode($result);
    exit;
}

// Test direct file handling with built-in functions for debugging
if ($_POST['action'] === 'test_image_info') {
    if (empty($_POST['image_path'])) {
        echo json_encode(['success' => false, 'error' => 'Missing image path']);
        exit;
    }
    
    $imagePath = trim($_POST['image_path']);
    
    // Validate the path exists
    if (!file_exists($imagePath)) {
        echo json_encode(['success' => false, 'error' => 'Image file not found']);
        exit;
    }
    
    // Get file information
    $fileSize = filesize($imagePath);
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $imagePath);
    finfo_close($finfo);
    
    // Get image dimensions if it's an image
    $dimensions = @getimagesize($imagePath);
    
    echo json_encode([
        'success' => true,
        'path' => $imagePath,
        'file_size' => $fileSize,
        'mime_type' => $mimeType,
        'dimensions' => $dimensions ? [
            'width' => $dimensions[0],
            'height' => $dimensions[1],
            'type' => $dimensions[2],
            'html' => $dimensions[3]
        ] : null
    ]);
    exit;
}

// No valid action matched
echo json_encode(['success' => false, 'error' => 'Invalid action']);
exit;