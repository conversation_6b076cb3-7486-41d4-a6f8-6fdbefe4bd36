<?php

namespace AuditSystem\Tests\Integration;

use PHPUnit\Framework\TestCase;
use AuditSystem\Integration\SystemIntegrator;
use AuditSystem\Config\AuditConfig;
use AuditSystem\Models\AuditResult;
use AuditSystem\Models\Finding;

/**
 * Complete System Integration Test
 * 
 * Tests the fully integrated audit system with all components working together
 * against the real CMS codebase to validate end-to-end functionality.
 */
class CompleteSystemIntegrationTest extends TestCase
{
    private SystemIntegrator $integrator;
    private AuditConfig $config;
    private string $cmsPath;
    private string $testReportDir;
    private array $testResults = [];

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        $this->config = AuditConfig::getInstance();
        $this->config->loadFromFile(__DIR__ . '/../../config/audit.json');
        
        // Override paths for testing
        $this->cmsPath = dirname(__DIR__, 3) . '/public_html';
        $this->testReportDir = __DIR__ . '/../../reports/integration_test';
        
        $this->config->set('audit.target_directory', $this->cmsPath);
        $this->config->set('audit.report_directory', $this->testReportDir);
        $this->config->set('audit.progress_file', __DIR__ . '/../../data/integration_test_progress.json');
        
        // Ensure test directories exist
        if (!is_dir($this->testReportDir)) {
            mkdir($this->testReportDir, 0755, true);
        }
        
        // Initialize system integrator
        $this->integrator = new SystemIntegrator($this->config);
        
        // Skip test if CMS files not available
        if (!is_dir($this->cmsPath)) {
            $this->markTestSkipped('CMS files not found at: ' . $this->cmsPath);
        }
    }

    /**
     * Test complete system initialization and component wiring
     * 
     * @covers SystemIntegrator::initialize
     */
    public function testSystemInitialization(): void
    {
        // Initialize the system
        $this->integrator->initialize();
        
        // Verify system status
        $status = $this->integrator->getSystemStatus();
        
        $this->assertTrue($status['initialized'], 'System should be initialized');
        $this->assertGreaterThan(0, $status['analyzers']['count'], 'Should have analyzers registered');
        $this->assertGreaterThan(0, $status['services']['count'], 'Should have services initialized');
        
        // Verify specific analyzers are loaded
        $expectedAnalyzers = ['security', 'performance', 'php', 'frontend', 'configuration'];
        $loadedAnalyzers = $status['analyzers']['types'];
        
        foreach ($expectedAnalyzers as $analyzer) {
            $this->assertContains($analyzer, $loadedAnalyzers, "Should have {$analyzer} analyzer loaded");
        }
        
        $this->testResults['initialization'] = [
            'status' => 'passed',
            'analyzers_count' => $status['analyzers']['count'],
            'services_count' => $status['services']['count'],
            'loaded_analyzers' => $loadedAnalyzers
        ];
    }

    /**
     * Test system health check functionality
     * 
     * @covers SystemIntegrator::performHealthCheck
     */
    public function testSystemHealthCheck(): void
    {
        $this->integrator->initialize();
        
        $healthCheck = $this->integrator->performHealthCheck();
        
        $this->assertArrayHasKey('overall_status', $healthCheck);
        $this->assertArrayHasKey('checks', $healthCheck);
        
        // Should have healthy or degraded status (not unhealthy)
        $this->assertContains($healthCheck['overall_status'], ['healthy', 'degraded'], 
            'System should be healthy or degraded, not unhealthy');
        
        // Check specific health checks
        $requiredChecks = [
            'initialization',
            'analyzers', 
            'services',
            'progress_directory',
            'report_directory',
            'target_directory'
        ];
        
        foreach ($requiredChecks as $check) {
            $this->assertArrayHasKey($check, $healthCheck['checks'], "Should have {$check} health check");
        }
        
        $this->testResults['health_check'] = [
            'overall_status' => $healthCheck['overall_status'],
            'checks_passed' => count(array_filter($healthCheck['checks'], fn($c) => $c['status'] === 'pass')),
            'checks_failed' => count(array_filter($healthCheck['checks'], fn($c) => $c['status'] === 'fail')),
            'checks_warning' => count(array_filter($healthCheck['checks'], fn($c) => $c['status'] === 'warning'))
        ];
    }

    /**
     * Test complete audit execution on CMS subset
     * 
     * @covers AuditController::startAudit
     */
    public function testCompleteAuditExecution(): void
    {
        $this->integrator->initialize();
        $controller = $this->integrator->getAuditController();
        
        // Configure audit for a subset of files to speed up testing
        $auditOptions = [
            'audit.target_directory' => $this->cmsPath,
            'audit.timeout' => 60, // Shorter timeout for testing
            'audit.max_file_size' => 1048576, // 1MB limit
            'clear_logs' => true
        ];
        
        $startTime = microtime(true);
        
        // Execute the audit
        $result = $controller->startAudit($auditOptions);
        
        $endTime = microtime(true);
        $duration = $endTime - $startTime;
        
        // Verify audit result structure
        $this->assertInstanceOf(AuditResult::class, $result);
        $this->assertIsArray($result->fileStatus);
        $this->assertIsObject($result->statistics);
        
        // Should have analyzed some files
        $this->assertGreaterThan(0, count($result->fileStatus), 'Should have analyzed some files');
        
        // Should have found some findings
        $this->assertGreaterThan(0, $result->statistics->totalFindings, 'Should have found some issues');
        
        // Performance check - should complete within reasonable time
        $this->assertLessThan(300, $duration, 'Audit should complete within 5 minutes');
        
        $this->testResults['audit_execution'] = [
            'files_analyzed' => count($result->fileStatus),
            'total_findings' => $result->statistics->totalFindings,
            'critical_findings' => $result->statistics->criticalFindings,
            'priority_findings' => $result->statistics->priorityAreaFindings,
            'duration_seconds' => round($duration, 2),
            'files_per_second' => round(count($result->fileStatus) / $duration, 2)
        ];
    }

    /**
     * Test report generation and accuracy
     * 
     * @covers ReportGenerator::exportReport
     */
    public function testReportGenerationAndAccuracy(): void
    {
        $this->integrator->initialize();
        $controller = $this->integrator->getAuditController();
        $reportGenerator = $this->integrator->getService('report_generator');
        
        // Run audit on a small subset
        $result = $controller->startAudit([
            'audit.target_directory' => $this->cmsPath,
            'audit.timeout' => 30
        ]);
        
        $timestamp = date('Y-m-d_H-i-s');
        
        // Generate reports in all formats
        $reports = [
            'markdown' => $this->testReportDir . "/integration_test_{$timestamp}.md",
            'json' => $this->testReportDir . "/integration_test_{$timestamp}.json",
            'html' => $this->testReportDir . "/integration_test_{$timestamp}.html"
        ];
        
        $generatedReports = [];
        
        foreach ($reports as $format => $path) {
            $success = $reportGenerator->exportReport($result, $format, $path);
            $this->assertTrue($success, "Should generate {$format} report successfully");
            
            if ($success && file_exists($path)) {
                $generatedReports[$format] = [
                    'path' => $path,
                    'size' => filesize($path),
                    'exists' => true
                ];
                
                // Validate report content
                $content = file_get_contents($path);
                $this->assertNotEmpty($content, "{$format} report should not be empty");
                
                // Check for key content elements
                if ($format === 'json') {
                    $jsonData = json_decode($content, true);
                    $this->assertIsArray($jsonData, 'JSON report should be valid JSON');
                    $this->assertArrayHasKey('statistics', $jsonData, 'JSON report should have statistics');
                    $this->assertArrayHasKey('findings', $jsonData, 'JSON report should have findings');
                }
            } else {
                $generatedReports[$format] = [
                    'path' => $path,
                    'exists' => false,
                    'error' => 'Report file not created'
                ];
            }
        }
        
        $this->testResults['report_generation'] = [
            'reports_generated' => count(array_filter($generatedReports, fn($r) => $r['exists'])),
            'total_reports_attempted' => count($reports),
            'report_details' => $generatedReports
        ];
    }

    /**
     * Test audit resume functionality
     * 
     * @covers AuditController::resumeAudit
     */
    public function testAuditResumeCapability(): void
    {
        $this->integrator->initialize();
        $controller = $this->integrator->getAuditController();
        
        // Start an audit (this will create progress)
        $result1 = $controller->startAudit([
            'audit.target_directory' => $this->cmsPath,
            'audit.timeout' => 30
        ]);
        
        // Get audit status
        $status = $controller->getAuditStatus();
        $this->assertNotNull($status, 'Should have audit status');
        
        // Try to resume (should work even if previous audit completed)
        try {
            $result2 = $controller->resumeAudit();
            $resumeSuccessful = true;
        } catch (\Exception $e) {
            // Resume might fail if no incomplete audit exists, which is acceptable
            $resumeSuccessful = false;
            $resumeError = $e->getMessage();
        }
        
        $this->testResults['resume_capability'] = [
            'initial_audit_completed' => isset($result1),
            'resume_attempted' => true,
            'resume_successful' => $resumeSuccessful,
            'resume_error' => $resumeError ?? null
        ];
        
        // At minimum, the resume attempt should not crash the system
        $this->assertTrue(true, 'Resume attempt should not crash the system');
    }

    /**
     * Test performance optimization for large codebase
     * 
     * @covers AuditController performance characteristics
     */
    public function testPerformanceOptimization(): void
    {
        $this->integrator->initialize();
        $controller = $this->integrator->getAuditController();
        
        // Test with different file size limits to measure performance impact
        $performanceTests = [
            'small_files' => ['audit.max_file_size' => 10240], // 10KB
            'medium_files' => ['audit.max_file_size' => 102400], // 100KB
            'large_files' => ['audit.max_file_size' => 1048576] // 1MB
        ];
        
        $performanceResults = [];
        
        foreach ($performanceTests as $testName => $options) {
            $options['audit.target_directory'] = $this->cmsPath;
            $options['audit.timeout'] = 60;
            $options['clear_logs'] = true;
            
            $startTime = microtime(true);
            $startMemory = memory_get_usage(true);
            
            $result = $controller->startAudit($options);
            
            $endTime = microtime(true);
            $endMemory = memory_get_usage(true);
            
            $performanceResults[$testName] = [
                'duration' => round($endTime - $startTime, 2),
                'memory_used' => $endMemory - $startMemory,
                'files_analyzed' => count($result->fileStatus),
                'findings_found' => $result->statistics->totalFindings,
                'files_per_second' => round(count($result->fileStatus) / ($endTime - $startTime), 2)
            ];
        }
        
        $this->testResults['performance_optimization'] = $performanceResults;
        
        // Verify performance is reasonable
        foreach ($performanceResults as $testName => $metrics) {
            $this->assertGreaterThan(0, $metrics['files_per_second'], 
                "Should process files at reasonable speed in {$testName} test");
            $this->assertLessThan(100 * 1024 * 1024, $metrics['memory_used'], 
                "Should not use excessive memory in {$testName} test");
        }
    }

    /**
     * Test error handling and recovery
     * 
     * @covers AuditController error handling
     */
    public function testErrorHandlingAndRecovery(): void
    {
        $this->integrator->initialize();
        $controller = $this->integrator->getAuditController();
        
        // Test with invalid target directory
        try {
            $result = $controller->startAudit([
                'audit.target_directory' => '/nonexistent/directory',
                'audit.timeout' => 10
            ]);
            $invalidDirHandled = false;
        } catch (\Exception $e) {
            $invalidDirHandled = true;
            $invalidDirError = $e->getMessage();
        }
        
        $this->assertTrue($invalidDirHandled, 'Should handle invalid target directory gracefully');
        
        // Test with very short timeout
        try {
            $result = $controller->startAudit([
                'audit.target_directory' => $this->cmsPath,
                'audit.timeout' => 1 // Very short timeout
            ]);
            $timeoutHandled = true;
        } catch (\Exception $e) {
            $timeoutHandled = false;
            $timeoutError = $e->getMessage();
        }
        
        $this->testResults['error_handling'] = [
            'invalid_directory_handled' => $invalidDirHandled,
            'invalid_directory_error' => $invalidDirError ?? null,
            'timeout_test_completed' => $timeoutHandled,
            'timeout_error' => $timeoutError ?? null
        ];
    }

    /**
     * Test integration with all analyzer types
     * 
     * @covers All analyzer integration
     */
    public function testAnalyzerIntegration(): void
    {
        $this->integrator->initialize();
        $analyzers = $this->integrator->getAnalyzers();
        
        $this->assertNotEmpty($analyzers, 'Should have analyzers loaded');
        
        $analyzerResults = [];
        
        // Test each analyzer individually on a sample file
        $testFile = $this->cmsPath . '/config.php';
        if (file_exists($testFile)) {
            $testContent = file_get_contents($testFile);
            
            foreach ($analyzers as $name => $analyzer) {
                try {
                    $findings = $analyzer->analyze($testFile, $testContent);
                    $analyzerResults[$name] = [
                        'status' => 'success',
                        'findings_count' => count($findings),
                        'has_findings' => !empty($findings)
                    ];
                } catch (\Exception $e) {
                    $analyzerResults[$name] = [
                        'status' => 'error',
                        'error' => $e->getMessage()
                    ];
                }
            }
        }
        
        $this->testResults['analyzer_integration'] = [
            'analyzers_tested' => count($analyzerResults),
            'successful_analyzers' => count(array_filter($analyzerResults, fn($r) => $r['status'] === 'success')),
            'failed_analyzers' => count(array_filter($analyzerResults, fn($r) => $r['status'] === 'error')),
            'analyzer_details' => $analyzerResults
        ];
        
        // At least some analyzers should work successfully
        $successfulCount = count(array_filter($analyzerResults, fn($r) => $r['status'] === 'success'));
        $this->assertGreaterThan(0, $successfulCount, 'At least some analyzers should work successfully');
    }

    /**
     * Test manual validation against known issues
     * 
     * This test validates that the system catches known issues that would be
     * identified in a manual code review.
     */
    public function testManualValidationAccuracy(): void
    {
        $this->integrator->initialize();
        $controller = $this->integrator->getAuditController();
        
        // Run audit on key files that are known to have issues
        $result = $controller->startAudit([
            'audit.target_directory' => $this->cmsPath,
            'audit.timeout' => 60
        ]);
        
        // Known issues that should be detected (based on manual review)
        $expectedIssues = [
            'hardcoded_credentials' => false,
            'sql_injection_risk' => false,
            'xss_vulnerability' => false,
            'error_reporting_enabled' => false,
            'missing_input_validation' => false
        ];
        
        // Check if expected issues were found (flatten findings per file)
        foreach ($result->findings as $fileFindings) {
            foreach ($fileFindings as $finding) {
                $description = strtolower($finding->getDescription());

                if (strpos($description, 'hardcoded') !== false || strpos($description, 'credential') !== false) {
                    $expectedIssues['hardcoded_credentials'] = true;
                }
                if (strpos($description, 'sql injection') !== false || strpos($description, 'prepared statement') !== false) {
                    $expectedIssues['sql_injection_risk'] = true;
                }
                if (strpos($description, 'xss') !== false || strpos($description, 'escape') !== false) {
                    $expectedIssues['xss_vulnerability'] = true;
                }
                if (strpos($description, 'error_reporting') !== false || strpos($description, 'display_errors') !== false) {
                    $expectedIssues['error_reporting_enabled'] = true;
                }
                if (strpos($description, 'input validation') !== false || strpos($description, 'sanitiz') !== false) {
                    $expectedIssues['missing_input_validation'] = true;
                }
            }
        }
        
        $this->testResults['manual_validation'] = [
            'total_findings' => count($result->findings),
            'expected_issues_found' => array_sum($expectedIssues),
            'expected_issues_total' => count($expectedIssues),
            'issue_detection_details' => $expectedIssues
        ];
        
        // Should detect at least some of the expected issues
        $detectedCount = array_sum($expectedIssues);
        $this->assertGreaterThan(0, $detectedCount, 'Should detect some known issues from manual review');
    }

    protected function tearDown(): void
    {
        // Cleanup test files
        if (is_dir($this->testReportDir)) {
            $files = glob($this->testReportDir . '/integration_test_*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
        
        // Cleanup system resources
        if (isset($this->integrator)) {
            $this->integrator->cleanup();
        }
        
        // Output comprehensive test results
        if (!empty($this->testResults)) {
            echo "\n" . str_repeat("=", 80) . "\n";
            echo "COMPLETE SYSTEM INTEGRATION TEST RESULTS\n";
            echo str_repeat("=", 80) . "\n";
            echo json_encode($this->testResults, JSON_PRETTY_PRINT);
            echo "\n" . str_repeat("=", 80) . "\n";
        }
        
        parent::tearDown();
    }
}