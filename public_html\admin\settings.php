<?php
/**
 * Admin Settings Page
 *
 * Allows administrators to configure various site options.
 * Note: This file currently only contains the HTML structure and placeholders.
 * PHP logic for loading and saving settings needs to be implemented.
 *
 * Changelog:
 * - v1.2 (2025-04-03): Added dedicated settings card for Popup 2025 features.
 * - v1.1 (2025-04-03): Refactored layout to two-column structure for consistency with other admin pages.
 * Replaced custom .settings-section with standard .card component.
 * Removed top tab navigation and consolidated buttons in header.
 * Moved optimization/caching settings to right sidebar.
 * Simplified Advertisement settings layout (removed sub-tabs).
 */

// Core configuration and authentication
require_once '../config.php'; // Adjust path if settings.php is not directly in admin/
require_once 'includes/auth_check.php'; // Check if admin is logged in
require_once '../includes/functions.php'; // General functions like escape()

$admin_page_title = 'Settings'; // Set the page title for the header

// --- Placeholder Variables for Settings (Load these from DB/config later) ---
// Example placeholders - replace with actual loading logic
$settings = [
    // --- Existing Settings ---
    'website_name' => 'Lako & Fino',
    'site_url' => defined('SITE_URL') ? SITE_URL : 'https://lakofino.com',
    'favicon_path' => null, // Placeholder, might store path or be handled differently
    'default_language' => 'bs',
    'default_article_layout' => 'standard',
    'default_category_id' => 'recepti', // Assuming 'recepti' corresponds to an ID or slug you use
    'show_article_meta' => true,
    'default_sidebar' => true,
    'default_similar_posts' => true,
    'default_fb_share' => true,
    'default_article_recommendations' => true,
    'default_author_id' => 'john-smith', // Example author ID/slug
    'random_author_assignment' => false,
    'show_author_bio' => true,
    'compress_images' => true,
    'convert_modern_formats' => true,
    'generate_responsive_sizes' => true,
    'strip_metadata' => true,
    'increase_contrast' => true,
    'cdn_enabled' => true,
    'cdn_url' => '',
    'cdn_images' => true,
    'cdn_css' => true,
    'cdn_js' => true,
    'cdn_fonts' => true,
    'enable_loading_trick' => true,
    'live_readers_count' => true,
    'min_readers_count' => 50,
    'loading_time' => 3,
    'knowledge_progress_bar' => true,
    'time_sensitive_offers' => true,
    'auto_internal_linking' => true,
    'min_keyword_length' => 4,
    'max_links_per_article' => 5,
    'default_keywords' => ['optimizacija web stranice', 'performanse'], // Example keywords as an array
    'adsense_client_id' => 'ca-pub-1234567890', // Example
    'lazy_load_ads' => true,
    'mobile_ad_visibility' => true,
    'ad_refresh_rate' => 0,
    'header_ad_enabled' => true,
    'header_ad_type' => 'adsense',
    'header_ad_size' => 'responsive',
    'header_ad_slot_id' => '1234567890',
    'header_ad_custom_html' => '',
    'header_ad_rules_desktop' => true,
    'header_ad_rules_mobile' => true,
    'header_ad_rules_articles' => true,
    'header_ad_rules_homepage' => true,
    'header_ad_schedule_start' => null,
    'header_ad_schedule_end' => null,
    'sidebar_ad_enabled' => true,
    'sidebar_ad_type' => 'affiliate',
    'sidebar_ad_size' => '300x250',
    'sidebar_ad_slot_id' => '', // Not needed for affiliate/custom
    'sidebar_ad_custom_html' => '', // Store custom HTML if type is custom
    'sidebar_ad_pos_top' => true,
    'sidebar_ad_pos_middle' => true,
    'sidebar_ad_pos_bottom' => false,
    'sidebar_ad_rotation_enabled' => true,
    'sidebar_ad_rotation_items' => [ // Example structure for rotating ads
        ['name' => 'Affiliate Banner 1', 'weight' => 60, 'code' => ''],
        ['name' => 'Affiliate Banner 2', 'weight' => 40, 'code' => ''],
    ],
    'incontent_ad_enabled' => true,
    'incontent_ad_default_type' => 'adsense',
    'incontent_ad_default_size' => 'responsive',
    'incontent_ad_pos_after_first' => true,
    'incontent_ad_pos_after_first_type' => 'default',
    'incontent_ad_pos_every_x' => true,
    'incontent_ad_pos_every_x_count' => 3,
    'incontent_ad_pos_every_x_type' => 'adsense',
    'incontent_ad_pos_middle' => true,
    'incontent_ad_pos_middle_type' => 'affiliate',
    'incontent_ad_pos_before_end' => false,
    'incontent_ad_pos_before_end_type' => 'default',
    'incontent_ad_max_per_article' => 3,
    'incontent_ad_min_words' => 150,
    'between_posts_ad_enabled' => true,
    'between_posts_ad_type' => 'custom',
    'between_posts_ad_format' => 'card',
    'between_posts_ad_frequency' => 3,
    'between_posts_ad_custom_html' => '<div class="promo-card bg-gradient-to-r from-red-100 to-pink-100 p-4 rounded-lg text-center">
    <h3 class="text-lg font-bold text-primary mb-2">Learn to Cook Like a Pro!</h3>
    <p class="text-sm mb-3">Join our online cooking masterclass and get 20% off with code: LAKOIFINO</p>
    <a href="#" class="btn py-1">Learn More</a>
</div>',
    'footer_ad_enabled' => false,
    'footer_ad_type' => 'adsense',
    'footer_ad_size' => 'responsive',
    'popup_ad_enabled' => true, // General popup toggle
    'popup_ad_type' => 'timed', // General popup type (timed, scroll, click, exit)
    'popup_ad_trigger_timing' => 20, // Seconds for timed, % for scroll, clicks for click
    'popup_ad_frequency' => 'once_week', // General frequency (once_session, once_day, once_week, always)
    'popup_ad_content' => '<div class="text-center">
    <h3 class="text-xl font-bold mb-2">Join Our Newsletter</h3>
    <p class="mb-4">Get the latest articles and recipes delivered to your inbox!</p>
    <div class="flex mb-2">
        <input type="email" placeholder="Your email address" class="flex-grow rounded-l px-3 py-2 border border-r-0">
        <button class="bg-primary text-white px-4 py-2 rounded-r">Subscribe</button>
    </div>
    <p class="text-xs text-gray-500">By subscribing, you agree to our privacy policy.</p>
</div>',
    'popup_ad_appearance' => 'center', // 'center', 'banner', 'slide-in'
    'minify_html' => true,
    'minify_css' => true,
    'minify_js' => true,
    'concat_css' => true,
    'concat_js' => true,
    'gzip_compression' => true,
    'browser_caching' => true,
    'html_cache_duration' => 1,
    'css_js_cache_duration' => 365,
    'image_cache_duration' => 365,
    'server_side_caching' => true,
    'database_caching' => true,
    'enable_article_cloaking' => false,
    'cloak_url_prefix' => 'go',
    'track_cloak_clicks' => true,

    // --- NEW Popup 2025 Settings ---
    'popup2025_enabled' => true,                 // Master switch for the popup2025 system
    'popup2025_use_ip_blocking' => true,         // Enable checking against blokirani.txt and country rules
    'popup2025_use_bot_detection' => true,       // Enable checking against bots.txt
    'popup2025_use_referrer_check' => false,     // Enable referrer validation
    'popup2025_track_clicks' => true,            // Enable click tracking via clickbaza.php
    'popup2025_check_intersection' => true,      // Enable viewability check using intersectionRatio.php
    'popup2025_device_filter' => 'all',          // Device targeting ('all', 'desktop', 'mobile')
    'popup2025_allowed_countries' => '',         // Comma-separated list of allowed country codes (e.g., US,CA,GB). Empty means all allowed.
    'popup2025_blocked_countries' => '',         // Comma-separated list of blocked country codes (e.g., RU,CN). Empty means none blocked.
    'popup2025_debug_mode' => false,             // Enable debug output for troubleshooting
    // Add more specific settings as needed based on the logic within popup2025 files
];

// Example: Fetch authors and categories for dropdowns (replace with your actual logic)
$available_authors = [
    ['id' => 'john-smith', 'name' => 'John Smith'],
    ['id' => 'amina-hodzic', 'name' => 'Amina Hodžić'],
    ['id' => 'emir-kovacevic', 'name' => 'Emir Kovačević'],
];
$available_categories = [
    ['id' => 'recepti', 'name' => 'Recepti'],
    ['id' => 'prirodni-lijekovi', 'name' => 'Prirodni lijekovi'],
    ['id' => 'vijesti', 'name' => 'Vijesti'],
    ['id' => 'tehnologija', 'name' => 'Tehnologija'],
];

// Using selected() function from includes/functions.php

// Helper function for toggles (add to functions.php or define here)
function render_toggle(string $name, string $label, string $description, bool $currentValue) {
    $id = escape($name) . 'Toggle';
    $checked = $currentValue ? 'true' : 'false';
    echo '<div class="setting-row">';
    echo '<div><label for="' . $id . '" class="setting-label">' . escape($label) . '</label>';
    if (!empty($description)) {
        echo '<p class="setting-description">' . escape($description) . '</p>';
    }
    echo '</div>';
    echo '<div class="setting-control">';
    // Use Alpine.js for interactive toggle state
    echo '<div x-data="{ enabled: ' . $checked . ' }">';
    // Hidden input submits the actual value (1 or 0)
    echo '<input type="hidden" name="settings[' . escape($name) . ']" :value="enabled ? 1 : 0">';
    // Checkbox primarily for Alpine state management, visually hidden
    echo '<input type="checkbox" class="hidden" id="' . $id . '" x-model="enabled">';
    // Visually styled label acting as the toggle switch
    echo '<label for="' . $id . '" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? \'bg-primary\' : \'bg-gray-200\'"><span class="toggle-thumb" :class="enabled ? \'transform translate-x-5\' : \'\'"></span></label>';
    echo '</div></div></div>';
}

// Helper function for checkboxes (add to functions.php or define here)
function render_checkbox(string $name, string $label, bool $currentValue) {
    $id = escape($name) . 'Checkbox';
    $checked = $currentValue ? 'checked' : '';
    echo '<label for="' . $id . '" class="flex items-center">';
    // Hidden input ensures a value (0) is sent even if the checkbox is unchecked
    echo '<input type="hidden" name="settings[' . escape($name) . ']" value="0">';
    // The actual checkbox
    echo '<input type="checkbox" id="' . $id . '" name="settings[' . escape($name) . ']" value="1" class="form-checkbox" ' . $checked . '>';
    echo '<span class="ml-2 text-sm">' . escape($label) . '</span>';
    echo '</label>';
}


// Include the admin header
include 'includes/header.php';
?>

<header class="h-16 bg-white border-b border-border flex items-center justify-between px-6 flex-shrink-0 sticky top-0 z-30">
    <h1 class="text-xl font-montserrat font-bold text-dark"><?php echo escape($admin_page_title); ?></h1>
    <div class="flex items-center space-x-4">
        <button type="button" class="btn-secondary" onclick="if(confirm('Reset all settings to default? This cannot be undone.')) { /* TODO: Add reset logic here - e.g., redirect to process_settings.php?action=reset */ }">
            Reset to Defaults
        </button>
        <button type="submit" form="settingsForm" class="btn-success">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            Save Settings
        </button>
    </div>
</header>

<div class="flex-1 overflow-y-auto bg-background">
    <div class="p-6">
        <?php // --- Display Success/Error Messages --- ?>
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="mb-4 bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded-lg text-sm shadow-sm">
                <?php echo escape($_SESSION['success_message']); unset($_SESSION['success_message']); ?>
            </div>
        <?php endif; ?>
        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm shadow-sm">
                <?php echo escape($_SESSION['error_message']); unset($_SESSION['error_message']); ?>
            </div>
        <?php endif; ?>

        <form id="settingsForm" action="process_settings.php" method="POST" enctype="multipart/form-data"> <?php // Action points to a processing script ?>
            <?php // Add a CSRF token here for security if your framework/setup supports it ?>
            <?php // wp_nonce_field('save_my_settings', 'my_settings_nonce'); ?>

            <div class="flex flex-col lg:flex-row gap-6">

                <?php // --- Main Content Column (Left) --- ?>
                <div class="lg:w-7/12 flex flex-col space-y-6">

                    <?php // --- Website Settings Card --- ?>
                    <div class="card">
                        <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">Website Settings</h3>
                        <div class="space-y-4">
                            <div class="setting-row">
                                <div>
                                    <label for="website_name" class="setting-label">Website Name</label>
                                    <p class="setting-description">The name displayed in the header and title</p>
                                </div>
                                <div class="setting-control">
                                    <input type="text" id="website_name" name="settings[website_name]" value="<?php echo escape($settings['website_name'] ?? ''); ?>" class="form-input">
                                </div>
                            </div>
                            <div class="setting-row">
                                <div>
                                    <label for="site_url" class="setting-label">Site URL</label>
                                    <p class="setting-description">Your website's base URL</p>
                                </div>
                                <div class="setting-control">
                                    <input type="url" id="site_url" name="settings[site_url]" value="<?php echo escape($settings['site_url'] ?? ''); ?>" class="form-input">
                                </div>
                            </div>
                            <div class="setting-row">
                                <div>
                                    <label for="favicon_upload" class="setting-label">Favicon</label>
                                    <p class="setting-description">Icon for browser tabs (.ico, .png)</p>
                                    <?php if (!empty($settings['favicon_path'])): // Assuming favicon_path stores the URL/path ?>
                                        <img src="<?php echo escape($settings['favicon_path']); ?>" alt="Current Favicon" class="h-8 w-8 mt-1 inline-block">
                                    <?php else: ?>
                                        <p class="text-xs text-gray-500 mt-1">No favicon set.</p>
                                    <?php endif; ?>
                                </div>
                                <div class="setting-control">
                                    <label class="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-md p-3 cursor-pointer hover:border-primary transition-colors">
                                        <input type="file" id="favicon_upload" name="favicon_upload" class="hidden" accept=".ico,.png,image/png,image/x-icon">
                                        <span class="text-sm text-gray-600">Upload new favicon</span>
                                    </label>
                                     <input type="hidden" name="settings[favicon_path]" value="<?php echo escape($settings['favicon_path'] ?? ''); ?>"> <?php // Keep track of existing path if needed ?>
                                </div>
                            </div>
                            <div class="setting-row">
                                <div>
                                    <label for="default_language" class="setting-label">Default Language</label>
                                    <p class="setting-description">Primary language for content</p>
                                </div>
                                <div class="setting-control">
                                    <select id="default_language" name="settings[default_language]" class="form-select">
                                        <option value="bs" <?php selected($settings['default_language'] ?? '', 'bs'); ?>>Bosanski</option>
                                        <option value="en" <?php selected($settings['default_language'] ?? '', 'en'); ?>>English</option>
                                        <option value="hr" <?php selected($settings['default_language'] ?? '', 'hr'); ?>>Hrvatski</option>
                                        <option value="sr" <?php selected($settings['default_language'] ?? '', 'sr'); ?>>Srpski</option>
                                        <?php // Add other languages as needed ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                     <?php // --- Content Default Settings Card --- ?>
                    <div class="card">
                        <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">Content Defaults</h3>
                         <div class="space-y-4">
                            <div class="setting-row">
                                <div>
                                    <label for="default_article_layout" class="setting-label">Default Article Layout</label>
                                    <p class="setting-description">Default layout for new articles</p>
                                </div>
                                <div class="setting-control">
                                    <select id="default_article_layout" name="settings[default_article_layout]" class="form-select">
                                        <option value="standard" <?php selected($settings['default_article_layout'] ?? '', 'standard'); ?>>Standard Layout</option>
                                        <option value="wide" <?php selected($settings['default_article_layout'] ?? '', 'wide'); ?>>Wide Layout</option>
                                        <option value="sidebar" <?php selected($settings['default_article_layout'] ?? '', 'sidebar'); ?>>With Sidebar</option>
                                        <option value="magazine" <?php selected($settings['default_article_layout'] ?? '', 'magazine'); ?>>Magazine Style</option>
                                        <?php // Add other layouts as needed ?>
                                    </select>
                                </div>
                            </div>
                            <div class="setting-row">
                                <div>
                                    <label for="default_category_id" class="setting-label">Default Category</label>
                                    <p class="setting-description">Default category for new articles</p>
                                </div>
                                <div class="setting-control">
                                    <select id="default_category_id" name="settings[default_category_id]" class="form-select">
                                        <option value="">None</option>
                                        <?php foreach ($available_categories as $cat): ?>
                                            <option value="<?php echo escape($cat['id']); ?>" <?php selected($settings['default_category_id'] ?? '', $cat['id']); ?>>
                                                <?php echo escape($cat['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <?php render_toggle('show_article_meta', 'Show Article Meta', 'Show author, date, reading time', $settings['show_article_meta'] ?? true); ?>
                            <?php render_toggle('default_sidebar', 'Default Sidebar', 'Enable sidebar by default', $settings['default_sidebar'] ?? true); ?>
                            <?php render_toggle('default_similar_posts', 'Default Similar Posts', 'Show similar posts by default', $settings['default_similar_posts'] ?? true); ?>
                            <?php render_toggle('default_fb_share', 'Default Facebook Share', 'Include FB share button by default', $settings['default_fb_share'] ?? true); ?>
                            <?php render_toggle('default_article_recommendations', 'Default Article Recs', 'Include in recommendations by default', $settings['default_article_recommendations'] ?? true); ?>
                        </div>
                    </div>

                    <?php // --- User Settings Card --- ?>
                    <div class="card">
                        <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">User Defaults</h3>
                        <div class="space-y-4">
                            <div class="setting-row">
                                <div>
                                    <label for="default_author_id" class="setting-label">Default Author</label>
                                    <p class="setting-description">Default author for new articles</p>
                                </div>
                                <div class="setting-control">
                                    <select id="default_author_id" name="settings[default_author_id]" class="form-select">
                                        <?php foreach ($available_authors as $author): ?>
                                            <option value="<?php echo escape($author['id']); ?>" <?php selected($settings['default_author_id'] ?? '', $author['id']); ?>>
                                                <?php echo escape($author['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <?php render_toggle('random_author_assignment', 'Random Author Assignment', 'Use random author by default', $settings['random_author_assignment'] ?? false); ?>
                            <?php render_toggle('show_author_bio', 'Author Biography', 'Display author bio at end of articles', $settings['show_author_bio'] ?? true); ?>
                        </div>
                    </div>

                    <?php // --- Loading Trick Card --- ?>
                    <div class="card">
                          <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">Loading Trick & Reach Boost</h3>
                          <div class="space-y-4">
                            <?php render_toggle('enable_loading_trick', 'Enable Loading Trick', 'Use loading screen for engagement', $settings['enable_loading_trick'] ?? true); ?>
                            <?php render_toggle('live_readers_count', 'Live Readers Count', 'Show dynamic reader counter', $settings['live_readers_count'] ?? true); ?>
                            <div class="setting-row">
                                <div>
                                    <label for="min_readers_count" class="setting-label">Minimum Readers Count</label>
                                    <p class="setting-description">Minimum number for reader counter</p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" id="min_readers_count" name="settings[min_readers_count]" value="<?php echo escape((string)($settings['min_readers_count'] ?? 50)); ?>" class="form-input" min="0">
                                </div>
                            </div>
                            <div class="setting-row">
                                <div>
                                    <label for="loading_time" class="setting-label">Loading Time (seconds)</label>
                                    <p class="setting-description">Duration of loading animation</p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" id="loading_time" name="settings[loading_time]" value="<?php echo escape((string)($settings['loading_time'] ?? 3)); ?>" class="form-input" min="0" step="0.1">
                                </div>
                            </div>
                            <?php render_toggle('knowledge_progress_bar', 'Reading Progress Bar', 'Show progress bar for articles', $settings['knowledge_progress_bar'] ?? true); ?>
                            <?php render_toggle('time_sensitive_offers', 'Time-sensitive Offers', 'Show limited-time offers', $settings['time_sensitive_offers'] ?? true); ?>
                        </div>
                    </div>

                    <?php // --- Auto Internal Linking Card --- ?>
                    <div class="card">
                        <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">Auto Internal Linking</h3>
                        <div class="space-y-4">
                             <?php render_toggle('auto_internal_linking', 'Enable Auto Linking', 'Automatically link keywords', $settings['auto_internal_linking'] ?? true); ?>
                            <div class="setting-row">
                                <div>
                                    <label for="min_keyword_length" class="setting-label">Min Keyword Length</label>
                                    <p class="setting-description">Min characters for auto-linking</p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" id="min_keyword_length" name="settings[min_keyword_length]" value="<?php echo escape((string)($settings['min_keyword_length'] ?? 4)); ?>" class="form-input" min="1">
                                </div>
                            </div>
                            <div class="setting-row">
                                <div>
                                    <label for="max_links_per_article" class="setting-label">Max Links per Article</label>
                                    <p class="setting-description">Max auto-generated links</p>
                                </div>
                                <div class="setting-control">
                                    <input type="number" id="max_links_per_article" name="settings[max_links_per_article]" value="<?php echo escape((string)($settings['max_links_per_article'] ?? 5)); ?>" class="form-input" min="0">
                                </div>
                            </div>
                            <?php // Alpine.js component for managing keywords list ?>
                            <div class="setting-row" x-data='{ keywords: <?php echo json_encode($settings['default_keywords'] ?? []); ?>, newKeyword: "", keywordsString: "", init() { this.updateKeywordsString(); this.$watch("keywords", () => this.updateKeywordsString()); }, addKeyword() { const kw = this.newKeyword.trim(); if (kw && !this.keywords.includes(kw)) { this.keywords.push(kw); } this.newKeyword = ""; }, removeKeyword(index) { this.keywords.splice(index, 1); }, updateKeywordsString() { this.keywordsString = this.keywords.join(","); } }'>
                                <div>
                                    <label for="defaultKeywordsInput" class="setting-label">Default Keywords</label>
                                    <p class="setting-description">Keywords to link across all articles (comma-separated)</p>
                                </div>
                                <div class="setting-control">
                                    <div class="space-y-2">
                                        <?php // Display current keywords as tags ?>
                                        <div class="flex flex-wrap gap-2 mb-2 min-h-[24px] p-2 border rounded-md bg-gray-50">
                                            <template x-for="(keyword, index) in keywords" :key="index">
                                                <span class="bg-gray-200 text-xs rounded-full px-2 py-1 flex items-center">
                                                    <span x-text="keyword"></span>
                                                    <button type="button" @click="removeKeyword(index)" class="ml-1 text-gray-500 hover:text-red-500 focus:outline-none">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                                                    </button>
                                                </span>
                                            </template>
                                            <span x-show="keywords.length === 0" class="text-xs text-gray-400 italic">No keywords added.</span>
                                        </div>
                                        <?php // Input field to add new keywords ?>
                                        <div class="flex gap-2">
                                            <input type="text" id="defaultKeywordsInput" x-model="newKeyword" @keydown.enter.prevent="addKeyword" @keydown.,.prevent="addKeyword" class="form-input text-sm flex-grow" placeholder="Add keyword and press Enter or comma">
                                            <button type="button" @click="addKeyword" class="btn text-xs py-1 px-2">Add</button>
                                        </div>
                                        <?php // Hidden input stores the comma-separated string for form submission ?>
                                        <input type="hidden" name="settings[default_keywords]" :value="keywordsString">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php // --- Advertisement Settings Card (Simplified) --- ?>
                     <div class="card">
                         <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">Advertisements</h3>
                         <div class="space-y-4">
                             <h4 class="font-medium text-gray-800 mb-1 text-base">General Ad Settings</h4>
                             <div class="setting-row border-t pt-3">
                                 <div>
                                     <label for="adsense_client_id" class="setting-label">AdSense Client ID</label>
                                     <p class="setting-description">Google AdSense publisher ID (e.g., ca-pub-xxxx)</p>
                                 </div>
                                 <div class="setting-control">
                                     <input type="text" id="adsense_client_id" name="settings[adsense_client_id]" value="<?php echo escape($settings['adsense_client_id'] ?? ''); ?>" class="form-input" placeholder="ca-pub-1234567890">
                                 </div>
                             </div>
                             <?php render_toggle('lazy_load_ads', 'Lazy Load Ads', 'Load ads after page content', $settings['lazy_load_ads'] ?? true); ?>
                             <?php render_toggle('mobile_ad_visibility', 'Mobile Ad Visibility', 'Show ads on mobile devices', $settings['mobile_ad_visibility'] ?? true); ?>
                             <div class="setting-row">
                                 <div>
                                     <label for="ad_refresh_rate" class="setting-label">Ad Refresh Rate (seconds)</label>
                                     <p class="setting-description">Automatically refresh ads after this interval. 0 = no refresh.</p>
                                 </div>
                                 <div class="setting-control">
                                     <input type="number" id="ad_refresh_rate" name="settings[ad_refresh_rate]" value="<?php echo escape((string)($settings['ad_refresh_rate'] ?? 0)); ?>" class="form-input" min="0" step="10">
                                 </div>
                             </div>

                             <?php // --- Header Ad Sub-section --- ?>
                             <h4 class="font-medium text-gray-800 mb-1 text-base border-t pt-4 mt-4">Header Ad</h4>
                             <?php render_toggle('header_ad_enabled', 'Enable Header Ad', 'Display an ad in the site header', $settings['header_ad_enabled'] ?? true); ?>
                             <?php // TODO: Add detailed header ad settings (type, size, slot, custom html, rules) using setting-row format if header_ad_enabled is true ?>
                             <?php /* Example structure:
                             <div x-show="document.querySelector('[name=\'settings[header_ad_enabled]\']').value == 1"> // Show only if enabled
                                <div class="setting-row"> ... Type Select ... </div>
                                <div class="setting-row"> ... Size Select ... </div>
                                <div class="setting-row"> ... Slot ID Input ... </div>
                                <div class="setting-row"> ... Custom HTML Textarea ... </div>
                                <div class="setting-row"> ... Rules Checkboxes ... </div>
                             </div>
                             */ ?>

                             <?php // --- Sidebar Ad Sub-section --- ?>
                             <h4 class="font-medium text-gray-800 mb-1 text-base border-t pt-4 mt-4">Sidebar Ad</h4>
                             <?php render_toggle('sidebar_ad_enabled', 'Enable Sidebar Ad', 'Display ads in the sidebar', $settings['sidebar_ad_enabled'] ?? true); ?>
                             <?php // TODO: Add detailed sidebar ad settings (type, size, slot/html, positions, rotation) using setting-row format if sidebar_ad_enabled is true ?>

                             <?php // --- In-Content Ads Sub-section --- ?>
                             <h4 class="font-medium text-gray-800 mb-1 text-base border-t pt-4 mt-4">In-Content Ads</h4>
                              <?php render_toggle('incontent_ad_enabled', 'Enable In-Content Ads', 'Insert ads within article content', $settings['incontent_ad_enabled'] ?? true); ?>
                              <?php // TODO: Add detailed in-content ad settings (defaults, positions, frequency, rules) using setting-row format if incontent_ad_enabled is true ?>

                              <?php // --- Between Posts Ads Sub-section --- ?>
                              <h4 class="font-medium text-gray-800 mb-1 text-base border-t pt-4 mt-4">Between Posts Ads</h4>
                              <?php render_toggle('between_posts_ad_enabled', 'Enable Between Posts Ads', 'Show ads between posts on archive/home pages', $settings['between_posts_ad_enabled'] ?? true); ?>
                              <?php // TODO: Add detailed between-posts ad settings (type, format, frequency, custom html) using setting-row format if between_posts_ad_enabled is true ?>

                              <?php // --- Footer Ad Sub-section --- ?>
                              <h4 class="font-medium text-gray-800 mb-1 text-base border-t pt-4 mt-4">Footer Ad</h4>
                              <?php render_toggle('footer_ad_enabled', 'Enable Footer Ad', 'Display an ad in the site footer', $settings['footer_ad_enabled'] ?? false); ?>
                              <?php // TODO: Add detailed footer ad settings (type, size, slot/html) using setting-row format if footer_ad_enabled is true ?>

                              <?php // --- General Popup/Overlay Ad Sub-section --- ?>
                              <h4 class="font-medium text-gray-800 mb-1 text-base border-t pt-4 mt-4">General Popup/Overlay Ad</h4>
                              <?php render_toggle('popup_ad_enabled', 'Enable General Popup Ad', 'Show a standard popup/overlay (independent of Popup 2025)', $settings['popup_ad_enabled'] ?? true); ?>
                              <?php // TODO: Add detailed general popup ad settings (type, trigger, frequency, content, appearance) using setting-row format if popup_ad_enabled is true ?>
                         </div>
                     </div>

                    <?php // --- NEW Popup 2025 Settings Card --- ?>
                    <div class="card">
                        <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">Popup 2025 Advanced Settings</h3>
                        <div class="space-y-4">
                            <?php render_toggle('popup2025_enabled', 'Enable Popup 2025', 'Master switch to activate the Popup 2025 system.', $settings['popup2025_enabled'] ?? true); ?>

                            <div x-data="{ enabled: <?php echo ($settings['popup2025_enabled'] ?? true) ? 'true' : 'false'; ?> }" x-show="enabled" class="space-y-4 border-t pt-4 mt-4">
                                <?php // These settings only appear if popup2025_enabled is true ?>
                                <?php render_toggle('popup2025_use_ip_blocking', 'Use IP Blocking', 'Block visitors based on IP address (uses blokirani.txt and country rules).', $settings['popup2025_use_ip_blocking'] ?? true); ?>
                                <div class="setting-row pl-6" x-data="{ ipBlockingEnabled: <?php echo ($settings['popup2025_use_ip_blocking'] ?? true) ? 'true' : 'false'; ?> }" x-show="ipBlockingEnabled">
                                     <div>
                                         <label for="popup2025_allowed_countries" class="setting-label !text-sm">Allowed Countries</label>
                                         <p class="setting-description">Comma-separated ISO codes (e.g., US,CA,GB). Leave empty to allow all.</p>
                                     </div>
                                     <div class="setting-control">
                                         <input type="text" id="popup2025_allowed_countries" name="settings[popup2025_allowed_countries]" value="<?php echo escape($settings['popup2025_allowed_countries'] ?? ''); ?>" class="form-input text-sm" placeholder="e.g., US,CA,GB">
                                     </div>
                                </div>
                                 <div class="setting-row pl-6" x-data="{ ipBlockingEnabled: <?php echo ($settings['popup2025_use_ip_blocking'] ?? true) ? 'true' : 'false'; ?> }" x-show="ipBlockingEnabled">
                                     <div>
                                         <label for="popup2025_blocked_countries" class="setting-label !text-sm">Blocked Countries</label>
                                         <p class="setting-description">Comma-separated ISO codes (e.g., RU,CN). Leave empty to block none.</p>
                                     </div>
                                     <div class="setting-control">
                                         <input type="text" id="popup2025_blocked_countries" name="settings[popup2025_blocked_countries]" value="<?php echo escape($settings['popup2025_blocked_countries'] ?? ''); ?>" class="form-input text-sm" placeholder="e.g., RU,CN,IR">
                                     </div>
                                </div>

                                <?php render_toggle('popup2025_use_bot_detection', 'Use Bot Detection', 'Block visitors identified as bots (uses bots.txt).', $settings['popup2025_use_bot_detection'] ?? true); ?>
                                <?php render_toggle('popup2025_use_referrer_check', 'Use Referrer Check', 'Validate the HTTP referrer (if enabled in referrer.php).', $settings['popup2025_use_referrer_check'] ?? false); ?>
                                <?php render_toggle('popup2025_track_clicks', 'Track Clicks', 'Log popup clicks (uses clickbaza.php - requires setup).', $settings['popup2025_track_clicks'] ?? true); ?>
                                <?php render_toggle('popup2025_check_intersection', 'Check Viewability', 'Use IntersectionObserver check (intersectionRatio.php).', $settings['popup2025_check_intersection'] ?? true); ?>

                                <div class="setting-row">
                                    <div>
                                        <label for="popup2025_device_filter" class="setting-label">Device Filter</label>
                                        <p class="setting-description">Show Popup 2025 only on specific devices.</p>
                                    </div>
                                    <div class="setting-control">
                                        <select id="popup2025_device_filter" name="settings[popup2025_device_filter]" class="form-select">
                                            <option value="all" <?php selected($settings['popup2025_device_filter'] ?? 'all', 'all'); ?>>All Devices</option>
                                            <option value="desktop" <?php selected($settings['popup2025_device_filter'] ?? 'all', 'desktop'); ?>>Desktop Only</option>
                                            <option value="mobile" <?php selected($settings['popup2025_device_filter'] ?? 'all', 'mobile'); ?>>Mobile Only</option>
                                        </select>
                                    </div>
                                </div>
                                <?php render_toggle('popup2025_debug_mode', 'Debug Mode', 'Enable verbose logging or output for troubleshooting (requires implementation in popup scripts).', $settings['popup2025_debug_mode'] ?? false); ?>
                                <?php // Add more settings here as needed, e.g., thresholds, specific URLs, etc. ?>
                            </div>
                        </div>
                    </div>


                </div> <?php // --- End Main Content Column --- ?>

                <?php // --- Sidebar Column (Right) --- ?>
                <div class="lg:w-5/12 flex-shrink-0">
                    <div class="sticky top-20 space-y-6"> <?php // Sticky container for sidebar cards ?>

                        <?php // --- Image Optimization Card --- ?>
                        <div class="card">
                            <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">Image Optimization</h3>
                            <div class="space-y-4">
                                <?php render_toggle('compress_images', 'Compress Images', 'Compress to '.(defined('IMAGE_QUALITY') ? IMAGE_QUALITY : 80).'% quality', $settings['compress_images'] ?? true); ?>
                                <?php render_toggle('convert_modern_formats', 'Convert to Modern Formats', 'Use WebP/AVIF if supported by browser', $settings['convert_modern_formats'] ?? true); ?>
                                <?php render_toggle('generate_responsive_sizes', 'Generate Responsive Sizes', 'Create multiple image sizes for different screens', $settings['generate_responsive_sizes'] ?? true); ?>
                                <?php render_toggle('strip_metadata', 'Strip Metadata', 'Remove EXIF/IPTC data from images', $settings['strip_metadata'] ?? true); ?>
                                <?php render_toggle('increase_contrast', 'Increase Contrast', 'Slightly increase image contrast (subtle effect)', $settings['increase_contrast'] ?? true); ?>
                            </div>
                        </div>

                        <?php // --- CDN Settings Card --- ?>
                        <div class="card">
                            <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">CDN Settings</h3>
                            <div class="space-y-4">
                                <?php render_toggle('cdn_enabled', 'Enable CDN', 'Load static resources from a Content Delivery Network', $settings['cdn_enabled'] ?? false); ?>
                                <div class="setting-row">
                                    <div>
                                        <label for="cdn_url" class="setting-label">CDN URL</label>
                                        <p class="setting-description">The base URL provided by your CDN provider.</p>
                                    </div>
                                    <div class="setting-control">
                                        <input type="url" id="cdn_url" name="settings[cdn_url]" value="<?php echo escape($settings['cdn_url'] ?? ''); ?>" class="form-input" placeholder="https://cdn.yourdomain.com">
                                    </div>
                                </div>
                                <div class="setting-row">
                                    <div>
                                        <label class="setting-label">CDN Resources</label>
                                        <p class="setting-description">Select which types of resources should be served via CDN.</p>
                                    </div>
                                    <div class="setting-control grid grid-cols-2 gap-2">
                                        <?php render_checkbox('cdn_images', 'Images', $settings['cdn_images'] ?? true); ?>
                                        <?php render_checkbox('cdn_css', 'CSS', $settings['cdn_css'] ?? true); ?>
                                        <?php render_checkbox('cdn_js', 'JS', $settings['cdn_js'] ?? true); ?>
                                        <?php render_checkbox('cdn_fonts', 'Fonts', $settings['cdn_fonts'] ?? true); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php // --- Code Optimization Card --- ?>
                        <div class="card">
                            <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">Code Optimization</h3>
                            <div class="space-y-4">
                                <?php render_toggle('minify_html', 'Minify HTML', 'Reduce HTML file size by removing whitespace', $settings['minify_html'] ?? true); ?>
                                <?php render_toggle('minify_css', 'Minify CSS', 'Reduce CSS file size by removing comments/whitespace', $settings['minify_css'] ?? true); ?>
                                <?php render_toggle('minify_js', 'Minify JavaScript', 'Reduce JS file size (requires compatible JS)', $settings['minify_js'] ?? true); ?>
                                <?php render_toggle('concat_css', 'Concatenate CSS', 'Combine multiple CSS files into one (reduces requests)', $settings['concat_css'] ?? true); ?>
                                <?php render_toggle('concat_js', 'Concatenate JavaScript', 'Combine multiple JS files into one (reduces requests)', $settings['concat_js'] ?? true); ?>
                                <?php render_toggle('gzip_compression', 'Gzip Compression', 'Enable Gzip/Brotli for text resources via .htaccess or server config', $settings['gzip_compression'] ?? true); ?>
                            </div>
                        </div>

                        <?php // --- Caching Settings Card --- ?>
                        <div class="card">
                             <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">Caching Settings</h3>
                             <div class="space-y-4">
                                <?php render_toggle('browser_caching', 'Browser Caching', 'Set Cache-Control/Expires headers for static assets', $settings['browser_caching'] ?? true); ?>
                                <div class="setting-row">
                                    <div>
                                        <label for="html_cache_duration" class="setting-label">HTML Cache (days)</label>
                                        <p class="setting-description">Browser cache duration for HTML documents (use 0 for dynamic pages)</p>
                                    </div>
                                    <div class="setting-control">
                                        <input type="number" id="html_cache_duration" name="settings[html_cache_duration]" value="<?php echo escape((string)($settings['html_cache_duration'] ?? 0)); ?>" class="form-input" min="0">
                                    </div>
                                </div>
                                <div class="setting-row">
                                    <div>
                                        <label for="css_js_cache_duration" class="setting-label">CSS/JS Cache (days)</label>
                                        <p class="setting-description">Browser cache duration for CSS & JS files</p>
                                    </div>
                                    <div class="setting-control">
                                        <input type="number" id="css_js_cache_duration" name="settings[css_js_cache_duration]" value="<?php echo escape((string)($settings['css_js_cache_duration'] ?? 365)); ?>" class="form-input" min="0">
                                    </div>
                                </div>
                                <div class="setting-row">
                                    <div>
                                        <label for="image_cache_duration" class="setting-label">Image Cache (days)</label>
                                        <p class="setting-description">Browser cache duration for image files</p>
                                    </div>
                                    <div class="setting-control">
                                        <input type="number" id="image_cache_duration" name="settings[image_cache_duration]" value="<?php echo escape((string)($settings['image_cache_duration'] ?? 365)); ?>" class="form-input" min="0">
                                    </div>
                                </div>
                                <?php render_toggle('server_side_caching', 'Server-Side Caching', 'Enable page caching (e.g., file-based, Redis, Memcached - requires server setup)', $settings['server_side_caching'] ?? true); ?>
                                <?php render_toggle('database_caching', 'Database Caching', 'Enable object/query caching (requires compatible setup)', $settings['database_caching'] ?? true); ?>
                             </div>
                        </div>

                        <?php // --- Cloaking Settings Card --- ?>
                         <div class="card">
                            <h3 class="text-lg font-montserrat font-semibold border-b border-gray-200 pb-3 mb-4 text-gray-800">Link Cloaking</h3>
                            <div class="space-y-4">
                                <?php render_toggle('enable_article_cloaking', 'Enable Link Cloaking', 'Use cloaked links for external/affiliate URLs (e.g., /go/link-name)', $settings['enable_article_cloaking'] ?? false); ?>
                                <div class="setting-row">
                                    <div>
                                        <label for="cloak_url_prefix" class="setting-label">Cloak URL Prefix</label>
                                        <p class="setting-description">Prefix used for cloaked URLs (e.g., 'go', 'out', 'recommend')</p>
                                    </div>
                                    <div class="setting-control">
                                        <input type="text" id="cloak_url_prefix" name="settings[cloak_url_prefix]" value="<?php echo escape($settings['cloak_url_prefix'] ?? 'go'); ?>" class="form-input">
                                    </div>
                                </div>
                                <?php render_toggle('track_cloak_clicks', 'Track Cloak Clicks', 'Record clicks on cloaked links (requires tracking implementation)', $settings['track_cloak_clicks'] ?? true); ?>
                            </div>
                        </div>

                    </div> <?php // End Sticky container ?>
                </div> <?php // --- End Sidebar Column --- ?>

            </div> <?php // End flex container ?>

            <?php // Save button is now in the header ?>

        </form>
    </div> <?php // end p-6 ?>
</div> <?php // end flex-1 ?>

<?php
// Include the admin footer
include 'includes/footer.php';
?>


