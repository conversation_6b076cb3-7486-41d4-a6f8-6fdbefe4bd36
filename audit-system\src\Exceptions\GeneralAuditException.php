<?php

namespace AuditSystem\Exceptions;

/**
 * General audit exception for non-specific audit errors
 */
class GeneralAuditException extends AuditException
{
    /**
     * Create exception for general audit failure
     *
     * @param string $message Error message
     * @param int $code Error code
     * @param \Exception|null $previous Previous exception
     * @return static
     */
    public static function create(string $message, int $code = 0, \Exception $previous = null): self
    {
        return new self($message, $code, $previous);
    }

    /**
     * Create exception for audit already running
     *
     * @return static
     */
    public static function auditAlreadyRunning(): self
    {
        return new self('Audit is already running. Use resumeAudit() or wait for completion.');
    }

    /**
     * Create exception for no analyzers registered
     *
     * @return static
     */
    public static function noAnalyzersRegistered(): self
    {
        return new self('No analyzers registered. Cannot perform audit.');
    }

    /**
     * Create exception for invalid progress tracker
     *
     * @return static
     */
    public static function invalidProgressTracker(): self
    {
        return new self('Invalid progress tracker implementation.');
    }

    /**
     * Create exception for directory creation failure
     *
     * @param string $directory Directory path
     * @return static
     */
    public static function cannotCreateDirectory(string $directory): self
    {
        return new self("Cannot create progress directory: {$directory}");
    }

    /**
     * Create exception for directory not writable
     *
     * @param string $directory Directory path
     * @return static
     */
    public static function directoryNotWritable(string $directory): self
    {
        return new self("Progress directory is not writable: {$directory}");
    }

    /**
     * Create exception for no existing progress
     *
     * @return static
     */
    public static function noExistingProgress(): self
    {
        return new self('No existing audit progress found. Use startAudit() instead.');
    }
}