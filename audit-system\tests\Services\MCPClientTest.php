<?php

namespace AuditSystem\Tests\Services;

use PHPUnit\Framework\TestCase;
use AuditSystem\Services\MCPClient;
use AuditSystem\Exceptions\MCPConnectionException;
use AuditSystem\Config\AuditConfig;

/**
 * Test cases for MCP client functionality
 */
class MCPClientTest extends TestCase
{
    private MCPClient $mcpClient;
    private AuditConfig $config;

    protected function setUp(): void
    {
        $this->config = AuditConfig::getInstance();
        $this->mcpClient = new MCPClient($this->config);
    }

    public function testConnection(): void
    {
        $this->assertTrue($this->mcpClient->connect());
        $this->assertTrue($this->mcpClient->isConnected());
    }

    public function testDisconnection(): void
    {
        $this->mcpClient->connect();
        $this->assertTrue($this->mcpClient->isConnected());
        
        $this->mcpClient->disconnect();
        $this->assertFalse($this->mcpClient->isConnected());
    }

    public function testGetCapabilities(): void
    {
        $capabilities = $this->mcpClient->getCapabilities();
        
        $this->assertIsArray($capabilities);
        $this->assertArrayHasKey('name', $capabilities);
        $this->assertArrayHasKey('version', $capabilities);
        $this->assertArrayHasKey('methods', $capabilities);
        $this->assertArrayHasKey('technologies', $capabilities);
        
        $this->assertEquals('context7', $capabilities['name']);
        $this->assertContains('get_best_practices', $capabilities['methods']);
        $this->assertContains('validate_code', $capabilities['methods']);
    }

    public function testGetBestPractices(): void
    {
        $this->mcpClient->connect();
        
        $practices = $this->mcpClient->getBestPractices('php-security');
        
        $this->assertIsArray($practices);
        $this->assertArrayHasKey('technology', $practices);
        $this->assertArrayHasKey('practices', $practices);
        $this->assertEquals('php-security', $practices['technology']);
        $this->assertIsArray($practices['practices']);
    }

    public function testValidateCode(): void
    {
        $this->mcpClient->connect();
        
        $code = '<?php echo $_GET["test"]; ?>';
        $result = $this->mcpClient->validateCode($code, 'php');
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('language', $result);
        $this->assertArrayHasKey('issues', $result);
        $this->assertArrayHasKey('score', $result);
        $this->assertEquals('php', $result['language']);
        $this->assertIsArray($result['issues']);
    }

    public function testValidateSecureCode(): void
    {
        $this->mcpClient->connect();
        
        $code = '<?php echo htmlspecialchars($_GET["test"]); ?>';
        $result = $this->mcpClient->validateCode($code, 'php');
        
        $this->assertIsArray($result);
        $this->assertEquals('php', $result['language']);
        $this->assertEmpty($result['issues']);
        $this->assertEquals(100, $result['score']);
    }

    public function testValidateInsecureCode(): void
    {
        $this->mcpClient->connect();
        
        $code = '<?php mysql_query("SELECT * FROM users WHERE id = " . $_GET["id"]); ?>';
        $result = $this->mcpClient->validateCode($code, 'php');
        
        $this->assertIsArray($result);
        $this->assertEquals('php', $result['language']);
        $this->assertNotEmpty($result['issues']);
        $this->assertLessThan(100, $result['score']);
        
        // Check for SQL injection issue
        $hasSecurityIssue = false;
        foreach ($result['issues'] as $issue) {
            if ($issue['type'] === 'security' && strpos($issue['message'], 'SQL injection') !== false) {
                $hasSecurityIssue = true;
                break;
            }
        }
        $this->assertTrue($hasSecurityIssue);
    }

    public function testRequestWithoutConnection(): void
    {
        $this->expectException(MCPConnectionException::class);
        $this->mcpClient->request('test_method');
    }

    public function testUnknownMethod(): void
    {
        $this->mcpClient->connect();
        
        $this->expectException(MCPConnectionException::class);
        $this->expectExceptionMessage('Unknown method: unknown_method');
        
        $this->mcpClient->request('unknown_method');
    }

    public function testSecurityGuidelines(): void
    {
        $this->mcpClient->connect();
        
        $guidelines = $this->mcpClient->request('get_security_guidelines');
        
        $this->assertIsArray($guidelines);
        $this->assertArrayHasKey('owasp_top_10', $guidelines);
        $this->assertIsArray($guidelines['owasp_top_10']);
    }

    public function testPerformanceRecommendations(): void
    {
        $this->mcpClient->connect();
        
        $recommendations = $this->mcpClient->request('get_performance_recommendations');
        
        $this->assertIsArray($recommendations);
        $this->assertArrayHasKey('database', $recommendations);
        $this->assertArrayHasKey('frontend', $recommendations);
        $this->assertArrayHasKey('caching', $recommendations);
    }
}