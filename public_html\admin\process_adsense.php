<?php
/**
 * Admin AdSense Unit Processing
 *
 * Processes form submissions from adsense_form.php for creating, updating, and deleting AdSense units.
 */

require_once '../config.php';
require_once 'includes/auth_check.php';
require_once '../includes/functions.php';

// Initialize variables
$errors = [];
$redirect_url = 'advertising.php'; // Default redirect URL

// Check for action
if (!isset($_POST['action'])) {
    $_SESSION['error_message'] = 'Invalid request: No action specified.';
    header('Location: ' . $redirect_url);
    exit;
}

$action = $_POST['action'];

// --- DELETE ACTION ---
if ($action === 'delete') {
    $adsense_id = isset($_POST['adsense_id']) ? (int)$_POST['adsense_id'] : null;
    
    if (!$adsense_id) {
        $_SESSION['error_message'] = 'Invalid request: No AdSense ID specified for deletion.';
        header('Location: ' . $redirect_url);
        exit;
    }
    
    try {
        // Delete the record
        $stmt = $pdo->prepare("DELETE FROM adsense_units WHERE id = :id");
        $stmt->bindParam(':id', $adsense_id, PDO::PARAM_INT);
        $stmt->execute();
        
        // Check if deletion was successful
        if ($stmt->rowCount() > 0) {
            $_SESSION['success_message'] = "AdSense unit successfully deleted.";
        } else {
            $_SESSION['error_message'] = "No AdSense unit found with that ID or you don't have permission to delete it.";
        }
    } catch (PDOException $e) {
        $_SESSION['error_message'] = "Database error: " . $e->getMessage();
    }
    
    header('Location: ' . $redirect_url);
    exit;
}

// --- CREATE/UPDATE ACTIONS ---
// Collect and sanitize form data
$adsense_id = isset($_POST['adsense_id']) ? (int)$_POST['adsense_id'] : null;

// Set redirect URL for edit mode
if ($adsense_id) {
    $redirect_url = "adsense_form.php?id={$adsense_id}";
}

// Collect form fields
$name = trim($_POST['name'] ?? '');
$ad_code = trim($_POST['ad_code'] ?? '');

// MODIFIED: Handle multiple placements
$placements = isset($_POST['placements']) && is_array($_POST['placements']) ? $_POST['placements'] : ['sidebar_bottom'];

// Ensure at least one placement is selected
if (empty($placements)) {
    $errors[] = 'At least one placement location must be selected';
    $placements = ['sidebar_bottom']; // Default if none provided
}

// MODIFIED: Convert placements array to JSON for database storage
$placement_json = json_encode($placements);

$status = $_POST['status'] ?? 'active';
$device_visibility = $_POST['device_visibility'] ?? 'all';
$position_index = isset($_POST['position_index']) ? (int)$_POST['position_index'] : 1;
$custom_css = trim($_POST['custom_css'] ?? '');
$fixed_width = isset($_POST['fixed_width']) ? (int)$_POST['fixed_width'] : 300;
$fixed_height = isset($_POST['fixed_height']) ? (int)$_POST['fixed_height'] : 250;
$placement_selector = trim($_POST['placement_selector'] ?? '');
$frequency = isset($_POST['frequency']) ? (int)$_POST['frequency'] : 1;
$skip_paragraphs = isset($_POST['skip_paragraphs']) ? (int)$_POST['skip_paragraphs'] : 2;
$max_ads_per_page = isset($_POST['max_ads_per_page']) ? (int)$_POST['max_ads_per_page'] : 3;

// Visibility settings
$show_on_homepage = isset($_POST['show_on_homepage']) ? (int)$_POST['show_on_homepage'] : 1;
$show_on_categories = isset($_POST['show_on_categories']) ? (int)$_POST['show_on_categories'] : 1;
$show_on_articles = isset($_POST['show_on_articles']) ? (int)$_POST['show_on_articles'] : 1;
$show_on_tags = isset($_POST['show_on_tags']) ? (int)$_POST['show_on_tags'] : 1;

// Handle start date and end date
$start_date = !empty($_POST['start_date']) ? trim($_POST['start_date']) : null;
$end_date = !empty($_POST['end_date']) ? trim($_POST['end_date']) : null;

// Format dates for MySQL
if ($start_date) {
    $start_date = date('Y-m-d H:i:s', strtotime($start_date));
}
if ($end_date) {
    $end_date = date('Y-m-d H:i:s', strtotime($end_date));
}

// Custom targeting and lazy load
$custom_targeting = trim($_POST['custom_targeting'] ?? '');
$lazy_load = isset($_POST['lazy_load']) ? (int)$_POST['lazy_load'] : 1;

// --- Validation ---
if (empty($name)) {
    $errors[] = 'Name is required';
}
if (empty($ad_code)) {
    $errors[] = 'AdSense code is required';
}
if (!in_array($status, ['active', 'inactive', 'scheduled'])) {
    $errors[] = 'Invalid status value';
}

// MODIFIED: Validate each selected placement
$valid_placements = [
    'sidebar_popular', 'sidebar_middle', 'sidebar_bottom',
    'article_beginning', 'in_content', 'after_content', 'article_bottom_banner',
    'recommended', 'header', 'footer', 'custom'
];

foreach ($placements as $placement) {
    if (!in_array($placement, $valid_placements)) {
        $errors[] = 'Invalid placement location: ' . htmlspecialchars($placement);
    }
}

if (!in_array($device_visibility, ['all', 'desktop', 'mobile', 'tablet'])) {
    $errors[] = 'Invalid device visibility';
}
if (in_array('custom', $placements) && empty($placement_selector)) {
    $errors[] = 'Custom CSS selector is required for custom placement';
}

// If scheduled status, ensure start date is provided
if ($status === 'scheduled' && empty($start_date)) {
    $errors[] = 'Start date is required for scheduled AdSense units';
}

// If errors, redirect back with error messages
if (!empty($errors)) {
    $_SESSION['form_errors'] = $errors;
    $_SESSION['form_data'] = $_POST; // Store form data for repopulation
    header('Location: ' . $redirect_url);
    exit;
}

// --- Database Operations ---
try {
    // Begin transaction
    $pdo->beginTransaction();
    
    if ($action === 'create') {
        // --- Create new AdSense unit ---
        $sql = "INSERT INTO adsense_units (
                    name, ad_code, placement, status, 
                    device_visibility, position_index, custom_css,
                    fixed_width, fixed_height, placement_selector,
                    frequency, skip_paragraphs, max_ads_per_page,
                    show_on_homepage, show_on_categories, show_on_articles, show_on_tags,
                    start_date, end_date, custom_targeting, lazy_load,
                    created_at, updated_at
                ) VALUES (
                    :name, :ad_code, :placement, :status,
                    :device_visibility, :position_index, :custom_css,
                    :fixed_width, :fixed_height, :placement_selector,
                    :frequency, :skip_paragraphs, :max_ads_per_page,
                    :show_on_homepage, :show_on_categories, :show_on_articles, :show_on_tags,
                    :start_date, :end_date, :custom_targeting, :lazy_load,
                    NOW(), NOW()
                )";
                
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':ad_code', $ad_code);
        // MODIFIED: Store JSON array in placement column
        $stmt->bindParam(':placement', $placement_json);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':device_visibility', $device_visibility);
        $stmt->bindParam(':position_index', $position_index, PDO::PARAM_INT);
        $stmt->bindParam(':custom_css', $custom_css);
        $stmt->bindParam(':fixed_width', $fixed_width, PDO::PARAM_INT);
        $stmt->bindParam(':fixed_height', $fixed_height, PDO::PARAM_INT);
        $stmt->bindParam(':placement_selector', $placement_selector);
        $stmt->bindParam(':frequency', $frequency, PDO::PARAM_INT);
        $stmt->bindParam(':skip_paragraphs', $skip_paragraphs, PDO::PARAM_INT);
        $stmt->bindParam(':max_ads_per_page', $max_ads_per_page, PDO::PARAM_INT);
        $stmt->bindParam(':show_on_homepage', $show_on_homepage, PDO::PARAM_INT);
        $stmt->bindParam(':show_on_categories', $show_on_categories, PDO::PARAM_INT);
        $stmt->bindParam(':show_on_articles', $show_on_articles, PDO::PARAM_INT);
        $stmt->bindParam(':show_on_tags', $show_on_tags, PDO::PARAM_INT);
        $stmt->bindParam(':start_date', $start_date, $start_date === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
        $stmt->bindParam(':end_date', $end_date, $end_date === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
        $stmt->bindParam(':custom_targeting', $custom_targeting);
        $stmt->bindParam(':lazy_load', $lazy_load, PDO::PARAM_INT);
        
        $stmt->execute();
        $new_adsense_id = $pdo->lastInsertId();
        
        $_SESSION['success_message'] = "New AdSense unit created successfully.";
        $redirect_url = "adsense_form.php?id={$new_adsense_id}&success=1";
        
    } elseif ($action === 'update' && $adsense_id) {
        // --- Update existing AdSense unit ---
        $sql = "UPDATE adsense_units SET
                    name = :name,
                    ad_code = :ad_code,
                    placement = :placement,
                    status = :status,
                    device_visibility = :device_visibility,
                    position_index = :position_index,
                    custom_css = :custom_css,
                    fixed_width = :fixed_width,
                    fixed_height = :fixed_height,
                    placement_selector = :placement_selector,
                    frequency = :frequency,
                    skip_paragraphs = :skip_paragraphs,
                    max_ads_per_page = :max_ads_per_page,
                    show_on_homepage = :show_on_homepage,
                    show_on_categories = :show_on_categories,
                    show_on_articles = :show_on_articles,
                    show_on_tags = :show_on_tags,
                    start_date = :start_date,
                    end_date = :end_date,
                    custom_targeting = :custom_targeting,
                    lazy_load = :lazy_load,
                    updated_at = NOW()
                WHERE id = :id";
                
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':ad_code', $ad_code);
        // MODIFIED: Store JSON array in placement column
        $stmt->bindParam(':placement', $placement_json);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':device_visibility', $device_visibility);
        $stmt->bindParam(':position_index', $position_index, PDO::PARAM_INT);
        $stmt->bindParam(':custom_css', $custom_css);
        $stmt->bindParam(':fixed_width', $fixed_width, PDO::PARAM_INT);
        $stmt->bindParam(':fixed_height', $fixed_height, PDO::PARAM_INT);
        $stmt->bindParam(':placement_selector', $placement_selector);
        $stmt->bindParam(':frequency', $frequency, PDO::PARAM_INT);
        $stmt->bindParam(':skip_paragraphs', $skip_paragraphs, PDO::PARAM_INT);
        $stmt->bindParam(':max_ads_per_page', $max_ads_per_page, PDO::PARAM_INT);
        $stmt->bindParam(':show_on_homepage', $show_on_homepage, PDO::PARAM_INT);
        $stmt->bindParam(':show_on_categories', $show_on_categories, PDO::PARAM_INT);
        $stmt->bindParam(':show_on_articles', $show_on_articles, PDO::PARAM_INT);
        $stmt->bindParam(':show_on_tags', $show_on_tags, PDO::PARAM_INT);
        $stmt->bindParam(':start_date', $start_date, $start_date === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
        $stmt->bindParam(':end_date', $end_date, $end_date === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
        $stmt->bindParam(':custom_targeting', $custom_targeting);
        $stmt->bindParam(':lazy_load', $lazy_load, PDO::PARAM_INT);
        $stmt->bindParam(':id', $adsense_id, PDO::PARAM_INT);
        
        $stmt->execute();
        
        $_SESSION['success_message'] = "AdSense unit updated successfully.";
        $redirect_url = "adsense_form.php?id={$adsense_id}&success=1";
        
    } else {
        // Invalid action
        throw new Exception("Invalid action specified");
    }
    
    // Commit transaction
    $pdo->commit();
    
} catch (Exception $e) {
    // Roll back transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    $_SESSION['error_message'] = "Error: " . $e->getMessage();
    error_log("Error in process_adsense.php: " . $e->getMessage());
}

// Redirect to appropriate page
header('Location: ' . $redirect_url);
exit;