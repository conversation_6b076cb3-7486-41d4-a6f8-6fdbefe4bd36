<?php

namespace AuditSystem\Tests\Services;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use AuditSystem\Services\BestPracticesChecker;
use AuditSystem\Services\MCPClient;
use AuditSystem\Exceptions\MCPConnectionException;
use AuditSystem\Config\AuditConfig;
use AuditSystem\Models\Finding;

/**
 * Test cases for best practices checker functionality
 */
class BestPracticesCheckerTest extends TestCase
{
    private BestPracticesChecker $checker;
    private MockObject $mcpClient;
    private AuditConfig $config;

    protected function setUp(): void
    {
        $this->config = AuditConfig::getInstance();
        $this->mcpClient = $this->createMock(MCPClient::class);
        $this->checker = new BestPracticesChecker($this->mcpClient, $this->config);
    }

    protected function tearDown(): void
    {
        // Clean up cache file after tests
        $cacheFile = $this->config->get('audit.target_directory', '.') . '/audit-system/data/mcp_cache.json';
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
        }
    }

    public function testCheckCodeWithMCPSuccess(): void
    {
        $code = '<?php echo $_GET["test"]; ?>';
        $expectedResult = [
            'language' => 'php',
            'issues' => [
                [
                    'type' => 'security',
                    'severity' => 'high',
                    'message' => 'Unvalidated user input detected'
                ]
            ],
            'score' => 80
        ];

        $this->mcpClient->expects($this->once())
            ->method('isConnected')
            ->willReturn(true);

        $this->mcpClient->expects($this->once())
            ->method('validateCode')
            ->with($code, 'php', '')
            ->willReturn($expectedResult);

        $result = $this->checker->checkCode($code, 'php');

        $this->assertEquals($expectedResult, $result);
    }

    public function testCheckCodeWithMCPFailureFallback(): void
    {
        $code = '<?php echo $_GET["test"]; ?>';

        $this->mcpClient->expects($this->once())
            ->method('isConnected')
            ->willReturn(false);

        $this->mcpClient->expects($this->once())
            ->method('connect')
            ->willThrowException(new MCPConnectionException('Connection failed'));

        $result = $this->checker->checkCode($code, 'php');

        $this->assertIsArray($result);
        $this->assertEquals('php', $result['language']);
        $this->assertEquals('fallback', $result['source']);
        $this->assertNotEmpty($result['issues']);
    }

    public function testCheckCodeCaching(): void
    {
        $code = '<?php echo "Hello World"; ?>';
        $expectedResult = [
            'language' => 'php',
            'issues' => [],
            'score' => 100
        ];

        $this->mcpClient->expects($this->once())
            ->method('isConnected')
            ->willReturn(true);

        $this->mcpClient->expects($this->once())
            ->method('validateCode')
            ->willReturn($expectedResult);

        // First call should hit MCP
        $result1 = $this->checker->checkCode($code, 'php');
        $this->assertEquals($expectedResult, $result1);

        // Second call should use cache (no additional MCP call)
        $result2 = $this->checker->checkCode($code, 'php');
        $this->assertEquals($expectedResult, $result2);
    }

    public function testGetBestPractices(): void
    {
        $technology = 'php-security';
        $expectedResult = [
            'technology' => 'php-security',
            'practices' => [
                'use_prepared_statements' => 'Always use PDO prepared statements',
                'validate_input' => 'Validate and sanitize all user input'
            ]
        ];

        $this->mcpClient->expects($this->once())
            ->method('isConnected')
            ->willReturn(true);

        $this->mcpClient->expects($this->once())
            ->method('getBestPractices')
            ->with($technology, '')
            ->willReturn($expectedResult);

        $result = $this->checker->getBestPractices($technology);

        $this->assertEquals($expectedResult, $result);
    }

    public function testGetBestPracticesWithFallback(): void
    {
        $technology = 'php-security';

        $this->mcpClient->expects($this->once())
            ->method('isConnected')
            ->willReturn(false);

        $this->mcpClient->expects($this->once())
            ->method('connect')
            ->willThrowException(new MCPConnectionException('Connection failed'));

        $result = $this->checker->getBestPractices($technology);

        $this->assertIsArray($result);
        $this->assertEquals($technology, $result['technology']);
        $this->assertEquals('fallback', $result['source']);
        $this->assertIsArray($result['practices']);
    }

    public function testValidateFindings(): void
    {
        $finding = new Finding(
            'test.php',
            10,
            Finding::TYPE_SECURITY,
            Finding::SEVERITY_HIGH,
            Finding::PRIORITY_AREA,
            'SQL injection vulnerability',
            'Use prepared statements',
            'mysql_query("SELECT * FROM users WHERE id = " . $_GET["id"]);'
        );

        $bestPractices = [
            'technology' => 'php-security',
            'practices' => [
                'use_prepared_statements' => 'Always use PDO prepared statements for database queries',
                'validate_input' => 'Validate and sanitize all user input'
            ]
        ];

        $this->mcpClient->expects($this->once())
            ->method('isConnected')
            ->willReturn(true);

        $this->mcpClient->expects($this->once())
            ->method('getBestPractices')
            ->with('php-security', 'security')
            ->willReturn($bestPractices);

        $enhancedFindings = $this->checker->validateFindings([$finding]);

        $this->assertCount(1, $enhancedFindings);
        $this->assertInstanceOf(Finding::class, $enhancedFindings[0]);
        $this->assertNotEmpty($enhancedFindings[0]->getReferences());
    }

    public function testValidateFindingsWithNonFindingObjects(): void
    {
        $invalidFindings = ['not a finding', 123, null];

        $result = $this->checker->validateFindings($invalidFindings);

        $this->assertEmpty($result);
    }

    public function testClearCache(): void
    {
        // First, create some cache data
        $code = '<?php echo "test"; ?>';
        $this->mcpClient->expects($this->once())
            ->method('isConnected')
            ->willReturn(true);
        $this->mcpClient->expects($this->once())
            ->method('validateCode')
            ->willReturn(['language' => 'php', 'issues' => [], 'score' => 100]);

        $this->checker->checkCode($code, 'php');

        // Verify cache has data
        $stats = $this->checker->getCacheStats();
        $this->assertGreaterThan(0, $stats['entries']);

        // Clear cache
        $this->checker->clearCache();

        // Verify cache is empty
        $stats = $this->checker->getCacheStats();
        $this->assertEquals(0, $stats['entries']);
    }

    public function testGetCacheStats(): void
    {
        $stats = $this->checker->getCacheStats();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('entries', $stats);
        $this->assertArrayHasKey('size', $stats);
        $this->assertArrayHasKey('hit_rate', $stats);
        $this->assertIsInt($stats['entries']);
        $this->assertIsNumeric($stats['size']);
        $this->assertIsFloat($stats['hit_rate']);
    }

    public function testFallbackValidationPHP(): void
    {
        // Test with insecure PHP code
        $insecureCode = '<?php mysql_query("SELECT * FROM users WHERE id = " . $_GET["id"]); ?>';
        
        $this->mcpClient->expects($this->once())
            ->method('isConnected')
            ->willReturn(false);
        $this->mcpClient->expects($this->once())
            ->method('connect')
            ->willThrowException(new MCPConnectionException('Connection failed'));

        $result = $this->checker->checkCode($insecureCode, 'php');

        $this->assertEquals('php', $result['language']);
        $this->assertEquals('fallback', $result['source']);
        $this->assertNotEmpty($result['issues']);
        $this->assertLessThan(100, $result['score']);

        // Check for SQL injection detection
        $hasSQLInjection = false;
        foreach ($result['issues'] as $issue) {
            if ($issue['type'] === 'security' && strpos($issue['message'], 'SQL injection') !== false) {
                $hasSQLInjection = true;
                break;
            }
        }
        $this->assertTrue($hasSQLInjection);
    }

    public function testFallbackValidationJavaScript(): void
    {
        // Test with insecure JavaScript code
        $insecureCode = 'eval(userInput);';
        
        $this->mcpClient->expects($this->once())
            ->method('isConnected')
            ->willReturn(false);
        $this->mcpClient->expects($this->once())
            ->method('connect')
            ->willThrowException(new MCPConnectionException('Connection failed'));

        $result = $this->checker->checkCode($insecureCode, 'javascript');

        $this->assertEquals('javascript', $result['language']);
        $this->assertEquals('fallback', $result['source']);
        $this->assertNotEmpty($result['issues']);
        $this->assertLessThan(100, $result['score']);

        // Check for eval detection
        $hasEvalIssue = false;
        foreach ($result['issues'] as $issue) {
            if (strpos($issue['message'], 'eval()') !== false) {
                $hasEvalIssue = true;
                break;
            }
        }
        $this->assertTrue($hasEvalIssue);
    }

    public function testFallbackValidationCSS(): void
    {
        // Test with deeply nested CSS
        $deepCSS = '.a { .b { .c { .d { color: red; } } } }';
        
        $this->mcpClient->expects($this->once())
            ->method('isConnected')
            ->willReturn(false);
        $this->mcpClient->expects($this->once())
            ->method('connect')
            ->willThrowException(new MCPConnectionException('Connection failed'));

        $result = $this->checker->checkCode($deepCSS, 'css');

        $this->assertEquals('css', $result['language']);
        $this->assertEquals('fallback', $result['source']);
        $this->assertNotEmpty($result['issues']);
        $this->assertLessThan(100, $result['score']);

        // Check for nesting detection
        $hasNestingIssue = false;
        foreach ($result['issues'] as $issue) {
            if (strpos($issue['message'], 'nesting') !== false) {
                $hasNestingIssue = true;
                break;
            }
        }
        $this->assertTrue($hasNestingIssue);
    }
}