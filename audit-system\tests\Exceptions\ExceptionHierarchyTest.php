<?php

use PHPUnit\Framework\TestCase;
use AuditSystem\Exceptions\AuditException;
use AuditSystem\Exceptions\FileAccessException;
use AuditSystem\Exceptions\AnalysisException;
use AuditSystem\Exceptions\ConfigurationException;
use AuditSystem\Exceptions\MCPConnectionException;
use AuditSystem\Exceptions\GeneralAuditException;
use AuditSystem\Exceptions\ProgressException;
use AuditSystem\Exceptions\ReportException;
use AuditSystem\Exceptions\SecurityException;

class ExceptionHierarchyTest extends TestCase
{
    public function testAllExceptionsExtendAuditException()
    {
        $exceptionClasses = [
            FileAccessException::class,
            AnalysisException::class,
            ConfigurationException::class,
            MCPConnectionException::class,
            GeneralAuditException::class,
            ProgressException::class,
            ReportException::class,
            SecurityException::class
        ];

        foreach ($exceptionClasses as $exceptionClass) {
            $this->assertTrue(
                is_subclass_of($exceptionClass, AuditException::class),
                "{$exceptionClass} should extend AuditException"
            );
        }
    }

    public function testAuditExceptionProvidesContext()
    {
        $exception = new GeneralAuditException('Test message', 123);
        $context = $exception->getContext();

        $this->assertIsArray($context);
        $this->assertArrayHasKey('exception_class', $context);
        $this->assertArrayHasKey('message', $context);
        $this->assertArrayHasKey('code', $context);
        $this->assertArrayHasKey('file', $context);
        $this->assertArrayHasKey('line', $context);
        $this->assertArrayHasKey('trace', $context);

        $this->assertEquals(GeneralAuditException::class, $context['exception_class']);
        $this->assertEquals('Test message', $context['message']);
        $this->assertEquals(123, $context['code']);
    }

    public function testFileAccessExceptionFactoryMethods()
    {
        $fileNotFound = FileAccessException::fileNotFound('/path/to/file.php');
        $this->assertInstanceOf(FileAccessException::class, $fileNotFound);
        $this->assertStringContainsString('/path/to/file.php', $fileNotFound->getMessage());

        $fileNotReadable = FileAccessException::fileNotReadable('/path/to/file.php');
        $this->assertInstanceOf(FileAccessException::class, $fileNotReadable);
        $this->assertStringContainsString('not readable', $fileNotReadable->getMessage());

        $dirNotAccessible = FileAccessException::directoryNotAccessible('/path/to/dir');
        $this->assertInstanceOf(FileAccessException::class, $dirNotAccessible);
        $this->assertStringContainsString('not accessible', $dirNotAccessible->getMessage());

        $fileTooLarge = FileAccessException::fileTooLarge('/path/to/file.php', 2000000, 1000000);
        $this->assertInstanceOf(FileAccessException::class, $fileTooLarge);
        $this->assertStringContainsString('too large', $fileTooLarge->getMessage());
        $this->assertStringContainsString('2000000', $fileTooLarge->getMessage());
        $this->assertStringContainsString('1000000', $fileTooLarge->getMessage());
    }

    public function testAnalysisExceptionFactoryMethods()
    {
        $analyzerFailed = AnalysisException::analyzerFailed('TestAnalyzer', '/file.php', 'parsing error');
        $this->assertInstanceOf(AnalysisException::class, $analyzerFailed);
        $this->assertStringContainsString('TestAnalyzer', $analyzerFailed->getMessage());
        $this->assertStringContainsString('/file.php', $analyzerFailed->getMessage());
        $this->assertStringContainsString('parsing error', $analyzerFailed->getMessage());

        $invalidContent = AnalysisException::invalidFileContent('/file.php', 'PHP');
        $this->assertInstanceOf(AnalysisException::class, $invalidContent);
        $this->assertStringContainsString('Invalid file content', $invalidContent->getMessage());

        $parsingFailed = AnalysisException::parsingFailed('/file.php', 'AST');
        $this->assertInstanceOf(AnalysisException::class, $parsingFailed);
        $this->assertStringContainsString('Failed to parse', $parsingFailed->getMessage());

        $timeout = AnalysisException::analysisTimeout('/file.php', 30);
        $this->assertInstanceOf(AnalysisException::class, $timeout);
        $this->assertStringContainsString('timed out', $timeout->getMessage());
        $this->assertStringContainsString('30 seconds', $timeout->getMessage());
    }

    public function testConfigurationExceptionFactoryMethods()
    {
        $missingConfig = ConfigurationException::missingConfiguration('database.host');
        $this->assertInstanceOf(ConfigurationException::class, $missingConfig);
        $this->assertStringContainsString('Missing required configuration', $missingConfig->getMessage());
        $this->assertStringContainsString('database.host', $missingConfig->getMessage());

        $invalidConfig = ConfigurationException::invalidConfiguration('timeout', 'invalid', 'integer');
        $this->assertInstanceOf(ConfigurationException::class, $invalidConfig);
        $this->assertStringContainsString('Invalid configuration', $invalidConfig->getMessage());
        $this->assertStringContainsString('timeout', $invalidConfig->getMessage());

        $configFileNotFound = ConfigurationException::configFileNotFound('/path/to/config.json');
        $this->assertInstanceOf(ConfigurationException::class, $configFileNotFound);
        $this->assertStringContainsString('Configuration file not found', $configFileNotFound->getMessage());

        $invalidFormat = ConfigurationException::invalidConfigFormat('/config.json', 'JSON');
        $this->assertInstanceOf(ConfigurationException::class, $invalidFormat);
        $this->assertStringContainsString('Invalid configuration file format', $invalidFormat->getMessage());
    }

    public function testMCPConnectionExceptionFactoryMethods()
    {
        $timeout = MCPConnectionException::timeout(30);
        $this->assertInstanceOf(MCPConnectionException::class, $timeout);
        $this->assertStringContainsString('timed out', $timeout->getMessage());
        $this->assertStringContainsString('30 seconds', $timeout->getMessage());

        $serverUnavailable = MCPConnectionException::serverUnavailable('http://localhost:3000');
        $this->assertInstanceOf(MCPConnectionException::class, $serverUnavailable);
        $this->assertStringContainsString('unavailable', $serverUnavailable->getMessage());
        $this->assertStringContainsString('http://localhost:3000', $serverUnavailable->getMessage());

        $invalidResponse = MCPConnectionException::invalidResponse('{"error": "invalid"}');
        $this->assertInstanceOf(MCPConnectionException::class, $invalidResponse);
        $this->assertStringContainsString('Invalid response', $invalidResponse->getMessage());

        $authFailed = MCPConnectionException::authenticationFailed();
        $this->assertInstanceOf(MCPConnectionException::class, $authFailed);
        $this->assertStringContainsString('Authentication', $authFailed->getMessage());
    }

    public function testProgressExceptionFactoryMethods()
    {
        $corrupted = ProgressException::progressFileCorrupted('/path/to/progress.json');
        $this->assertInstanceOf(ProgressException::class, $corrupted);
        $this->assertStringContainsString('corrupted', $corrupted->getMessage());

        $backupFailed = ProgressException::backupFailed('/backup/path', 'disk full');
        $this->assertInstanceOf(ProgressException::class, $backupFailed);
        $this->assertStringContainsString('backup', $backupFailed->getMessage());
        $this->assertStringContainsString('disk full', $backupFailed->getMessage());

        $restoreFailed = ProgressException::restoreFailed('/backup/path');
        $this->assertInstanceOf(ProgressException::class, $restoreFailed);
        $this->assertStringContainsString('restore', $restoreFailed->getMessage());

        $concurrentAccess = ProgressException::concurrentAccess();
        $this->assertInstanceOf(ProgressException::class, $concurrentAccess);
        $this->assertStringContainsString('Concurrent access', $concurrentAccess->getMessage());

        $invalidState = ProgressException::invalidState('corrupted phase data');
        $this->assertInstanceOf(ProgressException::class, $invalidState);
        $this->assertStringContainsString('Invalid progress state', $invalidState->getMessage());
    }

    public function testReportExceptionFactoryMethods()
    {
        $generationFailed = ReportException::generationFailed('HTML', 'template error');
        $this->assertInstanceOf(ReportException::class, $generationFailed);
        $this->assertStringContainsString('Failed to generate', $generationFailed->getMessage());
        $this->assertStringContainsString('HTML', $generationFailed->getMessage());

        $templateNotFound = ReportException::templateNotFound('/templates/report.html');
        $this->assertInstanceOf(ReportException::class, $templateNotFound);
        $this->assertStringContainsString('template not found', $templateNotFound->getMessage());

        $invalidFormat = ReportException::invalidFormat('XML', ['json', 'html', 'text']);
        $this->assertInstanceOf(ReportException::class, $invalidFormat);
        $this->assertStringContainsString('Invalid report format', $invalidFormat->getMessage());
        $this->assertStringContainsString('XML', $invalidFormat->getMessage());

        $exportFailed = ReportException::exportFailed('/reports/audit.pdf', 'permission denied');
        $this->assertInstanceOf(ReportException::class, $exportFailed);
        $this->assertStringContainsString('export', $exportFailed->getMessage());

        $insufficientData = ReportException::insufficientData('no findings available');
        $this->assertInstanceOf(ReportException::class, $insufficientData);
        $this->assertStringContainsString('Insufficient data', $insufficientData->getMessage());
    }

    public function testSecurityExceptionFactoryMethods()
    {
        $unauthorized = SecurityException::unauthorizedAccess('/admin/config', 'read');
        $this->assertInstanceOf(SecurityException::class, $unauthorized);
        $this->assertStringContainsString('Unauthorized access', $unauthorized->getMessage());

        $pathTraversal = SecurityException::pathTraversal('../../../etc/passwd');
        $this->assertInstanceOf(SecurityException::class, $pathTraversal);
        $this->assertStringContainsString('Path traversal', $pathTraversal->getMessage());

        $restrictedType = SecurityException::restrictedFileType('/file.exe', 'executable');
        $this->assertInstanceOf(SecurityException::class, $restrictedType);
        $this->assertStringContainsString('restricted', $restrictedType->getMessage());

        $sizeLimit = SecurityException::sizeLimitExceeded('upload', 2000000, 1000000);
        $this->assertInstanceOf(SecurityException::class, $sizeLimit);
        $this->assertStringContainsString('Size limit exceeded', $sizeLimit->getMessage());

        $malicious = SecurityException::maliciousContent('/file.php', 'SQL injection');
        $this->assertInstanceOf(SecurityException::class, $malicious);
        $this->assertStringContainsString('Malicious content', $malicious->getMessage());
    }

    public function testGeneralAuditExceptionFactoryMethods()
    {
        $general = GeneralAuditException::create('Custom error message', 500);
        $this->assertInstanceOf(GeneralAuditException::class, $general);
        $this->assertEquals('Custom error message', $general->getMessage());
        $this->assertEquals(500, $general->getCode());

        $alreadyRunning = GeneralAuditException::auditAlreadyRunning();
        $this->assertInstanceOf(GeneralAuditException::class, $alreadyRunning);
        $this->assertStringContainsString('already running', $alreadyRunning->getMessage());

        $noAnalyzers = GeneralAuditException::noAnalyzersRegistered();
        $this->assertInstanceOf(GeneralAuditException::class, $noAnalyzers);
        $this->assertStringContainsString('No analyzers', $noAnalyzers->getMessage());

        $invalidTracker = GeneralAuditException::invalidProgressTracker();
        $this->assertInstanceOf(GeneralAuditException::class, $invalidTracker);
        $this->assertStringContainsString('Invalid progress tracker', $invalidTracker->getMessage());

        $cannotCreate = GeneralAuditException::cannotCreateDirectory('/path/to/dir');
        $this->assertInstanceOf(GeneralAuditException::class, $cannotCreate);
        $this->assertStringContainsString('Cannot create', $cannotCreate->getMessage());

        $notWritable = GeneralAuditException::directoryNotWritable('/path/to/dir');
        $this->assertInstanceOf(GeneralAuditException::class, $notWritable);
        $this->assertStringContainsString('not writable', $notWritable->getMessage());

        $noProgress = GeneralAuditException::noExistingProgress();
        $this->assertInstanceOf(GeneralAuditException::class, $noProgress);
        $this->assertStringContainsString('No existing', $noProgress->getMessage());
    }

    public function testExceptionChaining()
    {
        $originalException = new \Exception('Original error');
        $auditException = new GeneralAuditException('Audit error', 0, $originalException);

        $this->assertSame($originalException, $auditException->getPrevious());
        $this->assertEquals('Original error', $auditException->getPrevious()->getMessage());
    }
}