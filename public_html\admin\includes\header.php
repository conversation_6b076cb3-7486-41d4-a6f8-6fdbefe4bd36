<?php
// This header assumes auth_check.php has already been included on the page calling it.
// It also assumes config.php is available

$admin_page_title = $admin_page_title ?? 'Admin Panel'; // Default title if not set
$current_page = basename($_SERVER['PHP_SELF']);

/**
 * Checks if a navigation item should be marked as active.
 *
 * @param string $page_name The PHP filename of the navigation item (e.g., 'index.php').
 * @param string $current_page The filename of the currently viewed page.
 * @return bool True if the item is active, false otherwise.
 */
function is_nav_active(string $page_name, string $current_page): bool {
    if ($page_name === $current_page) {
        return true;
    }
    // Special case: Mark 'Articles' nav item as active when on the article form page
    if ($page_name === 'articles.php' && $current_page === 'article_form.php') {
        return true;
    }
    // Special case: Mark 'Advertising' nav item as active when on ad management pages
    if ($page_name === 'advertising.php' && ($current_page === 'ad_form.php' || $current_page === 'adsense_form.php')) {
        return true;
    }
    // Special case for analytics pages
    if (($page_name === 'analytics.php' || $page_name === 'ad_analytics.php') && ($current_page === 'analytics.php' || $current_page === 'ad_analytics.php')) {
         return $page_name === $current_page;
    }
    // *** ADDED: Special case for Categories & Tags management page ***
    if ($page_name === 'manage_categories_tags.php' && $current_page === 'manage_categories_tags.php') {
        return true;
    }
    // Special case: Highlight Categories & Tags when processing actions
    if ($page_name === 'manage_categories_tags.php' && $current_page === 'process_category_tag.php') {
        return true;
    }
    // Special case for Internal Links management
    if ($page_name === 'internal_links.php' && $current_page === 'internal_links.php') {
        return true;
    }
    return false;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="theme-color" content="#ff6481">
    <meta name="robots" content="noindex, nofollow">
    <title><?php echo escape($admin_page_title); ?> - <?php echo defined('SITE_NAME') ? SITE_NAME : 'CMS'; ?> CMS</title>

    <link rel="preconnect" href="https://cdn.tailwindcss.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Tailwind CSS Configuration
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        background: '#FFF4F5', // Light pinkish background
                        primary: '#ff6481',    // Main pink color
                        secondary: '#ffd6dd',  // Lighter pink for active/hover states
                        dark: '#333333',       // Main dark text color
                        light: '#ffffff',      // White
                        border: '#feeaec',     // Very light pink border color
                        success: '#10b981',    // Green for success messages/badges
                        warning: '#f59e0b',    // Yellow for warnings/draft status
                        danger: '#ef4444',     // Red for errors/delete actions
                        info: '#3b82f6'        // Blue for informational messages/scheduled status
                    },
                    fontFamily: {
                        sans: ['Open Sans', 'sans-serif'],      // Default sans-serif font
                        montserrat: ['Montserrat', 'sans-serif'], // Heading/accent font
                    },
                    borderRadius: {
                        'xl': '1rem',          // Extra large border radius
                        '2xl': '1.5rem',         // 2x large border radius
                    },
                }
            }
        };
    </script>

    <style type="text/tailwindcss">
        @layer components {
            /* Buttons */
            .btn { @apply bg-primary text-white px-3 py-2 rounded text-sm font-medium transition-all duration-300 hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 font-montserrat disabled:opacity-50 disabled:cursor-not-allowed; }
            .btn-secondary { @apply bg-gray-200 text-gray-700 px-3 py-2 rounded text-sm font-medium transition-all duration-300 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-50 font-montserrat disabled:opacity-50 disabled:cursor-not-allowed; }
            .btn-success { @apply bg-success text-white px-3 py-2 rounded text-sm font-medium transition-all duration-300 hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-success focus:ring-opacity-50 font-montserrat disabled:opacity-50 disabled:cursor-not-allowed; }
            .btn-danger { @apply bg-danger text-white px-3 py-2 rounded text-sm font-medium transition-all duration-300 hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-danger focus:ring-opacity-50 font-montserrat disabled:opacity-50 disabled:cursor-not-allowed; }
            .btn-sm { @apply px-2 py-1 text-xs; } /* Smaller button variant */

            /* Form Elements */
            .form-input, .form-textarea, .form-select { @apply w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-colors duration-200 text-sm shadow-sm; }
            .form-textarea { @apply min-h-[150px] leading-relaxed; } /* Default height for textareas */
            .form-checkbox { @apply h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary; }

            /* Card */
            .card { @apply bg-white rounded-xl shadow-sm border border-border p-4 md:p-6; }

            /* Navigation */
            .nav-item { @apply flex items-center px-3 py-2 rounded-md text-gray-700 hover:text-primary hover:bg-secondary transition-colors duration-200 text-sm; }
            .nav-item-active { @apply flex items-center px-3 py-2 rounded-md text-primary bg-secondary transition-colors duration-200 font-semibold text-sm; }

            /* Table */
            .table { @apply min-w-full divide-y divide-border; }
            .table th { @apply px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50; }
            .table td { @apply px-4 py-2 whitespace-nowrap text-sm text-gray-700; }
            .table tbody tr:nth-child(even) { @apply bg-gray-50/50; }
            .table tbody tr:hover { @apply bg-secondary/30; }

            /* Badges */
            .badge { @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium; }
            .badge-success { @apply bg-green-100 text-green-800; }
            .badge-warning { @apply bg-yellow-100 text-yellow-800; }
            .badge-danger { @apply bg-red-100 text-red-800; }
            .badge-info { @apply bg-blue-100 text-blue-800; }
            .badge-gray { @apply bg-gray-100 text-gray-800; }

            /* Toggle Switch (for Alpine.js) */
            .toggle-switch { @apply relative inline-block w-10 h-5 transition-colors duration-200 ease-in-out rounded-full cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary; }
            .toggle-thumb { @apply absolute top-0.5 left-0.5 bg-white w-4 h-4 rounded-full shadow transform transition-transform duration-200 ease-in-out; }

            /* Collapsible Sections (for Alpine.js) */
            .collapsible-header { @apply w-full flex items-center justify-between p-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 transition-colors cursor-pointer; }
            .collapsible-content { @apply border border-t-0 border-gray-300 rounded-b-md bg-white p-4; }

            /* Dropdown Menu (for Alpine.js) */
            .dropdown-menu { @apply absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10; }
            .dropdown-item { @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 w-full text-left; } /* Ensure button styles work */

             /* Analytics Specific */
            .metric-card { @apply bg-white rounded-xl shadow-sm border border-border p-4 flex flex-col; }
            .metric-value { @apply text-3xl font-bold text-gray-900 font-montserrat; }
            .metric-label { @apply text-sm text-gray-500; }
            .metric-change-positive { @apply ml-2 text-sm text-success flex items-center; }
            .metric-change-negative { @apply ml-2 text-sm text-danger flex items-center; }
            .tab-button { @apply px-4 py-2 text-sm font-medium rounded-t-lg; }
            .tab-button-active { @apply bg-white border border-b-0 border-border text-primary; }
            .tab-button-inactive { @apply bg-gray-50 text-gray-600 hover:text-primary border border-transparent; }
        }

        /* Base body styles */
        body {
            background-color: theme('colors.background');
            font-family: theme('fontFamily.sans');
            color: theme('colors.dark');
            overflow-x: hidden; /* Prevent horizontal scroll */
        }

        /* Style placeholder text */
        #content::placeholder {
             color: #9ca3af; /* gray-400 */
        }

        /* Ensure header stays above content */
        header.sticky {
             z-index: 40;
        }
    </style>
</head>
<body class="bg-background">
    <div class="flex h-screen overflow-hidden">
        <div class="w-64 bg-white border-r border-border h-full flex flex-col flex-shrink-0 shadow-sm">
            <div class="px-6 py-4 border-b border-border flex items-center flex-shrink-0">
                 <a href="index.php" class="flex items-center">
                    <span class="font-montserrat text-xl font-extrabold text-primary">
                        <span class="relative inline-block">
                            Lako <span class="text-[#333] italic font-light">&</span> <span class="relative">Fino
                                <span class="absolute -bottom-1 left-0 w-full h-1 bg-primary rounded-full"></span>
                            </span>
                        </span>
                    </span>
                    <span class="text-xs font-medium text-gray-500 ml-2">CMS</span>
                 </a>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 py-4">
                <ul class="space-y-1">
                    <li>
                        <a href="index.php" class="<?php echo is_nav_active('index.php', $current_page) ? 'nav-item-active' : 'nav-item'; ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" /></svg>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="articles.php" class="<?php echo is_nav_active('articles.php', $current_page) ? 'nav-item-active' : 'nav-item'; ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /></svg>
                            Articles
                        </a>
                    </li>
                    <li>
                        <a href="manage_categories_tags.php" class="<?php echo is_nav_active('manage_categories_tags.php', $current_page) ? 'nav-item-active' : 'nav-item'; ?>">
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 12h14M5 16h14" /> </svg>
                            Categories & Tags
                        </a>
                    </li>
                    <li>
                        <a href="internal_links.php" class="<?php echo is_nav_active('internal_links.php', $current_page) ? 'nav-item-active' : 'nav-item'; ?>">
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" /></svg>
                            Internal Links
                        </a>
                    </li>
                    <li>
                        <a href="advertising.php" class="<?php echo is_nav_active('advertising.php', $current_page) ? 'nav-item-active' : 'nav-item'; ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" /></svg>
                            Advertising
                        </a>
                    </li>
                    <li>
                        <a href="analytics.php" class="<?php echo is_nav_active('analytics.php', $current_page) ? 'nav-item-active' : 'nav-item'; ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" /></svg>
                            Website Analytics
                        </a>
                    </li>
                     <li>
                        <a href="ad_analytics.php" class="<?php echo is_nav_active('ad_analytics.php', $current_page) ? 'nav-item-active' : 'nav-item'; ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" /></svg>
                            Ads Analytics
                        </a>
                    </li>
                     <li>
                        <a href="#" class="nav-item opacity-50 cursor-not-allowed" title="Coming Soon">
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                            Media
                        </a>
                    </li>
                    <?php /* REMOVED OLD DISABLED TAGS LINK */ ?>
                    <li>
                        <a href="settings.php" class="<?php echo is_nav_active('settings.php', $current_page) ? 'nav-item-active' : 'nav-item'; ?>">
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                            Settings
                        </a>
                    </li>
                     <li>
                        <a href="register.php" class="<?php echo is_nav_active('register.php', $current_page) ? 'nav-item-active' : 'nav-item'; ?>">
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" /></svg>
                            Add User
                        </a>
                    </li>
                </ul>

                <hr class="my-6 border-border">

                <ul class="space-y-1">
                    <li>
                        <a href="logout.php" class="nav-item">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" /></svg>
                            Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <div class="flex-1 flex flex-col overflow-hidden">
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-background">
                <?php // ?>