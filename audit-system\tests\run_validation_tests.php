<?php

/**
 * Validation Test Runner
 * 
 * Executes the complete validation test suite for the CMS audit system
 * and generates a comprehensive report.
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../src/bootstrap.php';

use PHPUnit\Framework\TestSuite;
use PHPUnit\TextUI\TestRunner;
use PHPUnit\Framework\TestResult;
use AuditSystem\Tests\Integration\RealCMSValidationTest;
use AuditSystem\Tests\Integration\SecurityVulnerabilityValidationTest;
use AuditSystem\Tests\Integration\PerformanceBottleneckValidationTest;
use AuditSystem\Tests\Integration\FalsePositiveMinimizationTest;
use AuditSystem\Tests\Integration\RegressionTestSuite;

class ValidationTestRunner
{
    private array $testResults = [];
    private string $reportPath;
    
    public function __construct()
    {
        $this->reportPath = __DIR__ . '/validation_report_' . date('Y-m-d_H-i-s') . '.html';
    }
    
    public function runAllValidationTests(): void
    {
        echo "=== CMS Audit System Validation Test Suite ===\n";
        echo "Starting comprehensive validation tests...\n\n";
        
        $testSuites = [
            'Real CMS Validation' => RealCMSValidationTest::class,
            'Security Vulnerability Validation' => SecurityVulnerabilityValidationTest::class,
            'Performance Bottleneck Validation' => PerformanceBottleneckValidationTest::class,
            'False Positive Minimization' => FalsePositiveMinimizationTest::class,
            'Regression Test Suite' => RegressionTestSuite::class
        ];
        
        $overallResults = [
            'total_suites' => count($testSuites),
            'passed_suites' => 0,
            'failed_suites' => 0,
            'total_tests' => 0,
            'passed_tests' => 0,
            'failed_tests' => 0,
            'errors' => 0,
            'start_time' => microtime(true),
            'suite_results' => []
        ];
        
        foreach ($testSuites as $suiteName => $testClass) {
            echo "Running $suiteName...\n";
            
            $suiteResult = $this->runTestSuite($testClass, $suiteName);
            $overallResults['suite_results'][$suiteName] = $suiteResult;
            
            $overallResults['total_tests'] += $suiteResult['test_count'];
            $overallResults['passed_tests'] += $suiteResult['passed'];
            $overallResults['failed_tests'] += $suiteResult['failed'];
            $overallResults['errors'] += $suiteResult['errors'];
            
            if ($suiteResult['success']) {
                $overallResults['passed_suites']++;
                echo "✅ $suiteName: PASSED\n";
            } else {
                $overallResults['failed_suites']++;
                echo "❌ $suiteName: FAILED\n";
            }
            
            echo "   Tests: {$suiteResult['passed']}/{$suiteResult['test_count']} passed\n";
            if ($suiteResult['failed'] > 0) {
                echo "   Failures: {$suiteResult['failed']}\n";
            }
            if ($suiteResult['errors'] > 0) {
                echo "   Errors: {$suiteResult['errors']}\n";
            }
            echo "\n";
        }
        
        $overallResults['end_time'] = microtime(true);
        $overallResults['duration'] = $overallResults['end_time'] - $overallResults['start_time'];
        
        $this->displaySummary($overallResults);
        $this->generateReport($overallResults);
    }
    
    private function runTestSuite(string $testClass, string $suiteName): array
    {
        $suite = new TestSuite($suiteName);
        $suite->addTestSuite($testClass);
        
        $result = new TestResult();
        $runner = new TestRunner();
        
        // Capture output
        ob_start();
        $suite->run($result);
        $output = ob_get_clean();
        
        return [
            'success' => $result->wasSuccessful(),
            'test_count' => $result->count(),
            'passed' => $result->count() - $result->failureCount() - $result->errorCount(),
            'failed' => $result->failureCount(),
            'errors' => $result->errorCount(),
            'output' => $output,
            'failures' => $this->extractFailures($result),
            'errors_detail' => $this->extractErrors($result)
        ];
    }
    
    private function extractFailures(TestResult $result): array
    {
        $failures = [];
        foreach ($result->failures() as $failure) {
            $failures[] = [
                'test' => $failure->getTestName(),
                'message' => $failure->getExceptionAsString()
            ];
        }
        return $failures;
    }
    
    private function extractErrors(TestResult $result): array
    {
        $errors = [];
        foreach ($result->errors() as $error) {
            $errors[] = [
                'test' => $error->getTestName(),
                'message' => $error->getExceptionAsString()
            ];
        }
        return $errors;
    }
    
    private function displaySummary(array $results): void
    {
        echo "=== VALIDATION TEST SUMMARY ===\n";
        echo "Duration: " . number_format($results['duration'], 2) . " seconds\n";
        echo "Test Suites: {$results['passed_suites']}/{$results['total_suites']} passed\n";
        echo "Total Tests: {$results['passed_tests']}/{$results['total_tests']} passed\n";
        
        if ($results['failed_tests'] > 0) {
            echo "Failed Tests: {$results['failed_tests']}\n";
        }
        
        if ($results['errors'] > 0) {
            echo "Errors: {$results['errors']}\n";
        }
        
        $successRate = ($results['total_tests'] > 0) 
            ? ($results['passed_tests'] / $results['total_tests']) * 100 
            : 0;
        
        echo "Success Rate: " . number_format($successRate, 1) . "%\n";
        
        if ($results['passed_suites'] === $results['total_suites']) {
            echo "\n🎉 ALL VALIDATION TESTS PASSED! 🎉\n";
            echo "The audit system successfully validates against the CMS codebase.\n";
        } else {
            echo "\n⚠️  SOME VALIDATION TESTS FAILED\n";
            echo "Review the detailed report for specific issues.\n";
        }
        
        echo "\nDetailed report saved to: {$this->reportPath}\n";
    }
    
    private function generateReport(array $results): void
    {
        $html = $this->generateHTMLReport($results);
        file_put_contents($this->reportPath, $html);
    }
    
    private function generateHTMLReport(array $results): string
    {
        $successRate = ($results['total_tests'] > 0) 
            ? ($results['passed_tests'] / $results['total_tests']) * 100 
            : 0;
        
        $statusColor = $successRate >= 90 ? '#28a745' : ($successRate >= 70 ? '#ffc107' : '#dc3545');
        
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CMS Audit System Validation Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: ' . $statusColor . '; }
        .metric-label { color: #666; margin-top: 5px; }
        .suite-results { margin-bottom: 30px; }
        .suite { border: 1px solid #ddd; border-radius: 5px; margin-bottom: 20px; }
        .suite-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; }
        .suite-content { padding: 15px; }
        .success { color: #28a745; }
        .failure { color: #dc3545; }
        .error { color: #fd7e14; }
        .test-details { margin-top: 10px; }
        .failure-details, .error-details { background: #f8f9fa; padding: 10px; border-radius: 3px; margin-top: 10px; }
        .code { font-family: monospace; background: #f1f1f1; padding: 2px 4px; border-radius: 3px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>CMS Audit System Validation Report</h1>
            <p>Generated on ' . date('Y-m-d H:i:s') . '</p>
        </div>
        
        <div class="summary">
            <div class="metric">
                <div class="metric-value">' . number_format($successRate, 1) . '%</div>
                <div class="metric-label">Success Rate</div>
            </div>
            <div class="metric">
                <div class="metric-value">' . $results['passed_tests'] . '/' . $results['total_tests'] . '</div>
                <div class="metric-label">Tests Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value">' . $results['passed_suites'] . '/' . $results['total_suites'] . '</div>
                <div class="metric-label">Suites Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value">' . number_format($results['duration'], 2) . 's</div>
                <div class="metric-label">Duration</div>
            </div>
        </div>';
        
        $html .= '<div class="suite-results"><h2>Test Suite Results</h2>';
        
        foreach ($results['suite_results'] as $suiteName => $suiteResult) {
            $suiteStatus = $suiteResult['success'] ? 'success' : 'failure';
            $suiteIcon = $suiteResult['success'] ? '✅' : '❌';
            
            $html .= '<div class="suite">
                <div class="suite-header">
                    <h3>' . $suiteIcon . ' ' . htmlspecialchars($suiteName) . '</h3>
                    <div class="' . $suiteStatus . '">
                        Tests: ' . $suiteResult['passed'] . '/' . $suiteResult['test_count'] . ' passed';
            
            if ($suiteResult['failed'] > 0) {
                $html .= ' | Failures: ' . $suiteResult['failed'];
            }
            if ($suiteResult['errors'] > 0) {
                $html .= ' | Errors: ' . $suiteResult['errors'];
            }
            
            $html .= '</div>
                </div>
                <div class="suite-content">';
            
            if (!empty($suiteResult['failures'])) {
                $html .= '<h4 class="failure">Failures:</h4>';
                foreach ($suiteResult['failures'] as $failure) {
                    $html .= '<div class="failure-details">
                        <strong>Test:</strong> ' . htmlspecialchars($failure['test']) . '<br>
                        <strong>Message:</strong> <pre>' . htmlspecialchars($failure['message']) . '</pre>
                    </div>';
                }
            }
            
            if (!empty($suiteResult['errors_detail'])) {
                $html .= '<h4 class="error">Errors:</h4>';
                foreach ($suiteResult['errors_detail'] as $error) {
                    $html .= '<div class="error-details">
                        <strong>Test:</strong> ' . htmlspecialchars($error['test']) . '<br>
                        <strong>Message:</strong> <pre>' . htmlspecialchars($error['message']) . '</pre>
                    </div>';
                }
            }
            
            $html .= '</div></div>';
        }
        
        $html .= '</div>';
        
        // Add validation criteria section
        $html .= '<div class="validation-criteria">
            <h2>Validation Criteria</h2>
            <table>
                <tr><th>Test Suite</th><th>Purpose</th><th>Success Criteria</th></tr>
                <tr>
                    <td>Real CMS Validation</td>
                    <td>Test against actual CMS files</td>
                    <td>Detect known vulnerabilities and performance issues</td>
                </tr>
                <tr>
                    <td>Security Vulnerability Validation</td>
                    <td>Validate security issue detection</td>
                    <td>Find hardcoded credentials, XSS, SQL injection patterns</td>
                </tr>
                <tr>
                    <td>Performance Bottleneck Validation</td>
                    <td>Validate performance issue detection</td>
                    <td>Identify N+1 queries, caching issues, inefficient operations</td>
                </tr>
                <tr>
                    <td>False Positive Minimization</td>
                    <td>Ensure clean code passes</td>
                    <td>Minimal findings for well-written, secure code</td>
                </tr>
                <tr>
                    <td>Regression Test Suite</td>
                    <td>Ensure consistent detection</td>
                    <td>Known issues consistently detected across runs</td>
                </tr>
            </table>
        </div>';
        
        $html .= '</div></body></html>';
        
        return $html;
    }
}

// Run the validation tests
if (php_sapi_name() === 'cli') {
    $runner = new ValidationTestRunner();
    $runner->runAllValidationTests();
} else {
    echo "This script should be run from the command line.\n";
    echo "Usage: php run_validation_tests.php\n";
}