#!/usr/bin/env php
<?php

/**
 * CLI Usage Examples for Lako & Fino CMS Audit Tool
 * 
 * This script demonstrates various ways to use the audit CLI tool
 * Run this script to see example commands and their expected outputs
 */

echo "=== Lako & Fino CMS Audit Tool - Usage Examples ===\n\n";

$cliScript = __DIR__ . '/../bin/audit.php';

if (!file_exists($cliScript)) {
    echo "ERROR: CLI script not found at: {$cliScript}\n";
    exit(1);
}

echo "CLI Script Location: {$cliScript}\n\n";

// Example commands with descriptions
$examples = [
    [
        'title' => '1. Basic Help',
        'description' => 'Display comprehensive help information',
        'command' => 'php audit.php help',
        'notes' => 'Shows all available commands, options, and usage examples'
    ],
    [
        'title' => '2. Version Information',
        'description' => 'Show version and system information',
        'command' => 'php audit.php version',
        'notes' => 'Displays tool version, PHP version, and platform info'
    ],
    [
        'title' => '3. Configuration Validation',
        'description' => 'Validate audit setup before running',
        'command' => 'php audit.php validate',
        'notes' => 'Checks directories, permissions, and PHP extensions'
    ],
    [
        'title' => '4. View Current Configuration',
        'description' => 'Display current audit configuration',
        'command' => 'php audit.php config',
        'notes' => 'Shows target directory, progress file, report directory, etc.'
    ],
    [
        'title' => '5. Basic Audit (Default Settings)',
        'description' => 'Run audit with default configuration',
        'command' => 'php audit.php audit',
        'notes' => 'Analyzes ../public_html directory with default settings'
    ],
    [
        'title' => '6. Audit with Custom Target',
        'description' => 'Audit specific directory',
        'command' => 'php audit.php audit --target=/path/to/cms',
        'notes' => 'Replace /path/to/cms with actual CMS directory path'
    ],
    [
        'title' => '7. Verbose Audit',
        'description' => 'Run audit with detailed output',
        'command' => 'php audit.php audit --verbose',
        'notes' => 'Shows configuration details and progress information'
    ],
    [
        'title' => '8. Quiet Audit',
        'description' => 'Run audit with minimal output',
        'command' => 'php audit.php audit --quiet',
        'notes' => 'Suppresses non-essential output, good for scripts'
    ],
    [
        'title' => '9. Custom Configuration File',
        'description' => 'Use custom audit configuration',
        'command' => 'php audit.php audit --config=custom-audit.json',
        'notes' => 'Load settings from custom configuration file'
    ],
    [
        'title' => '10. Audit with Custom Settings',
        'description' => 'Override multiple configuration options',
        'command' => 'php audit.php audit --target=../public_html --timeout=600 --max-file-size=2097152',
        'notes' => 'Set custom timeout (10 minutes) and max file size (2MB)'
    ],
    [
        'title' => '11. Resume Interrupted Audit',
        'description' => 'Continue from last checkpoint',
        'command' => 'php audit.php resume',
        'notes' => 'Resumes audit from where it left off using progress file'
    ],
    [
        'title' => '12. Check Audit Status',
        'description' => 'View current audit progress',
        'command' => 'php audit.php status',
        'notes' => 'Shows phase, completion percentage, and statistics'
    ],
    [
        'title' => '13. Watch Progress in Real-time',
        'description' => 'Monitor audit progress continuously',
        'command' => 'php audit.php watch',
        'notes' => 'Updates display every second until audit completes'
    ],
    [
        'title' => '14. Clear Logs and Start Fresh',
        'description' => 'Start audit with clean log files',
        'command' => 'php audit.php audit --clear-logs',
        'notes' => 'Removes previous audit logs before starting'
    ],
    [
        'title' => '15. Production-Ready Audit',
        'description' => 'Comprehensive audit for production environment',
        'command' => 'php audit.php audit --target=../public_html --timeout=1800 --clear-logs --verbose',
        'notes' => 'Extended timeout (30 min), clean logs, detailed output'
    ]
];

// Display examples
foreach ($examples as $example) {
    echo "=== {$example['title']} ===\n";
    echo "Description: {$example['description']}\n";
    echo "Command: {$example['command']}\n";
    echo "Notes: {$example['notes']}\n\n";
}

echo "=== Advanced Usage Patterns ===\n\n";

echo "1. Automated CI/CD Integration:\n";
echo "   php audit.php audit --quiet --config=ci-audit.json && echo 'Audit passed' || echo 'Audit failed'\n\n";

echo "2. Scheduled Audit with Email Notification:\n";
echo "   php audit.php audit --quiet > audit.log 2>&1 && mail -s 'Audit Complete' <EMAIL> < audit.log\n\n";

echo "3. Development Workflow:\n";
echo "   # Validate setup\n";
echo "   php audit.php validate\n";
echo "   # Run audit with verbose output\n";
echo "   php audit.php audit --verbose\n";
echo "   # Check specific issues in reports directory\n\n";

echo "4. Large Codebase Optimization:\n";
echo "   php audit.php audit --timeout=3600 --max-file-size=5242880 --target=/large/codebase\n\n";

echo "5. Monitoring Long-Running Audit:\n";
echo "   # Terminal 1: Start audit\n";
echo "   php audit.php audit --target=/large/codebase\n";
echo "   # Terminal 2: Monitor progress\n";
echo "   php audit.php watch\n\n";

echo "=== Configuration File Examples ===\n\n";

echo "Basic configuration file (audit-config.json):\n";
echo json_encode([
    'audit' => [
        'target_directory' => '../public_html',
        'progress_file' => 'data/progress.json',
        'report_directory' => 'reports',
        'timeout' => 300,
        'max_file_size' => 1048576
    ],
    'priority_areas' => [
        'patterns' => [
            'ad_*',
            'includes/ad_*',
            'admin/advertising*',
            'smrsaj*',
            'image.php',
            'includes/security*',
            'config.php'
        ]
    ],
    'analyzers' => [
        'security' => ['enabled' => true],
        'performance' => ['enabled' => true],
        'quality' => ['enabled' => true]
    ]
], JSON_PRETTY_PRINT) . "\n\n";

echo "=== Exit Codes ===\n\n";
echo "0 - Success: Audit completed successfully\n";
echo "1 - Error: Configuration error, validation failure, or audit error\n\n";

echo "=== Report Files ===\n\n";
echo "After successful audit, check the reports directory for:\n";
echo "- audit_report_YYYY-MM-DD_HH-MM-SS.md (Markdown format)\n";
echo "- audit_report_YYYY-MM-DD_HH-MM-SS.json (JSON format)\n";
echo "- audit_report_YYYY-MM-DD_HH-MM-SS.html (HTML format)\n\n";

echo "=== Troubleshooting ===\n\n";
echo "Common issues and solutions:\n\n";

echo "1. Permission Denied:\n";
echo "   - Ensure PHP has read access to target directory\n";
echo "   - Ensure write access to progress and report directories\n";
echo "   - Run: chmod +x bin/audit.php\n\n";

echo "2. Memory Limit Exceeded:\n";
echo "   - Increase PHP memory limit: php -d memory_limit=512M audit.php\n";
echo "   - Use smaller max-file-size: --max-file-size=524288\n\n";

echo "3. Timeout Issues:\n";
echo "   - Increase timeout: --timeout=1800\n";
echo "   - Process smaller batches of files\n\n";

echo "4. Configuration Not Found:\n";
echo "   - Verify config file path: --config=path/to/config.json\n";
echo "   - Check JSON syntax with: php -l config.json\n\n";

echo "=== Support ===\n\n";
echo "For additional help:\n";
echo "- Run: php audit.php help\n";
echo "- Check log files in logs/ directory\n";
echo "- Review generated reports for detailed findings\n\n";

echo "=== End of Examples ===\n";