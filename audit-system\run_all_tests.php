<?php

/**
 * Test Suite Runner
 * 
 * Runs all PHPUnit tests with proper configuration
 */

echo "=== AUDIT SYSTEM TEST SUITE ===\n";
echo "Running all tests with PHPUnit...\n\n";

// Test suites to run
$testSuites = [
    'Unit Tests' => [
        'tests/Models/',
        'tests/Services/',
        'tests/Analyzers/',
        'tests/Controllers/',
        'tests/Exceptions/'
    ],
    'Integration Tests' => [
        'tests/Integration/CompleteSystemIntegrationTest.php',
        'tests/Integration/RealCMSValidationTest.php'
    ]
];

$totalTests = 0;
$passedTests = 0;
$failedTests = 0;

foreach ($testSuites as $suiteName => $testPaths) {
    echo "Running {$suiteName}:\n";
    echo str_repeat("-", 50) . "\n";
    
    foreach ($testPaths as $testPath) {
        if (!file_exists($testPath) && !is_dir($testPath)) {
            echo "⚠️  Skipping {$testPath} (not found)\n";
            continue;
        }
        
        echo "Testing: " . basename($testPath) . "... ";
        
        $command = "php -d extension=mbstring phpunit.phar {$testPath} 2>&1";
        $output = [];
        $returnCode = 0;
        
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "✅ PASS\n";
            $passedTests++;
        } else {
            echo "❌ FAIL\n";
            $failedTests++;
            
            // Show error details for failed tests
            if (count($output) > 0) {
                echo "   Error: " . end($output) . "\n";
            }
        }
        
        $totalTests++;
    }
    
    echo "\n";
}

echo str_repeat("=", 60) . "\n";
echo "TEST SUITE SUMMARY\n";
echo str_repeat("=", 60) . "\n";
echo "Total Test Suites: {$totalTests}\n";
echo "Passed: {$passedTests}\n";
echo "Failed: {$failedTests}\n";

if ($failedTests === 0) {
    echo "\n🎉 ALL TEST SUITES PASSED!\n";
    echo "\nThe audit system is working correctly with PHPUnit.\n";
    echo "\nYou can now run individual tests with:\n";
    echo "  php -d extension=mbstring phpunit.phar [test-file]\n";
    echo "  php -d extension=mbstring phpunit.phar --filter [test-method] [test-file]\n";
    echo "  php -d extension=mbstring phpunit.phar tests/Integration/\n";
    
    exit(0);
} else {
    echo "\n❌ {$failedTests} test suite(s) failed.\n";
    echo "\nTo debug specific failures, run:\n";
    echo "  php -d extension=mbstring phpunit.phar [failed-test-path] --verbose\n";
    
    exit(1);
}