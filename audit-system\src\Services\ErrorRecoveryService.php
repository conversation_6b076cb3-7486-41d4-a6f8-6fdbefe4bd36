<?php

namespace AuditSystem\Services;

use AuditSystem\Interfaces\LoggerInterface;
use AuditSystem\Exceptions\AuditException;
use AuditSystem\Exceptions\FileAccessException;
use AuditSystem\Exceptions\AnalysisException;
use AuditSystem\Exceptions\MCPConnectionException;
use AuditSystem\Exceptions\ConfigurationException;
use AuditSystem\Exceptions\ProgressException;
use AuditSystem\Exceptions\ReportException;
use AuditSystem\Exceptions\SecurityException;

/**
 * Service for handling error recovery strategies
 */
class ErrorRecoveryService
{
    private LoggerInterface $logger;
    private array $recoveryStrategies;
    private array $recoveryAttempts;
    private int $maxRetryAttempts;
    private array $fallbackOptions;

    public function __construct(LoggerInterface $logger, int $maxRetryAttempts = 3)
    {
        $this->logger = $logger;
        $this->maxRetryAttempts = $maxRetryAttempts;
        $this->recoveryAttempts = [];
        $this->initializeRecoveryStrategies();
        $this->initializeFallbackOptions();
    }

    /**
     * Initialize recovery strategies for different exception types
     *
     * @return void
     */
    private function initializeRecoveryStrategies(): void
    {
        $this->recoveryStrategies = [
            FileAccessException::class => [
                'retry_with_delay',
                'skip_file',
                'use_alternative_path'
            ],
            AnalysisException::class => [
                'retry_with_basic_analyzer',
                'partial_analysis',
                'skip_problematic_analyzer'
            ],
            MCPConnectionException::class => [
                'retry_connection',
                'use_cached_data',
                'fallback_to_local_analysis'
            ],
            ConfigurationException::class => [
                'use_default_config',
                'prompt_for_config',
                'skip_dependent_features'
            ],
            ProgressException::class => [
                'restore_from_backup',
                'rebuild_progress',
                'start_fresh'
            ],
            ReportException::class => [
                'use_alternative_format',
                'generate_minimal_report',
                'export_raw_data'
            ],
            SecurityException::class => [
                'apply_security_restrictions',
                'skip_suspicious_content',
                'quarantine_file'
            ]
        ];
    }

    /**
     * Initialize fallback options for graceful degradation
     *
     * @return void
     */
    private function initializeFallbackOptions(): void
    {
        $this->fallbackOptions = [
            'mcp_server' => [
                'use_local_best_practices',
                'skip_best_practice_validation',
                'use_cached_recommendations'
            ],
            'file_analysis' => [
                'basic_syntax_check',
                'pattern_matching_only',
                'skip_complex_analysis'
            ],
            'report_generation' => [
                'text_only_report',
                'json_export_only',
                'summary_report_only'
            ],
            'progress_tracking' => [
                'memory_only_progress',
                'simplified_tracking',
                'disable_progress_tracking'
            ]
        ];
    }

    /**
     * Attempt to recover from an exception
     *
     * @param AuditException $exception Exception to recover from
     * @param string $context Context where the error occurred
     * @param array $additionalData Additional data for recovery
     * @return mixed Recovery result or null if recovery failed
     */
    public function attemptRecovery(AuditException $exception, string $context, array $additionalData = [])
    {
        $exceptionClass = get_class($exception);
        $recoveryKey = $exceptionClass . ':' . $context;

        // Check if we've exceeded retry attempts for this specific error
        if (($this->recoveryAttempts[$recoveryKey] ?? 0) >= $this->maxRetryAttempts) {
            $this->logger->error("Maximum recovery attempts exceeded for {$exceptionClass} in {$context}");
            return null;
        }

        $this->recoveryAttempts[$recoveryKey] = ($this->recoveryAttempts[$recoveryKey] ?? 0) + 1;

        $strategies = $this->recoveryStrategies[$exceptionClass] ?? ['default_recovery'];

        foreach ($strategies as $strategy) {
            try {
                $this->logger->info("Attempting recovery strategy: {$strategy} for {$exceptionClass} in {$context}");
                
                $result = $this->executeRecoveryStrategy($strategy, $exception, $context, $additionalData);
                
                if ($result !== null) {
                    $this->logger->logErrorRecovery($exceptionClass, $strategy, $context, true);
                    // Don't reset counter immediately - keep for statistics
                    return $result;
                }
            } catch (\Exception $recoveryException) {
                $this->logger->warning("Recovery strategy {$strategy} failed: " . $recoveryException->getMessage());
                continue;
            }
        }

        $this->logger->logErrorRecovery($exceptionClass, 'all_strategies', $context, false);
        return null;
    }

    /**
     * Execute a specific recovery strategy
     *
     * @param string $strategy Recovery strategy name
     * @param AuditException $exception Original exception
     * @param string $context Context where error occurred
     * @param array $additionalData Additional data for recovery
     * @return mixed Recovery result
     */
    private function executeRecoveryStrategy(string $strategy, AuditException $exception, string $context, array $additionalData)
    {
        switch ($strategy) {
            case 'retry_with_delay':
                return $this->retryWithDelay($exception, $context, $additionalData);
                
            case 'skip_file':
                return $this->skipFile($exception, $context, $additionalData);
                
            case 'use_alternative_path':
                return $this->useAlternativePath($exception, $context, $additionalData);
                
            case 'retry_with_basic_analyzer':
                return $this->retryWithBasicAnalyzer($exception, $context, $additionalData);
                
            case 'partial_analysis':
                return $this->performPartialAnalysis($exception, $context, $additionalData);
                
            case 'skip_problematic_analyzer':
                return $this->skipProblematicAnalyzer($exception, $context, $additionalData);
                
            case 'retry_connection':
                return $this->retryConnection($exception, $context, $additionalData);
                
            case 'use_cached_data':
                return $this->useCachedData($exception, $context, $additionalData);
                
            case 'fallback_to_local_analysis':
                return $this->fallbackToLocalAnalysis($exception, $context, $additionalData);
                
            case 'use_default_config':
                return $this->useDefaultConfig($exception, $context, $additionalData);
                
            case 'restore_from_backup':
                return $this->restoreFromBackup($exception, $context, $additionalData);
                
            case 'rebuild_progress':
                return $this->rebuildProgress($exception, $context, $additionalData);
                
            case 'use_alternative_format':
                return $this->useAlternativeFormat($exception, $context, $additionalData);
                
            case 'apply_security_restrictions':
                return $this->applySecurityRestrictions($exception, $context, $additionalData);
                
            case 'default_recovery':
            default:
                return $this->defaultRecovery($exception, $context, $additionalData);
        }
    }

    /**
     * Retry operation with exponential backoff delay
     */
    private function retryWithDelay(AuditException $exception, string $context, array $additionalData)
    {
        // Don't retry for file too large errors - skip them immediately
        if ($exception instanceof \AuditSystem\Exceptions\FileAccessException) {
            $message = $exception->getMessage();
            if (strpos($message, 'too large') !== false || strpos($message, 'exceeds maximum') !== false) {
                return null; // Skip to next strategy (skip_file)
            }
        }
        
        $attempt = $this->recoveryAttempts[get_class($exception) . ':' . $context] ?? 1;
        $delay = min(pow(2, $attempt - 1), 30); // Max 30 seconds delay
        
        $this->logger->info("Retrying operation after {$delay} seconds delay");
        sleep($delay);
        
        // Return a signal to retry the original operation
        return ['action' => 'retry', 'delay' => $delay];
    }

    /**
     * Skip the problematic file and continue
     */
    private function skipFile(AuditException $exception, string $context, array $additionalData)
    {
        $filePath = $additionalData['file_path'] ?? 'unknown';
        $this->logger->warning("Skipping problematic file: {$filePath}");
        
        return ['action' => 'skip', 'file' => $filePath];
    }

    /**
     * Try alternative file path
     */
    private function useAlternativePath(AuditException $exception, string $context, array $additionalData)
    {
        $originalPath = $additionalData['file_path'] ?? '';
        $alternatives = $this->generateAlternativePaths($originalPath);
        
        foreach ($alternatives as $altPath) {
            if (file_exists($altPath) && is_readable($altPath)) {
                $this->logger->info("Using alternative path: {$altPath}");
                return ['action' => 'use_alternative', 'path' => $altPath];
            }
        }
        
        return null;
    }

    /**
     * Retry with basic analyzer only
     */
    private function retryWithBasicAnalyzer(AuditException $exception, string $context, array $additionalData)
    {
        $this->logger->info("Retrying analysis with basic analyzer only");
        return ['action' => 'use_basic_analyzer'];
    }

    /**
     * Perform partial analysis with available data
     */
    private function performPartialAnalysis(AuditException $exception, string $context, array $additionalData)
    {
        $this->logger->info("Performing partial analysis with available data");
        return ['action' => 'partial_analysis'];
    }

    /**
     * Skip the problematic analyzer
     */
    private function skipProblematicAnalyzer(AuditException $exception, string $context, array $additionalData)
    {
        $analyzer = $additionalData['analyzer'] ?? 'unknown';
        $this->logger->warning("Skipping problematic analyzer: {$analyzer}");
        
        return ['action' => 'skip_analyzer', 'analyzer' => $analyzer];
    }

    /**
     * Retry MCP connection with different parameters
     */
    private function retryConnection(AuditException $exception, string $context, array $additionalData)
    {
        $this->logger->info("Retrying MCP connection");
        return ['action' => 'retry_connection'];
    }

    /**
     * Use cached MCP data if available
     */
    private function useCachedData(AuditException $exception, string $context, array $additionalData)
    {
        $this->logger->info("Using cached MCP data");
        return ['action' => 'use_cache'];
    }

    /**
     * Fallback to local analysis without MCP
     */
    private function fallbackToLocalAnalysis(AuditException $exception, string $context, array $additionalData)
    {
        $this->logger->logGracefulDegradation('MCP Server', $exception->getMessage(), 'local analysis');
        return ['action' => 'local_analysis'];
    }

    /**
     * Use default configuration values
     */
    private function useDefaultConfig(AuditException $exception, string $context, array $additionalData)
    {
        $this->logger->info("Using default configuration values");
        return ['action' => 'use_defaults'];
    }

    /**
     * Restore progress from backup
     */
    private function restoreFromBackup(AuditException $exception, string $context, array $additionalData)
    {
        $backupPath = $additionalData['backup_path'] ?? null;
        if ($backupPath && file_exists($backupPath)) {
            $this->logger->info("Restoring progress from backup: {$backupPath}");
            return ['action' => 'restore_backup', 'backup_path' => $backupPath];
        }
        
        return null;
    }

    /**
     * Rebuild progress from scratch
     */
    private function rebuildProgress(AuditException $exception, string $context, array $additionalData)
    {
        $this->logger->info("Rebuilding progress from scratch");
        return ['action' => 'rebuild_progress'];
    }

    /**
     * Use alternative report format
     */
    private function useAlternativeFormat(AuditException $exception, string $context, array $additionalData)
    {
        $alternativeFormats = ['json', 'text', 'csv'];
        $currentFormat = $additionalData['format'] ?? '';
        
        foreach ($alternativeFormats as $format) {
            if ($format !== $currentFormat) {
                $this->logger->info("Using alternative report format: {$format}");
                return ['action' => 'alternative_format', 'format' => $format];
            }
        }
        
        return null;
    }

    /**
     * Apply security restrictions and continue
     */
    private function applySecurityRestrictions(AuditException $exception, string $context, array $additionalData)
    {
        $this->logger->warning("Applying security restrictions due to: " . $exception->getMessage());
        return ['action' => 'apply_restrictions'];
    }

    /**
     * Default recovery strategy
     */
    private function defaultRecovery(AuditException $exception, string $context, array $additionalData)
    {
        $this->logger->info("Applying default recovery strategy");
        return ['action' => 'continue_with_warning'];
    }

    /**
     * Generate alternative file paths
     *
     * @param string $originalPath Original file path
     * @return array Alternative paths to try
     */
    private function generateAlternativePaths(string $originalPath): array
    {
        $alternatives = [];
        $pathInfo = pathinfo($originalPath);
        
        // Try different case variations
        $alternatives[] = strtolower($originalPath);
        $alternatives[] = strtoupper($originalPath);
        
        // Try with different extensions
        if (isset($pathInfo['extension'])) {
            $alternatives[] = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.bak';
            $alternatives[] = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.orig';
        }
        
        return array_unique($alternatives);
    }

    /**
     * Enable graceful degradation for a service
     *
     * @param string $service Service name
     * @param string $reason Reason for degradation
     * @return array Fallback options
     */
    public function enableGracefulDegradation(string $service, string $reason): array
    {
        $fallbacks = $this->fallbackOptions[$service] ?? ['disable_service'];
        
        $this->logger->logGracefulDegradation($service, $reason, implode(', ', $fallbacks));
        
        return $fallbacks;
    }

    /**
     * Get recovery statistics
     *
     * @return array Recovery attempt statistics
     */
    public function getRecoveryStatistics(): array
    {
        $totalAttempts = 0;
        foreach ($this->recoveryAttempts as $attempts) {
            $totalAttempts += $attempts;
        }
        
        $stats = [
            'total_attempts' => $totalAttempts,
            'unique_errors' => count($this->recoveryAttempts),
            'by_exception' => []
        ];
        
        foreach ($this->recoveryAttempts as $key => $attempts) {
            $parts = explode(':', $key, 2);
            $exception = $parts[0] ?? 'unknown';
            $context = $parts[1] ?? 'unknown';
            
            if (!isset($stats['by_exception'][$exception])) {
                $stats['by_exception'][$exception] = [];
            }
            
            $stats['by_exception'][$exception][$context] = $attempts;
        }
        
        return $stats;
    }

    /**
     * Reset recovery attempt counters
     *
     * @return void
     */
    public function resetRecoveryCounters(): void
    {
        $this->recoveryAttempts = [];
        $this->logger->info("Recovery attempt counters reset");
    }
}