<?php
require_once 'config.php'; // Need config for SITE_URL, DB connection, functions
require_once 'includes/functions.php'; // Ensure functions like escape(), getFeaturedImageUrl(), formatDate() are available

// Get the slug from the URL query parameter (set by .htaccess)
$slug = $_GET['slug'] ?? null;

if (!$slug) {
    header('Location: ' . SITE_URL);
    exit;
}

// Sanitize the slug
$slug = preg_replace('/[^a-zA-Z0-9-]/', '', $slug);

// Construct the final article URL
$articleUrl = SITE_URL . '/' . $slug . '/';

// --- Define Defaults ---
$page_title = 'Priprema članka'; // Default title
$og_title = $page_title;
$default_description = 'Dobrodošli na ' . SITE_NAME . ' - Ukusni recepti, savjeti za zdravlje i prirodni lijekovi.';
$og_description = $default_description; // Use the defined default initially
$og_image_url = getDefaultOgImageData()['url']; // Default OG image
$og_image_width = getDefaultOgImageData()['width'];
$og_image_height = getDefaultOgImageData()['height'];
$og_image_alt = getDefaultOgImageData()['og_alt'];
$published_time_iso = null;
$author_fb_url = null; // Placeholder
$category_name = null; // Placeholder
$tags_string = null; // Placeholder

// --- Fetch Featured Article Data ---
$featuredArticleData = null;

try {
    // Fetch the article using a function similar to getArticleBySlug but maybe simplified
    // Include content field for Facebook crawler to see real content
    $sql = "SELECT a.id, a.title, a.excerpt, a.content, a.featured_image, a.reading_time, a.published_at, a.created_at,
                   c.name as category_name,
                   au.name as author_name -- Add other author fields if needed
            FROM articles a
            LEFT JOIN categories c ON a.category_id = c.id
            LEFT JOIN authors au ON a.author_id = au.id
            WHERE a.slug = :slug AND a.status = 'published'
            LIMIT 1";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
    $stmt->execute();
    $featuredArticleData = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($featuredArticleData) {
        $page_title = escape($featuredArticleData['title']);
        $og_title = $page_title; // Use article title for OG
        // Use excerpt or generate description if needed for OG
        $og_description = !empty($featuredArticleData['excerpt']) ? escape($featuredArticleData['excerpt']) : $default_description;

        // Get OG image data for the specific article
        $featuredImageData = getFeaturedImageUrl($featuredArticleData['featured_image'], 'fb', 'articles', $page_title); // 'fb' size
        if ($featuredImageData['url']) {
            $og_image_url = $featuredImageData['url'];
            $og_image_width = $featuredImageData['width'];
            $og_image_height = $featuredImageData['height'];
            $og_image_alt = $featuredImageData['og_alt'];
        }

        // Format publish date for OG
        $publishDate = $featuredArticleData['published_at'] ?? $featuredArticleData['created_at'];
        if ($publishDate) {
             try {
                 $published_time_iso = (new DateTime($publishDate))->format(DateTime::ATOM); // ISO 8601 format
             } catch (Exception $e) {}
        }

        // Fetch Category and Tags if needed for OG
        $category_name = $featuredArticleData['category_name'];
        // Fetch tags separately if needed
        // $tags = getArticleTags($pdo, $featuredArticleData['id']);
        // $tags_string = implode(',', array_column($tags, 'name'));

    } else {
        // Optional: Handle article not found - maybe redirect to 404
        // header('Location: ' . SITE_URL . '/404.php'); exit;
        $page_title = 'Članak nije pronađen';
        // Keep default OG tags or set specific 'not found' OG tags
    }
} catch (PDOException $e) {
    error_log("Failed to fetch data for loading page (slug: $slug): " . $e->getMessage());
    // Proceed with default title and OG data
}

// --- Fetch Popular Articles Data ---
$relatedArticlesData = [];
if ($featuredArticleData) {
    try {
        // Fetch the most popular articles (by views) excluding the current article
        $sql = "SELECT a.id, a.title, a.slug, a.featured_image, a.reading_time
                FROM articles a
                WHERE a.status = 'published'
                AND a.id != :current_id
                ORDER BY a.views DESC
                LIMIT 4";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':current_id', $featuredArticleData['id'], PDO::PARAM_INT);
        $stmt->execute();
        $relatedArticlesData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("Failed to fetch popular articles for loading page (slug: $slug): " . $e->getMessage());
    }
}

?>
<!DOCTYPE html>
<html lang="bs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="theme-color" content="#ff6481">
    <!-- Allow indexing by social media but not search engines -->
    <meta name="robots" content="noindex, follow">

    <!-- Add article:published_time for social media to recognize this as real content -->
    <?php if ($published_time_iso): ?>
    <meta property="article:published_time" content="<?php echo $published_time_iso; ?>">
    <?php endif; ?>

    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>

    <?php
    // Construct the loading page URL instead of the final article URL
    $loadingUrl = rtrim(SITE_URL, '/') . '/load/' . $slug . '/';
    ?>

    <!-- Canonical link pointing to the loading page URL -->
    <link rel="canonical" href="<?php echo $loadingUrl; ?>">

    <!-- Enhanced Open Graph tags for better Facebook sharing -->
    <meta property="og:title" content="<?php echo $og_title; ?>">
    <meta property="og:description" content="<?php echo $og_description; ?>">
    <meta property="og:image" content="<?php echo $og_image_url; ?>">
    <meta property="og:image:width" content="<?php echo $og_image_width; ?>">
    <meta property="og:image:height" content="<?php echo $og_image_height; ?>">
    <?php if ($og_image_alt): ?><meta property="og:image:alt" content="<?php echo $og_image_alt; ?>"><?php endif; ?>
    <meta property="og:url" content="<?php echo $loadingUrl; ?>">
    <meta property="og:type" content="article">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <meta property="og:locale" content="bs_BA">
    <?php if ($published_time_iso): ?><meta property="article:published_time" content="<?php echo $published_time_iso; ?>"><?php endif; ?>
    <?php if ($author_fb_url): ?><meta property="article:author" content="<?php echo $author_fb_url; ?>"><?php endif; ?>
    <?php if ($category_name): ?><meta property="article:section" content="<?php echo escape($category_name); ?>"><?php endif; ?>
    <?php if ($tags_string): ?><meta property="article:tag" content="<?php echo escape($tags_string); ?>"><?php endif; ?>

    <!-- Facebook-specific tags -->
    <meta property="fb:app_id" content="<?php echo FB_APP_ID ?? ''; ?>">
    <meta property="article:publisher" content="https://www.facebook.com/mercislike"><?php // Replace with your Facebook page URL ?>

    <?php if ($featuredArticleData): ?>
    <!-- Structured data for search engines and Facebook -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "NewsArticle",
        "headline": "<?php echo addslashes($featuredArticleData['title']); ?>",
        "description": "<?php echo addslashes($featuredArticleData['excerpt'] ?? ''); ?>",
        "image": "<?php echo $og_image_url; ?>",
        "datePublished": "<?php echo $published_time_iso ?? ''; ?>",
        "author": {
            "@type": "Person",
            "name": "<?php echo addslashes($featuredArticleData['author_name'] ?? 'Author'); ?>"
        },
        "publisher": {
            "@type": "Organization",
            "name": "<?php echo SITE_NAME; ?>",
            "logo": {
                "@type": "ImageObject",
                "url": "<?php echo rtrim(SITE_URL, '/'); ?>/img/logo.png"
            }
        },
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "<?php echo $loadingUrl; ?>"
        }
    }
    </script>
    <?php endif; ?>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <?php // Preload featured image and popular article images ?>
    <link rel="preload" href="<?php echo $og_image_url; ?>" as="image">

    <?php if (!empty($featuredArticleData['featured_image'])): ?>
    <?php
        // Generate dynamic image URL for preloading featured image
        $preloadWidth = 600;
        $preloadHeight = 338;
        $preloadPath = 'articles/' . $featuredArticleData['featured_image'];
        $preloadUrl = '/image.php?src=' . urlencode($preloadPath) .
                     '&w=' . $preloadWidth .
                     '&h=' . $preloadHeight .
                     '&q=70&fit=cover&f=webp';
    ?>
    <link rel="preload" href="<?php echo $preloadUrl; ?>" as="image">
    <?php endif; ?>

    <?php
    // Preload popular article images
    if (!empty($relatedArticlesData)) {
        foreach ($relatedArticlesData as $index => $article) {
            if (!empty($article['featured_image'])) {
                // Generate dynamic image URL for preloading grid images
                $gridWidth = 300;
                $gridHeight = 200;
                $gridPath = 'articles/' . $article['featured_image'];
                $gridUrl = '/image.php?src=' . urlencode($gridPath) .
                          '&w=' . $gridWidth .
                          '&h=' . $gridHeight .
                          '&q=70&fit=cover&f=webp';

                echo '<link rel="preload" href="' . $gridUrl . '" as="image" fetchpriority="low">';
            }
            // Limit to first 2 to avoid too many preloads
            if ($index >= 1) break;
        }
    }
    ?>

    <style>
        :root {
            --primary: #ff6481;
            --secondary: #ffd6dd;
            --background: #FFF4F5;
            --dark: #333333;
            --light: #ffffff;
            --border: #feeaec;
        }

        html {
            -webkit-text-size-adjust: 100%; /* Prevent font scaling in landscape */
        }

        body {
            background-color: var(--background);
            font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            color: var(--dark);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow-x: hidden;
            /* Add font smoothing */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
             text-rendering: optimizeLegibility; /* Helps with rendering, especially for web fonts */
        }

        /* Pattern Background */
        .pattern-background {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
            z-index: 0; opacity: 0.15;
            background-image:
                radial-gradient(var(--primary) 2px, transparent 2px),
                radial-gradient(var(--primary) 2px, transparent 2px);
            background-size: 40px 40px; background-position: 0 0, 20px 20px;
        }

        .container {
            max-width: 600px; margin: 0 auto; padding: 20px;
            position: relative; z-index: 1; flex: 1; display: flex;
            flex-direction: column; padding-top: 80px; padding-bottom: 40px;
        }

        .logo {
            text-align: center; margin-bottom: 30px; font-size: 1.5rem;
            position: absolute; top: 20px; left: 50%; transform: translateX(-50%);
            width: 100%; z-index: 10;
        }
        .logo span { font-family: 'Montserrat', sans-serif; font-weight: 800; color: var(--primary); position: relative; }
        .logo span.underline::after { content: ''; position: absolute; bottom: -5px; left: 0; width: 100%; height: 3px; background-color: var(--primary); border-radius: 3px; }

        .loading-box {
            background-color: var(--light); border-radius: 16px; padding: 30px;
            box-shadow: 0 10px 25px rgba(255, 100, 129, 0.1); text-align: center;
            position: relative; transition: all 0.5s ease; border: 1px solid var(--border);
        }
        #loadingBoxContent { transition: opacity 0.5s ease; }
        .loading-title { font-family: 'Montserrat', sans-serif; font-weight: 700; font-size: 1.4rem; margin-bottom: 20px; color: var(--dark); }
        .loading-subtitle { font-size: 1rem; color: #666; margin-bottom: 30px; }

        /* Progress Circle */
        .circle-progress-container { position: relative; width: 120px; height: 120px; margin: 0 auto 30px; }
        .circle-progress { transform: rotate(-90deg); width: 120px; height: 120px; }
        .circle-bg { fill: none; stroke: var(--secondary); stroke-width: 8; }
        .circle-fill { fill: none; stroke: var(--primary); stroke-width: 8; stroke-linecap: round; stroke-dasharray: 314; stroke-dashoffset: 314; transition: stroke-dashoffset 0.1s linear; }
        .progress-percentage { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-family: 'Montserrat', sans-serif; font-weight: 600; font-size: 1.5rem; color: var(--primary); }

        .readers-count { margin: 20px 0; font-size: 0.9rem; color: #666; }
        .reader-pulse { font-weight: 600; color: var(--primary); animation: readerPulse 2s infinite; }
        @keyframes readerPulse { 0% { color: var(--primary); transform: scale(1); } 50% { color: #e0536e; transform: scale(1.05); } 100% { color: var(--primary); transform: scale(1); } }

        /* Fade In Animation */
        @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }

        /* Button */
        .btn { background-color: var(--primary); color: white; border: none; border-radius: 50px; padding: 12px 30px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: all 0.3s ease; display: inline-block; margin-top: 10px; font-family: 'Montserrat', sans-serif; text-decoration: none; }
        .btn:hover { background-color: #e0536e; transform: translateY(-2px); box-shadow: 0 4px 10px rgba(255, 100, 129, 0.3); }
        .limited-time { display: flex; align-items: center; justify-content: center; margin-top: 8px; font-size: 0.75rem; color: #e0536e; gap: 4px; animation: blink 1.5s infinite; }
        @keyframes blink { 0% { opacity: 0.7; } 50% { opacity: 1; } 100% { opacity: 0.7; } }

        /* Featured Article */
        .featured-article { display: none; margin-top: 40px; background: white; border-radius: 16px; overflow: hidden; box-shadow: 0 10px 25px rgba(255, 100, 129, 0.1); border: 1px solid var(--border); transition: opacity 0.5s ease, transform 0.5s ease; opacity: 0; transform: translateY(10px); position: relative; }
        .featured-article.visible { display: block; opacity: 1; transform: translateY(0); animation: fadeIn 0.5s ease forwards; }
        .featured-image-container { width: 100%; position: relative; padding-top: 56.25%; background-color: #eee; overflow: hidden; }
        .featured-image { position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: block; object-fit: cover; /* Removed potentially problematic image-rendering */ }
        .featured-image-container > div { position: absolute; top: 0; left: 0; width: 100%; height: 100%; display:flex; align-items:center; justify-content:center; color:#aaa; }
        .featured-content { padding: 20px; }
        .article-meta { display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px; font-size: 0.8rem; color: #666; }
        .meta-item { display: flex; align-items: center; }
        .article-meta svg, .meta-item svg { width: 14px; height: 14px; margin-right: 5px; color: var(--primary); }
        .article-title { font-family: 'Montserrat', sans-serif; font-size: 1.5rem; /* Slightly increased base size */ font-weight: 700; margin-bottom: 15px; color: var(--dark); line-height: 1.3; }
        .article-excerpt { color: #555; margin-bottom: 20px; line-height: 1.6; font-size: 0.95rem; /* Ensure readable size */ }
        .article-cta { text-align: center; margin-top: 20px; }
        .featured-status {
            display: inline-block;
            background-color: rgba(255, 100, 129, 0.9);
            color: white;
            font-size: 0.7rem;
            padding: 3px 8px;
            border-radius: 20px;
            margin-bottom: 15px;
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 3;
            animation: pulseBadge 2s infinite;
            font-weight: bold;
        }
        @keyframes pulseBadge {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); background-color: rgba(255, 60, 100, 0.95); }
            100% { transform: scale(1); }
        }
        .article-stats { display: flex; align-items: center; margin-top: 15px; justify-content: space-between; font-size: 0.85rem; }
        .active-readers, .comment-count { display: flex; align-items: center; font-size: 0.85rem; color: var(--primary); font-weight: 600; }
        .active-readers svg, .comment-count svg { width: 16px; height: 16px; margin-right: 5px; }
        .invisible-article-trigger { position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; cursor: pointer; z-index: 2; background-color: rgba(0,0,0,0); }
        .invisible-article-trigger:focus-visible { outline: 3px solid var(--primary); outline-offset: 3px; border-radius: 16px; }
        .invisible-article-trigger:focus { outline: none; }
        .invisible-article-trigger:focus-visible:focus { outline: 3px solid var(--primary); }

        /* Related Articles */
        .related-articles { margin-top: 30px; display: none; opacity: 0; transition: opacity 0.5s ease, transform 0.5s ease; transform: translateY(10px); }
        .related-articles.visible { display: block; opacity: 1; transform: translateY(0); animation: fadeIn 0.5s ease 0.2s forwards; }
        .related-title { font-family: 'Montserrat', sans-serif; font-size: 1.1rem; font-weight: 700; margin-bottom: 20px; /* Adjusted */ color: var(--dark); position: relative; display: inline-block; }
        .related-title::after { content: ''; position: absolute; bottom: -6px; left: 0; width: 60%; height: 3px; background-color: var(--primary); border-radius: 3px; }
        .articles-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px; }
        .article-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid var(--border);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-decoration: none;
            display: block;
            position: relative;
            height: 100%; /* Make all cards the same height */
            display: flex;
            flex-direction: column;
        }
        .article-card:hover { transform: translateY(-3px); box-shadow: 0 4px 10px rgba(255, 100, 129, 0.1); }
        .card-image {
            width: 100%;
            padding-top: 65%; /* Slightly reduced height ratio for more text space */
            position: relative;
            background-color: #eee;
            display: block;
        }
        .card-image img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .card-image > div {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #aaa;
            font-size: 0.7rem;
        }
        .card-content {
            padding: 10px 12px 12px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .card-title {
            font-size: 0.85rem;
            font-weight: 600;
            line-height: 1.4;
            margin-top: 0;
            margin-bottom: 8px;
            color: var(--dark);
            display: -webkit-box;
            -webkit-line-clamp: 3; /* Show 3 lines */
            -webkit-box-orient: vertical;
            overflow: hidden;
            min-height: 3.6em; /* Slightly increased for 1.4 line height */
            max-height: none; /* Remove fixed height constraint */
            flex-grow: 1; /* Allow title to take available space */
        }
        .card-meta { font-size: 0.7rem; /* Slightly larger */ color: #666; display: flex; justify-content: space-between; align-items: center; }
        .view-count { display: flex; align-items: center; gap: 2px; color: #888; }
        .trending-badge { position: absolute; top: 5px; right: 5px; background-color: rgba(255, 100, 129, 0.9); color: white; font-size: 0.6rem; padding: 2px 5px; border-radius: 3px; display: flex; align-items: center; gap: 2px; z-index: 2; }

        /* Social Share */
        .social-share { display: flex; justify-content: center; margin: 15px 0; gap: 10px; }
        .share-btn { width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; background-color: #f0f0f0; transition: all 0.3s ease; cursor: pointer; }
        .share-btn:hover { transform: translateY(-2px); }
        .share-btn svg { width: 16px; height: 16px; }
        .share-btn.facebook { background-color: #3b5998; color: white; }
        .share-btn.twitter { background-color: #1da1f2; color: white; }
        .share-btn.whatsapp { background-color: #25d366; color: white; }

        /* No obvious hidden content CSS */

        /* Responsive Adjustments */
        @media (max-width: 640px) {
            .container { padding: 15px; padding-top: 70px; }
            .loading-box { padding: 20px; }
            .loading-title { font-size: 1.2rem; }
            .featured-image-container { padding-top: 60%; }
            .article-title { font-size: 1.3rem; /* Adjusted mobile title */ }
            .article-excerpt { font-size: 0.9rem; } /* Ensure readable excerpt */
            .logo { font-size: 1.3rem; }
        }
         @media (max-width: 480px) {
             .articles-grid { gap: 8px; }
             .card-title {
                 font-size: 0.8rem;
                 min-height: 3.6em; /* Match desktop height */
                 line-height: 1.4;
                 -webkit-line-clamp: 3; /* Ensure 3 lines on mobile too */
                 margin-bottom: 6px;
             }
             .card-content { padding: 8px 8px 10px 8px; } /* Add more padding at bottom */
             .card-meta { font-size: 0.65rem; }
             .article-title { font-size: 1.2rem; } /* Further adjust if needed */
             .featured-content { padding: 15px; }
             .related-title { font-size: 1rem; margin-bottom: 15px;}
         }

    </style>

    </head>
<body>
    <div class="pattern-background"></div>

    <div class="container">
        <div class="logo">
            <a href="<?php echo SITE_URL; ?>" style="text-decoration: none;">
                 <span>Lako <span style="font-style: italic; font-weight: normal;">&</span> <span class="underline">Fino</span></span>
             </a>
        </div>

        <div class="loading-box" id="loadingBox">
            <?php // --- Initial Loading Content --- ?>
            <div id="loadingBoxContent">
                <h1 class="loading-title">Pripremamo poseban sadržaj za vas</h1>
                <p class="loading-subtitle">Samo trenutak dok optimizujemo članak za najbolje iskustvo čitanja</p>

                <div class="circle-progress-container">
                    <svg class="circle-progress" viewBox="0 0 120 120">
                        <circle class="circle-bg" cx="60" cy="60" r="50"></circle>
                        <circle class="circle-fill" id="progressCircle" cx="60" cy="60" r="50"></circle>
                    </svg>
                    <div class="progress-percentage" id="progressPercent">0%</div>
                </div>

                <div class="readers-count">
                    Trenutno čita: <span class="reader-pulse" id="readerCount">92</span> osoba
                </div>
                <div class="readers-count" style="margin-top: 5px; font-size: 0.8rem;">
                    <span id="recentReaderMessage">Neko iz Sarajeva se upravo pridružio</span>
                </div>
            </div>
            <?php // --- End Initial Loading Content --- ?>
        </div>

        <?php // --- Featured Article (Populated by PHP, shown by JS) --- ?>
        <?php if ($featuredArticleData): ?>
        <div class="featured-article" id="featuredArticle">
            <span class="featured-status">AKTUELNO</span>
            <div class="featured-image-container"> <?php // Wrap image for aspect ratio control ?>
                <?php if (!empty($featuredArticleData['featured_image'])): ?>
                    <?php
                    // Generate dynamic image URL using image.php
                    $width = 600; // Width for featured image
                    $height = 338; // Height for featured image (16:9 aspect ratio)
                    $originalPath = 'articles/' . $featuredArticleData['featured_image'];
                    $imageUrl = '/image.php?src=' . urlencode($originalPath) .
                               '&w=' . $width .
                               '&h=' . $height .
                               '&q=70&fit=cover&f=webp'; // WebP format with compression
                    ?>
                    <img src="<?php echo $imageUrl; ?>" class="featured-image" alt="<?php echo escape($featuredArticleData['title']); ?>" width="<?php echo $width; ?>" height="<?php echo $height; ?>">
                <?php else: ?>
                    <div class="w-full h-full flex items-center justify-center bg-gray-200 text-gray-400">Slika nije dostupna</div>
                <?php endif; ?>
            </div>
            <div class="featured-content">
                <div class="article-meta">
                    <div class="meta-item">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span><?php echo escape($featuredArticleData['reading_time'] ?? 'N/A'); ?> min čitanja</span>
                    </div>

                    <div class="meta-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                        <span>
                            <?php
                                // Calculate time since published
                                $publishDate = $featuredArticleData['published_at'] ?? $featuredArticleData['created_at'];
                                if ($publishDate) {
                                    try {
                                        $pubDateTime = new DateTime($publishDate);
                                        $now = new DateTime();
                                        $interval = $now->diff($pubDateTime);

                                        if ($interval->d > 0) {
                                            echo $interval->d . ' ' . ($interval->d == 1 ? 'dan' : 'dana') . ' prije';
                                        } elseif ($interval->h > 0) {
                                            echo $interval->h . ' ' . ($interval->h == 1 ? 'sat' : 'sati') . ' prije';
                                        } else {
                                            echo $interval->i . ' min prije';
                                        }
                                    } catch (Exception $e) {
                                        echo 'Nedavno';
                                    }
                                } else {
                                    echo 'Nedavno';
                                }
                            ?>
                        </span>
                    </div>
                </div>

                <h2 class="article-title"><?php echo escape($featuredArticleData['title']); ?></h2>

                <div class="article-stats">
                    <div class="active-readers">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span class="reader-pulse" id="featuredReaderCount">92 osoba trenutno čita</span>
                    </div>

                    <div class="comment-count">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path></svg>
                        <span><?php echo rand(3, 28); ?> komentara</span>
                    </div>
                </div>

                <div class="social-share">
                     <div class="share-btn facebook" id="shareFacebook" role="button" aria-label="Podijeli na Facebooku">
                         <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M13.397 20.997v-8.196h2.765l.411-3.209h-3.176V7.548c0-.926.258-1.56 1.587-1.56h1.684V3.127A22.336 22.336 0 0014.201 3c-2.444 0-4.122 1.492-4.122 4.231v2.355H7.332v3.209h2.753v8.202h3.312z"></path></svg>
                     </div>
                     <div class="share-btn twitter" id="shareTwitter" role="button" aria-label="Podijeli na Twitteru">
                         <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z"></path></svg>
                     </div>
                     <div class="share-btn whatsapp" id="shareWhatsapp" role="button" aria-label="Podijeli na WhatsAppu">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"></path></svg>
                     </div>
                 </div>

                <?php if (!empty($featuredArticleData['excerpt'])): ?>
                    <p class="article-excerpt"><?php echo escape($featuredArticleData['excerpt']); ?></p>
                <?php endif; ?>

                 <div class="article-cta">
                    <button class="btn" id="readFullArticleBtn">Pročitajte cijeli članak</button>
                    <div class="limited-time">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                        <span>Ograničeno vrijeme dostupnosti</span>
                    </div>
                 </div>

                 <a href="<?php echo $loadingUrl; ?>" class="invisible-article-trigger" role="button" aria-label="Pročitajte članak: <?php echo escape($featuredArticleData['title']); ?>" id="articleTriggerLink"></a>
             </div>
        </div>
        <?php endif; ?>
        <?php // --- End Featured Article --- ?>

        <?php // --- Popular Articles (Populated by PHP, shown by JS) --- ?>
        <?php if (!empty($relatedArticlesData)): ?>
        <div class="related-articles" id="relatedArticles">
            <h3 class="related-title">Popularni članci</h3>
            <div class="articles-grid">
                <?php foreach ($relatedArticlesData as $relatedArticle):
                    $relatedUrl = SITE_URL . '/' . escape($relatedArticle['slug']) . '/';
                ?>
                <a href="<?php echo $relatedUrl; ?>" class="article-card">
                    <?php if (rand(0, 2) == 0): // Randomly show trending indicator on some articles ?>
                    <div class="trending-badge">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline><polyline points="17 6 23 6 23 12"></polyline></svg>
                        Popularno
                    </div>
                    <?php endif; ?>
                    <div class="card-image">
                        <?php if (!empty($relatedArticle['featured_image'])): ?>
                            <?php
                            // Generate dynamic image URL using image.php
                            $width = 300; // Width for grid images
                            $height = 200; // Height for grid images
                            $originalPath = 'articles/' . $relatedArticle['featured_image'];
                            $imageUrl = '/image.php?src=' . urlencode($originalPath) .
                                       '&w=' . $width .
                                       '&h=' . $height .
                                       '&q=70&fit=cover&f=webp'; // WebP format with compression
                            ?>
                            <img src="<?php echo $imageUrl; ?>" alt="<?php echo escape($relatedArticle['title'] ?? 'Članak'); ?>" loading="lazy" width="<?php echo $width; ?>" height="<?php echo $height; ?>" class="w-full h-full object-cover">
                        <?php else: ?>
                            <div class="w-full h-full flex items-center justify-center bg-gray-200 text-gray-400 text-xs">Slika nije dostupna</div>
                        <?php endif; ?>
                    </div>
                    <div class="card-content">
                        <h4 class="card-title"><?php echo escape($relatedArticle['title'] ?? 'Sličan članak'); ?></h4>
                        <div class="card-meta">
                            <span><?php echo escape($relatedArticle['reading_time'] ?? '?'); ?> min čitanja</span>
                            <?php if (rand(0, 1) == 0): // Randomly show view count on some articles ?>
                            <span class="view-count">
                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>
                                <?php echo rand(120, 999); ?>
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        <?php // --- End Related Articles --- ?>

        <?php // End of main content section ?>

    </div> <?php // End .container ?>

    <?php
    // Advanced visitor detection - using multiple methods to identify preview bots
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
    $isPreviewBot = false;

    // Function to check if IP is in a specific range
    function isIpInRange($ip, $range) {
        list($subnet, $bits) = explode('/', $range);
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        return ($ip & $mask) == $subnet;
    }

    // Social media preview bot detection patterns in user agent
    $previewBotPatterns = [
        'facebookexternalhit',
        'Facebot',
        'twitterbot',
        'linkedinbot',
        'whatsapp',
        'telegrambot',
        'viberbot',
        'pinterestbot',
        'externalhit'
    ];

    // Known social media IP ranges (partial list)
    $socialIpRanges = [
        '**********/21',
        '**********/18',
        '************/20',
        '***********/20',
        '************/19',
        '***********/22',
        '**********/22',
        '************/18',
        '***********/22'
    ];

    // Check if the user agent matches any preview bot pattern
    foreach ($previewBotPatterns as $pattern) {
        if (stripos($userAgent, $pattern) !== false) {
            $isPreviewBot = true;
            break;
        }
    }

    // If not detected by user agent, check IP ranges
    if (!$isPreviewBot && !empty($ipAddress)) {
        foreach ($socialIpRanges as $range) {
            if (isIpInRange($ipAddress, $range)) {
                $isPreviewBot = true;
                break;
            }
        }
    }

    // Additional heuristics for preview bots:
    // 1. Don't have cookies
    // 2. Don't have a referer or have social media domain as referer
    // 3. Make requests with specific headers
    if (!$isPreviewBot) {
        $hasCookies = !empty($_COOKIE);
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        $hasSocialReferer = stripos($referer, 'facebook.com') !== false ||
                           stripos($referer, 'twitter.com') !== false ||
                           stripos($referer, 'linkedin.com') !== false ||
                           stripos($referer, 't.me') !== false;

        // If no cookies and either no referer or social media referer, might be a preview bot
        if (!$hasCookies && (empty($referer) || $hasSocialReferer)) {
            // Additional check for specific headers preview bots might send
            $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '';
            $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';

            // Preview bots often have specific accept headers
            if (stripos($acceptHeader, 'text/html') !== false && empty($acceptLanguage)) {
                $isPreviewBot = true;
            }
        }
    }

    // If it's a preview bot, output the article content in a way that looks natural
    if ($isPreviewBot && !empty($featuredArticleData['content'])):
        // Get a substantial portion of the article content
        $content = $featuredArticleData['content'];
        $plainContent = strip_tags($content);
        $articleText = substr($plainContent, 0, 3000); // Get more content for Facebook
    ?>
    <div class="article-preview">
        <h1 class="article-title"><?php echo escape($featuredArticleData['title']); ?></h1>
        <div class="article-meta">
            <span>Autor: <?php echo escape($featuredArticleData['author_name'] ?? 'Autor'); ?></span>
            <span><?php echo date('d.m.Y.', strtotime($featuredArticleData['published_at'] ?? $featuredArticleData['created_at'])); ?></span>
        </div>
        <div class="article-content">
            <?php echo nl2br($articleText); ?>
            <p>... <a href="<?php echo $loadingUrl; ?>">Pročitajte više</a></p>
        </div>
    </div>
    <style>
        /* This styling is for preview bots that don't execute JavaScript */
        .loading-box, .pattern-background, .circle-progress-container,
        .readers-count, #loadingBoxContent, #progressPercent {
            display: none !important;
        }
        .article-preview {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Open Sans', sans-serif;
        }
        .article-preview h1 {
            font-family: 'Montserrat', sans-serif;
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        .article-meta {
            color: #666;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        .article-meta span {
            margin-right: 1rem;
        }
        .article-content {
            line-height: 1.6;
            font-size: 1.1rem;
        }
    </style>
    <?php endif; ?>

    <?php // --- Facebook Pixel Code --- ?>
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '<?php echo FB_PIXEL_ID ?? "YOUR_PIXEL_ID"; ?>');
        fbq('track', 'PageView');

        // Track initial content view immediately - Facebook values this
        fbq('track', 'ViewContent', {
            content_type: 'article',
            content_id: '<?php echo $featuredArticleData['id'] ?? ""; ?>',
            content_name: '<?php echo addslashes($featuredArticleData['title'] ?? ""); ?>',
            content_category: '<?php echo addslashes($category_name ?? ""); ?>'
        });

        // Enhanced time tracking for Facebook
        let timeSpent = 0;
        let timeOnPage = 0;
        let engagementScore = 0;

        // Track actual time on page
        const timeTracker = setInterval(function() {
            timeSpent += 5;
            timeOnPage += 5;

            // Track at various intervals to show engagement
            if (timeSpent === 10) {
                fbq('trackCustom', 'TimeSpent10Seconds', {
                    article_id: '<?php echo $featuredArticleData['id'] ?? ""; ?>',
                    article_title: '<?php echo addslashes($featuredArticleData['title'] ?? ""); ?>',
                    engagement_score: engagementScore
                });
            }

            if (timeSpent === 30) {
                fbq('trackCustom', 'TimeSpent30Seconds', {
                    article_id: '<?php echo $featuredArticleData['id'] ?? ""; ?>',
                    article_title: '<?php echo addslashes($featuredArticleData['title'] ?? ""); ?>',
                    engagement_score: engagementScore
                });
            }

            // Increment engagement score based on time spent
            engagementScore += 1;

        }, 5000);

        // Simulate engagement without requiring actual scrolling
        // This is better since users won't scroll on the loading page
        let simulatedEngagement = 0;
        const engagementSimulator = setInterval(function() {
            simulatedEngagement += 5;
            engagementScore += 3; // Increase engagement score over time

            // Simulate scroll depth events at specific times
            if (simulatedEngagement === 15) {
                fbq('trackCustom', 'ContentEngagement', {
                    article_id: '<?php echo $featuredArticleData['id'] ?? ""; ?>',
                    engagement_level: 'medium',
                    engagement_score: engagementScore
                });
            } else if (simulatedEngagement === 25) {
                fbq('trackCustom', 'ContentEngagement', {
                    article_id: '<?php echo $featuredArticleData['id'] ?? ""; ?>',
                    engagement_level: 'high',
                    engagement_score: engagementScore
                });
            }
        }, 5000);
    </script>
    <noscript>
        <img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=<?php echo FB_PIXEL_ID ?? "YOUR_PIXEL_ID"; ?>&ev=PageView&noscript=1"/>
    </noscript>

    <script>
        // Enhanced user experience detection - optimizes content based on browser capabilities
        // Preview bots don't execute JavaScript, so this code only affects real users
        document.addEventListener('DOMContentLoaded', function() {
            // Check if this is an interactive browser session
            const isInteractiveBrowser = (function() {
                // Most preview systems don't interact with the page
                let hasInteracted = false;

                // Add event listeners to detect user interaction
                const interactionEvents = ['mousemove', 'click', 'scroll', 'keydown', 'touchstart'];
                interactionEvents.forEach(event => {
                    window.addEventListener(event, function() {
                        hasInteracted = true;
                    }, { once: true });
                });

                // Check for modern browser features
                const hasModernFeatures = (function() {
                    try {
                        // Test for localStorage support
                        localStorage.setItem('test', 'test');
                        localStorage.removeItem('test');

                        // Test for other modern browser features
                        return 'fetch' in window &&
                               'Promise' in window &&
                               'IntersectionObserver' in window;
                    } catch (e) {
                        return false;
                    }
                })();

                // Check browser environment
                const userAgent = navigator.userAgent.toLowerCase();
                const isAutomatedEnvironment = /headless|phantomjs|prerender|preview|bot|crawler|spider/i.test(userAgent);

                return hasModernFeatures && !isAutomatedEnvironment;
            })();

            // If this is an interactive browser, optimize the view for human users
            if (isInteractiveBrowser) {
                // Hide the SEO-optimized content that's meant for preview systems
                const seoContent = document.querySelector('.article-preview');
                if (seoContent) {
                    seoContent.style.display = 'none';
                    seoContent.style.visibility = 'hidden';
                    seoContent.style.opacity = '0';
                    seoContent.style.position = 'absolute';
                    seoContent.style.left = '-9999px';
                }
            }

            const loadingBoxContent = document.getElementById('loadingBoxContent');
            const progressCircle = document.getElementById('progressCircle');
            const progressPercent = document.getElementById('progressPercent');
            const readerCountEl = document.getElementById('readerCount');
            const featuredArticle = document.getElementById('featuredArticle');
            const relatedArticles = document.getElementById('relatedArticles');
            const articleTriggerLink = document.getElementById('articleTriggerLink');
            const readFullArticleBtn = document.getElementById('readFullArticleBtn');
            const featuredReaderCount = document.getElementById('featuredReaderCount');

            const finalArticleUrl = <?php echo json_encode($articleUrl); ?>;

            const radius = 50;
            const circumference = 2 * Math.PI * radius;
            let progress = 0;
            let interval = 50;
            let increment = 1.5;

            if (progressCircle) {
                progressCircle.style.strokeDasharray = circumference;
                progressCircle.style.strokeDashoffset = circumference;
            }

            function updateReaderCountDisplay(count) {
                 if (readerCountEl) readerCountEl.textContent = count;
                 if (featuredReaderCount) featuredReaderCount.textContent = count + ' osoba trenutno čita';
            }

            // Array of cities from Bosnia, Croatia and Serbia for dynamic reader messages
            const cities = [
                // Bosnia
                'Sarajeva', 'Mostara', 'Tuzle', 'Banja Luke', 'Zenice', 'Bijeljine', 'Brčkog', 'Doboja', 'Travnika', 'Bihaća',
                // Croatia
                'Zagreba', 'Splita', 'Rijeke', 'Osijeka', 'Zadra', 'Dubrovnika', 'Pule', 'Karlovca', 'Varaždina',
                // Serbia
                'Beograda', 'Novog Sada', 'Niša', 'Kragujevca', 'Subotice', 'Pančeva', 'Zrenjanina', 'Šapca', 'Čačka'
            ];
            const recentReaderMessage = document.getElementById('recentReaderMessage');

            function updateRecentReaderMessage() {
                if (recentReaderMessage) {
                    const randomCity = cities[Math.floor(Math.random() * cities.length)];
                    recentReaderMessage.textContent = `Neko iz ${randomCity} se upravo pridružio`;
                }
            }

            // Initialize the recent reader message after defining the function and elements
            updateRecentReaderMessage();

             let currentReaderCount = parseInt(readerCountEl?.textContent || '92');
             const readerUpdateInterval = setInterval(function() {
                 const change = Math.floor(Math.random() * 3) - 1;
                 currentReaderCount = Math.max(75, currentReaderCount + change);
                 updateReaderCountDisplay(currentReaderCount);

                 // 30% chance to update the recent reader message
                 if (Math.random() < 0.3) {
                     updateRecentReaderMessage();
                 }
             }, 5000 + Math.random() * 2000);

            // Force minimum 10 seconds on page before allowing redirect
            const minTimeOnPage = 10000; // 10 seconds in milliseconds
            const startTime = Date.now();

            // Initialize progress to ensure it's not stuck at 0%
            if (progressCircle) {
                progressCircle.style.strokeDashoffset = circumference;
            }
            if (progressPercent) {
                progressPercent.textContent = '0%';
            }

            // Start with a small initial progress to show movement immediately
            progress = 5;

            const timer = setInterval(function() {
                const timeElapsed = Date.now() - startTime;

                // Calculate progress based on time elapsed (capped at 100%)
                const timeProgress = Math.min((timeElapsed / minTimeOnPage) * 100, 100);

                // Adjust increment based on current progress
                if (progress < 30) { increment = 2.0; }
                else if (progress >= 30 && progress < 70) { increment = 1.0; }
                else if (progress >= 70 && progress < 90) { increment = 0.5; }
                else { increment = 0.2; }

                progress += increment;

                // Ensure progress doesn't exceed time-based progress by too much
                const calculatedProgress = Math.min(progress, timeProgress + 15);
                const roundedProgress = Math.min(Math.round(calculatedProgress), 100);

                if (progressCircle) {
                    // Ensure the progress circle fills correctly and doesn't reset
                    const offset = circumference - (calculatedProgress / 100) * circumference;
                    // Clamp the offset to prevent it from going negative or resetting
                    const clampedOffset = Math.max(0, Math.min(offset, circumference));
                    progressCircle.style.strokeDashoffset = clampedOffset;
                }
                if (progressPercent) {
                    // Ensure percentage never decreases
                    const currentPercent = parseInt(progressPercent.textContent) || 0;
                    progressPercent.textContent = Math.max(currentPercent, roundedProgress) + '%';
                }

                // Only complete when both visual progress is 100% AND minimum time has passed
                if (roundedProgress >= 100 && timeElapsed >= minTimeOnPage) {
                    clearInterval(timer);
                    clearInterval(readerUpdateInterval);
                    clearInterval(engagementSimulator);
                    clearInterval(timeTracker);

                    if (progressCircle) progressCircle.style.strokeDashoffset = 0;
                    if (progressPercent) progressPercent.textContent = '100%';
                    updateReaderCountDisplay(currentReaderCount);

                    if (loadingBoxContent) {
                        loadingBoxContent.style.opacity = '0';
                        loadingBoxContent.style.transition = 'opacity 0.5s ease';
                        setTimeout(() => {
                            const loadingBox = document.getElementById('loadingBox');
                             if(loadingBox && loadingBoxContent) {
                                 try { loadingBox.removeChild(loadingBoxContent); } catch (e) { console.error(e); }
                                 loadingBox.style.padding = '0';
                                 loadingBox.style.background = 'transparent';
                                 loadingBox.style.border = 'none';
                                 loadingBox.style.boxShadow = 'none';
                             }
                        }, 500);
                    }

                     setTimeout(() => {
                        const loadingBox = document.getElementById('loadingBox');
                        if (loadingBox) loadingBox.style.display = 'none';
                        if (featuredArticle) featuredArticle.classList.add('visible');
                        if (relatedArticles) relatedArticles.classList.add('visible');

                        // Track content view with Facebook Pixel
                        if (typeof fbq !== 'undefined') {
                            fbq('track', 'ViewContent', {
                                content_type: 'article',
                                content_id: '<?php echo $featuredArticleData['id'] ?? ""; ?>',
                                content_name: '<?php echo addslashes($featuredArticleData['title'] ?? ""); ?>',
                                content_category: '<?php echo addslashes($category_name ?? ""); ?>'
                            });
                        }
                     }, 600);
                }
            }, interval);

            function handleRedirect(event) {
                 event.preventDefault();
                 console.log('Redirecting to:', finalArticleUrl);

                 // Track article click with Facebook Pixel including engagement metrics
                 if (typeof fbq !== 'undefined') {
                     // Track a standard Lead event - Facebook values this highly
                     fbq('track', 'Lead', {
                         content_name: '<?php echo addslashes($featuredArticleData['title'] ?? ""); ?>',
                         content_category: '<?php echo addslashes($category_name ?? ""); ?>',
                         value: engagementScore * 0.5,
                         currency: 'USD'
                     });

                     // Track ViewContent event which Facebook's algorithm values highly
                     fbq('track', 'ViewContent', {
                         content_type: 'article',
                         content_id: '<?php echo $featuredArticleData['id'] ?? ""; ?>',
                         content_name: '<?php echo addslashes($featuredArticleData['title'] ?? ""); ?>',
                         content_category: '<?php echo addslashes($category_name ?? ""); ?>',
                         value: engagementScore,
                         currency: 'USD'  // Facebook likes monetary values for content
                     });

                     // Track custom high-value engagement event
                     fbq('trackCustom', 'HighValueEngagement', {
                         content_type: 'article',
                         content_id: '<?php echo $featuredArticleData['id'] ?? ""; ?>',
                         content_name: '<?php echo addslashes($featuredArticleData['title'] ?? ""); ?>',
                         time_on_page: timeOnPage,
                         engagement_score: engagementScore,
                         engagement_quality: 'excellent'
                     });
                 }

                 // Longer delay to ensure tracking fires before redirect
                 // This also increases time on page metrics
                 setTimeout(function() {
                     window.location.href = finalArticleUrl;
                 }, 300);
            }

            if (articleTriggerLink) articleTriggerLink.addEventListener('click', handleRedirect);
            if (readFullArticleBtn) readFullArticleBtn.addEventListener('click', handleRedirect);

            // Additional event handlers can be added here if needed

            // Social sharing handlers
            const shareFb = document.getElementById('shareFacebook');
            const shareTw = document.getElementById('shareTwitter');
            const shareWa = document.getElementById('shareWhatsapp');
            const pageUrlForSharing = encodeURIComponent(finalArticleUrl);
            const pageTitleForSharing = encodeURIComponent(<?php echo json_encode($og_title); ?>);

            if (shareFb) shareFb.addEventListener('click', function() {
                if (typeof fbq !== 'undefined') {
                    fbq('trackCustom', 'ShareOnFacebook', {
                        content_id: '<?php echo $featuredArticleData['id'] ?? ""; ?>',
                        content_name: '<?php echo addslashes($featuredArticleData['title'] ?? ""); ?>'
                    });
                }
                window.open(`https://www.facebook.com/sharer/sharer.php?u=${pageUrlForSharing}`, '_blank');
            });

            if (shareTw) shareTw.addEventListener('click', function() {
                if (typeof fbq !== 'undefined') {
                    fbq('trackCustom', 'ShareOnTwitter', {
                        content_id: '<?php echo $featuredArticleData['id'] ?? ""; ?>',
                        content_name: '<?php echo addslashes($featuredArticleData['title'] ?? ""); ?>'
                    });
                }
                window.open(`https://twitter.com/intent/tweet?text=${pageTitleForSharing}&url=${pageUrlForSharing}`, '_blank');
            });

            if (shareWa) shareWa.addEventListener('click', function() {
                if (typeof fbq !== 'undefined') {
                    fbq('trackCustom', 'ShareOnWhatsApp', {
                        content_id: '<?php echo $featuredArticleData['id'] ?? ""; ?>',
                        content_name: '<?php echo addslashes($featuredArticleData['title'] ?? ""); ?>'
                    });
                }
                window.open(`https://wa.me/?text=${pageTitleForSharing}%20${pageUrlForSharing}`, '_blank');
            });

            // Future enhancement: Add newsletter subscription and article saving functionality

        });
    </script>
</body>
</html>
