<?php

namespace AuditSystem\Tests\Integration;

use PHPUnit\Framework\TestCase;
use AuditSystem\Controllers\AuditController;
use AuditSystem\Config\AuditConfig;
use AuditSystem\Services\ProgressTracker;
use AuditSystem\Services\FileScanner;
use AuditSystem\Services\AuditLogger;
use AuditSystem\Models\AuditResult;
use AuditSystem\Models\AuditStatus;
use AuditSystem\Exceptions\AuditException;
use AuditSystem\Exceptions\ConfigurationException;

/**
 * Integration tests for AuditController complete workflows
 */
class AuditControllerIntegrationTest extends TestCase
{
    private AuditController $controller;
    private AuditConfig $config;
    private ProgressTracker $progressTracker;
    private FileScanner $fileScanner;
    private AuditLogger $logger;
    private string $testDataDir;
    private string $testProgressFile;
    private string $testLogDir;

    protected function setUp(): void
    {
        parent::setUp();

        // Setup test directories
        $this->testDataDir = __DIR__ . '/../../test_data';
        $this->testProgressFile = $this->testDataDir . '/test_progress.json';
        $this->testLogDir = $this->testDataDir . '/logs';

        // Create test directories
        if (!is_dir($this->testDataDir)) {
            mkdir($this->testDataDir, 0755, true);
        }
        if (!is_dir($this->testLogDir)) {
            mkdir($this->testLogDir, 0755, true);
        }

        // Initialize components
        $this->config = AuditConfig::getInstance();
        $this->config->set('audit.target_directory', $this->testDataDir . '/sample_code');
        $this->config->set('audit.progress_file', $this->testProgressFile);

        $this->progressTracker = new ProgressTracker($this->testProgressFile);
        $this->fileScanner = new FileScanner($this->config);
        $this->logger = new AuditLogger($this->testLogDir);

        $this->controller = new AuditController(
            $this->config,
            $this->progressTracker,
            $this->fileScanner,
            $this->logger
        );

        // Create sample test files
        $this->createSampleTestFiles();

        // Register mock analyzers
        $this->registerMockAnalyzers();
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        // Clean up test files
        $this->cleanupTestFiles();
    }

    /**
     * Test complete audit workflow from start to finish
     */
    public function testCompleteAuditWorkflow(): void
    {
        // Start audit
        $result = $this->controller->startAudit([
            'clear_logs' => true
        ]);

        $this->assertInstanceOf(AuditResult::class, $result);
        $this->assertNotEmpty($result->findings);
        $this->assertNotEmpty($result->fileStatus);

        // Verify audit status after completion
        $status = $this->controller->getAuditStatus();
        $this->assertEquals('completed', $status->phase);
        $this->assertEquals(100.0, $status->completionPercentage);
        $this->assertFalse($status->isRunning);
        $this->assertNull($status->currentFile);
    }

    /**
     * Test audit resume functionality
     */
    public function testAuditResumeWorkflow(): void
    {
        // Start audit and simulate interruption
        $this->simulateInterruptedAudit();

        // Verify progress exists
        $status = $this->controller->getAuditStatus();
        $this->assertGreaterThan(0, $status->processedFiles);
        $this->assertLessThan(100.0, $status->completionPercentage);

        // Resume audit
        $result = $this->controller->resumeAudit();

        $this->assertInstanceOf(AuditResult::class, $result);

        // Verify completion
        $finalStatus = $this->controller->getAuditStatus();
        $this->assertEquals('completed', $finalStatus->phase);
        $this->assertEquals(100.0, $finalStatus->completionPercentage);
    }

    /**
     * Test error handling during audit execution
     */
    public function testAuditErrorHandling(): void
    {
        // Register analyzer that will fail
        $this->controller->registerAnalyzer(new FailingMockAnalyzer());

        // Audit should complete despite analyzer failures
        $result = $this->controller->startAudit([]);

        $this->assertInstanceOf(AuditResult::class, $result);

        // Check that errors were logged
        $logs = $this->logger->getRecentLogs(50, 'error');
        $this->assertNotEmpty($logs);
    }

    /**
     * Test configuration validation
     */
    public function testConfigurationValidation(): void
    {
        $this->expectException(ConfigurationException::class);

        $this->controller->startAudit([
            'audit.timeout' => -1 // Invalid timeout
        ]);
    }

    /**
     * Test concurrent audit prevention
     */
    public function testConcurrentAuditPrevention(): void
    {
        // Mock running state
        $reflection = new \ReflectionClass($this->controller);
        $isRunningProperty = $reflection->getProperty('isRunning');
        $isRunningProperty->setAccessible(true);
        $isRunningProperty->setValue($this->controller, true);

        $this->expectException(AuditException::class);
        $this->expectExceptionMessage('Audit is already running');

        $this->controller->startAudit([]);
    }

    /**
     * Test resume without existing progress
     */
    public function testResumeWithoutProgress(): void
    {
        // Ensure no progress file exists
        if (file_exists($this->testProgressFile)) {
            unlink($this->testProgressFile);
        }

        $this->expectException(AuditException::class);
        $this->expectExceptionMessage('No existing audit progress found');

        $this->controller->resumeAudit();
    }

    /**
     * Test audit with invalid target directory
     */
    public function testAuditWithInvalidTargetDirectory(): void
    {
        $this->expectException(AuditException::class);

        $this->controller->startAudit([
            'audit.target_directory' => '/nonexistent/directory'
        ]);
    }

    /**
     * Test progress tracking accuracy
     */
    public function testProgressTrackingAccuracy(): void
    {
        // Start audit and check progress at different stages
        $initialStatus = $this->controller->getAuditStatus();
        $this->assertEquals('not_started', $initialStatus->phase);

        // Start audit in background (simulate)
        $result = $this->controller->startAudit([]);

        // Final status should show completion
        $finalStatus = $this->controller->getAuditStatus();
        $this->assertEquals('completed', $finalStatus->phase);
        $this->assertEquals(100.0, $finalStatus->completionPercentage);
    }

    /**
     * Test logging functionality
     */
    public function testLoggingFunctionality(): void
    {
        // Clear logs
        $this->logger->clearLogs();

        // Run audit
        $this->controller->startAudit(['clear_logs' => false]);

        // Check that logs were created
        $infoLogs = $this->logger->getRecentLogs(50, 'info');
        $this->assertNotEmpty($infoLogs);

        $debugLogs = $this->logger->getRecentLogs(50, 'debug');
        $this->assertNotEmpty($debugLogs);

        // Verify log content
        $allLogs = $this->logger->getRecentLogs(100);
        $logContent = implode(' ', $allLogs);

        $this->assertStringContainsString('Starting new audit', $logContent);
        $this->assertStringContainsString('Audit execution completed', $logContent);
    }

    /**
     * Test performance metrics logging
     */
    public function testPerformanceMetricsLogging(): void
    {
        $startTime = microtime(true);

        $this->controller->startAudit([]);

        $duration = microtime(true) - $startTime;

        // Performance should be logged
        $this->logger->logPerformance('test_audit', $duration, [
            'test_metric' => 'test_value'
        ]);

        // Verify performance log exists
        $this->assertTrue(file_exists($this->testLogDir . '/performance.log'));
    }

    /**
     * Test memory usage during large audit
     */
    public function testMemoryUsageDuringAudit(): void
    {
        $initialMemory = memory_get_usage();

        // Create more test files for memory test
        $this->createLargeTestDataSet();

        $result = $this->controller->startAudit([]);

        $finalMemory = memory_get_usage();
        $memoryIncrease = $finalMemory - $initialMemory;

        // Memory increase should be reasonable (less than 50MB for test)
        $this->assertLessThan(50 * 1024 * 1024, $memoryIncrease);

        $this->assertInstanceOf(AuditResult::class, $result);
    }

    /**
     * Create sample test files for audit
     */
    private function createSampleTestFiles(): void
    {
        $sampleCodeDir = $this->testDataDir . '/sample_code';
        if (!is_dir($sampleCodeDir)) {
            mkdir($sampleCodeDir, 0755, true);
        }

        // Create sample PHP file
        file_put_contents($sampleCodeDir . '/test.php', '<?php
class TestClass {
    public function testMethod() {
        return "test";
    }
}
');

        // Create sample HTML file
        file_put_contents($sampleCodeDir . '/test.html', '<html>
<head><title>Test</title></head>
<body><h1>Test Page</h1></body>
</html>');

        // Create sample CSS file
        file_put_contents($sampleCodeDir . '/test.css', 'body {
    margin: 0;
    padding: 0;
}');

        // Create sample JS file
        file_put_contents($sampleCodeDir . '/test.js', 'function test() {
    console.log("test");
}');
    }

    /**
     * Create larger test dataset for performance testing
     */
    private function createLargeTestDataSet(): void
    {
        $sampleCodeDir = $this->testDataDir . '/sample_code';

        // Create 50 test files
        for ($i = 1; $i <= 50; $i++) {
            file_put_contents($sampleCodeDir . "/test_{$i}.php", "<?php
class TestClass{$i} {
    public function method1() { return 'test'; }
    public function method2() { return 'test'; }
    public function method3() { return 'test'; }
}
");
        }
    }

    /**
     * Register mock analyzers for testing
     */
    private function registerMockAnalyzers(): void
    {
        $this->controller->registerAnalyzer(new MockPHPAnalyzer());
        $this->controller->registerAnalyzer(new MockSecurityAnalyzer());
    }

    /**
     * Simulate interrupted audit for resume testing
     */
    private function simulateInterruptedAudit(): void
    {
        // Create partial progress
        $progress = new \AuditSystem\Models\AuditProgress();
        $progress->setPendingFiles([
            $this->testDataDir . '/sample_code/test.php',
            $this->testDataDir . '/sample_code/test.html'
        ]);
        $progress->markFileCompleted($this->testDataDir . '/sample_code/test.php', []);
        $progress->currentPhase = 'analyzing';

        $this->progressTracker->saveProgress($progress);
    }

    /**
     * Clean up test files
     */
    private function cleanupTestFiles(): void
    {
        $this->removeDirectory($this->testDataDir);
    }

    /**
     * Recursively remove directory
     */
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }
}

/**
 * Mock analyzer for testing
 */
class MockPHPAnalyzer implements \AuditSystem\Interfaces\AnalyzerInterface
{
    public function analyze(string $filePath, string $content): array
    {
        return [
            new \AuditSystem\Models\Finding(
                $filePath,
                1,
                'quality',
                'low',
                'NON_PRIORITY',
                'Mock finding',
                'Mock recommendation'
            )
        ];
    }

    public function getSupportedFileTypes(): array
    {
        return ['php'];
    }

    public function getName(): string
    {
        return 'MockPHPAnalyzer';
    }
}

/**
 * Mock security analyzer for testing
 */
class MockSecurityAnalyzer implements \AuditSystem\Interfaces\AnalyzerInterface
{
    public function analyze(string $filePath, string $content): array
    {
        return [
            new \AuditSystem\Models\Finding(
                $filePath,
                1,
                'security',
                'medium',
                'PRIORITY_AREA',
                'Mock security finding',
                'Mock security recommendation'
            )
        ];
    }

    public function getSupportedFileTypes(): array
    {
        return ['php', 'html'];
    }

    public function getName(): string
    {
        return 'MockSecurityAnalyzer';
    }
}

/**
 * Mock analyzer that fails for error testing
 */
class FailingMockAnalyzer implements \AuditSystem\Interfaces\AnalyzerInterface
{
    public function analyze(string $filePath, string $content): array
    {
        throw new \Exception('Mock analyzer failure');
    }

    public function getSupportedFileTypes(): array
    {
        return ['*'];
    }

    public function getName(): string
    {
        return 'FailingMockAnalyzer';
    }

}