<?php
// Core configuration and authentication
require_once '../config.php'; // Adjust path as needed
require_once 'includes/auth_check.php'; // Check if admin is logged in
require_once '../includes/functions.php'; // General functions like escape(), formatDate(), selected()

// --- Page Setup ---
$admin_page_title = 'Website Analytics';
$error_message = $GLOBALS['error_message'] ?? null; // Use global error message if set by functions
$selected_period = $_GET['period'] ?? '30d'; // Default to 30 days
$limit = 10; // Limit for tables

// --- Helper function to get date interval string for SQL ---
if (!function_exists('getSqlDateInterval')) { // Avoid redeclaring if in functions.php
    function getSqlDateInterval(string $period): string {
        return match ($period) {
            '7d' => 'INTERVAL 7 DAY',
            '90d' => 'INTERVAL 90 DAY',
            'year' => 'INTERVAL 1 YEAR',
            'all' => 'INTERVAL 10 YEAR', // Effectively all time for recent data
            default => 'INTERVAL 30 DAY', // Default is 30d
        };
    }
}
$date_interval_sql = getSqlDateInterval($selected_period);


// --- Website Analytics Data Fetching Functions (using page_views) ---

function getWebsitePerformanceStats(PDO $pdo, string $date_interval_sql): array {
    $stats = [
        'total_page_views' => 0,
        'avg_time_on_site_seconds' => 0, // Placeholder
        'bounce_rate_percent' => 0, // Placeholder
        'internal_link_ctr_percent' => 0, // Placeholder
        'page_views_change_percent' => 0, // Placeholder
        'time_on_site_change_percent' => 0, // Placeholder
        'bounce_rate_change_percent' => 0, // Placeholder
        'internal_link_ctr_change_percent' => 0, // Placeholder
    ];
    try {
        // Fetch real page views from the new table
        $sql = "SELECT COUNT(*) FROM page_views WHERE timestamp >= DATE_SUB(NOW(), $date_interval_sql)";
        $stmt = $pdo->query($sql);
        $stats['total_page_views'] = (int)$stmt->fetchColumn();

        // --- Placeholder Logic (Replace with real calculations if session/event tracking is added) ---
        if ($stats['total_page_views'] > 0) {
            // These would require session analysis which is complex
            $stats['avg_time_on_site_seconds'] = rand(120, 300); // Dummy avg time
            $stats['bounce_rate_percent'] = rand(250, 550) / 10; // Dummy bounce rate
            // This requires event tracking
            $stats['internal_link_ctr_percent'] = rand(50, 150) / 10; // Dummy CTR
            // These require comparing to a previous period
            $stats['page_views_change_percent'] = rand(-20, 30);
            $stats['time_on_site_change_percent'] = rand(-10, 15);
            $stats['bounce_rate_change_percent'] = rand(-5, 5);
            $stats['internal_link_ctr_change_percent'] = rand(-3, 8);
        }
        // --- End Placeholder Logic ---

    } catch (PDOException $e) {
        if(!str_contains($e->getMessage(), 'page_views')) {
            error_log("Error fetching page views stats: " . $e->getMessage());
            $GLOBALS['error_message'] = "Database error fetching page views. Ensure 'page_views' table exists.";
        } else {
             // Table doesn't exist, use default placeholders
             $stats['total_page_views'] = 0; // Show 0 if table missing
             $GLOBALS['error_message'] = "Page view tracking table ('page_views') not found. Displaying placeholder data for other metrics.";
        }
         // Set placeholder values if query failed or table missing
         $stats['avg_time_on_site_seconds'] = $stats['avg_time_on_site_seconds'] ?: 227;
         $stats['bounce_rate_percent'] = $stats['bounce_rate_percent'] ?: 32.6;
         $stats['internal_link_ctr_percent'] = $stats['internal_link_ctr_percent'] ?: 8.7;
         $stats['page_views_change_percent'] = $stats['page_views_change_percent'] ?: 18.3;
         $stats['time_on_site_change_percent'] = $stats['time_on_site_change_percent'] ?: 5.2;
         $stats['bounce_rate_change_percent'] = $stats['bounce_rate_change_percent'] ?: -2.1;
         $stats['internal_link_ctr_change_percent'] = $stats['internal_link_ctr_change_percent'] ?: 1.2;
    }
    return $stats;
}

// --- Placeholder Functions (Keep as is unless tracking is implemented) ---
function getLoadingTrickStats(PDO $pdo, string $period = '30d'): array {
    // Placeholder data - Requires specific tracking for loading.php views and conversions.
    return [ 'loading_trick_views' => 43576, 'loading_trick_conversions' => 34251, 'loading_trick_views_change_percent' => 24.3, 'loading_trick_conversion_change_percent' => 5.8, 'avg_wait_time_seconds' => 14.2, 'avg_wait_time_change_percent' => -8.6, 'wait_time_distribution' => [ '0-5s' => 12.4, '5-10s' => 23.7, '10-15s' => 43.6, '15+s' => 20.3 ], 'abandonment_rate_percent' => 21.4, 'abandonment_rate_change_percent' => -5.8, 'abandonment_points' => [ 'Initial loading (0-5s)' => 38.8, 'Progress 90-100%' => 26.6, 'Button interaction' => 19.6 ] ];
}
function getTopPerformingArticles(PDO $pdo, string $sortBy = 'views', string $date_interval_sql = 'INTERVAL 30 DAY', int $limit = 5): array {
    // Fetches based on simple views from 'articles' table. Needs join for comments/other metrics.
    $orderByClause = 'a.views DESC'; // Default
    if ($sortBy === 'comments') { $orderByClause = '(SELECT COUNT(*) FROM comments c WHERE c.article_id = a.id AND c.is_approved = 1) DESC'; }
    try {
        $sql = "SELECT a.id, a.title, a.views, au.name as author_name, (SELECT COUNT(*) FROM comments c WHERE c.article_id = a.id AND c.is_approved = 1) as comment_count FROM articles a LEFT JOIN authors au ON a.author_id = au.id WHERE a.status = 'published' ORDER BY $orderByClause LIMIT :limit";
        $stmt = $pdo->prepare($sql); $stmt->bindParam(':limit', $limit, PDO::PARAM_INT); $stmt->execute(); $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($articles as &$article) { $article['loading_views'] = $article['views'] > 0 ? rand((int)($article['views'] * 0.3), (int)($article['views'] * 0.6)) : 0; $article['conversion_rate'] = $article['loading_views'] > 0 ? rand(700, 950) / 10 : 0; $article['link_ctr'] = $article['views'] > 0 ? rand(50, 150) / 10 : 0; $article['avg_read_time_seconds'] = rand(180, 480); } unset($article); return $articles;
    } catch (PDOException $e) { error_log("Error fetching top articles: " . $e->getMessage()); $GLOBALS['error_message'] = "Database error fetching top articles."; return []; }
}
function getCtrByRecommendationType(PDO $pdo, string $period = '30d'): array { /* Placeholder */ return [ 'Time-sensitive offers' => 21.7, '"Live" reader counters' => 18.3, 'Related articles (image)' => 14.9, 'Related articles (text)' => 9.2, '"Also read" footer links' => 7.4 ]; }
function getCtrByPosition(PDO $pdo, string $period = '30d'): array { /* Placeholder */ return [ 'First paragraph (intro)' => 8.7, '33% through content' => 19.4, '66% through content' => 15.3, 'Before conclusion' => 11.5, 'After conclusion' => 16.8 ]; }
// --- End Placeholder Functions ---


// --- Fetch Data ---
$websitePerformance = getWebsitePerformanceStats($pdo, $date_interval_sql);
$loadingTrickStats = getLoadingTrickStats($pdo, $selected_period); // Using placeholder
$topArticlesByViews = getTopPerformingArticles($pdo, 'views', $date_interval_sql, 5);
$topArticlesByEngagement = getTopPerformingArticles($pdo, 'comments', $date_interval_sql, 5);
$topArticlesByTrick = getTopPerformingArticles($pdo, 'loading_views', $date_interval_sql, 5); // Uses placeholder sort
$ctrByType = getCtrByRecommendationType($pdo, $selected_period); // Using placeholder
$ctrByPosition = getCtrByPosition($pdo, $selected_period); // Using placeholder

// Include the admin header
include 'includes/header.php';
?>

<script defer src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.13.5/cdn.min.js"></script>

<header class="h-16 bg-white border-b border-border flex items-center justify-between px-6 flex-shrink-0 sticky top-0 z-30">
    <div class="flex items-center"> <h1 class="text-xl font-montserrat font-bold text-dark"><?php echo escape($admin_page_title); ?></h1> </div>
    <div class="flex items-center space-x-4">
        <form method="GET" action="analytics.php" class="relative">
            <select class="form-select h-10 pr-10 pl-3 py-2 text-sm appearance-none" name="period" onchange="this.form.submit()">
                <option value="7d" <?php selected($selected_period, '7d'); ?>>Last 7 days</option>
                <option value="30d" <?php selected($selected_period, '30d'); ?>>Last 30 days</option>
                <option value="90d" <?php selected($selected_period, '90d'); ?>>Last 90 days</option>
                <option value="year" <?php selected($selected_period, 'year'); ?>>This year</option>
                <option value="all" <?php selected($selected_period, 'all'); ?>>All time</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700"> <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /></svg> </div>
        </form>
        <button class="btn-secondary text-sm" disabled title="Export Not Implemented"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" /></svg> Export Data </button>
    </div>
</header>

<div class="flex-1 overflow-y-auto bg-background">
    <div class="p-6 space-y-6">

        <?php if (!empty($error_message)): ?>
        <div class="mb-4 bg-yellow-100 border border-yellow-300 text-yellow-800 px-4 py-3 rounded-lg text-sm shadow-sm"> <?php echo escape($error_message); ?> </div>
        <?php endif; ?>

        <div class="mb-6">
             <h2 class="text-lg font-montserrat font-bold mb-4 text-gray-700">Website Performance Overview</h2>
             <p class="text-xs text-gray-500 mb-4 italic">Note: Page Views from DB log. Other stats are placeholders requiring GA/Advanced Tracking.</p>
             <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="metric-card"> <div class="flex items-center justify-between mb-2"> <h3 class="text-sm font-semibold text-gray-500">TOTAL PAGE VIEWS</h3> <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full"><?php echo escape(ucfirst($selected_period)); ?></span> </div> <div class="flex items-baseline"> <span class="metric-value"><?php echo number_format($websitePerformance['total_page_views']); ?></span> <span class="<?php echo $websitePerformance['page_views_change_percent'] >= 0 ? 'metric-change-positive' : 'metric-change-negative'; ?>"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $websitePerformance['page_views_change_percent'] >= 0 ? 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6' : 'M13 17h8m0 0V9m0 8l-8-8-4 4-6-6'; ?>" /> </svg> <?php echo abs($websitePerformance['page_views_change_percent']); ?>% </span> </div> <span class="metric-label">vs. previous period (placeholder)</span> </div>
                <div class="metric-card"> <div class="flex items-center justify-between mb-2"> <h3 class="text-sm font-semibold text-gray-500">AVG. TIME ON SITE</h3> <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full"><?php echo escape(ucfirst($selected_period)); ?></span> </div> <div class="flex items-baseline"> <span class="metric-value"><?php echo floor($websitePerformance['avg_time_on_site_seconds'] / 60); ?>m <?php echo $websitePerformance['avg_time_on_site_seconds'] % 60; ?>s</span> <span class="<?php echo $websitePerformance['time_on_site_change_percent'] >= 0 ? 'metric-change-positive' : 'metric-change-negative'; ?>"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $websitePerformance['time_on_site_change_percent'] >= 0 ? 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6' : 'M13 17h8m0 0V9m0 8l-8-8-4 4-6-6'; ?>" /> </svg> <?php echo abs($websitePerformance['time_on_site_change_percent']); ?>% </span> </div> <span class="metric-label">vs. previous period (placeholder)</span> </div>
                <div class="metric-card"> <div class="flex items-center justify-between mb-2"> <h3 class="text-sm font-semibold text-gray-500">BOUNCE RATE</h3> <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full"><?php echo escape(ucfirst($selected_period)); ?></span> </div> <div class="flex items-baseline"> <span class="metric-value"><?php echo $websitePerformance['bounce_rate_percent']; ?>%</span> <span class="<?php echo $websitePerformance['bounce_rate_change_percent'] <= 0 ? 'metric-change-positive' : 'metric-change-negative'; ?>"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $websitePerformance['bounce_rate_change_percent'] <= 0 ? 'M13 17h8m0 0V9m0 8l-8-8-4 4-6-6' : 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6'; ?>" /> </svg> <?php echo abs($websitePerformance['bounce_rate_change_percent']); ?>% </span> </div> <span class="metric-label">vs. previous period (placeholder)</span> </div>
                <div class="metric-card"> <div class="flex items-center justify-between mb-2"> <h3 class="text-sm font-semibold text-gray-500">INTERNAL LINK CTR</h3> <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full"><?php echo escape(ucfirst($selected_period)); ?></span> </div> <div class="flex items-baseline"> <span class="metric-value"><?php echo $websitePerformance['internal_link_ctr_percent']; ?>%</span> <span class="<?php echo $websitePerformance['internal_link_ctr_change_percent'] >= 0 ? 'metric-change-positive' : 'metric-change-negative'; ?>"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo $websitePerformance['internal_link_ctr_change_percent'] >= 0 ? 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6' : 'M13 17h8m0 0V9m0 8l-8-8-4 4-6-6'; ?>" /> </svg> <?php echo abs($websitePerformance['internal_link_ctr_change_percent']); ?>% </span> </div> <span class="metric-label">vs. previous period (placeholder)</span> </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-border overflow-hidden mb-6" x-data="{ tab: 'views' }">
             <div class="px-6 py-4 border-b border-border flex justify-between items-center"> <h2 class="text-lg font-montserrat font-bold text-gray-800">Top Performing Articles</h2> <div class="flex border border-gray-200 rounded overflow-hidden"> <button @click="tab = 'views'" :class="tab === 'views' ? 'tab-button-active' : 'tab-button-inactive'" class="tab-button">By Views</button> <button @click="tab = 'engagement'" :class="tab === 'engagement' ? 'tab-button-active' : 'tab-button-inactive'" class="tab-button">By Engagement</button> <button @click="tab = 'trick'" :class="tab === 'trick' ? 'tab-button-active' : 'tab-button-inactive'" class="tab-button">By Loading Trick</button> </div> </div>
            <div class="p-4"> <div class="overflow-x-auto">
                    <table class="min-w-full table" x-show="tab === 'views'"> <thead> <tr><th>Article</th><th>Author</th><th>Views</th><th>Comments</th><th>Avg. Read Time</th></tr> </thead> <tbody class="divide-y divide-border"> <?php if(empty($topArticlesByViews)): ?> <tr><td colspan="5" class="text-center py-4 text-gray-500">No article data available.</td></tr> <?php else: ?> <?php foreach ($topArticlesByViews as $article): ?> <tr> <td> <a href="article_form.php?id=<?php echo $article['id']; ?>" class="text-sm font-medium text-gray-900 hover:text-primary truncate block max-w-xs" title="<?php echo escape($article['title']); ?>"> <?php echo escape($article['title']); ?> </a> </td> <td><?php echo escape($article['author_name'] ?? 'N/A'); ?></td> <td><?php echo number_format($article['views']); ?></td> <td><?php echo number_format($article['comment_count']); ?></td> <td><?php echo floor($article['avg_read_time_seconds'] / 60); ?>m <?php echo $article['avg_read_time_seconds'] % 60; ?>s</td> </tr> <?php endforeach; ?> <?php endif; ?> </tbody> </table>
                     <table class="min-w-full table" x-show="tab === 'engagement'" style="display: none;"> <thead> <tr><th>Article</th><th>Author</th><th>Comments</th><th>Views</th><th>Avg. Read Time</th></tr> </thead> <tbody class="divide-y divide-border"> <?php if(empty($topArticlesByEngagement)): ?> <tr><td colspan="5" class="text-center py-4 text-gray-500">No article data available.</td></tr> <?php else: ?> <?php foreach ($topArticlesByEngagement as $article): ?> <tr> <td><a href="article_form.php?id=<?php echo $article['id']; ?>" class="text-sm font-medium text-gray-900 hover:text-primary truncate block max-w-xs" title="<?php echo escape($article['title']); ?>"><?php echo escape($article['title']); ?></a></td> <td><?php echo escape($article['author_name'] ?? 'N/A'); ?></td> <td><?php echo number_format($article['comment_count']); ?></td> <td><?php echo number_format($article['views']); ?></td> <td><?php echo floor($article['avg_read_time_seconds'] / 60); ?>m <?php echo $article['avg_read_time_seconds'] % 60; ?>s</td> </tr> <?php endforeach; ?> <?php endif; ?> </tbody> </table>
                     <table class="min-w-full table" x-show="tab === 'trick'" style="display: none;"> <caption class="text-xs text-left text-gray-500 p-2 italic">Loading Views & Conversion are placeholder data.</caption> <thead> <tr><th>Article</th><th>Author</th><th>Loading Views</th><th>Conversion %</th><th>Total Views</th></tr> </thead> <tbody class="divide-y divide-border"> <?php if(empty($topArticlesByTrick)): ?> <tr><td colspan="5" class="text-center py-4 text-gray-500">No article data available.</td></tr> <?php else: ?> <?php foreach ($topArticlesByTrick as $article): ?> <tr> <td><a href="article_form.php?id=<?php echo $article['id']; ?>" class="text-sm font-medium text-gray-900 hover:text-primary truncate block max-w-xs" title="<?php echo escape($article['title']); ?>"><?php echo escape($article['title']); ?></a></td> <td><?php echo escape($article['author_name'] ?? 'N/A'); ?></td> <td><?php echo number_format($article['loading_views']); ?></td> <td><?php echo $article['conversion_rate']; ?>%</td> <td><?php echo number_format($article['views']); ?></td> </tr> <?php endforeach; ?> <?php endif; ?> </tbody> </table>
            </div> </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-border overflow-hidden mb-6"> <div class="px-6 py-4 border-b border-border"> <h2 class="text-lg font-montserrat font-bold text-gray-800">In-Article CTR Analysis (Placeholder)</h2> <p class="text-xs text-gray-500 mt-1 italic">Requires detailed click tracking with position/type data.</p> </div> <div class="p-6"> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <div class="bg-gray-50 p-4 rounded-lg border border-gray-200"> <h4 class="text-sm font-semibold text-gray-700 mb-3">CTR by Recommendation Type</h4> <div class="space-y-3"> <?php foreach ($ctrByType as $type => $ctr): ?> <div> <div class="flex justify-between text-xs mb-1"> <span><?php echo escape($type); ?></span> <span class="font-medium"><?php echo $ctr; ?>%</span> </div> <div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden"> <div class="bg-success h-full rounded-full" style="width: <?php echo $ctr; ?>%"></div> </div> </div> <?php endforeach; ?> </div> </div> <div class="bg-gray-50 p-4 rounded-lg border border-gray-200"> <h4 class="text-sm font-semibold text-gray-700 mb-3">CTR by Position in Article</h4> <div class="space-y-3"> <?php foreach ($ctrByPosition as $position => $ctr): ?> <div> <div class="flex justify-between text-xs mb-1"> <span><?php echo escape($position); ?></span> <span class="font-medium"><?php echo $ctr; ?>%</span> </div> <div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden"> <div class="bg-info h-full rounded-full" style="width: <?php echo $ctr; ?>%"></div> </div> </div> <?php endforeach; ?> </div> </div> </div> </div> </div>

        <div class="bg-white rounded-xl shadow-sm border border-border overflow-hidden"> <div class="px-6 py-4 border-b border-border"> <h2 class="text-lg font-montserrat font-bold text-gray-800">Loading Trick Performance (Placeholder)</h2> <p class="text-xs text-gray-500 mt-1 italic">Requires tracking loading page views and conversions.</p> </div> <div class="p-6"> <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6"> <div class="metric-card"> <h3 class="text-sm font-semibold text-gray-500 mb-2">LOADING TRICK VIEWS</h3> <span class="metric-value"><?php echo number_format($loadingTrickStats['loading_trick_views']); ?></span> </div> <div class="metric-card"> <h3 class="text-sm font-semibold text-gray-500 mb-2">CONVERSION RATE</h3> <span class="metric-value"><?php echo round(($loadingTrickStats['loading_trick_conversions'] / ($loadingTrickStats['loading_trick_views'] ?: 1)) * 100, 1); ?>%</span> </div> <div class="metric-card"> <h3 class="text-sm font-semibold text-gray-500 mb-2">AVG. WAIT TIME</h3> <span class="metric-value"><?php echo round($loadingTrickStats['avg_wait_time_seconds'], 1); ?>s</span> </div> </div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <div class="bg-gray-50 p-4 rounded-lg border border-gray-200"> <h4 class="text-sm font-semibold text-gray-700 mb-3">Conversion Rate by Device</h4> <div class="space-y-3"> <div><div class="flex justify-between text-xs mb-1"><span>Mobile</span><span class="font-medium">83.2%</span></div><div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden"><div class="bg-success h-full rounded-full" style="width: 83.2%"></div></div></div> <div><div class="flex justify-between text-xs mb-1"><span>Desktop</span><span class="font-medium">72.6%</span></div><div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden"><div class="bg-success h-full rounded-full" style="width: 72.6%"></div></div></div> <div><div class="flex justify-between text-xs mb-1"><span>Tablet</span><span class="font-medium">77.8%</span></div><div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden"><div class="bg-success h-full rounded-full" style="width: 77.8%"></div></div></div> </div> </div> <div class="bg-gray-50 p-4 rounded-lg border border-gray-200"> <h4 class="text-sm font-semibold text-gray-700 mb-3">Loading Trick Abandonment</h4> <div class="space-y-3"> <div><div class="flex justify-between text-xs mb-1"><span>Total Abandonment Rate</span><span class="font-medium"><?php echo $loadingTrickStats['abandonment_rate_percent']; ?>%</span></div><div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden"><div class="bg-danger h-full rounded-full" style="width: <?php echo $loadingTrickStats['abandonment_rate_percent']; ?>%"></div></div></div> <div class="mt-2"><div class="text-sm font-medium text-gray-700 mb-1">Top Abandonment Points</div> <?php foreach($loadingTrickStats['abandonment_points'] as $point => $percent): ?> <div><div class="flex justify-between text-xs mb-1"><span><?php echo escape($point); ?></span><span class="font-medium"><?php echo $percent; ?>%</span></div><div class="w-full h-2 bg-gray-200 rounded-full overflow-hidden"><div class="bg-warning h-full rounded-full" style="width: <?php echo $percent; ?>%"></div></div></div> <?php endforeach; ?> </div> </div> </div> </div> </div> </div>

    </div> </div> <?php
// Include the admin footer
include 'includes/footer.php';
?>

