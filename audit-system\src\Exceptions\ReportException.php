<?php

namespace AuditSystem\Exceptions;

/**
 * Exception thrown when report generation operations fail
 */
class ReportException extends AuditException
{
    /**
     * Create exception for report generation failure
     *
     * @param string $reportType Type of report that failed to generate
     * @param string $reason Reason for failure
     * @return static
     */
    public static function generationFailed(string $reportType, string $reason): self
    {
        return new self("Failed to generate {$reportType} report: {$reason}");
    }

    /**
     * Create exception for template not found
     *
     * @param string $templatePath Path to missing template
     * @return static
     */
    public static function templateNotFound(string $templatePath): self
    {
        return new self("Report template not found: {$templatePath}");
    }

    /**
     * Create exception for invalid report format
     *
     * @param string $format Invalid format requested
     * @param array $supportedFormats List of supported formats
     * @return static
     */
    public static function invalidFormat(string $format, array $supportedFormats): self
    {
        $supported = implode(', ', $supportedFormats);
        return new self("Invalid report format '{$format}'. Supported formats: {$supported}");
    }

    /**
     * Create exception for report export failure
     *
     * @param string $exportPath Path where export failed
     * @param string $reason Reason for export failure
     * @return static
     */
    public static function exportFailed(string $exportPath, string $reason): self
    {
        return new self("Failed to export report to {$exportPath}: {$reason}");
    }

    /**
     * Create exception for insufficient data
     *
     * @param string $missingData Description of missing data
     * @return static
     */
    public static function insufficientData(string $missingData): self
    {
        return new self("Insufficient data for report generation: {$missingData}");
    }
}