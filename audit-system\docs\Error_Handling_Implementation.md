# Error Handling and Logging Implementation

## Overview

This document describes the comprehensive error handling and logging system implemented for the audit system. The implementation provides robust error recovery strategies, detailed logging capabilities, and graceful degradation mechanisms to ensure the audit system can handle various failure scenarios while maintaining operational continuity.

## Exception Hierarchy

### Base Exception Class

All audit-related exceptions extend from `AuditException`, which provides:
- Consistent error context information
- Structured logging support
- Exception chaining capabilities

```php
abstract class AuditException extends Exception
{
    public function getContext(): array; // Returns structured context for logging
}
```

### Specialized Exception Classes

1. **FileAccessException** - File system access failures
   - `fileNotFound()` - File does not exist
   - `fileNotReadable()` - File permissions issues
   - `directoryNotAccessible()` - Directory access problems
   - `fileTooLarge()` - File size limit exceeded

2. **AnalysisException** - Code analysis failures
   - `analyzerFailed()` - Analyzer execution failure
   - `invalidFileContent()` - Unexpected file format
   - `parsingFailed()` - Syntax parsing errors
   - `analysisTimeout()` - Analysis time limit exceeded

3. **ConfigurationException** - Configuration-related errors
   - `missingConfiguration()` - Required config missing
   - `invalidConfiguration()` - Invalid config values
   - `configFileNotFound()` - Config file missing
   - `invalidConfigFormat()` - Malformed config file

4. **MCPConnectionException** - MCP server communication failures
   - `timeout()` - Connection timeout
   - `serverUnavailable()` - Server not responding
   - `invalidResponse()` - Malformed server response
   - `authenticationFailed()` - Authentication issues

5. **ProgressException** - Progress tracking failures
   - `progressFileCorrupted()` - Progress data corruption
   - `backupFailed()` - Progress backup failure
   - `restoreFailed()` - Progress restoration failure
   - `concurrentAccess()` - Multiple audit instances
   - `invalidState()` - Inconsistent progress state

6. **ReportException** - Report generation failures
   - `generationFailed()` - Report creation failure
   - `templateNotFound()` - Missing report template
   - `invalidFormat()` - Unsupported report format
   - `exportFailed()` - Report export failure
   - `insufficientData()` - Missing required data

7. **SecurityException** - Security-related failures
   - `unauthorizedAccess()` - Access control violation
   - `pathTraversal()` - Path traversal attempt
   - `restrictedFileType()` - Forbidden file type
   - `sizeLimitExceeded()` - Security size limits
   - `maliciousContent()` - Threat detection

## Error Recovery Service

### Recovery Strategies

The `ErrorRecoveryService` implements multiple recovery strategies for each exception type:

#### File Access Errors
- **retry_with_delay** - Exponential backoff retry
- **skip_file** - Continue without problematic file
- **use_alternative_path** - Try alternative file paths

#### Analysis Errors
- **retry_with_basic_analyzer** - Use simpler analysis
- **partial_analysis** - Analyze available portions
- **skip_problematic_analyzer** - Disable failing analyzer

#### MCP Connection Errors
- **retry_connection** - Retry with different parameters
- **use_cached_data** - Use previously cached results
- **fallback_to_local_analysis** - Disable MCP features

#### Configuration Errors
- **use_default_config** - Apply default values
- **prompt_for_config** - Request user input
- **skip_dependent_features** - Disable affected features

#### Progress Errors
- **restore_from_backup** - Use backup progress file
- **rebuild_progress** - Reconstruct from available data
- **start_fresh** - Begin new audit session

### Recovery Limits

- Maximum retry attempts: 3 per error type/context combination
- Exponential backoff delays: 1s, 2s, 4s, 8s (max 30s)
- Recovery statistics tracking for monitoring

### Graceful Degradation

When services become unavailable, the system provides fallback options:

- **MCP Server**: Local best practices, cached data, skip validation
- **File Analysis**: Basic syntax checks, pattern matching only
- **Report Generation**: Text-only reports, JSON export, summary only
- **Progress Tracking**: Memory-only, simplified tracking, disabled

## Enhanced Logging System

### Log Types

1. **Audit Log** (`audit.log`) - General audit operations
2. **Error Log** (`error.log`) - Errors, warnings, critical issues
3. **Performance Log** (`performance.log`) - Timing and resource metrics

### Logging Features

#### Exception Logging
```php
$logger->logException($exception, 'error', ['operation' => 'file_scan']);
```

#### Error Recovery Logging
```php
$logger->logErrorRecovery('FileAccessException', 'retry_with_delay', 'file_analysis', true);
```

#### Performance Monitoring
```php
$logger->logPerformance('file_analysis', 2.5, ['files_processed' => 10]);
$logger->logResourceUsage('scanning', ['memory_usage' => 1024000, 'cpu_time' => 5.2]);
```

#### Progress Tracking
```php
$logger->logProgress('analyzing', 50, 100, 25);
$logger->logCheckpoint('analyzing', ['completed_files' => 25, 'findings' => 12]);
```

#### Graceful Degradation
```php
$logger->logGracefulDegradation('MCP Server', 'Connection timeout', 'use local analysis');
```

### Log Management

#### Automatic Log Rotation
- Configurable size limits (default: 10MB)
- Timestamped rotation files
- Automatic cleanup of old logs

#### Log Statistics
- File sizes and line counts
- Last modification times
- Performance metrics

#### Log Archival
- Configurable retention period (default: 30 days)
- Automatic cleanup of old rotated logs

## Integration with Audit Controller

### Error Handling in Audit Execution

The `AuditController` integrates comprehensive error handling:

1. **Pre-execution Validation**
   - Configuration validation
   - Environment checks
   - Resource availability

2. **File Processing Errors**
   - Individual file failure isolation
   - Recovery strategy application
   - Progress preservation

3. **Analyzer Failures**
   - Analyzer-specific error handling
   - Fallback to basic analyzers
   - Partial analysis results

4. **MCP Server Issues**
   - Connection retry logic
   - Graceful degradation to local analysis
   - Cached data utilization

5. **Progress Corruption**
   - Automatic backup creation
   - Progress restoration from backups
   - Fresh start when necessary

### Error Recovery Workflow

```
Error Occurs → Exception Thrown → Recovery Service Consulted → 
Strategy Applied → Success/Failure Logged → Audit Continues/Fails
```

## Testing

### Unit Tests

- **ExceptionHierarchyTest** - Validates exception structure and factory methods
- **ErrorRecoveryServiceTest** - Tests all recovery strategies
- **AuditLoggerTest** - Comprehensive logging functionality tests

### Integration Tests

- **ErrorHandlingIntegrationTest** - End-to-end error scenarios
- Real file system errors
- Analyzer failures
- MCP connection issues
- Progress corruption scenarios

## Configuration

### Error Handling Settings

```json
{
  "audit": {
    "max_retry_attempts": 3,
    "timeout": 300,
    "max_file_size": 1048576,
    "enable_recovery": true,
    "log_level": "info"
  },
  "logging": {
    "max_log_size": 10485760,
    "retention_days": 30,
    "enable_rotation": true
  }
}
```

### Recovery Strategy Configuration

Recovery strategies can be customized per exception type and context, allowing fine-tuned error handling for specific scenarios.

## Monitoring and Metrics

### Recovery Statistics

- Total recovery attempts
- Success/failure rates by exception type
- Most common error patterns
- Recovery strategy effectiveness

### Performance Impact

- Error handling overhead monitoring
- Recovery operation timing
- Resource usage during error scenarios

## Best Practices

1. **Always log exceptions** with full context
2. **Use appropriate recovery strategies** for each error type
3. **Monitor recovery statistics** for system health
4. **Implement graceful degradation** for non-critical features
5. **Maintain audit continuity** despite individual failures
6. **Regular log rotation** to prevent disk space issues
7. **Archive old logs** for compliance and debugging

## Future Enhancements

1. **Machine Learning** for error pattern recognition
2. **Automated recovery strategy optimization**
3. **Real-time error monitoring dashboards**
4. **Integration with external monitoring systems**
5. **Predictive failure detection**