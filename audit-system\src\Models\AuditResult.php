<?php

namespace AuditSystem\Models;

use DateTime;

/**
 * Represents the complete result of an audit
 */
class AuditResult
{
    public array $findings;
    public AuditStatistics $statistics;
    public array $fileStatus;
    public DateTime $completedAt;
    public string $version;

    public function __construct()
    {
        $this->findings = [];
        $this->statistics = new AuditStatistics();
        $this->fileStatus = [];
        $this->completedAt = new DateTime();
        $this->version = '1.0.0';
    }

    /**
     * Add findings for a file
     *
     * @param string $filePath
     * @param Finding[] $fileFindings
     * @return void
     */
    public function addFileFindings(string $filePath, array $fileFindings): void
    {
        $this->findings[$filePath] = $fileFindings;
        $this->fileStatus[$filePath] = empty($fileFindings) ? 'optimal' : 'needs_change';
        $this->statistics->updateFromFindings($fileFindings);
    }

    /**
     * Get all findings grouped by priority
     *
     * @return array
     */
    public function getFindingsByPriority(): array
    {
        $priorityFindings = [];
        $nonPriorityFindings = [];

        foreach ($this->findings as $filePath => $fileFindings) {
            foreach ($fileFindings as $finding) {
                if ($finding->priority === Finding::PRIORITY_AREA) {
                    $priorityFindings[] = $finding;
                } else {
                    $nonPriorityFindings[] = $finding;
                }
            }
        }

        return [
            'priority_area' => $priorityFindings,
            'non_priority' => $nonPriorityFindings
        ];
    }

    /**
     * Get findings by severity level
     *
     * @return array
     */
    public function getFindingsBySeverity(): array
    {
        $severityGroups = [
            Finding::SEVERITY_CRITICAL => [],
            Finding::SEVERITY_HIGH => [],
            Finding::SEVERITY_MEDIUM => [],
            Finding::SEVERITY_LOW => []
        ];

        foreach ($this->findings as $filePath => $fileFindings) {
            foreach ($fileFindings as $finding) {
                $severityGroups[$finding->severity][] = $finding;
            }
        }

        return $severityGroups;
    }

    /**
     * Convert to array for serialization
     *
     * @return array
     */
    public function toArray(): array
    {
        $findingsArray = [];
        foreach ($this->findings as $file => $fileFindings) {
            $findingsArray[$file] = array_map(fn($finding) => $finding->toArray(), $fileFindings);
        }

        return [
            'findings' => $findingsArray,
            'statistics' => $this->statistics->toArray(),
            'fileStatus' => $this->fileStatus,
            'completedAt' => $this->completedAt->format('Y-m-d H:i:s'),
            'version' => $this->version
        ];
    }
}