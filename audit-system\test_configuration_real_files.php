<?php

// Test ConfigurationAnalyzer with real CMS files
require_once 'src/Interfaces/AnalyzerInterface.php';
require_once 'src/Models/Finding.php';
require_once 'src/Analyzers/ConfigurationAnalyzer.php';

use AuditSystem\Analyzers\ConfigurationAnalyzer;
use AuditSystem\Models\Finding;

echo "Testing ConfigurationAnalyzer with Real CMS Files...\n\n";

$analyzer = new ConfigurationAnalyzer();

// Test with actual config.php
echo "Analyzing ../public_html/config.php...\n";
$configContent = file_get_contents('../public_html/config.php');
$configFindings = $analyzer->analyze('../public_html/config.php', $configContent);

echo "Found " . count($configFindings) . " findings in config.php:\n";
foreach ($configFindings as $finding) {
    echo "  - Line {$finding->line}: [{$finding->severity}] {$finding->description}\n";
    echo "    Recommendation: {$finding->recommendation}\n";
    echo "    Priority: {$finding->priority}\n\n";
}

// Test with actual .htaccess
echo "\nAnalyzing ../public_html/.htaccess...\n";
$htaccessContent = file_get_contents('../public_html/.htaccess');
$htaccessFindings = $analyzer->analyze('../public_html/.htaccess', $htaccessContent);

echo "Found " . count($htaccessFindings) . " findings in .htaccess:\n";
foreach ($htaccessFindings as $finding) {
    echo "  - Line {$finding->line}: [{$finding->severity}] {$finding->description}\n";
    echo "    Recommendation: {$finding->recommendation}\n";
    echo "    Priority: {$finding->priority}\n\n";
}

// Test with composer.json if it exists
$composerPath = 'composer.json';
if (file_exists($composerPath)) {
    echo "\nAnalyzing composer.json...\n";
    $composerContent = file_get_contents($composerPath);
    $composerFindings = $analyzer->analyze($composerPath, $composerContent);
    
    echo "Found " . count($composerFindings) . " findings in composer.json:\n";
    foreach ($composerFindings as $finding) {
        echo "  - Line {$finding->line}: [{$finding->severity}] {$finding->description}\n";
        echo "    Recommendation: {$finding->recommendation}\n";
        echo "    Priority: {$finding->priority}\n\n";
    }
}

echo "Real file analysis completed!\n";