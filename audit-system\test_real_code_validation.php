<?php

require_once 'vendor/autoload.php';

use AuditSystem\Tests\Analyzers\PHPAnalyzerRealCodeTest;

echo "Running PHPAnalyzer Real Code Validation Tests...\n\n";

$test = new PHPAnalyzerRealCodeTest();

$methods = [
    'testConfigurationFileAnalysis',
    'testDatabaseFunctionAnalysis', 
    'testAdminFileAnalysis',
    'testImageProcessingAnalysis',
    'testLegacyCodeAnalysis',
    'testWellStructuredCodeProducesFewerFindings'
];

$passed = 0;
$failed = 0;

foreach ($methods as $method) {
    echo "Running $method... ";
    try {
        $test->setUp();
        $test->$method();
        echo "PASSED\n";
        $passed++;
    } catch (Exception $e) {
        echo "FAILED: " . $e->getMessage() . "\n";
        $failed++;
    } catch (Error $e) {
        echo "ERROR: " . $e->getMessage() . "\n";
        $failed++;
    } catch (AssertionError $e) {
        echo "ASSERTION FAILED: " . $e->getMessage() . "\n";
        $failed++;
    }
}

echo "\n";
echo "Results: $passed passed, $failed failed\n";

if ($failed === 0) {
    echo "All validation tests passed! The PHP analyzer correctly identifies:\n";
    echo "- Configuration file issues\n";
    echo "- Database function problems\n";
    echo "- Admin file mixed concerns\n";
    echo "- Image processing issues\n";
    echo "- Legacy code problems\n";
    echo "- Handles well-structured code appropriately\n";
} else {
    echo "Some validation tests failed. Review the implementation.\n";
}