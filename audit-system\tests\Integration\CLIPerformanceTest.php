<?php

namespace AuditSystem\Tests\Integration;

use PHPUnit\Framework\TestCase;

/**
 * Performance tests for the CLI audit tool
 */
class CLIPerformanceTest extends TestCase
{
    private string $cliScript;
    private string $performanceTestDir;
    private string $performanceConfigFile;

    protected function setUp(): void
    {
        $this->cliScript = __DIR__ . '/../../bin/audit.php';
        $this->performanceTestDir = __DIR__ . '/../fixtures/performance-test';
        $this->performanceConfigFile = __DIR__ . '/../fixtures/performance-config.json';
        
        $this->createPerformanceTestEnvironment();
    }

    protected function tearDown(): void
    {
        $this->cleanupPerformanceTest();
    }

    /**
     * Test CLI performance with large number of files
     * 
     * @group performance
     */
    public function testLargeCodebasePerformance(): void
    {
        $this->markTestSkipped('Performance test - run manually with --group performance');
        
        // Create 100 test files
        $this->createLargeTestCodebase(100);
        
        $startTime = microtime(true);
        
        $output = $this->runCLI([
            'audit',
            '--config=' . $this->performanceConfigFile,
            '--target=' . $this->performanceTestDir,
            '--quiet'
        ]);
        
        $endTime = microtime(true);
        $duration = $endTime - $startTime;
        
        // Should complete within reasonable time (adjust based on system)
        $this->assertLessThan(60, $duration, 'Audit should complete within 60 seconds for 100 files');
        
        // Should not contain errors
        $this->assertStringNotContainsString('ERROR:', $output);
        
        // Should generate reports
        $reportFiles = glob($this->performanceTestDir . '/../performance-reports/audit_report_*.json');
        $this->assertGreaterThan(0, count($reportFiles));
    }

    /**
     * Test memory usage during large audit
     * 
     * @group performance
     */
    public function testMemoryUsage(): void
    {
        $this->markTestSkipped('Performance test - run manually with --group performance');
        
        // Create test files with varying sizes
        $this->createVariedSizeTestFiles();
        
        $initialMemory = memory_get_usage(true);
        
        $output = $this->runCLI([
            'audit',
            '--config=' . $this->performanceConfigFile,
            '--target=' . $this->performanceTestDir,
            '--quiet'
        ]);
        
        $peakMemory = memory_get_peak_usage(true);
        $memoryUsed = $peakMemory - $initialMemory;
        
        // Memory usage should be reasonable (adjust based on system)
        $this->assertLessThan(128 * 1024 * 1024, $memoryUsed, 'Memory usage should be less than 128MB');
        
        $this->assertStringNotContainsString('ERROR:', $output);
    }

    /**
     * Test progress monitoring accuracy
     */
    public function testProgressMonitoringAccuracy(): void
    {
        // Create a moderate number of test files
        $this->createLargeTestCodebase(20);
        
        $output = $this->runCLI([
            'audit',
            '--config=' . $this->performanceConfigFile,
            '--target=' . $this->performanceTestDir,
            '--verbose'
        ]);
        
        // Check that progress information is included
        $this->assertStringContainsString('Found', $output);
        $this->assertStringContainsString('files to analyze', $output);
        $this->assertStringContainsString('Audit completed', $output);
        
        // Should not contain errors
        $this->assertStringNotContainsString('ERROR:', $output);
    }

    /**
     * Test concurrent status checking
     */
    public function testConcurrentStatusChecking(): void
    {
        // This test would ideally run audit in background and check status
        // For now, we'll test the status command with existing progress
        $this->createTestProgressData();
        
        $output = $this->runCLI([
            'status',
            '--config=' . $this->performanceConfigFile
        ]);
        
        $this->assertStringContainsString('Audit Status', $output);
        $this->assertStringContainsString('Progress:', $output);
        
        // Status should respond quickly
        $startTime = microtime(true);
        $this->runCLI(['status', '--config=' . $this->performanceConfigFile]);
        $endTime = microtime(true);
        
        $this->assertLessThan(2, $endTime - $startTime, 'Status command should respond within 2 seconds');
    }

    /**
     * Test CLI responsiveness with various argument combinations
     */
    public function testArgumentParsingPerformance(): void
    {
        $argumentCombinations = [
            ['help'],
            ['version'],
            ['config', '--target=' . $this->performanceTestDir],
            ['validate', '--config=' . $this->performanceConfigFile],
            ['status', '--verbose'],
            ['config', '--timeout=120', '--max-file-size=2097152']
        ];
        
        foreach ($argumentCombinations as $args) {
            $startTime = microtime(true);
            $output = $this->runCLI($args);
            $endTime = microtime(true);
            
            $duration = $endTime - $startTime;
            
            // Each command should respond quickly
            $this->assertLessThan(5, $duration, 
                'Command ' . implode(' ', $args) . ' should respond within 5 seconds');
            
            // Should not contain errors (except for expected validation errors)
            if (!in_array('validate', $args)) {
                $this->assertStringNotContainsString('ERROR:', $output);
            }
        }
    }

    /**
     * Run CLI command and return output
     */
    private function runCLI(array $args): string
    {
        $command = 'php ' . escapeshellarg($this->cliScript);
        
        foreach ($args as $arg) {
            $command .= ' ' . escapeshellarg($arg);
        }
        
        $output = shell_exec($command . ' 2>&1');
        
        return $output ?? '';
    }

    /**
     * Create performance test environment
     */
    private function createPerformanceTestEnvironment(): void
    {
        $reportDir = dirname($this->performanceTestDir) . '/performance-reports';
        $progressFile = dirname($this->performanceTestDir) . '/performance-progress.json';
        
        // Create directories
        if (!is_dir($this->performanceTestDir)) {
            mkdir($this->performanceTestDir, 0755, true);
        }
        
        if (!is_dir($reportDir)) {
            mkdir($reportDir, 0755, true);
        }
        
        if (!is_dir(dirname($progressFile))) {
            mkdir(dirname($progressFile), 0755, true);
        }
        
        // Create performance configuration
        $config = [
            'audit' => [
                'target_directory' => $this->performanceTestDir,
                'progress_file' => $progressFile,
                'report_directory' => $reportDir,
                'timeout' => 300,
                'max_file_size' => 2097152 // 2MB
            ],
            'priority_areas' => [
                'patterns' => [
                    'perf_*',
                    'large_*',
                    'config.php'
                ]
            ]
        ];
        
        file_put_contents($this->performanceConfigFile, json_encode($config, JSON_PRETTY_PRINT));
    }

    /**
     * Create large test codebase
     */
    private function createLargeTestCodebase(int $fileCount): void
    {
        for ($i = 1; $i <= $fileCount; $i++) {
            $filename = sprintf('perf_test_%03d.php', $i);
            $filepath = $this->performanceTestDir . '/' . $filename;
            
            $content = $this->generateTestPHPContent($i);
            file_put_contents($filepath, $content);
        }
        
        // Create some CSS and JS files too
        for ($i = 1; $i <= min(10, $fileCount / 5); $i++) {
            $cssFile = $this->performanceTestDir . "/style_{$i}.css";
            $jsFile = $this->performanceTestDir . "/script_{$i}.js";
            
            file_put_contents($cssFile, $this->generateTestCSSContent($i));
            file_put_contents($jsFile, $this->generateTestJSContent($i));
        }
    }

    /**
     * Create test files with varying sizes
     */
    private function createVariedSizeTestFiles(): void
    {
        $sizes = [1024, 5120, 10240, 51200, 102400]; // 1KB to 100KB
        
        foreach ($sizes as $index => $size) {
            $filename = "large_file_{$index}.php";
            $filepath = $this->performanceTestDir . '/' . $filename;
            
            $content = "<?php\n// Large test file - {$size} bytes\n";
            $content .= str_repeat("// Padding line to increase file size\n", $size / 40);
            $content .= "\n?>";
            
            file_put_contents($filepath, $content);
        }
    }

    /**
     * Generate test PHP content
     */
    private function generateTestPHPContent(int $index): string
    {
        return "<?php
// Performance test file {$index}

class TestClass{$index} {
    private \$property{$index};
    
    public function __construct() {
        \$this->property{$index} = 'test value {$index}';
    }
    
    public function testMethod{$index}() {
        \$query = \"SELECT * FROM table{$index} WHERE id = \" . \$_GET['id'];
        return \$query;
    }
    
    public function anotherMethod{$index}() {
        echo \$_POST['data'];
        return true;
    }
}

\$instance{$index} = new TestClass{$index}();
\$result{$index} = \$instance{$index}->testMethod{$index}();
?>";
    }

    /**
     * Generate test CSS content
     */
    private function generateTestCSSContent(int $index): string
    {
        return "/* Performance test CSS file {$index} */

.test-class-{$index} {
    color: #ff{$index}{$index}{$index};
    font-size: {$index}px;
    margin: {$index}px;
    padding: {$index}px;
}

.another-class-{$index} {
    background-color: rgba({$index}, {$index}, {$index}, 0.5);
    border: {$index}px solid #000;
}

@media (max-width: 768px) {
    .test-class-{$index} {
        font-size: " . ($index + 2) . "px;
    }
}";
    }

    /**
     * Generate test JavaScript content
     */
    private function generateTestJSContent(int $index): string
    {
        return "// Performance test JavaScript file {$index}

function testFunction{$index}() {
    var data{$index} = document.getElementById('input{$index}').value;
    document.getElementById('output{$index}').innerHTML = data{$index};
    return data{$index};
}

function anotherFunction{$index}() {
    var xhr{$index} = new XMLHttpRequest();
    xhr{$index}.open('POST', '/api/test{$index}', true);
    xhr{$index}.send('data=' + encodeURIComponent(document.getElementById('data{$index}').value));
}

document.addEventListener('DOMContentLoaded', function() {
    testFunction{$index}();
});";
    }

    /**
     * Create test progress data
     */
    private function createTestProgressData(): void
    {
        $progressFile = dirname($this->performanceTestDir) . '/performance-progress.json';
        
        $progressData = [
            'phase' => 'analyzing',
            'completed_files' => [
                $this->performanceTestDir . '/perf_test_001.php',
                $this->performanceTestDir . '/perf_test_002.php'
            ],
            'pending_files' => [
                $this->performanceTestDir . '/perf_test_003.php',
                $this->performanceTestDir . '/perf_test_004.php'
            ],
            'findings' => [],
            'last_update' => date('Y-m-d H:i:s'),
            'statistics' => [
                'total_findings' => 5,
                'critical_findings' => 2,
                'priority_area_findings' => 3
            ]
        ];
        
        file_put_contents($progressFile, json_encode($progressData, JSON_PRETTY_PRINT));
    }

    /**
     * Clean up performance test files
     */
    private function cleanupPerformanceTest(): void
    {
        $this->removeDirectory(dirname($this->performanceTestDir));
    }

    /**
     * Recursively remove directory
     */
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
}