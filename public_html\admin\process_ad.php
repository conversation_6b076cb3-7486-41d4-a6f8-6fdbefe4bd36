<?php
/**
 * Admin Affiliate Ad Processing
 *
 * Processes form submissions from ad_form.php for creating, updating, and deleting affiliate ads.
 */

require_once '../config.php';
require_once 'includes/auth_check.php';
require_once '../includes/functions.php';

// Initialize variables
$errors = [];
$redirect_url = 'advertising.php'; // Default redirect URL

// Check for action
if (!isset($_POST['action'])) {
    $_SESSION['error_message'] = 'Invalid request: No action specified.';
    header('Location: ' . $redirect_url);
    exit;
}

$action = $_POST['action'];

// --- DELETE ACTION ---
if ($action === 'delete') {
    $ad_id = isset($_POST['ad_id']) ? (int)$_POST['ad_id'] : null;
    
    if (!$ad_id) {
        $_SESSION['error_message'] = 'Invalid request: No ad ID specified for deletion.';
        header('Location: ' . $redirect_url);
        exit;
    }
    
    try {
        // First get the featured image to delete files if needed
        $stmt = $pdo->prepare("SELECT featured_image FROM affiliate_ads WHERE id = :id");
        $stmt->bindParam(':id', $ad_id, PDO::PARAM_INT);
        $stmt->execute();
        $featured_image = $stmt->fetchColumn();
        
        // Delete the record
        $stmt = $pdo->prepare("DELETE FROM affiliate_ads WHERE id = :id");
        $stmt->bindParam(':id', $ad_id, PDO::PARAM_INT);
        $stmt->execute();
        
        // Check if deletion was successful
        if ($stmt->rowCount() > 0) {
            // Deletion successful - optional: delete the image files
            if (!empty($featured_image)) {
                // We'd need to implement image file deletion here - for now, just log it
                // deleteAdImage($featured_image); // Implement this function if needed
            }
            
            $_SESSION['success_message'] = "Affiliate ad successfully deleted.";
        } else {
            $_SESSION['error_message'] = "No ad found with that ID or you don't have permission to delete it.";
        }
    } catch (PDOException $e) {
        $_SESSION['error_message'] = "Database error: " . $e->getMessage();
    }
    
    header('Location: ' . $redirect_url);
    exit;
}

// --- CREATE/UPDATE ACTIONS ---
// Collect and sanitize form data
$ad_id = isset($_POST['ad_id']) ? (int)$_POST['ad_id'] : null;
$current_featured_image = isset($_POST['current_featured_image']) ? trim($_POST['current_featured_image']) : '';

// Set redirect URL for edit mode
if ($ad_id) {
    $redirect_url = "ad_form.php?id={$ad_id}";
}

// Collect form fields
$title = trim($_POST['title'] ?? '');
$description = trim($_POST['description'] ?? '');
$external_url = trim($_POST['external_url'] ?? '');
$category_id = !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null;
$status = $_POST['status'] ?? 'active';

// MODIFIED: Handle multiple display positions
$display_positions = isset($_POST['display_positions']) && is_array($_POST['display_positions']) ? $_POST['display_positions'] : ['sidebar_popular'];

// Ensure at least one position is selected
if (empty($display_positions)) {
    $errors[] = 'At least one display position must be selected';
    $display_positions = ['sidebar_popular']; // Default if none provided
}

// MODIFIED: Convert display positions array to JSON for database storage
$display_position_json = json_encode($display_positions);

$open_in_new_tab = isset($_POST['open_in_new_tab']) ? (int)$_POST['open_in_new_tab'] : 1;
$show_sponsored_label = isset($_POST['show_sponsored_label']) ? (int)$_POST['show_sponsored_label'] : 1;
$custom_css = trim($_POST['custom_css'] ?? '');
$placement_frequency = isset($_POST['placement_frequency']) ? (int)$_POST['placement_frequency'] : 3;
$placement_type = $_POST['placement_type'] ?? 'fixed';
$author_id = !empty($_POST['author_id']) ? (int)$_POST['author_id'] : null;
$placement_priority = isset($_POST['placement_priority']) ? (int)$_POST['placement_priority'] : 5;
$mobile_visibility = isset($_POST['mobile_visibility']) ? (int)$_POST['mobile_visibility'] : 1;
$desktop_visibility = isset($_POST['desktop_visibility']) ? (int)$_POST['desktop_visibility'] : 1;
$tablet_visibility = isset($_POST['tablet_visibility']) ? (int)$_POST['tablet_visibility'] : 1;

// Handle start date and end date
$start_date = !empty($_POST['start_date']) ? trim($_POST['start_date']) : null;
$end_date = !empty($_POST['end_date']) ? trim($_POST['end_date']) : null;

// Format dates for MySQL
if ($start_date) {
    $start_date = date('Y-m-d H:i:s', strtotime($start_date));
}
if ($end_date) {
    $end_date = date('Y-m-d H:i:s', strtotime($end_date));
}

// Handle tracking code
$tracking_code = trim($_POST['tracking_code'] ?? '');

// --- Validation ---
if (empty($title)) {
    $errors[] = 'Title is required';
}
if (empty($external_url)) {
    $errors[] = 'External URL is required';
} elseif (!filter_var($external_url, FILTER_VALIDATE_URL)) {
    $errors[] = 'External URL is not valid';
}
if (!in_array($status, ['active', 'inactive', 'scheduled', 'archived'])) {
    $errors[] = 'Invalid status value';
}

// MODIFIED: Validate each selected position
$valid_positions = [
    'sidebar_popular', 'sidebar_middle', 'sidebar_bottom',
    'article_beginning', 'in_content', 'after_content', 'article_bottom_banner',
    'recommended', 'header', 'footer', 'multiple'
];

foreach ($display_positions as $position) {
    if (!in_array($position, $valid_positions)) {
        $errors[] = 'Invalid display position: ' . htmlspecialchars($position);
    }
}

if (!in_array($placement_type, ['fixed', 'ctr_based'])) {
    $errors[] = 'Invalid placement type';
}

// If scheduled status, ensure start date is provided
if ($status === 'scheduled' && empty($start_date)) {
    $errors[] = 'Start date is required for scheduled ads';
}

// --- Handle Featured Image ---
$featured_image_to_save = $current_featured_image; // Default to current image

// Check for image upload
if (isset($_FILES['featured_image_upload']) && $_FILES['featured_image_upload']['error'] === UPLOAD_ERR_OK) {
    // Process uploaded image
    $result = handleImageUpload($_FILES['featured_image_upload'], 'ads');
    
    if (is_array($result) && isset($result['error'])) {
        $errors[] = $result['error'];
    } elseif (is_string($result)) {
        $featured_image_to_save = $result; // Save the returned filename
    }
} 
// If no upload but URL provided
elseif (empty($_FILES['featured_image_upload']['tmp_name']) && !empty($_POST['featured_image_url'])) {
    $featured_image_url = trim($_POST['featured_image_url']);
    
    if (filter_var($featured_image_url, FILTER_VALIDATE_URL)) {
        // Process image from URL
        $result = handleImageUpload($featured_image_url, 'ads');
        
        if (is_array($result) && isset($result['error'])) {
            $errors[] = $result['error'];
        } elseif (is_string($result)) {
            $featured_image_to_save = $result; // Save the returned filename
        }
    } else {
        $errors[] = 'Featured image URL is not valid';
    }
}

// If errors, redirect back with error messages
if (!empty($errors)) {
    $_SESSION['form_errors'] = $errors;
    $_SESSION['form_data'] = $_POST; // Store form data for repopulation
    header('Location: ' . $redirect_url);
    exit;
}

// --- Database Operations ---
try {
    // Begin transaction
    $pdo->beginTransaction();
    
    if ($action === 'create') {
        // --- Create new ad ---
        $sql = "INSERT INTO affiliate_ads (
                    title, description, external_url, category_id, 
                    featured_image, display_position, status, 
                    open_in_new_tab, show_sponsored_label, custom_css,
                    placement_frequency, placement_type, author_id,
                    placement_priority, mobile_visibility, desktop_visibility,
                    tablet_visibility, start_date, end_date, tracking_code,
                    created_at, updated_at
                ) VALUES (
                    :title, :description, :external_url, :category_id,
                    :featured_image, :display_position, :status,
                    :open_in_new_tab, :show_sponsored_label, :custom_css,
                    :placement_frequency, :placement_type, :author_id,
                    :placement_priority, :mobile_visibility, :desktop_visibility,
                    :tablet_visibility, :start_date, :end_date, :tracking_code,
                    NOW(), NOW()
                )";
                
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':external_url', $external_url);
        $stmt->bindParam(':category_id', $category_id, $category_id === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
        $stmt->bindParam(':featured_image', $featured_image_to_save);
        // MODIFIED: Store JSON array of positions in display_position column
        $stmt->bindParam(':display_position', $display_position_json);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':open_in_new_tab', $open_in_new_tab, PDO::PARAM_INT);
        $stmt->bindParam(':show_sponsored_label', $show_sponsored_label, PDO::PARAM_INT);
        $stmt->bindParam(':custom_css', $custom_css);
        $stmt->bindParam(':placement_frequency', $placement_frequency, PDO::PARAM_INT);
        $stmt->bindParam(':placement_type', $placement_type);
        $stmt->bindParam(':author_id', $author_id, $author_id === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
        $stmt->bindParam(':placement_priority', $placement_priority, PDO::PARAM_INT);
        $stmt->bindParam(':mobile_visibility', $mobile_visibility, PDO::PARAM_INT);
        $stmt->bindParam(':desktop_visibility', $desktop_visibility, PDO::PARAM_INT);
        $stmt->bindParam(':tablet_visibility', $tablet_visibility, PDO::PARAM_INT);
        $stmt->bindParam(':start_date', $start_date, $start_date === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
        $stmt->bindParam(':end_date', $end_date, $end_date === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
        $stmt->bindParam(':tracking_code', $tracking_code);
        
        $stmt->execute();
        $new_ad_id = $pdo->lastInsertId();
        
        $_SESSION['success_message'] = "New affiliate ad created successfully.";
        $redirect_url = "ad_form.php?id={$new_ad_id}&success=1";
        
    } elseif ($action === 'update' && $ad_id) {
        // --- Update existing ad ---
        $sql = "UPDATE affiliate_ads SET
                    title = :title,
                    description = :description,
                    external_url = :external_url,
                    category_id = :category_id,
                    featured_image = :featured_image,
                    display_position = :display_position,
                    status = :status,
                    open_in_new_tab = :open_in_new_tab,
                    show_sponsored_label = :show_sponsored_label,
                    custom_css = :custom_css,
                    placement_frequency = :placement_frequency,
                    placement_type = :placement_type,
                    author_id = :author_id,
                    placement_priority = :placement_priority,
                    mobile_visibility = :mobile_visibility,
                    desktop_visibility = :desktop_visibility,
                    tablet_visibility = :tablet_visibility,
                    start_date = :start_date,
                    end_date = :end_date,
                    tracking_code = :tracking_code,
                    updated_at = NOW()
                WHERE id = :id";
                
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':external_url', $external_url);
        $stmt->bindParam(':category_id', $category_id, $category_id === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
        $stmt->bindParam(':featured_image', $featured_image_to_save);
        // MODIFIED: Store JSON array of positions in display_position column
        $stmt->bindParam(':display_position', $display_position_json);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':open_in_new_tab', $open_in_new_tab, PDO::PARAM_INT);
        $stmt->bindParam(':show_sponsored_label', $show_sponsored_label, PDO::PARAM_INT);
        $stmt->bindParam(':custom_css', $custom_css);
        $stmt->bindParam(':placement_frequency', $placement_frequency, PDO::PARAM_INT);
        $stmt->bindParam(':placement_type', $placement_type);
        $stmt->bindParam(':author_id', $author_id, $author_id === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
        $stmt->bindParam(':placement_priority', $placement_priority, PDO::PARAM_INT);
        $stmt->bindParam(':mobile_visibility', $mobile_visibility, PDO::PARAM_INT);
        $stmt->bindParam(':desktop_visibility', $desktop_visibility, PDO::PARAM_INT);
        $stmt->bindParam(':tablet_visibility', $tablet_visibility, PDO::PARAM_INT);
        $stmt->bindParam(':start_date', $start_date, $start_date === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
        $stmt->bindParam(':end_date', $end_date, $end_date === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
        $stmt->bindParam(':tracking_code', $tracking_code);
        $stmt->bindParam(':id', $ad_id, PDO::PARAM_INT);
        
        $stmt->execute();
        
        $_SESSION['success_message'] = "Affiliate ad updated successfully.";
        $redirect_url = "ad_form.php?id={$ad_id}&success=1";
        
    } else {
        // Invalid action
        throw new Exception("Invalid action specified");
    }
    
    // Commit transaction
    $pdo->commit();
    
} catch (Exception $e) {
    // Roll back transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    $_SESSION['error_message'] = "Error: " . $e->getMessage();
    error_log("Error in process_ad.php: " . $e->getMessage());
}

// Redirect to appropriate page
header('Location: ' . $redirect_url);
exit;