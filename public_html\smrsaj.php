<?php
// smrsaj.php

// Include the configuration and header
require_once 'config.php'; // Assuming this sets up $pdo and other configs
require_once 'includes/header.php'; // Include your standard header

?>

<div class="max-w-900 mx-auto px-4 py-8">
    <div class="bg-white rounded-xl shadow-sm border border-border">
        <div class="bg-primary px-6 py-4 rounded-t-xl">
            <h1 class="text-2xl font-montserrat font-bold text-center text-white">Personalizirani Plan Mršavljenja</h1>
        </div>
        
        <div class="p-6">
            <p class="text-center text-gray-darker mb-6">Popunite formu ispod kako biste dobili personalizirani savjet za zdravo i brzo mršavljenje.</p>

            <form id="weightLossForm" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Gender Field -->
                    <div>
                        <label for="gender" class="block text-sm font-medium text-dark-contrast mb-1">Spol:</label>
                        <select class="w-full px-3 py-2 border border-border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white text-dark" id="gender" name="gender" required>
                            <option value="" disabled selected>Izaberite...</option>
                            <option value="muski">Muški</option>
                            <option value="zenski">Ženski</option>
                        </select>
                    </div>

                    <!-- Age Field -->
                    <div>
                        <label for="age" class="block text-sm font-medium text-dark-contrast mb-1">Godine:</label>
                        <input type="number" class="w-full px-3 py-2 border border-border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white text-dark" id="age" name="age" min="16" max="100" required placeholder="Unesite godine">
                    </div>

                    <!-- Height Field -->
                    <div>
                        <label for="height" class="block text-sm font-medium text-dark-contrast mb-1">Visina (cm):</label>
                        <input type="number" class="w-full px-3 py-2 border border-border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white text-dark" id="height" name="height" min="100" max="250" required placeholder="Npr. 175">
                    </div>

                    <!-- Weight Field -->
                    <div>
                        <label for="weight" class="block text-sm font-medium text-dark-contrast mb-1">Težina (kg):</label>
                        <input type="number" class="w-full px-3 py-2 border border-border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white text-dark" id="weight" name="weight" min="30" max="300" step="0.1" required placeholder="Npr. 70.5">
                    </div>

                    <!-- Activity Level Field -->
                    <div>
                        <label for="activity_level" class="block text-sm font-medium text-dark-contrast mb-1">Nivo Fizičke Aktivnosti:</label>
                        <select class="w-full px-3 py-2 border border-border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white text-dark" id="activity_level" name="activity_level" required>
                            <option value="" disabled selected>Izaberite...</option>
                            <option value="sedentary">Sjedeći način života (malo ili nimalo vježbanja)</option>
                            <option value="lightly_active">Lagano aktivan (lagano vježbenje/sport 1-3 dana/sedmično)</option>
                            <option value="moderately_active">Umjereno aktivan (umjereno vježbenje/sport 3-5 dana/sedmično)</option>
                            <option value="very_active">Veoma aktivan (intenzivno vježbenje/sport 6-7 dana/sedmično)</option>
                            <option value="extra_active">Ekstra aktivan (veoma intenzivno vježbenje/sport i fizički posao)</option>
                        </select>
                    </div>

                    <!-- Goal Weight Field -->
                    <div>
                        <label for="goal_weight" class="block text-sm font-medium text-dark-contrast mb-1">Ciljna Težina (kg):</label>
                        <input type="number" class="w-full px-3 py-2 border border-border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white text-dark" id="goal_weight" name="goal_weight" min="30" max="300" step="0.1" required placeholder="Npr. 65">
                    </div>
                </div>

                <!-- Preferences Field -->
                <div>
                    <label for="preferences" class="block text-sm font-medium text-dark-contrast mb-1">Preferencije u Ishrani ili Restrikcije (opciono):</label>
                    <textarea class="w-full px-3 py-2 border border-border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white text-dark" id="preferences" name="preferences" rows="3" placeholder="Npr. vegetarijanac, alergija na orašaste plodove, ne volim ribu..."></textarea>
                </div>

                <!-- Submit Button -->
                <div class="pt-4">
                    <button type="submit" class="btn w-full font-montserrat" id="submitButton">
                        Dobijte Personalizirani Savjet
                    </button>
                </div>
            </form>

            <!-- Results Area - Moved to a modal dialog that doesn't block main content -->
            <div id="resultArea" class="fixed inset-0 z-40 hidden">
                <!-- Semi-transparent overlay -->
                <div class="absolute inset-0 bg-dark bg-opacity-50"></div>
                
                <!-- Modal container - FIXED: Added more top space to avoid header overlap -->
                <div class="relative z-50 max-w-2xl mx-auto mt-24 sm:mt-28 md:mt-32 lg:mt-36 mb-6 px-4 overflow-y-auto" style="max-height: 85vh;">
                    <!-- Modal content with limited height on mobile -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden max-h-[80vh] md:max-h-[85vh] overflow-y-auto">
                        <div class="bg-primary px-6 py-4 sticky top-0 z-10 rounded-t-xl">
                            <div class="flex justify-between items-center">
                                <h2 class="text-xl font-montserrat font-bold text-white">Vaš Personalizirani Savjet</h2>
                                <!-- Close button -->
                                <button id="closeResultModal" class="text-white hover:text-gray-200 focus:outline-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                
                        <!-- Enhanced Loading Indicator with Animation and Progress -->
                        <div id="loadingIndicator" class="bg-white p-6">
                            <div class="flex flex-col items-center justify-center">
                                <!-- Progress Circle -->
                                <div class="relative mb-4">
                                    <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                                        <circle class="text-secondary opacity-25" stroke-width="6" stroke="currentColor" fill="transparent" r="44" cx="50" cy="50"/>
                                        <circle id="progressCircle" class="text-primary" stroke-width="6" stroke-linecap="round" stroke="currentColor" fill="transparent" r="44" cx="50" cy="50" style="stroke-dasharray: 276.5; stroke-dashoffset: 276.5"/>
                                    </svg>
                                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-primary font-montserrat font-bold text-lg" id="progressText">0%</div>
                                </div>
                                <div class="text-dark-contrast font-montserrat font-medium mb-4" id="calculationStatus">Inicijalizacija...</div>
                                <div class="bg-background rounded-lg border border-border p-4 w-full max-w-md mb-4">
                                    <div class="flex items-center animate-pulse" id="statusTextContainer">
                                        <div class="w-2 h-2 bg-primary rounded-full mr-2 animate-ping"></div>
                                        <div class="text-sm text-gray-darker" id="statusText">Povezujemo se sa AI modelom...</div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-medium">Procijenjeno vrijeme odgovora: <span id="estimatedTime">45-60 sekundi</span></div>
                                <div class="text-sm text-gray-medium mt-1">Trenutno obrađujemo <span class="text-primary font-medium" id="activeRequests">37</span> zahtjeva</div>
                            </div>
                        </div>
                
                <!-- Advice Content -->
                <div id="adviceContent" class="bg-white p-6 hidden prose prose-sm max-w-none">
                    <!-- Content will be populated by JavaScript -->
                </div>
                
                <div class="px-6 py-4 bg-background border-t border-border">
                    <p class="text-center text-gray-medium text-sm">
                        Napomena: Ovaj savjet je generisan od strane vještačke inteligencije i služi samo u informativne svrhe. Konsultirajte se sa ljekarom ili nutricionistom prije nego što započnete bilo kakav program mršavljenja.
                    </p>
                </div>
            </div>
            </div>
            
            <!-- Error Message -->
            <div id="errorArea" class="bg-red-50 border border-red-200 p-4 mt-6 rounded-xl hidden" role="alert">
                <p class="text-red-700">Došlo je do greške. Molimo pokušajte ponovo kasnije.</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const weightLossForm = document.getElementById('weightLossForm');
    if (!weightLossForm) return;
    
    const submitButton = document.getElementById('submitButton');
    const resultArea = document.getElementById('resultArea');
    const adviceContent = document.getElementById('adviceContent');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const errorArea = document.getElementById('errorArea');
    const progressCircle = document.getElementById('progressCircle');
    const progressText = document.getElementById('progressText');
    const calculationStatus = document.getElementById('calculationStatus');
    const statusText = document.getElementById('statusText');
    const activeRequests = document.getElementById('activeRequests');
    
    // Get all form input elements
    const formInputs = weightLossForm.querySelectorAll('input, select, textarea');
    
    // Add input event listeners for better mobile experience
    formInputs.forEach((input, index) => {
        // When an input receives focus on mobile
        input.addEventListener('focus', function() {
            // On mobile devices, scroll to make sure the input is visible
            if (window.innerWidth < 768) {
                // Slight delay to ensure keyboard has opened
                setTimeout(() => {
                    // Scroll to position the input in the upper portion of the visible area
                    const inputRect = this.getBoundingClientRect();
                    const scrollPosition = window.pageYOffset + inputRect.top - 100;
                    window.scrollTo({
                        top: scrollPosition,
                        behavior: 'smooth'
                    });
                }, 300);
            }
        });
        
        // For non-final inputs, allow Enter key to move to next field
        if (index < formInputs.length - 1 && input.tagName !== 'TEXTAREA') {
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    formInputs[index + 1].focus();
                }
            });
        }
    });
    
    // Track active request
    let currentFetchController = null;
    
    // Animation configurations
    const calculationStages = [
        "Inicijalizacija...",
        "Analiziranje podataka...",
        "Izračunavanje BMI...",
        "Određivanje kalorijskih potreba...",
        "Kreiranje plana ishrane...",
        "Planiranje fizičke aktivnosti...",
        "Optimizacija vremenskog okvira...",
        "Finalizacija personaliziranog savjeta..."
    ];
    
    const statusMessages = [
        "Povezujemo se sa AI modelom...",
        "Obrađujemo vaše podatke...",
        "Računanje metaboličkih parametara...",
        "Prilagođavanje preporuka vašim ciljevima...",
        "Kreiranje personaliziranog plana ishrane...",
        "Računanje optimalnih fizičkih aktivnosti...",
        "Analiziranje vremenskog okvira za postizanje cilja...",
        "Optimizacija pristupa za vaše specifične potrebe...",
        "Finalizacija savjeta..."
    ];
    
    // Add a cancel button to the loading indicator
    const cancelButton = document.createElement('button');
    cancelButton.className = 'text-primary text-sm mt-4 hover:underline';
    cancelButton.textContent = 'Otkaži i nastavi sa korištenjem sajta';
    cancelButton.onclick = function() {
        // If there's an active request, abort it
        if (currentFetchController) {
            currentFetchController.abort();
        }
        
        // Reset the form state
        resetFormState();
    };
    loadingIndicator.appendChild(cancelButton);
    
    // Reset form state function
    function resetFormState() {
        // Clear all intervals if they exist
        if (window.activeIntervals) {
            clearAllIntervals(window.activeIntervals);
        }
        
        // Hide loading UI
        resultArea.classList.add('hidden');
        loadingIndicator.classList.add('hidden');
        errorArea.classList.add('hidden');
        
        // Reset button
        submitButton.disabled = false;
        submitButton.innerHTML = 'Dobijte Personalizirani Savjet';
        
        // Re-enable scrolling on the body
        document.body.style.overflow = '';
    }
    
    // Handle closing the modal
    const closeResultModal = document.getElementById('closeResultModal');
    if (closeResultModal) {
        closeResultModal.addEventListener('click', resetFormState);
    }
    
    // Close modal when clicking outside of it
    resultArea.addEventListener('click', function(event) {
        // Check if the click was on the overlay (not on modal content)
        if (event.target === resultArea) {
            resetFormState();
        }
    });
    
    // Simulated progress animation
    function startProgressAnimation() {
        let progress = 0;
        const circumference = 2 * Math.PI * 44; // Circle radius is 44
        
        // Random initial active requests between 22-45
        activeRequests.textContent = Math.floor(Math.random() * 23) + 22;
        
        // Update calculation stage periodically
        let stageIndex = 0;
        const stageInterval = setInterval(() => {
            if (stageIndex < calculationStages.length) {
                calculationStatus.textContent = calculationStages[stageIndex];
                stageIndex++;
            } else {
                clearInterval(stageInterval);
            }
        }, 3500);
        
        // Update status message periodically
        let messageIndex = 0;
        const messageInterval = setInterval(() => {
            if (messageIndex < statusMessages.length) {
                statusText.textContent = statusMessages[messageIndex];
                messageIndex++;
            } else {
                clearInterval(messageInterval);
            }
        }, 4200);
        
        // Periodically update active requests
        const requestsInterval = setInterval(() => {
            const currentRequests = parseInt(activeRequests.textContent);
            const change = Math.floor(Math.random() * 5) - 2; // Between -2 and +2
            activeRequests.textContent = Math.max(15, currentRequests + change);
        }, 3000);
        
        // Main progress interval
        return {
            progressInterval: setInterval(() => {
                // Different speed phases
                if (progress < 30) {
                    progress += 0.8; // Faster at start
                } else if (progress < 70) {
                    progress += 0.5; // Medium in middle
                } else if (progress < 90) {
                    progress += 0.3; // Slower near end
                } else {
                    progress += 0.1; // Very slow at end
                }
                
                // Update visual progress
                const dashoffset = circumference - (progress / 100) * circumference;
                progressCircle.style.strokeDasharray = circumference;
                progressCircle.style.strokeDashoffset = dashoffset;
                progressText.textContent = Math.min(Math.round(progress), 99) + '%';
                
                // Stop progress at 99% (will go to 100% when response arrives)
                if (progress >= 99) {
                    clearInterval(this.progressInterval);
                }
            }, 150),
            stageInterval,
            messageInterval,
            requestsInterval
        };
    }
    
    // Function to clean up intervals
    function clearAllIntervals(intervals) {
        clearInterval(intervals.progressInterval);
        clearInterval(intervals.stageInterval);
        clearInterval(intervals.messageInterval);
        clearInterval(intervals.requestsInterval);
    }
    
    // Form submission handler
    weightLossForm.addEventListener('submit', function(event) {
        event.preventDefault(); // Prevent default form submission

        const form = event.target;
        
        // Hide mobile keyboard if it's open
        document.activeElement.blur();
        
        // Basic client-side validation check
        if (!form.checkValidity()) {
          form.reportValidity();
          return;
        }
        
        // Check if goal weight is less than current weight
        const currentWeight = parseFloat(document.getElementById('weight').value);
        const goalWeight = parseFloat(document.getElementById('goal_weight').value);
        
        if (goalWeight >= currentWeight) {
            alert('Ciljna težina mora biti manja od trenutne težine.');
            return;
        }

        const formData = new FormData(form);

        // Prepare UI for loading
        resultArea.classList.remove('hidden');
        adviceContent.classList.add('hidden');
        errorArea.classList.add('hidden');
        loadingIndicator.classList.remove('hidden');
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="inline-block animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></span> Obrada...';

        
        // Allow scrolling the modal but prevent scrolling the body
        document.body.style.overflow = 'hidden';
        
        // Start progress animation
        const intervals = startProgressAnimation();
        window.activeIntervals = intervals; // Store globally so we can access from the cancel button
        
        // Create an AbortController for fetch
        currentFetchController = new AbortController();
        const signal = currentFetchController.signal;

        // Send data to the backend API
        fetch('smrsaj-deepseek-api.php', {
            method: 'POST',
            body: formData,
            signal: signal
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Complete the progress to 100%
            progressCircle.style.strokeDashoffset = 0;
            progressText.textContent = '100%';
            calculationStatus.textContent = 'Završeno!';
                            statusText.textContent = 'Savjet je spreman!';
            
            // Clear all animation intervals
            clearAllIntervals(intervals);
            window.activeIntervals = null;
            
            // Show completion for a moment before showing results
            setTimeout(() => {
                loadingIndicator.classList.add('hidden');
                
                if (data.success && data.advice) {
                    // Display the advice with auto-scroll
                    adviceContent.innerHTML = data.advice;
                    adviceContent.classList.remove('hidden');
                    
                    // Scroll to results
                    const resultTopPosition = resultArea.getBoundingClientRect().top;
                    const scrollPosition = window.pageYOffset + resultTopPosition - 100; // 100px offset for better visibility
                    window.scrollTo({
                        top: scrollPosition,
                        behavior: 'smooth'
                    });
                } else {
                    // Display error message from server
                    errorArea.querySelector('p').textContent = data.message || 'Došlo je do greške prilikom generisanja savjeta.';
                    errorArea.classList.remove('hidden');
                    resultArea.classList.add('hidden');
                }
            }, 1000); // Show completion for 1 second
        })
        .catch(error => {
            console.error('Fetch Error:', error);
            
            // Clear all animation intervals
            clearAllIntervals(intervals);
            window.activeIntervals = null;
            
            // Only show error if it's not an abort error (user cancelled)
            if (error.name !== 'AbortError') {
                loadingIndicator.classList.add('hidden');
                errorArea.querySelector('p').textContent = 'Došlo je do mrežne greške ili greške na serveru. Molimo pokušajte ponovo. (' + error.message + ')';
                errorArea.classList.remove('hidden');
                resultArea.classList.add('hidden');
            } else {
                // If user cancelled, just reset the form state
                resetFormState();
            }
        })
        .finally(() => {
            // Clear the abort controller reference
            currentFetchController = null;
            
            // Re-enable the button regardless of success or error
            submitButton.disabled = false;
            submitButton.innerHTML = 'Dobijte Personalizirani Savjet';
        });
    });
});
</script>

<style>
/* Add custom styles for the Markdown content in adviceContent */
#adviceContent h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: var(--dark-contrast);
  font-family: 'Montserrat', sans-serif;
}

#adviceContent h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
  color: var(--dark-contrast);
  font-family: 'Montserrat', sans-serif;
}

#adviceContent h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: var(--dark-contrast);
  font-family: 'Montserrat', sans-serif;
}

#adviceContent p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

#adviceContent ul, #adviceContent ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

#adviceContent ul {
  list-style-type: disc;
}

#adviceContent ol {
  list-style-type: decimal;
}

#adviceContent li {
  margin-bottom: 0.5rem;
}

#adviceContent a {
  color: var(--primary);
  text-decoration: none;
}

#adviceContent a:hover {
  text-decoration: underline;
}

#adviceContent strong {
  font-weight: 600;
  color: var(--dark-contrast);
}

#adviceContent em {
  font-style: italic;
}

#adviceContent blockquote {
  border-left: 4px solid var(--primary);
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: var(--dark-contrast);
}

#adviceContent code {
  background-color: var(--background);
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

#adviceContent pre {
  background-color: var(--background);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin-bottom: 1rem;
}

#adviceContent table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

#adviceContent th, #adviceContent td {
  border: 1px solid var(--border);
  padding: 0.5rem;
}

#adviceContent th {
  background-color: var(--background);
  font-weight: 600;
}

/* Animation for status message dot */
@keyframes ping {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Mobile form improvements */
@media (max-width: 767px) {
  /* Add more space between form fields on mobile */
  #weightLossForm .grid {
    row-gap: 1.5rem;
  }
  
  /* Increase the touch target size for mobile inputs */
  #weightLossForm input,
  #weightLossForm select,
  #weightLossForm textarea,
  #weightLossForm button {
    min-height: 3rem;
    font-size: 1rem;
  }
  
  /* Add more padding for better touch experience */
  #weightLossForm input,
  #weightLossForm select,
  #weightLossForm textarea {
    padding: 0.75rem;
  }
  
  /* Make sure labels are clearly visible */
  #weightLossForm label {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    display: block;
  }
  
  /* Ensure focused elements stand out */
  #weightLossForm input:focus,
  #weightLossForm select:focus,
  #weightLossForm textarea:focus {
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.3);
  }
  
  /* Improve spacing around the form */
  .p-6 {
    padding: 1.5rem;
  }
}
</style>

<?php
// Include the standard footer
require_once 'includes/footer.php';
?>