/* Contest Timer and Popup Styles */
.contest-timer-container {
    position: fixed;
    bottom: 50px; /* Position above the mobile bottom navigation */
    left: 0;
    right: 0;
    z-index: 9999; /* Increased z-index to ensure visibility */
    width: 100%;
    background-color: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    border-top: 2px solid #ff6481;
    overflow: hidden;
    transition: all 0.3s ease;
    font-family: 'Montserrat', sans-serif;
    opacity: 0;
    visibility: hidden;
    transform: translateY(100%);
}

.contest-timer-container.visible {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    display: block !important;
    animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
    0% { border-top-color: #ff6481; }
    50% { border-top-color: #ff9db0; }
    100% { border-top-color: #ff6481; }
}

.contest-timer-container.minimized {
    height: 30px;
    cursor: pointer;
    overflow: hidden;
}

.contest-timer-header {
    background-color: #ff6481;
    color: white;
    padding: 8px 15px;
    font-weight: 700;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contest-timer-body {
    padding: 10px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.contest-timer-display {
    font-size: 22px;
    font-weight: 800;
    color: #ff6481;
    font-family: 'Montserrat', sans-serif;
    margin: 0 10px;
}

.contest-timer-message {
    font-size: 14px;
    color: #4b5563;
    line-height: 1.4;
    flex: 1;
    margin: 0 10px;
}

.contest-timer-progress-container {
    flex-basis: 100%;
    margin-top: 8px;
}

.contest-timer-progress {
    height: 4px;
    background-color: #ffd6dd;
    overflow: hidden;
    width: 100%;
}

.contest-timer-progress-bar {
    height: 100%;
    background-color: #ff6481;
    transition: width 1s linear;
}

.contest-timer-icon {
    display: none;
}

.minimized .contest-timer-icon {
    display: block;
    text-align: center;
    line-height: 30px;
    color: #ff6481;
}

.minimized .contest-timer-header,
.minimized .contest-timer-body {
    display: none;
}

.contest-timer-controls {
    display: flex;
    align-items: center;
}

.contest-timer-minimize {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contest-timer-button {
    background-color: #ff6481;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-left: 10px;
    white-space: nowrap;
}

.contest-timer-button:hover {
    background-color: #ff4d6e;
}

/* Contest Popup Styles */
.contest-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.contest-popup-overlay.active {
    opacity: 1;
    visibility: visible;
}

.contest-popup {
    background-color: white;
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.contest-popup-overlay.active .contest-popup {
    transform: translateY(0);
}

.contest-popup-header {
    background-color: #ff6481;
    color: white;
    padding: 15px 20px;
    font-weight: 700;
    font-size: 18px;
    text-align: center;
    position: relative;
}

.contest-popup-close {
    position: absolute;
    right: 15px;
    top: 15px;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contest-popup-body {
    padding: 20px;
    text-align: center;
}

.contest-popup-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
    background-color: #ffd6dd;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ff6481;
}

.contest-popup-title {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.contest-popup-message {
    font-size: 14px;
    color: #4b5563;
    margin-bottom: 20px;
    line-height: 1.6;
}

.contest-popup-button {
    background-color: #ff6481;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 12px 20px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: inline-block;
    text-decoration: none;
}

.contest-popup-button:hover {
    background-color: #ff4d6e;
}

/* Confetti Animation */
.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #ff6481;
    opacity: 0;
}

@media (max-width: 768px) {
    .contest-timer-container {
        padding-bottom: env(safe-area-inset-bottom, 0); /* For iPhone X and newer */
    }

    .contest-timer-body {
        flex-direction: column;
        align-items: flex-start;
    }

    .contest-timer-message, .contest-timer-display {
        margin-bottom: 5px;
    }

    .contest-timer-button {
        margin-top: 5px;
        margin-left: 0;
    }
}

@media (max-width: 480px) {
    .contest-timer-header {
        font-size: 12px;
        padding: 6px 10px;
    }

    .contest-timer-display {
        font-size: 18px;
    }

    .contest-timer-message {
        font-size: 12px;
    }
}
