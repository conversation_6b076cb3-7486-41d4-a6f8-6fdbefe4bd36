<?php
require_once '../config.php'; // Adjust path as needed
require_once 'includes/auth_check.php'; // Check if admin is logged in

// Check if it's a POST request and article_id is set
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['article_id']) || !is_numeric($_POST['article_id'])) {
    $_SESSION['error_message'] = 'Nevažeći zahtjev za brisanje.';
    header('Location: articles.php');
    exit;
}

$article_id = (int)$_POST['article_id'];

// --- Delete the article ---
try {
    // Optional: First delete related tags from article_tags if needed (depends on foreign key constraints)
    // $stmt_tags = $pdo->prepare("DELETE FROM article_tags WHERE article_id = :id");
    // $stmt_tags->bindParam(':id', $article_id, PDO::PARAM_INT);
    // $stmt_tags->execute();

    // Delete the article itself
    $stmt = $pdo->prepare("DELETE FROM articles WHERE id = :id");
    $stmt->bindParam(':id', $article_id, PDO::PARAM_INT);
    $stmt->execute();

    // Check if any row was actually deleted
    if ($stmt->rowCount() > 0) {
        $_SESSION['success_message'] = 'Članak uspješno obrisan.';
    } else {
        $_SESSION['error_message'] = 'Članak nije pronađen ili je već obrisan.';
    }

} catch (PDOException $e) {
    // Handle potential foreign key constraint errors or other DB issues
    $_SESSION['error_message'] = "Greška pri brisanju članka: " . $e->getMessage();
    // Log error in a real application
}

// Redirect back to the articles list
header('Location: articles.php');
exit;
?>
```

And the logout scri