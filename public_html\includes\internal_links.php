<?php
/**
 * includes/internal_links.php
 * Functions for handling internal linking in articles
 */

/**
 * Get all active internal links from the database
 *
 * @param PDO $pdo The PDO database connection object
 * @return array An array of internal link data
 */
function getActiveInternalLinks(PDO $pdo): array {
    $sql = "SELECT * FROM internal_links WHERE is_active = 1 ORDER BY keyword ASC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get a single internal link by ID
 *
 * @param PDO $pdo The PDO database connection object
 * @param int $id The internal link ID
 * @return array|null The internal link data or null if not found
 */
function getInternalLinkById(PDO $pdo, int $id): ?array {
    $sql = "SELECT * FROM internal_links WHERE id = :id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id, PDO::PARAM_INT);
    $stmt->execute();
    $link = $stmt->fetch(PDO::FETCH_ASSOC);
    return $link ?: null;
}

/**
 * Create a new internal link
 *
 * @param PDO $pdo The PDO database connection object
 * @param string $keyword The keyword to link
 * @param string $url The URL to link to
 * @param int|null $targetArticleId The target article ID (optional)
 * @param string $matchType The match type (exact, case_insensitive, partial)
 * @param int $maxReplacements The maximum number of replacements per article
 * @return array Result with success status and message or error
 */
function createInternalLink(PDO $pdo, string $keyword, string $url, ?int $targetArticleId = null, string $matchType = 'case_insensitive', int $maxReplacements = 1): array {
    try {
        $sql = "INSERT INTO internal_links (keyword, url, target_article_id, match_type, max_replacements)
                VALUES (:keyword, :url, :target_article_id, :match_type, :max_replacements)";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':keyword', $keyword, PDO::PARAM_STR);
        $stmt->bindParam(':url', $url, PDO::PARAM_STR);
        $stmt->bindParam(':target_article_id', $targetArticleId, $targetArticleId === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
        $stmt->bindParam(':match_type', $matchType, PDO::PARAM_STR);
        $stmt->bindParam(':max_replacements', $maxReplacements, PDO::PARAM_INT);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Internal link created successfully.',
            'id' => $pdo->lastInsertId()
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'error' => 'Database error: ' . $e->getMessage()
        ];
    }
}

/**
 * Update an existing internal link
 *
 * @param PDO $pdo The PDO database connection object
 * @param int $id The internal link ID
 * @param string $keyword The keyword to link
 * @param string $url The URL to link to
 * @param int|null $targetArticleId The target article ID (optional)
 * @param string $matchType The match type (exact, case_insensitive, partial)
 * @param int $maxReplacements The maximum number of replacements per article
 * @param bool $isActive Whether the link is active
 * @return array Result with success status and message or error
 */
function updateInternalLink(PDO $pdo, int $id, string $keyword, string $url, ?int $targetArticleId = null, string $matchType = 'case_insensitive', int $maxReplacements = 1, bool $isActive = true): array {
    try {
        $sql = "UPDATE internal_links
                SET keyword = :keyword,
                    url = :url,
                    target_article_id = :target_article_id,
                    match_type = :match_type,
                    max_replacements = :max_replacements,
                    is_active = :is_active
                WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':keyword', $keyword, PDO::PARAM_STR);
        $stmt->bindParam(':url', $url, PDO::PARAM_STR);
        $stmt->bindParam(':target_article_id', $targetArticleId, $targetArticleId === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
        $stmt->bindParam(':match_type', $matchType, PDO::PARAM_STR);
        $stmt->bindParam(':max_replacements', $maxReplacements, PDO::PARAM_INT);
        $isActiveInt = $isActive ? 1 : 0;
        $stmt->bindParam(':is_active', $isActiveInt, PDO::PARAM_INT);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Internal link updated successfully.'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'error' => 'Database error: ' . $e->getMessage()
        ];
    }
}

/**
 * Delete an internal link
 *
 * @param PDO $pdo The PDO database connection object
 * @param int $id The internal link ID
 * @return array Result with success status and message or error
 */
function deleteInternalLink(PDO $pdo, int $id): array {
    try {
        $sql = "DELETE FROM internal_links WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        return [
            'success' => true,
            'message' => 'Internal link deleted successfully.'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'error' => 'Database error: ' . $e->getMessage()
        ];
    }
}

/**
 * Process article content to add internal links
 *
 * @param PDO $pdo The PDO database connection object
 * @param string $content The article content
 * @return string The processed content with internal links
 */
function processContentWithInternalLinks(PDO $pdo, string $content): string {
    // Get all active internal links
    $links = getActiveInternalLinks($pdo);
    if (empty($links)) {
        return $content; // No links to process
    }

    // Create a DOMDocument to parse the HTML content
    $dom = new DOMDocument();
    // Preserve whitespace to maintain formatting
    $dom->preserveWhiteSpace = true;
    // Set error handling to avoid warnings for invalid HTML
    libxml_use_internal_errors(true);
    // Load the HTML content with UTF-8 encoding
    $dom->loadHTML('<?xml encoding="UTF-8">' . $content);
    // Clear any errors
    libxml_clear_errors();

    // Get the body element
    $body = $dom->getElementsByTagName('body')->item(0);
    if (!$body) {
        return $content; // No body element found
    }

    // Process each internal link
    foreach ($links as $link) {
        $keyword = $link['keyword'];
        $url = $link['url'];
        $matchType = $link['match_type'];
        $maxReplacements = (int)$link['max_replacements'];

        // Skip empty keywords
        if (empty($keyword)) {
            continue;
        }

        // Process text nodes in the body
        processTextNodes($body, $keyword, $url, $matchType, $maxReplacements);
    }

    // Get the processed HTML content
    $processedContent = '';
    $bodyNodes = $body->childNodes;
    foreach ($bodyNodes as $node) {
        $processedContent .= $dom->saveHTML($node);
    }

    return $processedContent;
}

/**
 * Process text nodes to add internal links
 *
 * @param DOMNode $node The DOM node to process
 * @param string $keyword The keyword to link
 * @param string $url The URL to link to
 * @param string $matchType The match type (exact, case_insensitive, partial)
 * @param int $maxReplacements The maximum number of replacements
 * @param int $replacementCount Reference to track the number of replacements made
 * @return void
 */
function processTextNodes(DOMNode $node, string $keyword, string $url, string $matchType, int $maxReplacements, int &$replacementCount = 0): void {
    // Skip if we've reached the maximum number of replacements
    if ($replacementCount >= $maxReplacements) {
        return;
    }

    // Skip processing for certain elements
    $skipElements = ['a', 'script', 'style', 'code', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
    if ($node->nodeType === XML_ELEMENT_NODE && in_array(strtolower($node->nodeName), $skipElements)) {
        return;
    }

    // Process text nodes
    if ($node->nodeType === XML_TEXT_NODE) {
        $text = $node->nodeValue;

        // Skip empty text nodes
        if (trim($text) === '') {
            return;
        }

        // Prepare the pattern based on match type
        switch ($matchType) {
            case 'exact':
                $pattern = '/\b' . preg_quote($keyword, '/') . '\b/';
                break;
            case 'case_insensitive':
                $pattern = '/\b' . preg_quote($keyword, '/') . '\b/i';
                break;
            case 'partial':
                $pattern = '/' . preg_quote($keyword, '/') . '/i';
                break;
            default:
                $pattern = '/\b' . preg_quote($keyword, '/') . '\b/i'; // Default to case_insensitive
        }

        // Check if the keyword exists in the text
        if (preg_match($pattern, $text)) {
            // Ensure URL doesn't have the /article/ prefix
            $cleanUrl = str_replace('/article/', '/', $url);

            // Make sure URL starts with a slash if it's a relative URL
            if (!preg_match('~^(?:f|ht)tps?://~i', $cleanUrl) && substr($cleanUrl, 0, 1) !== '/') {
                $cleanUrl = '/' . $cleanUrl;
            }

            // Replace only the first occurrence
            $replacement = '<a href="' . htmlspecialchars($cleanUrl) . '" class="internal-link">' . '$0' . '</a>';
            $newText = preg_replace($pattern, $replacement, $text, 1);

            // Create a document fragment with the new HTML
            $fragment = $node->ownerDocument->createDocumentFragment();
            $fragment->appendXML($newText);

            // Replace the text node with the fragment
            $node->parentNode->replaceChild($fragment, $node);

            // Increment the replacement count
            $replacementCount++;
        }
    } else {
        // Process child nodes recursively
        if ($node->hasChildNodes()) {
            $childNodes = $node->childNodes;
            $childCount = $childNodes->length;

            // We need to iterate backwards because the DOM structure changes as we process nodes
            for ($i = $childCount - 1; $i >= 0; $i--) {
                // Skip if we've reached the maximum number of replacements
                if ($replacementCount >= $maxReplacements) {
                    break;
                }

                $childNode = $childNodes->item($i);
                if ($childNode) {
                    processTextNodes($childNode, $keyword, $url, $matchType, $maxReplacements, $replacementCount);
                }
            }
        }
    }
}

/**
 * Suggest internal links for an article using DeepSeek AI
 *
 * @param PDO $pdo The PDO database connection object
 * @param string $content The article content
 * @param string $title The article title
 * @return array An array of suggested internal links
 */
function suggestInternalLinks(PDO $pdo, string $content, string $title): array {
    // Log the function call for debugging
    error_log("suggestInternalLinks called with title: " . $title . ", content length: " . strlen($content));

    // Get all published articles
    $sql = "SELECT id, title, slug FROM articles WHERE status = 'published' ORDER BY views DESC LIMIT 20";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($articles)) {
        error_log("No published articles found for internal linking");

        // Return dummy suggestions for testing
        return [
            [
                'keyword' => 'example keyword',
                'article_id' => 1,
                'title' => 'Example Article',
                'url' => '/example-article/',
                'reason' => 'This is a sample suggestion because no published articles were found.'
            ]
        ];
    }

    // Prepare the prompt for DeepSeek
    $articlesText = '';
    foreach ($articles as $article) {
        $articlesText .= "- " . $article['title'] . " (ID: " . $article['id'] . ", Slug: " . $article['slug'] . ")\n";
    }

    // Use PROMPT_INTERNAL_LINKS constant if defined
    if (defined('PROMPT_INTERNAL_LINKS')) {
        $basePrompt = PROMPT_INTERNAL_LINKS;
        $basePrompt = str_replace('%TITLE%', $title, $basePrompt);
    } else {
        $basePrompt = "I have an article with the title: \"$title\". Here's a snippet of the content:\n\n";
        $basePrompt .= "%CONTENT%\n\n";
        $basePrompt .= "Here are some other articles on my site that I could link to:\n%ARTICLES%\n\n";
        $basePrompt .= "Please suggest 3-5 internal links I could add to my article. For each suggestion, provide:\n";
        $basePrompt .= "1. The keyword or phrase in my article that should be linked\n";
        $basePrompt .= "2. The ID of the article to link to\n";
        $basePrompt .= "3. A brief explanation of why this link would be valuable\n\n";
        $basePrompt .= "Format your response as a JSON array with objects containing 'keyword', 'article_id', and 'reason' properties.";
    }

    // Replace placeholders
    $contentSnippet = substr(strip_tags($content), 0, 1000) . "...";
    $prompt = str_replace(['%CONTENT%', '%ARTICLES%'], [$contentSnippet, $articlesText], $basePrompt);

    error_log("Calling DeepSeek API for internal link suggestions");

    // Call DeepSeek API
    if (function_exists('callDeepSeekAPI')) {
        $result = callDeepSeekAPI($prompt, $contentSnippet, 0.7, 2000);

        if ($result['success'] && !empty($result['text'])) {
            // Try to parse the JSON response
            try {
                // Extract JSON from the response (it might contain markdown formatting)
                preg_match('/```json\s*(.*?)\s*```|(\[.*\])/s', $result['text'], $matches);
                $jsonText = $matches[1] ?? $matches[2] ?? $result['text'];

                // Clean up the JSON text
                $jsonText = preg_replace('/^```json\s*|\s*```$/s', '', $jsonText);
                $jsonText = trim($jsonText);

                error_log("Extracted JSON: " . substr($jsonText, 0, 100) . "...");

                $suggestions = json_decode($jsonText, true);

                if (json_last_error() === JSON_ERROR_NONE && is_array($suggestions)) {
                    // Process the suggestions to add URLs
                    foreach ($suggestions as &$suggestion) {
                        if (isset($suggestion['article_id'])) {
                            // Find the article in our list
                            foreach ($articles as $article) {
                                if ($article['id'] == $suggestion['article_id']) {
                                    $suggestion['url'] = '/' . $article['slug'] . '/';
                                    $suggestion['title'] = $article['title'];
                                    break;
                                }
                            }
                        }
                    }

                    error_log("Returning " . count($suggestions) . " internal link suggestions");
                    return $suggestions;
                } else {
                    error_log("JSON decode error: " . json_last_error_msg() . " for text: " . substr($jsonText, 0, 100) . "...");
                }
            } catch (Exception $e) {
                error_log("Error parsing DeepSeek API response: " . $e->getMessage());
            }
        } else {
            error_log("DeepSeek API call failed: " . ($result['error'] ?? 'Unknown error'));
        }
    } else {
        error_log("callDeepSeekAPI function not found");
    }

    // If we get here, something went wrong - return dummy suggestions for testing
    error_log("Returning fallback dummy suggestions");
    return [
        [
            'keyword' => 'sample keyword from article',
            'article_id' => $articles[0]['id'] ?? 1,
            'title' => $articles[0]['title'] ?? 'First Article',
            'url' => '/' . ($articles[0]['slug'] ?? 'first-article') . '/',
            'reason' => 'This is a fallback suggestion because the API call failed.'
        ],
        [
            'keyword' => 'another relevant term',
            'article_id' => $articles[1]['id'] ?? 2,
            'title' => $articles[1]['title'] ?? 'Second Article',
            'url' => '/' . ($articles[1]['slug'] ?? 'second-article') . '/',
            'reason' => 'This is another fallback suggestion.'
        ]
    ];
}

/**
 * Fix internal links in the database by removing the /article/ prefix
 *
 * @param PDO $pdo The PDO database connection object
 * @return array Result with success status, count of fixed links, and message or error
 */
function fixInternalLinks(PDO $pdo): array {
    try {
        // Find links with the /article/ prefix
        $sql = "SELECT id, url FROM internal_links WHERE url LIKE '%/article/%'";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $links = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $count = count($links);
        if ($count === 0) {
            return [
                'success' => true,
                'count' => 0,
                'message' => 'No internal links with /article/ prefix found.'
            ];
        }

        // Update each link
        $sql = "UPDATE internal_links SET url = :new_url WHERE id = :id";
        $stmt = $pdo->prepare($sql);

        foreach ($links as $link) {
            $newUrl = str_replace('/article/', '/', $link['url']);
            $stmt->bindParam(':new_url', $newUrl, PDO::PARAM_STR);
            $stmt->bindParam(':id', $link['id'], PDO::PARAM_INT);
            $stmt->execute();
        }

        return [
            'success' => true,
            'count' => $count,
            'message' => "Fixed $count internal links by removing the /article/ prefix."
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'error' => 'Database error: ' . $e->getMessage()
        ];
    }
}

/**
 * Get all categories for an article (for internal linking)
 *
 * Note: This function is only defined if it doesn't already exist
 * to avoid conflicts with functions.php
 *
 * @param PDO $pdo The PDO database connection object
 * @param int $articleId The article ID
 * @return array An array of category data
 */
if (!function_exists('getArticleCategories')) {
    function getArticleCategories(PDO $pdo, int $articleId): array {
        $sql = "SELECT c.id, c.name, c.slug
                FROM categories c
                JOIN article_categories ac ON c.id = ac.category_id
                WHERE ac.article_id = :article_id
                ORDER BY c.name ASC";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':article_id', $articleId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
