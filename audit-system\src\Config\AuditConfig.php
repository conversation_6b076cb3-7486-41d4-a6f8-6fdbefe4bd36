<?php

namespace AuditSystem\Config;

/**
 * Configuration management for audit system settings
 */
class AuditConfig
{
    private array $config;
    private static ?AuditConfig $instance = null;

    public function __construct(array $overrides = [])
    {
        // Start from defaults and merge overrides (deep merge)
        $this->config = array_replace_recursive($this->getDefaultConfig(), $overrides);
    }

    /**
     * Get singleton instance
     *
     * @return AuditConfig
     */
    public static function getInstance(): AuditConfig
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Load configuration from file
     *
     * @param string $configPath Path to configuration file
     * @return void
     */
    public function loadFromFile(string $configPath): void
    {
        if (file_exists($configPath)) {
            $fileConfig = json_decode(file_get_contents($configPath), true);
            if ($fileConfig !== null) {
                $this->config = array_merge($this->config, $fileConfig);
            }
        }
    }

    /**
     * Get configuration value
     *
     * @param string $key Configuration key (supports dot notation)
     * @param mixed $default Default value if key not found
     * @return mixed
     */
    public function get(string $key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!is_array($value) || !array_key_exists($k, $value)) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Set configuration value
     *
     * @param string $key Configuration key (supports dot notation)
     * @param mixed $value Value to set
     * @return void
     */
    public function set(string $key, $value): void
    {
        $keys = explode('.', $key);
        $config = &$this->config;

        foreach ($keys as $k) {
            if (!isset($config[$k]) || !is_array($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }

        $config = $value;
    }

    /**
     * Get all configuration
     *
     * @return array
     */
    public function getAll(): array
    {
        return $this->config;
    }

    /**
     * Save configuration to file
     *
     * @param string $configPath Path to save configuration
     * @return bool True if save was successful
     */
    public function saveToFile(string $configPath): bool
    {
        $json = json_encode($this->config, JSON_PRETTY_PRINT);
        return file_put_contents($configPath, $json) !== false;
    }

    /**
     * Get default configuration
     *
     * @return array
     */
    private function getDefaultConfig(): array
    {
        return [
            'audit' => [
                'target_directory' => 'public_html',
                'progress_file' => 'audit-system/data/progress.json',
                'report_directory' => 'audit-system/reports',
                'max_file_size' => 1048576, // 1MB
                'timeout' => 300, // 5 minutes
                'parallel_processing' => false
            ],
            'analyzers' => [
                'php' => [
                    'enabled' => true,
                    'max_complexity' => 10,
                    'max_function_length' => 50,
                    'check_naming_conventions' => true
                ],
                'security' => [
                    'enabled' => true,
                    'check_sql_injection' => true,
                    'check_xss' => true,
                    'check_file_uploads' => true,
                    'check_csrf' => true
                ],
                'performance' => [
                    'enabled' => true,
                    'check_database_queries' => true,
                    'check_asset_loading' => true,
                    'check_caching' => true,
                    'check_image_optimization' => true
                ],
                'frontend' => [
                    'enabled' => true,
                    'check_css_structure' => true,
                    'check_js_performance' => true,
                    'check_responsive_design' => true,
                    'check_accessibility' => true
                ],
                'config' => [
                    'enabled' => true,
                    'check_security_settings' => true,
                    'check_performance_settings' => true
                ]
            ],
            'priority_areas' => [
                'patterns' => [
                    'ad_*',
                    'includes/ad_*',
                    'admin/advertising*',
                    'smrsaj*',
                    'image.php',
                    'includes/security*',
                    'config.php'
                ],
                'directories' => [
                    'admin',
                    'includes',
                    'assets'
                ]
            ],
            'file_filters' => [
                'include_extensions' => ['php', 'html', 'css', 'js', 'htaccess'],
                'exclude_patterns' => [
                    '*.log',
                    '*.tmp',
                    'cache/*',
                    'uploads/*',
                    'wp-import/*'
                ],
                'max_file_size' => 1048576 // 1MB
            ],
            'mcp' => [
                'enabled' => true,
                'server_url' => 'context7',
                'timeout' => 30,
                'fallback_enabled' => true
            ],
            'reporting' => [
                'include_code_snippets' => true,
                'max_snippet_lines' => 10,
                'group_by_priority' => true,
                'include_statistics' => true,
                'export_formats' => ['json', 'html', 'markdown']
            ]
        ];
    }
}