<?php
/*
 * Changelog (2025-04-17 v1.10.0):
 * - Added "Generate random comments" checkbox under Publish Settings.
 * - Checkbox is checked by default for new articles (when $edit_mode is false).
 * - Checkbox value is submitted with the form.
 *
 * Changelog (2025-04-06 v1.9.0):
 * - Added basic Markdown toolbar buttons (Bold, Italic, Link, H2, List) above the content textarea.
 * - Added JavaScript functions (insertMarkdown, insertLink) to handle toolbar button clicks.
 * - Added basic styling for the toolbar.
 * - Adjusted content textarea height calculation.
 *
 * Changelog (2025-04-06 v1.8.3):
 * - Refactored Tags AI update: handleAIClick now dispatches a custom event 'ai-suggest-tags'.
 * - The tagsComponent Alpine component now listens for 'ai-suggest-tags' to update tags.
 * - Removed direct call to tagsComponent.__x.$data.updateTagsFromAI from handleAIClick.
 * - Removed console logs related to tags update debugging.
 *
 * Changelog (2025-04-06 v1.8.2):
 * - Added console.log debugging inside handleAIClick for the 'suggest_tags' action.
 *
 * Changelog (2025-04-06 v1.8.1):
 * - Corrected handleAIClick function call for 'rewrite_combined' action to pass 'title' as the contextElementId.
 *
 * Changelog (2025-04-06 v1.8.0):
 * - Modified "AI Rewrite Options" dropdown to include checkboxes for "Rewrite title" and "Rewrite content", checked by default.
 * - Updated Alpine.js data for the dropdown to manage checkbox states.
 * - Updated the main AI trigger button within the dropdown.
 * - Modified handleAIClick JavaScript function to read checkbox states and send them to the handler.
 * - Updated deepseek_handler.php call in handleAIClick to pass checkbox states.
 *
 * Changelog (2025-04-06 v1.7.0):
 * - Refactored tags component x-data definition into a separate JS function (tagsComponentData)
 * to improve clarity and avoid potential syntax errors from embedding complex PHP/JS in the attribute.
 * - Added <script> block at the end for the tagsComponentData function.
 *
 * Changelog (2025-04-06 v1.6.1):
 * - Fixed syntax error in x-data attribute for tags component.
 *
 * Changelog (2025-04-06 v1.6.0):
 * - Added JavaScript for DeepSeek API integration.
 * - Added IDs to relevant form fields (title, content, excerpt, meta_title, meta_description, focus_keyword, tagsInput).
 * - Added IDs to AI buttons (aiRewriteTitleBtn, aiRewriteExcerptBtn, aiSuggestTagsBtn, aiFillMetaTitleBtn, aiFillMetaDescBtn, aiFillFocusKeywordBtn).
 * - Implemented handleAIClick function to manage AJAX calls to deepseek_handler.php.
 * - Added loading states and error handling for AI buttons.
 *
 * Changelog (2025-04-05 v1.5.0):
 * - Added display of "Loading Trick" link alongside regular link on success, if enabled.
 * - Updated success message area to accommodate both links.
 * - Constructed loading trick URL based on article slug.
 *
 * Changelog (2025-04-03 v1.4.0):
 * - Added display of success message, article link, and copy button on successful save/update.
 * - Added JavaScript for clipboard copy functionality without alert().
 *
 * Changelog (2025-04-03 v1.3.0):
 * - Added file input for featured image upload alongside URL input.
 * - Added enctype="multipart/form-data" to the form tag.
 * - Minor UI adjustments for the new input.
 *
 * Changelog (2025-04-03 v1.2.2):
 * - Set "Use Random Author" toggle to be ON by default for NEW articles.
 * - Author dropdown is now disabled by default for NEW articles.
 * - Added this changelog entry.
 *
 * Changelog (2025-04-03 v1.2.1):
 * - Updated Excerpt AI icon placement to appear inside the textarea (bottom-right).
 * - Added padding to Excerpt textarea to accommodate the icon.
 *
 * Changelog (2025-04-03 v1.2.0):
 * - Added versioning.
 * - Changed default article status for new articles from 'draft' to 'published'.
 * - Added AI Rewrite UI button placeholder for Excerpt.
 * - Added AI Suggest UI button placeholder for Tags.
 * - Added AI Suggest UI button placeholder for Auto Internal Linking Keywords.
 * - Added AI Fill UI button placeholders for SEO fields (Meta Title, Meta Desc, Focus Keyword).
 */

require_once '../config.php'; // Adjust path to config file
require_once 'includes/auth_check.php'; // Ensure admin is logged in
require_once '../includes/functions.php'; // Include helper functions

$edit_mode = false; // Flag to check if editing an existing article
$article_id = null; // Initialize article ID
$show_success_link = false; // Flag to show the link/copy button area
$article_url = ''; // Initialize article URL
$loading_trick_url = ''; // Initialize loading trick URL

// Default article data structure - Default status is 'published'
$article = [
    'title' => '', 'content' => '', 'category_id' => '', 'author_id' => '',
    'status' => 'published', 'featured_image' => '', 'slug' => '', 'excerpt' => '',
    'published_at' => null, 'meta_title' => '', 'meta_description' => '', 'focus_keyword' => '',
    'reading_time' => 5, // Default reading time
    'enable_sidebar' => 1, // Default layout options
    'show_similar_posts' => 1,
    'enable_fb_share' => 1,
    'include_in_recommendations' => 1,
    'custom_recommendations_code' => '',
    'enable_loading_trick' => 1, // Default feature toggles
    'trick_type' => 'loading', // Default trick type
    'cloak_article_link' => 0,
    'enable_adsense' => 1,
    'enable_affiliate_ads' => 1,
    'enable_custom_ads' => 0,
    'custom_css' => '',
    'youtube_url' => '',
    'enable_auto_linking' => true, // Default for new toggle
    'auto_link_keywords' => '', // Default keywords string
    // 'generate_comments' => !$edit_mode, // Set default for new articles below form elements
];
$article_tags_string = ''; // Comma-separated string of tags for the form
$current_featured_image_base = ''; // To store the base name if editing

// Check if editing an existing article (ID passed in URL)
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $edit_mode = true;
    $article_id = (int)$_GET['id'];

    // Check if redirected after a successful save
    if (isset($_GET['success']) && $_GET['success'] == 1) {
        $show_success_link = true;
    }

    // Fetch article data from the database
    try {
        // Select all columns INCLUDING featured_image, enable_loading_trick, trick_type, and comment_count
        $stmt = $pdo->prepare("SELECT title, content, category_id, author_id, status, featured_image, slug, excerpt, published_at, meta_title, meta_description, focus_keyword, reading_time, enable_sidebar, show_similar_posts, enable_fb_share, include_in_recommendations, custom_recommendations_code, enable_loading_trick, trick_type, cloak_article_link, enable_adsense, enable_affiliate_ads, enable_custom_ads, custom_css, youtube_url, enable_auto_linking, auto_link_keywords, comment_count, generate_comments FROM articles WHERE id = :id");
        $stmt->bindParam(':id', $article_id, PDO::PARAM_INT);
        $stmt->execute();
        $article_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($article_data) {
            // Merge fetched data with defaults (fetched status will override the default 'published')
            $article = array_merge($article, $article_data);
            $current_featured_image_base = $article['featured_image']; // Store the current base filename

            // Construct URLs if success flag is set and slug exists
            if ($show_success_link && !empty($article['slug'])) {
                $article_url = rtrim(SITE_URL, '/') . '/' . escape($article['slug']) . '/';
                // Construct loading trick URL if enabled and type is loading
                if ($article['enable_loading_trick'] && $article['trick_type'] === 'loading') {
                    // Assumes .htaccess rule: RewriteRule ^load/([^/]+)/?$ loading.php?slug=$1 [L,QSA]
                    $loading_trick_url = rtrim(SITE_URL, '/') . '/load/' . escape($article['slug']) . '/';
                }
            }

            // Format published_at date for datetime-local input
            $article['published_at_formatted'] = '';
            if (!empty($article['published_at'])) {
                try {
                    $publishDate = new DateTime($article['published_at']);
                    // Format needed for <input type="datetime-local">
                    $article['published_at_formatted'] = $publishDate->format('Y-m-d\TH:i');
                } catch (Exception $e) {
                    // Ignore if date format is invalid
                }
            }

            // Fetch associated tags
            $tags_result = getArticleTags($pdo, $article_id);
            $tag_names = array_column($tags_result, 'name');
            $article_tags_string = implode(', ', $tag_names); // Create comma-separated string

        } else {
            // Article not found, redirect with error
            $_SESSION['error_message'] = "Article with ID $article_id not found.";
            header('Location: articles.php'); exit;
        }
    } catch (PDOException $e) {
        // Database error, redirect with error
        $_SESSION['error_message'] = "Database error fetching article: " . $e->getMessage();
        // Log error: error_log("Error fetching article ID $article_id: " . $e->getMessage());
        header('Location: articles.php'); exit;
    }
}

// Fetch categories and active authors for dropdowns
try {
    $categories = $pdo->query("SELECT id, name FROM categories ORDER BY name ASC")->fetchAll(PDO::FETCH_ASSOC);
    $authors = $pdo->query("SELECT id, name FROM authors WHERE status = 'active' ORDER BY name ASC")->fetchAll(PDO::FETCH_ASSOC);

    // Fetch article categories if editing an existing article
    $article_categories = [];
    if ($edit_mode && $article_id) {
        $stmt_categories = $pdo->prepare("SELECT category_id FROM article_categories WHERE article_id = :article_id");
        $stmt_categories->bindParam(':article_id', $article_id, PDO::PARAM_INT);
        $stmt_categories->execute();
        $article_categories = $stmt_categories->fetchAll(PDO::FETCH_COLUMN);
    }
} catch (PDOException $e) {
    $categories = []; $authors = []; $article_categories = [];
    $error_message = "Database error fetching categories/authors: " . $e->getMessage();
    // Log error: error_log("Error fetching categories/authors: " . $e->getMessage());
}

// Set the page title based on mode (New/Edit)
$admin_page_title = $edit_mode ? 'Edit Article' : 'New Article';

// Include the admin header
include 'includes/header.php';
?>

<header class="h-16 bg-white border-b border-border flex items-center justify-between px-6 flex-shrink-0 sticky top-0 z-30">
    <div class="flex items-center">
        <h1 class="text-xl font-montserrat font-bold text-dark"><?php echo escape($admin_page_title); ?></h1>
        <?php if ($edit_mode && isset($article['status'])): // Added check for status existence ?>
            <div class="ml-4">
                <span class="badge
                    <?php echo ($article['status'] === 'published') ? 'badge-success' : ''; ?>
                    <?php echo ($article['status'] === 'draft') ? 'badge-warning' : ''; ?>
                    <?php echo ($article['status'] === 'scheduled') ? 'badge-info' : ''; ?>
                    <?php echo ($article['status'] === 'archived') ? 'badge-gray' : ''; ?>
                ">
                    <?php echo ucfirst(escape($article['status'])); ?>
                </span>
            </div>
        <?php endif; ?>
    </div>
    <div class="flex items-center space-x-3">
         <?php // Button now links to regular article URL regardless of trick enabled ?>
         <?php if ($edit_mode && !empty($article_url)): ?>
             <a href="<?php echo $article_url; ?>" target="_blank" class="btn-secondary" title="View Live Article">
                 <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" /></svg>
                 View
             </a>
         <?php else: ?>
             <button type="button" class="btn-secondary" disabled title="Preview functionality not implemented yet">
                 <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                 Preview
             </button>
         <?php endif; ?>
        <button type="button" class="btn-secondary" disabled title="Schedule functionality not implemented yet">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
            Schedule
        </button>
        <button type="submit" form="articleForm" class="btn-success">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
            <?php echo $edit_mode ? 'Update Article' : 'Publish Article'; ?>
        </button>
    </div>
</header>

<form id="articleForm" action="process_article.php" method="POST" enctype="multipart/form-data" class="p-6 h-full">
    <?php if ($edit_mode): ?>
        <input type="hidden" name="article_id" value="<?php echo $article_id; ?>">
        <input type="hidden" name="current_featured_image" value="<?php echo escape($current_featured_image_base); ?>">
    <?php endif; ?>

    <?php // --- Display Success Message and Links/Copy Buttons --- ?>
    <?php if ($show_success_link && isset($_SESSION['success_message'])): ?>
        <div class="mb-4 bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded-lg text-sm shadow-sm flex flex-col gap-3">
            <div class="flex items-start justify-between">
                 <span><?php echo escape($_SESSION['success_message']); unset($_SESSION['success_message']); ?></span>
                 <a href="article_form.php" class="btn-sm bg-blue-500 text-white rounded px-2 py-1 text-xs flex-shrink-0 ml-4">
                     + New Article
                 </a>
            </div>

            <?php // Regular Link ?>
            <?php if (!empty($article_url)): ?>
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2">
                <span class="font-medium text-xs sm:text-sm flex-shrink-0 w-24">Regular Link:</span>
                <a href="<?php echo $article_url; ?>" target="_blank" class="text-green-700 hover:underline break-all text-xs sm:text-sm flex-grow"><?php echo $article_url; ?></a>
                <button type="button" onclick="copyLinkToClipboard('<?php echo $article_url; ?>', this)" class="btn-sm bg-green-600 text-white rounded px-2 py-1 text-xs flex items-center gap-1 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" /></svg>
                    <span class="copy-text">Copy</span>
                </button>
            </div>
            <?php endif; ?>

            <?php // Loading Trick Link (Show only if enabled, type is loading, and URL exists) ?>
            <?php if (!empty($loading_trick_url)): ?>
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 border-t border-green-200 pt-2 mt-2">
                <span class="font-medium text-xs sm:text-sm flex-shrink-0 w-24 text-orange-700">
                    Loading Link:
                </span>
                <a href="<?php echo $loading_trick_url; ?>" target="_blank" class="text-orange-700 hover:underline break-all text-xs sm:text-sm flex-grow"><?php echo $loading_trick_url; ?></a>
                <button type="button" onclick="copyLinkToClipboard('<?php echo $loading_trick_url; ?>', this)" class="btn-sm bg-orange-500 text-white rounded px-2 py-1 text-xs flex items-center gap-1 flex-shrink-0">
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" /></svg>
                    <span class="copy-text">Copy</span>
                </button>
            </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    <?php // --- End Success Message --- ?>


    <?php if (isset($_SESSION['form_errors'])): ?>
        <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm space-y-1 shadow-sm">
            <p class="font-semibold">Please correct the following errors:</p>
            <ul class="list-disc list-inside">
                <?php foreach ($_SESSION['form_errors'] as $error): ?>
                    <li><?php echo escape($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['form_errors']); unset($_SESSION['form_data']); // Clear errors after displaying ?>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm shadow-sm">
            <?php echo escape($error_message); ?>
        </div>
    <?php endif; ?>

    <div class="flex flex-col lg:flex-row gap-6 h-full">

        <div class="lg:w-7/12 flex flex-col h-full">

            <div class="mb-4">
                <div class="flex items-center justify-between mb-1">
                    <label for="title" class="block text-sm font-medium text-gray-700">Article Title *</label>
                    <div x-data="{ showOptions: false, rewriteTitleChecked: true, rewriteContentChecked: true }" class="relative">
                         <button type="button" @click="showOptions = !showOptions" class="text-xs text-primary flex items-center gap-1 focus:outline-none hover:underline" title="AI Rewrite Options">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                              <path stroke-linecap="round" stroke-linejoin="round" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                            </svg>
                            AI Rewrite Options
                         </button>
                         <div x-show="showOptions" @click.away="showOptions = false"
                              class="absolute right-0 mt-1 bg-white rounded-md shadow-lg border border-gray-200 p-3 z-10 w-64"
                              style="display: none;" x-cloak>
                            <div class="space-y-3">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" x-model="rewriteTitleChecked" class="form-checkbox">
                                    <span class="ml-2 text-sm text-gray-700">Rewrite title</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" x-model="rewriteContentChecked" class="form-checkbox">
                                    <span class="ml-2 text-sm text-gray-700">Rewrite content</span>
                                </label>

                                <div class="pt-3 border-t border-gray-100">
                                    <button type="button" id="aiRewriteCombinedBtn"
                                            @click="handleAIClick('rewrite_combined', 'content', ['title', 'content'], rewriteTitleChecked, rewriteContentChecked, 'title')"
                                            :disabled="!rewriteTitleChecked && !rewriteContentChecked"
                                            class="btn w-full text-sm py-1 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <span class="ai-btn-text">Rewrite Selected</span>
                                        <span class="ai-loading hidden ml-1">
                                            <svg class="animate-spin h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <input type="text" id="title" name="title" class="form-input text-xl font-montserrat font-bold py-3" placeholder="Enter article title" required value="<?php echo escape($article['title']); ?>">
            </div>

            <div class="mb-4 flex-grow flex flex-col">
                 <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Content *</label>
                 <?php // --- START: Markdown Toolbar --- ?>
                 <div class="markdown-toolbar bg-white border border-b-0 border-border rounded-t-xl p-2 flex items-center space-x-1 flex-shrink-0">
                     <button type="button" class="md-toolbar-btn" onclick="insertMarkdown('**', '**')" title="Bold">
                         <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>
                         <span class="font-bold">B</span>
                     </button>
                     <button type="button" class="md-toolbar-btn" onclick="insertMarkdown('_', '_')" title="Italic">
                         <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16m-7 6h7" /></svg>
                         <span class="italic">I</span>
                     </button>
                     <button type="button" class="md-toolbar-btn" onclick="insertLink()" title="Link">
                         <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" /></svg>
                     </button>
                     <span class="h-6 w-px bg-gray-300 mx-1"></span>
                     <button type="button" class="md-toolbar-btn" onclick="insertMarkdown('\n## ', '')" title="Heading 2">
                         <span class="font-bold">H2</span>
                     </button>
                      <button type="button" class="md-toolbar-btn" onclick="insertMarkdown('\n* ', '')" title="List Item">
                         <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h8m-8 6h16" /></svg>
                     </button>
                      <button type="button" class="p-1.5 rounded hover:bg-gray-100" title="Add Media (Not functional)" disabled><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg></button>
                 </div>
                 <?php // --- END: Markdown Toolbar --- ?>
                 <?php // Rows calculation needs adjustment if toolbar changes height significantly ?>
                 <textarea id="content" name="content" class="form-textarea w-full flex-grow border-t-0 rounded-t-none rounded-b-xl resize-none p-4" placeholder="Write your article content here... Supports Markdown syntax." required rows="18"><?php echo escape($article['content']); ?></textarea>
            </div>

            <div class="space-y-2 flex-shrink-0">
                <div x-data="{ open: false }">
                    <button type="button" @click="open = !open" class="collapsible-header">
                        <span class="text-sm font-semibold text-gray-700">Excerpt</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform transition-transform" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div x-show="open" x-transition class="collapsible-content" style="display: none;" x-cloak>
                        <div class="relative">
                            <textarea id="excerpt" name="excerpt" class="form-textarea h-20 pr-8" placeholder="Short summary of the article (optional)..."><?php echo escape($article['excerpt'] ?? ''); ?></textarea>
                             <button type="button" id="aiRewriteExcerptBtn" onclick="handleAIClick('generate_excerpt', 'content', 'excerpt', null, null, null)" class="absolute bottom-2 right-2 text-primary hover:text-primary/80 focus:outline-none p-1 rounded hover:bg-gray-100" title="AI Generate Excerpt">
                                <span class="ai-btn-text">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                      <path stroke-linecap="round" stroke-linejoin="round" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                                    </svg>
                                </span>
                                <span class="ai-loading hidden">
                                    <svg class="animate-spin h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                                </span>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Used for meta description if not set specifically, and in article listings.</p>
                    </div>
                </div>

                <div x-data="{ open: false }">
                    <button type="button" @click="open = !open" class="collapsible-header">
                        <span class="text-sm font-semibold text-gray-700">SEO Options</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform transition-transform" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div x-show="open" x-transition class="collapsible-content space-y-4" style="display: none;" x-cloak>
                        <div>
                            <div class="flex items-center justify-between mb-1">
                                <label for="meta_title" class="block text-xs font-medium text-gray-700">Meta Title</label>
                                <button type="button" id="aiFillMetaTitleBtn" onclick="handleAIClick('suggest_meta_title', 'content', 'meta_title', null, null, 'title')" class="text-primary hover:text-primary/80 focus:outline-none p-0.5 rounded hover:bg-gray-100" title="AI Suggest Meta Title">
                                    <span class="ai-btn-text"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg></span>
                                    <span class="ai-loading hidden"><svg class="animate-spin h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg></span>
                                </button>
                            </div>
                            <input type="text" id="meta_title" name="meta_title" class="form-input text-sm" placeholder="Enter meta title (optional)..." value="<?php echo escape($article['meta_title']); ?>">
                            <p class="text-xs text-gray-500 mt-1">Recommended: 50-60 characters. If empty, article title is used.</p>
                        </div>
                        <div>
                             <div class="flex items-center justify-between mb-1">
                                <label for="meta_description" class="block text-xs font-medium text-gray-700">Meta Description</label>
                                <button type="button" id="aiFillMetaDescBtn" onclick="handleAIClick('suggest_meta_desc', 'content', 'meta_description', null, null, 'title')" class="text-primary hover:text-primary/80 focus:outline-none p-0.5 rounded hover:bg-gray-100" title="AI Suggest Meta Description">
                                    <span class="ai-btn-text"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg></span>
                                    <span class="ai-loading hidden"><svg class="animate-spin h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg></span>
                                </button>
                            </div>
                            <textarea id="meta_description" name="meta_description" class="form-textarea text-sm h-24" placeholder="Enter meta description (optional)..."><?php echo escape($article['meta_description']); ?></textarea>
                            <p class="text-xs text-gray-500 mt-1">Recommended: 150-160 characters. If empty, excerpt or content start is used.</p>
                        </div>
                        <div>
                             <div class="flex items-center justify-between mb-1">
                                <label for="focus_keyword" class="block text-xs font-medium text-gray-700">Focus Keyword</label>
                                <button type="button" id="aiFillFocusKeywordBtn" onclick="handleAIClick('suggest_focus_keyword', 'content', 'focus_keyword', null, null, 'title')" class="text-primary hover:text-primary/80 focus:outline-none p-0.5 rounded hover:bg-gray-100" title="AI Suggest Focus Keyword">
                                     <span class="ai-btn-text"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg></span>
                                     <span class="ai-loading hidden"><svg class="animate-spin h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg></span>
                                </button>
                            </div>
                            <input type="text" id="focus_keyword" name="focus_keyword" class="form-input text-sm" placeholder="e.g. web optimization (optional)" value="<?php echo escape($article['focus_keyword']); ?>">
                            <p class="text-xs text-gray-500 mt-1">Main keyword for SEO analysis (feature not implemented).</p>
                        </div>
                    </div>
                </div>

                <?php /* --- Media Options (Keep as is) --- */ ?>
                <div x-data="{ open: false }">
                    <button type="button" @click="open = !open" class="collapsible-header">
                        <span class="text-sm font-semibold text-gray-700">Media Options</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform transition-transform" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div x-show="open" x-transition class="collapsible-content space-y-4" style="display: none;" x-cloak>
                         <div>
                            <label for="youtube_url" class="block text-xs font-medium text-gray-700 mb-1">YouTube Video URL</label>
                            <input type="url" id="youtube_url" name="youtube_url" class="form-input text-sm" placeholder="Paste YouTube video URL (optional)" value="<?php echo escape($article['youtube_url']); ?>">
                            <p class="text-xs text-gray-500 mt-1">If provided, video might be embedded (feature not implemented).</p>
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-700 mb-1">Media Gallery (Placeholder)</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-md p-4 text-center text-gray-500 text-sm">
                                Media gallery functionality coming soon.
                            </div>
                        </div>
                        <div>
                            <label for="embed_code" class="block text-xs font-medium text-gray-700 mb-1">Embedded Content (Placeholder)</label>
                            <textarea id="embed_code" name="embed_code" class="form-textarea text-sm h-24 font-mono" placeholder="Paste embed code from Instagram, Twitter, etc. (Not functional)"></textarea>
                        </div>
                    </div>
                </div>

                <?php /* --- Advanced Options (Keep as is) --- */ ?>
                <div x-data="{ open: false }">
                    <button type="button" @click="open = !open" class="collapsible-header">
                        <span class="text-sm font-semibold text-gray-700">Advanced Options</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform transition-transform" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div x-show="open" x-transition class="collapsible-content space-y-4" style="display: none;" x-cloak>
                        <div>
                            <label for="slug" class="block text-xs font-medium text-gray-700 mb-1">Permalink (Slug)</label>
                            <div class="flex">
                                <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-xs">
                                    <?php echo rtrim(SITE_URL, '/'); ?>/
                                </span>
                                <input type="text" id="slug" name="slug" class="form-input text-sm rounded-l-none" placeholder="auto-generated-slug" value="<?php echo escape($article['slug']); ?>">
                            </div>
                             <p class="text-xs text-gray-500 mt-1">Leave empty to auto-generate. Use lowercase letters, numbers, and hyphens.</p>
                        </div>
                         <div>
                            <label class="block text-xs font-medium text-gray-700 mb-2">Advertisement Options</label>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between bg-gray-50 p-2 rounded border border-gray-200">
                                    <div>
                                        <span class="block text-sm font-medium">AdSense Ads</span>
                                        <span class="text-xs text-gray-500">Show Google AdSense</span>
                                    </div>
                                    <div x-data="{ enabled: <?php echo $article['enable_adsense'] ? 'true' : 'false'; ?> }">
                                        <input type="hidden" name="enable_adsense" :value="enabled ? 1 : 0">
                                        <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                            <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                        </button>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between bg-gray-50 p-2 rounded border border-gray-200">
                                    <div>
                                        <span class="block text-sm font-medium">Affiliate Ads</span>
                                        <span class="text-xs text-gray-500">Show affiliate products</span>
                                    </div>
                                     <div x-data="{ enabled: <?php echo $article['enable_affiliate_ads'] ? 'true' : 'false'; ?> }">
                                        <input type="hidden" name="enable_affiliate_ads" :value="enabled ? 1 : 0">
                                        <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                            <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                        </button>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between bg-gray-50 p-2 rounded border border-gray-200">
                                    <div>
                                        <span class="block text-sm font-medium">Custom Ads</span>
                                        <span class="text-xs text-gray-500">Show custom ad code</span>
                                    </div>
                                    <div x-data="{ enabled: <?php echo $article['enable_custom_ads'] ? 'true' : 'false'; ?> }">
                                        <input type="hidden" name="enable_custom_ads" :value="enabled ? 1 : 0">
                                        <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                            <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label for="custom_css" class="block text-xs font-medium text-gray-700 mb-1">Custom CSS</label>
                            <textarea id="custom_css" name="custom_css" class="form-textarea font-mono text-xs h-24" placeholder=".article-custom { color: #333; } /* Optional CSS for this article */"><?php echo escape($article['custom_css']); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="lg:w-5/12 flex-shrink-0">
            <div class="bg-white border border-border rounded-xl p-4 space-y-4 sticky top-20">

                <?php /* --- Featured Image Section (Keep as is) --- */ ?>
                <div x-data="{ currentImage: '<?php echo $current_featured_image_base ? getFeaturedImageUrl($current_featured_image_base, 'ss')['url'] : ''; ?>', fileChosen: false }">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Featured Image</label>

                    <div class="mb-2 border border-gray-200 rounded-lg overflow-hidden h-40 flex items-center justify-center bg-gray-50">
                        <img x-show="currentImage" :src="currentImage" alt="Featured Image Preview" class="object-contain h-full w-full" >
                        <div x-show="!currentImage" class="text-center text-gray-400 text-sm p-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-10 w-10 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                            <span>No image selected</span>
                        </div>
                    </div>

                    <div>
                        <label for="featured_image_upload" class="block text-xs font-medium text-gray-700 mb-1">Upload New Image</label>
                        <input type="file" id="featured_image_upload" name="featured_image_upload" accept="image/jpeg,image/png,image/gif,image/webp"
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90 file:cursor-pointer"
                               @change="fileChosen = true; currentImage = $event.target.files[0] ? URL.createObjectURL($event.target.files[0]) : (document.getElementById('featured_image_url').value || '')">
                        <p class="text-xs text-gray-500 mt-1">Max file size: <?php echo defined('MAX_FILE_SIZE') ? (MAX_FILE_SIZE / 1024 / 1024) : 'N/A'; ?>MB. Replaces URL below if chosen.</p>
                    </div>

                    <div class="my-3 text-center text-xs text-gray-400">OR</div>

                    <div>
                        <label for="featured_image_url" class="block text-xs font-medium text-gray-700 mb-1">Paste Image URL</label>
                        <input type="url" id="featured_image_url" name="featured_image_url" class="form-input text-sm" placeholder="https://example.com/image.jpg"
                               value="<?php echo !$edit_mode ? '' : escape($current_featured_image_base ? getFeaturedImageUrl($current_featured_image_base, 'original')['url'] : ''); // Show original URL if editing and exists ?>"
                               @input="if (!fileChosen) { currentImage = $event.target.value }">
                         <p class="text-xs text-gray-500 mt-1">Enter URL if not uploading directly. Upload takes precedence.</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Categories <span class="text-xs text-primary">(Multiple Selection Enabled)</span></label>
                        <div x-data="categoriesComponentData()" id="categoriesComponent">
                            <div class="bg-white border border-gray-300 rounded-md shadow-sm p-2 max-h-48 overflow-y-auto">
                                <div class="mb-2 pb-2 border-b border-gray-200">
                                    <span class="text-xs text-gray-500 italic">Select multiple categories by checking the boxes below.</span>
                                </div>
                                <?php foreach ($categories as $category): ?>
                                <div class="flex items-center mb-1">
                                    <input type="checkbox"
                                           id="category_<?php echo $category['id']; ?>"
                                           name="category_ids[]"
                                           value="<?php echo $category['id']; ?>"
                                           class="form-checkbox h-4 w-4 text-primary focus:ring-primary"
                                           <?php
                                           // Check if this is the primary category
                                           $isPrimary = isset($article['category_id']) && $article['category_id'] == $category['id'];
                                           // Check if this category is selected (for existing article-category relationships)
                                           $isSelected = $isPrimary || (isset($article_categories) && in_array($category['id'], $article_categories));
                                           echo $isSelected ? 'checked' : '';
                                           ?>
                                           @change="updatePrimaryCategory($event, <?php echo $category['id']; ?>)">
                                    <label for="category_<?php echo $category['id']; ?>" class="ml-2 text-sm text-gray-700 cursor-pointer" @click.prevent="setPrimaryCategory(<?php echo $category['id']; ?>)">
                                        <?php echo escape($category['name']); ?>
                                        <span x-show="primaryCategoryId == <?php echo $category['id']; ?>" class="ml-1 text-xs text-primary font-bold">(Primary Category)</span>
                                        <span x-show="primaryCategoryId != <?php echo $category['id']; ?>" class="ml-1 text-xs text-gray-400 hover:text-primary">(Click to set as primary)</span>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <input type="hidden" name="category_id" :value="primaryCategoryId">
                            <p class="text-xs text-gray-500 mt-1">Select multiple categories. The first selected category will be the primary one (shown in article listings).</p>
                        </div>
                    </div>
                    <?php /* --- Tags Section --- */ ?>
                    <div x-data="tagsComponentData()" id="tagsComponent" @ai-suggest-tags.window="updateTagsFromAI($event.detail.tags)">
                        <div class="flex items-center justify-between mb-1">
                             <label for="tagsInput" class="block text-sm font-medium text-gray-700">Tags</label>
                             <button type="button" id="aiSuggestTagsBtn" onclick="handleAIClick('suggest_tags', 'content', 'tagsComponent', null, null, null)" class="text-primary hover:text-primary/80 focus:outline-none p-0.5 rounded hover:bg-gray-100" title="AI Suggest Tags">
                                <span class="ai-btn-text"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg></span>
                                <span class="ai-loading hidden"><svg class="animate-spin h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg></span>
                            </button>
                        </div>
                        <div class="flex gap-1 mb-2">
                            <input type="text" id="tagsInput" x-model="newTag" @keydown.enter.prevent="addTag" @keydown.,.prevent="addTag" class="form-input py-1.5 flex-grow" placeholder="Add a tag...">
                            <button type="button" @click="addTag" class="btn-secondary text-sm px-3 py-1.5">Add</button>
                        </div>
                        <input type="hidden" name="tags" id="tagsHiddenInput" :value="tagsString">
                        <div class="flex flex-wrap gap-1 min-h-[24px]">
                            <template x-for="(tag, index) in tags" :key="index">
                                <span class="bg-gray-100 text-xs rounded-full px-2 py-1 flex items-center shadow-sm">
                                    <span x-text="tag"></span>
                                    <button type="button" @click="removeTag(index)" class="ml-1 text-gray-500 hover:text-red-500 focus:outline-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                                    </button>
                                </span>
                            </template>
                            <span x-show="tags.length === 0" class="text-xs text-gray-400 italic">No tags added yet.</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Separate tags with commas or press Enter.</p>
                    </div>
                </div>


                <?php /* --- AVG Time Trick Section --- */ ?>
                <div x-data="{ enabled: <?php echo $article['enable_loading_trick'] ? 'true' : 'false'; ?>, trickType: '<?php echo $article['trick_type'] ?? 'loading'; ?>' }" class="pt-2 border-t border-gray-100">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Engagement Tricks</span>
                        <div>
                            <input type="hidden" name="enable_loading_trick" :value="enabled ? 1 : 0">
                            <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                            </button>
                        </div>
                    </div>
                    <div x-show="enabled" class="mb-2">
                        <label for="trick_type" class="block text-xs font-medium text-gray-700 mb-1">Trick Type</label>
                        <select id="trick_type" name="trick_type" class="form-select text-sm" x-model="trickType">
                            <option value="loading">Loading Trick</option>
                            <option value="avg_time">AVG Time Trick</option>
                        </select>
                        <p class="text-xs text-gray-500 mt-1" x-show="trickType === 'loading'">Shows a loading animation before displaying the article. Creates a separate landing page.</p>
                        <p class="text-xs text-gray-500 mt-1" x-show="trickType === 'avg_time'">Shows a banner when user scrolls 70% of content with countdown and Facebook message popup. Runs directly on the article page.</p>
                    </div>
                </div>
                <?php /* --- Cloak Link Toggle (Keep as is) --- */ ?>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Cloak Article Link</span>
                     <div x-data="{ enabled: <?php echo $article['cloak_article_link'] ? 'true' : 'false'; ?> }">
                        <input type="hidden" name="cloak_article_link" :value="enabled ? 1 : 0">
                        <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                            <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                        </button>
                    </div>
                </div>

                <?php /* --- Layout Options (Keep as is) --- */ ?>
                 <div x-data="{ open: false, sidebarEnabled: <?php echo $article['enable_sidebar'] ? 'true' : 'false'; ?>, similarPostsEnabled: <?php echo $article['show_similar_posts'] ? 'true' : 'false'; ?>, fbShareEnabled: <?php echo $article['enable_fb_share'] ? 'true' : 'false'; ?>, articleRecsEnabled: <?php echo $article['include_in_recommendations'] ? 'true' : 'false'; ?>, customRecs: <?php echo !empty($article['custom_recommendations_code']) ? 'true' : 'false'; ?> }" class="relative border-t border-gray-100 pt-2">
                    <button type="button" @click="open = !open" class="w-full flex items-center justify-between p-1.5 border border-gray-300 rounded-md bg-white text-sm hover:bg-gray-50 shadow-sm">
                        <span class="text-sm font-medium text-gray-700">Layout Options</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 transform transition-transform" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div x-show="open" @click.away="open = false" class="absolute z-10 mt-1 w-full border border-gray-300 rounded-md bg-white shadow-lg p-2 space-y-2" style="display: none;" x-cloak>
                        <label class="flex items-center justify-between p-1 hover:bg-gray-50 rounded cursor-pointer">
                            <span class="text-sm font-medium text-gray-700">Enable Sidebar</span>
                            <div>
                                <input type="hidden" name="enable_sidebar" :value="sidebarEnabled ? 1 : 0">
                                <button type="button" @click.stop="sidebarEnabled = !sidebarEnabled" class="toggle-switch" :class="sidebarEnabled ? 'bg-primary' : 'bg-gray-200'"><span class="toggle-thumb" :class="sidebarEnabled ? 'transform translate-x-5' : ''"></span></button>
                            </div>
                        </label>
                        <label class="flex items-center justify-between p-1 hover:bg-gray-50 rounded cursor-pointer">
                            <span class="text-sm font-medium text-gray-700">Show Similar Posts</span>
                             <div>
                                <input type="hidden" name="show_similar_posts" :value="similarPostsEnabled ? 1 : 0">
                                <button type="button" @click.stop="similarPostsEnabled = !similarPostsEnabled" class="toggle-switch" :class="similarPostsEnabled ? 'bg-primary' : 'bg-gray-200'"><span class="toggle-thumb" :class="similarPostsEnabled ? 'transform translate-x-5' : ''"></span></button>
                            </div>
                        </label>
                        <label class="flex items-center justify-between p-1 hover:bg-gray-50 rounded cursor-pointer">
                            <span class="text-sm font-medium text-gray-700">Facebook Share Button</span>
                             <div>
                                <input type="hidden" name="enable_fb_share" :value="fbShareEnabled ? 1 : 0">
                                <button type="button" @click.stop="fbShareEnabled = !fbShareEnabled" class="toggle-switch" :class="fbShareEnabled ? 'bg-primary' : 'bg-gray-200'"><span class="toggle-thumb" :class="fbShareEnabled ? 'transform translate-x-5' : ''"></span></button>
                            </div>
                        </label>
                        <label class="flex items-center justify-between p-1 hover:bg-gray-50 rounded cursor-pointer">
                            <span class="text-sm font-medium text-gray-700">Include in Article Recs</span>
                            <div>
                                <input type="hidden" name="include_in_recommendations" :value="articleRecsEnabled ? 1 : 0">
                                <button type="button" @click.stop="articleRecsEnabled = !articleRecsEnabled" class="toggle-switch" :class="articleRecsEnabled ? 'bg-primary' : 'bg-gray-200'"><span class="toggle-thumb" :class="articleRecsEnabled ? 'transform translate-x-5' : ''"></span></button>
                            </div>
                        </label>
                        <div>
                            <label class="flex items-center justify-between p-1 hover:bg-gray-50 rounded cursor-pointer">
                                <span class="text-sm font-medium text-gray-700">Custom Recs Code</span>
                                <div>
                                    <button type="button" @click.stop="customRecs = !customRecs" class="toggle-switch" :class="customRecs ? 'bg-primary' : 'bg-gray-200'"><span class="toggle-thumb" :class="customRecs ? 'transform translate-x-5' : ''"></span></button>
                                </div>
                            </label>
                            <div x-show="customRecs" class="mt-2 p-2 bg-gray-50 rounded border border-gray-200">
                                <textarea name="custom_recommendations_code" class="form-textarea text-xs font-mono h-20" placeholder="Paste custom HTML/JS code here..."><?php echo escape($article['custom_recommendations_code']); ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <?php /* --- Image Optimization (Keep as is) --- */ ?>
                <div x-data="{ open: false }" class="relative">
                     <button type="button" @click="open = !open" class="w-full flex items-center justify-between p-1.5 border border-gray-300 rounded-md bg-white text-sm hover:bg-gray-50 shadow-sm">
                        <span class="text-sm font-medium text-gray-700">Image Optimization</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 transform transition-transform" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                     <div x-show="open" @click.away="open = false" class="absolute z-10 mt-1 w-full border border-gray-300 rounded-md bg-white shadow-lg p-2 space-y-2" style="display: none;" x-cloak>
                        <p class="text-xs text-gray-500 italic">Image processing options (quality, sizes, contrast, etc.) are currently managed globally via <code class="text-xs bg-gray-100 px-1 rounded">config.php</code>.</p>
                     </div>
                </div>
                <?php /* --- CDN Load Options (Keep as is) --- */ ?>
                <div x-data="{ open: false }" class="relative">
                    <button type="button" @click="open = !open" class="w-full flex items-center justify-between p-1.5 border border-gray-300 rounded-md bg-white text-sm hover:bg-gray-50 shadow-sm">
                        <span class="text-sm font-medium text-gray-700">CDN Load Options</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 transform transition-transform" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                     <div x-show="open" @click.away="open = false" class="absolute z-10 mt-1 w-full border border-gray-300 rounded-md bg-white shadow-lg p-2 space-y-2" style="display: none;" x-cloak>
                         <p class="text-xs text-gray-500 italic">CDN settings (<code class="text-xs bg-gray-100 px-1 rounded">CDN_ENABLED</code>, <code class="text-xs bg-gray-100 px-1 rounded">CDN_BASE_URL</code>) are managed globally via <code class="text-xs bg-gray-100 px-1 rounded">config.php</code>.</p>
                     </div>
                </div>

                <?php /* --- Auto Internal Linking --- */ ?>
                 <div class="border-t border-gray-100 pt-2" x-data='{
                        keywords: <?php echo json_encode(array_map('trim', array_filter(explode(',', $article['auto_link_keywords'] ?? '')))); ?>,
                        newKeyword: "",
                        autoLinkEnabled: <?php echo ($article['enable_auto_linking'] ?? true) ? 'true' : 'false'; ?>,
                        keywordsString: "", // For hidden input
                        suggestedLinks: [],
                        showSuggestions: false,
                        isLoading: false,
                        init() {
                            this.updateKeywordsString();
                            this.$watch("keywords", () => this.updateKeywordsString());
                        },
                        addKeyword() {
                            const kw = this.newKeyword.trim();
                            if (kw && !this.keywords.includes(kw)) {
                                this.keywords.push(kw);
                            }
                            this.newKeyword = "";
                        },
                        removeKeyword(index) {
                            this.keywords.splice(index, 1);
                        },
                        updateKeywordsString() {
                            this.keywordsString = this.keywords.join(",");
                        },
                        suggestLinks() {
                            const title = document.getElementById("title").value.trim();
                            const content = document.getElementById("content").value.trim();

                            if (!title || !content) {
                                alert("Please enter both title and content before suggesting links.");
                                return;
                            }

                            this.isLoading = true;
                            this.showSuggestions = true;

                            // Log values for debugging
                            console.log("Sending title:", title);
                            console.log("Sending content:", content);

                            // Prepare data for the API call
                            const formData = new FormData();
                            formData.append("action", "suggest_internal_links");
                            formData.append("title_text", title); // Changed from "title" to "title_text" to match DeepSeek handler
                            formData.append("input_text", content); // Changed from "content" to "input_text" to match DeepSeek handler

                            // Make AJAX call to the DeepSeek handler
                            fetch("deepseek_handler.php", {
                                method: "POST",
                                body: formData
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success && data.suggestions) {
                                    this.suggestedLinks = data.suggestions;
                                } else {
                                    console.error("Error suggesting links:", data.error || "Unknown error");
                                    alert("Failed to suggest links: " + (data.error || "Unknown error"));
                                    this.suggestedLinks = [];
                                }
                            })
                            .catch(error => {
                                console.error("Error suggesting links:", error);
                                alert("Failed to suggest links: " + error.message);
                                this.suggestedLinks = [];
                            })
                            .finally(() => {
                                this.isLoading = false;
                            });
                        },
                        addSuggestedKeyword(keyword) {
                            if (keyword && !this.keywords.includes(keyword)) {
                                this.keywords.push(keyword);
                            }
                        }
                    }'>
                    <div class="flex items-center justify-between mb-1">
                         <label class="flex items-center gap-2 text-sm font-medium text-gray-700">
                             <span>Auto Internal Linking</span>
                              <button type="button" id="aiSuggestKeywordsBtn" @click="suggestLinks" class="text-primary hover:opacity-80 focus:outline-none" :disabled="isLoading" title="AI Suggest Keywords">
                                <span class="ai-btn-text" x-show="!isLoading"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg></span>
                                <span class="ai-loading" x-show="isLoading"><svg class="animate-spin h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg></span>
                              </button>
                         </label>
                        <div>
                             <input type="hidden" name="enable_auto_linking" :value="autoLinkEnabled ? 1 : 0">
                            <button type="button" @click="autoLinkEnabled = !autoLinkEnabled" class="toggle-switch" :class="autoLinkEnabled ? 'bg-primary' : 'bg-gray-200'" title="Enable/Disable Auto Linking">
                                <span class="toggle-thumb" :class="autoLinkEnabled ? 'transform translate-x-5' : ''"></span>
                            </button>
                        </div>
                    </div>
                    <div class="text-xs text-gray-500 mb-2">Automatically link keywords in content to other articles on your site.</div>

                    <!-- AI Suggested Links -->
                    <div x-show="showSuggestions" class="mb-3 border border-gray-200 rounded-md p-2 bg-gray-50">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="text-sm font-medium text-gray-700">Suggested Links</h4>
                            <button type="button" @click="showSuggestions = false" class="text-gray-400 hover:text-gray-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div x-show="isLoading" class="text-center py-4">
                            <svg class="animate-spin h-5 w-5 text-primary mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <p class="text-xs text-gray-500 mt-2">Generating suggestions...</p>
                        </div>
                        <div x-show="!isLoading && suggestedLinks.length === 0" class="text-center py-4">
                            <p class="text-xs text-gray-500">No suggestions available. Try again with more content.</p>
                        </div>
                        <div x-show="!isLoading && suggestedLinks.length > 0" class="space-y-2 max-h-40 overflow-y-auto">
                            <template x-for="(link, index) in suggestedLinks" :key="index">
                                <div class="bg-white p-2 rounded border border-gray-200 text-xs">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="font-medium" x-text="link.keyword"></div>
                                            <div class="text-gray-500 text-xs" x-text="link.title || 'Article #' + link.article_id"></div>
                                        </div>
                                        <button type="button" @click="addSuggestedKeyword(link.keyword)" class="text-primary hover:text-primary-dark text-xs">
                                            Add
                                        </button>
                                    </div>
                                    <div class="mt-1 text-gray-600" x-text="link.reason"></div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- Manual Keywords -->
                    <div class="space-y-2">
                        <div class="flex gap-1">
                            <input type="text" x-model="newKeyword" @keydown.enter.prevent="addKeyword" @keydown.,.prevent="addKeyword" class="form-input text-xs flex-grow" placeholder="Add keyword...">
                            <button type="button" @click="addKeyword" class="btn-secondary text-xs py-1 px-2">Add</button>
                        </div>
                        <div class="flex flex-wrap gap-1 min-h-[24px]">
                            <template x-for="(keyword, index) in keywords" :key="index">
                                <span class="bg-gray-100 text-xs rounded-full px-2 py-1 flex items-center shadow-sm">
                                    <span x-text="keyword"></span>
                                    <button type="button" @click="removeKeyword(index)" class="ml-1 text-gray-500 hover:text-red-500 focus:outline-none">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                                    </button>
                                </span>
                            </template>
                             <span x-show="keywords.length === 0" class="text-xs text-gray-400 italic">No keywords added yet.</span>
                             <input type="hidden" name="auto_link_keywords" :value="keywordsString">
                        </div>
                        <div class="flex justify-between items-center">
                            <p class="text-xs text-gray-500">Separate keywords with commas or press Enter.</p>
                            <a href="internal_links.php" target="_blank" class="text-xs text-primary hover:underline">Manage Internal Links</a>
                        </div>
                    </div>
                </div>

                  <?php /* --- Author Section --- */ ?>
                 <div x-data="{ randomAuthorEnabled: <?php echo isset($article['use_random_author']) ? ($article['use_random_author'] ? 'true' : 'false') : (!$edit_mode ? 'true' : 'false'); ?> }"> <?php // Default ON for new articles ?>
                     <div class="flex items-center justify-between mb-1">
                        <label for="author_id" class="block text-sm font-medium text-gray-700">Author</label>
                        <div>
                             <label class="flex items-center cursor-pointer" title="Use Random Author">
                                <span class="text-xs text-gray-600 mr-2">Use Random Author</span>
                                <input type="hidden" name="use_random_author" :value="randomAuthorEnabled ? 1 : 0">
                                <button type="button" @click="randomAuthorEnabled = !randomAuthorEnabled" class="toggle-switch" :class="randomAuthorEnabled ? 'bg-primary' : 'bg-gray-200'">
                                    <span class="toggle-thumb" :class="randomAuthorEnabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                             </label>
                        </div>
                    </div>
                    <select id="author_id" name="author_id" class="form-select" :disabled="randomAuthorEnabled" :class="{ 'bg-gray-100 cursor-not-allowed': randomAuthorEnabled }">
                        <option value="">-- Select Author --</option>
                         <?php foreach ($authors as $author): ?>
                            <option value="<?php echo $author['id']; ?>" <?php echo (isset($article['author_id']) && $article['author_id'] == $author['id']) ? 'selected' : ''; ?>>
                                <?php echo escape($author['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                     <p class="text-xs text-gray-500 mt-1">If 'Use Random Author' is ON, the selected author will be ignored.</p>
                </div>

                <?php /* --- Publish Settings (Keep as is) --- */ ?>
                <div x-data="{ open: false }" class="border-t border-gray-100 pt-2">
                    <button type="button" @click="open = !open" class="w-full flex items-center justify-between p-1.5 border border-gray-300 rounded-md bg-white text-sm hover:bg-gray-50 shadow-sm">
                        <span class="text-sm font-medium text-gray-700">Publish Settings</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 transform transition-transform" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div x-show="open" x-transition class="mt-1 border border-gray-300 rounded-md bg-gray-50 p-3 space-y-3" style="display: none;" x-cloak>
                         <div>
                            <label for="status" class="block text-xs font-medium text-gray-700 mb-1">Status *</label>
                            <select id="status" name="status" class="form-select py-1.5" required>
                                <option value="draft" <?php echo (isset($article['status']) && $article['status'] === 'draft') ? 'selected' : ''; ?>>Draft</option>
                                <option value="published" <?php echo (isset($article['status']) && $article['status'] === 'published') ? 'selected' : ''; ?>>Published</option>
                                <option value="scheduled" <?php echo (isset($article['status']) && $article['status'] === 'scheduled') ? 'selected' : ''; ?>>Scheduled</option>
                                <option value="archived" <?php echo (isset($article['status']) && $article['status'] === 'archived') ? 'selected' : ''; ?>>Archived</option>
                            </select>
                        </div>
                         <div>
                            <label for="published_at" class="block text-xs font-medium text-gray-700 mb-1">Publish Date/Time</label>
                            <input type="datetime-local" id="published_at" name="published_at" class="form-input py-1.5" value="<?php echo escape($article['published_at_formatted'] ?? ''); ?>">
                            <p class="text-xs text-gray-500 mt-1">Set for 'Scheduled' status or to backdate 'Published'.</p>
                        </div>
                         <div class="pt-2 border-t border-gray-200" x-data="{ generateComments: <?php echo !$edit_mode ? 'true' : (isset($article['generate_comments']) && $article['generate_comments'] ? 'true' : 'false'); ?>, commentCount: <?php echo isset($article['comment_count']) ? $article['comment_count'] : 5; ?> }">
                             <label class="flex items-center cursor-pointer">
                                 <input type="checkbox" name="generate_comments" value="1" class="form-checkbox" x-model="generateComments" <?php echo !$edit_mode ? 'checked' : ''; ?>>
                                 <span class="ml-2 text-sm text-gray-700">Generate random comments</span>
                             </label>
                             <div class="flex items-center mt-2" x-show="generateComments">
                                 <label for="comment_count" class="text-xs text-gray-700 mr-2">Number of comments:</label>
                                 <input type="number" id="comment_count" name="comment_count" class="form-input py-1 px-2 text-xs w-16" min="1" max="10" x-model="commentCount" value="<?php echo isset($article['comment_count']) ? $article['comment_count'] : 5; ?>">
                             </div>
                             <p class="text-xs text-gray-500 mt-1">Adds AI-generated comments on save. (Default: 5 comments for new articles)</p>
                        </div>
                         </div>
                </div>

                 </div>
        </div>
    </div>
</form>

<?php /* --- JavaScript for Tags Component, AI, Markdown Toolbar, Copy Link --- */ ?>
<style>
    .markdown-toolbar { /* Basic styling for the toolbar */
        background-color: #f9fafb; /* gray-50 */
        padding: 0.5rem;
        border: 1px solid #e5e7eb; /* gray-200 */
        border-bottom: none;
        border-top-left-radius: 0.375rem; /* rounded-md */
        border-top-right-radius: 0.375rem; /* rounded-md */
        display: flex;
        align-items: center;
        gap: 0.25rem; /* space-x-1 */
    }
    .md-toolbar-btn {
        padding: 0.375rem; /* p-1.5 */
        border-radius: 0.375rem; /* rounded-md */
        background-color: transparent;
        border: 1px solid transparent;
        color: #6b7280; /* gray-500 */
        transition: background-color 0.2s, color 0.2s, border-color 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 2rem; /* Ensure buttons have some width */
    }
    .md-toolbar-btn:hover {
        background-color: #f3f4f6; /* gray-100 */
        color: #374151; /* gray-700 */
    }
     .md-toolbar-btn:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(255, 100, 129, 0.5); /* Ring like Tailwind */
        border-color: #ff6481; /* primary */
    }
    .md-toolbar-btn svg {
        height: 1.25rem; /* h-5 */
        width: 1.25rem; /* w-5 */
    }
     .md-toolbar-btn span:not(svg span) { /* Style text inside buttons if needed */
        font-size: 0.875rem; /* text-sm */
        line-height: 1.25rem;
     }
    /* Image upload notification */
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
    .upload-fade-out {
        animation: fadeOut 0.5s ease-out forwards;
    }
</style>

<script>
    // Function to copy text to clipboard and provide visual feedback
    function copyLinkToClipboard(textToCopy, buttonElement) {
        navigator.clipboard.writeText(textToCopy).then(() => {
            const originalTextElement = buttonElement.querySelector('.copy-text');
            if (!originalTextElement) return;
            const originalText = originalTextElement.textContent;
            originalTextElement.textContent = 'Copied!';
            const parentDiv = buttonElement.closest('div');
             if (parentDiv && parentDiv.querySelector('.text-orange-700')) {
                 buttonElement.classList.add('bg-orange-600'); buttonElement.classList.remove('bg-orange-500');
             } else {
                 buttonElement.classList.add('bg-green-700'); buttonElement.classList.remove('bg-green-600');
             }
            setTimeout(() => {
                 if (parentDiv && parentDiv.querySelector('.text-orange-700')) {
                     buttonElement.classList.remove('bg-orange-600'); buttonElement.classList.add('bg-orange-500');
                 } else {
                     buttonElement.classList.remove('bg-green-700'); buttonElement.classList.add('bg-green-600');
                 }
                originalTextElement.textContent = originalText;
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy link: ', err);
            const originalTextElement = buttonElement.querySelector('.copy-text');
            if (!originalTextElement) return;
            const originalText = 'Copy';
            originalTextElement.textContent = 'Error';
            buttonElement.classList.add('bg-red-500');
            setTimeout(() => {
                originalTextElement.textContent = originalText;
                buttonElement.classList.remove('bg-red-500');
                 const parentDiv = buttonElement.closest('div');
                 if (parentDiv && parentDiv.querySelector('.text-orange-700')) {
                     buttonElement.classList.add('bg-orange-500');
                 } else {
                     buttonElement.classList.add('bg-green-600');
                 }
            }, 2000);
        });
    }

    // --- Alpine.js Data Function for Categories Component ---
    function categoriesComponentData() {
        return {
            primaryCategoryId: <?php echo isset($article['category_id']) && $article['category_id'] ? $article['category_id'] : 'null'; ?>,
            updatePrimaryCategory(event, categoryId) {
                // If a checkbox is checked and there's no primary category yet, set this as primary
                if (event.target.checked) {
                    if (this.primaryCategoryId === null) {
                        this.primaryCategoryId = categoryId;
                    }
                } else {
                    // If the primary category is unchecked, find the first checked checkbox and make it primary
                    if (this.primaryCategoryId === categoryId) {
                        const checkboxes = document.querySelectorAll('input[name="category_ids[]"]:checked');
                        if (checkboxes.length > 0) {
                            this.primaryCategoryId = parseInt(checkboxes[0].value);
                        } else {
                            this.primaryCategoryId = null;
                        }
                    }
                }
            },
            // Method to manually set a category as primary when clicked
            setPrimaryCategory(categoryId) {
                // Only set as primary if the checkbox is checked
                const checkbox = document.getElementById(`category_${categoryId}`);
                if (checkbox && checkbox.checked) {
                    this.primaryCategoryId = categoryId;
                }
            }
        };
    }

    // --- Alpine.js Data Function for Tags Component ---
    function tagsComponentData() {
        const initialTagsJson = <?php echo json_encode($article_tags_string); ?>;
        const initialTagsArray = initialTagsJson ? initialTagsJson.split(',').map(t => t.trim()).filter(t => t) : [];

        return {
            tags: initialTagsArray,
            newTag: "",
            tagsString: "",
            init() {
                this.updateTagsString();
                this.$watch("tags", () => this.updateTagsString());
                // Listen for the custom event dispatched by handleAIClick
                 this.$el.addEventListener('ai-suggest-tags', (event) => {
                     if (event.detail && event.detail.tags) {
                         this.updateTagsFromAI(event.detail.tags);
                     }
                 });
            },
            addTag() {
                const tagToAdd = this.newTag.trim();
                if (tagToAdd && !this.tags.map(t => t.toLowerCase()).includes(tagToAdd.toLowerCase())) {
                    this.tags.push(tagToAdd);
                }
                this.newTag = "";
            },
            removeTag(index) {
                this.tags.splice(index, 1);
            },
            updateTagsString() {
                this.tagsString = this.tags.join(", ");
                const hiddenInput = document.getElementById("tagsHiddenInput");
                if (hiddenInput) {
                    hiddenInput.value = this.tagsString;
                }
            },
            updateTagsFromAI(aiTagsString) {
                 if (typeof aiTagsString !== 'string' || !aiTagsString) {
                     console.error("Invalid tags string received from AI.");
                     return;
                 }
                const aiTags = aiTagsString.split(',').map(t => t.trim().toLowerCase()).filter(t => t);
                const currentLowerTags = this.tags.map(t => t.toLowerCase());
                let added = 0;
                aiTags.forEach(aiTag => {
                    if (!currentLowerTags.includes(aiTag)) {
                        this.tags.push(aiTag); // Add lowercase version
                        added++;
                    }
                });
            }
        };
    }


    // --- DeepSeek API Integration (Keep as is) ---
    function handleAIClick(action, sourceElementId, targetElementIdOrIds, rewriteTitle = false, rewriteContent = false, contextElementId = null) {
        const sourceElement = document.getElementById(sourceElementId);
        let targetElements = {}; // Store target elements by ID

        if (Array.isArray(targetElementIdOrIds)) {
            targetElementIdOrIds.forEach(id => {
                const el = document.getElementById(id);
                if (el) targetElements[id] = el;
            });
        } else {
            // Handle tagsComponent separately as it doesn't need a target element found here
            if (targetElementIdOrIds !== 'tagsComponent') {
                const el = document.getElementById(targetElementIdOrIds);
                if (el) targetElements[targetElementIdOrIds] = el;
            }
        }

        const contextElement = contextElementId ? document.getElementById(contextElementId) : null;

        let buttonId = '';
        switch(action) {
            case 'rewrite_combined': buttonId = 'aiRewriteCombinedBtn'; break;
            case 'generate_excerpt': buttonId = 'aiRewriteExcerptBtn'; break;
            case 'suggest_tags': buttonId = 'aiSuggestTagsBtn'; break;
            case 'suggest_meta_title': buttonId = 'aiFillMetaTitleBtn'; break;
            case 'suggest_meta_desc': buttonId = 'aiFillMetaDescBtn'; break;
            case 'suggest_focus_keyword': buttonId = 'aiFillFocusKeywordBtn'; break;
             // Add case for rewrite_title if needed for a separate button
             case 'rewrite_title': buttonId = 'aiRewriteTitleBtn'; break; // Assuming button ID
            default: console.error('Unknown AI action:', action); return;
        }
        const button = document.getElementById(buttonId);

        // Validation
        if (!sourceElement || (Object.keys(targetElements).length === 0 && targetElementIdOrIds !== 'tagsComponent') || !button) {
            console.error('Required elements not found for AI action:', action, `(Source: ${sourceElementId}, Targets: ${JSON.stringify(targetElementIdOrIds)}, Button: ${buttonId})`);
            alert('Error: Could not find necessary form elements.');
            return;
        }
         const contextText = contextElement ? contextElement.value.trim() : '';
         let primaryInputText = sourceElement.value.trim();
         if (!primaryInputText) { alert(`Please enter content in '${sourceElementId}' first.`); return; }
         if (contextElementId && !contextElement) { alert('Error: Could not find the context element.'); return; }
         if (contextElementId && !contextText && (action === 'suggest_meta_title' || action === 'suggest_meta_desc' || action === 'suggest_focus_keyword' || action === 'rewrite_combined' || action === 'rewrite_title')) { alert(`Please enter content in '${contextElementId}' for context.`); return; }


        // Show loading state
        const btnTextSpan = button.querySelector('.ai-btn-text');
        const loadingSpinner = button.querySelector('.ai-loading');
        if (!btnTextSpan || !loadingSpinner) { console.error('Button loading elements not found'); return; }
        btnTextSpan.style.display = 'none'; loadingSpinner.style.display = 'inline-block'; button.disabled = true;

        // Prepare data
        const formData = new FormData();
        formData.append('action', action);
        formData.append('input_text', primaryInputText);
        if (contextElementId && contextElement) { formData.append('title_text', contextText); }
        if (action === 'rewrite_combined') { formData.append('rewrite_title', rewriteTitle ? '1' : '0'); formData.append('rewrite_content', rewriteContent ? '1' : '0'); }

        // Make AJAX call
        fetch('deepseek_handler.php', { method: 'POST', body: formData })
        .then(response => {
            if (!response.ok) { return response.text().then(text => { console.error("Raw error response:", text); throw new Error(`HTTP error! status: ${response.status}, message: ${text.substring(0, 500)}`); }); }
            const contentType = response.headers.get("content-type");
            if (contentType && contentType.indexOf("application/json") !== -1) { return response.json(); }
            else { return response.text().then(text => { console.error("Non-JSON response received:", text); throw new Error(`Expected JSON response, but received ${contentType || 'text/plain'}`); }); }
        })
        .then(data => {
            if (data.success) {
                if (action === 'rewrite_combined') {
                    if (data.rewritten_title && targetElements['title']) { targetElements['title'].value = data.rewritten_title; targetElements['title'].dispatchEvent(new Event('input', { bubbles: true })); }
                    if (data.rewritten_content && targetElements['content']) { targetElements['content'].value = data.rewritten_content; targetElements['content'].dispatchEvent(new Event('input', { bubbles: true })); }
                } else if (targetElementIdOrIds === 'tagsComponent') {
                    // Dispatch custom event for tags
                    if (data.text) {
                        const event = new CustomEvent('ai-suggest-tags', { detail: { tags: data.text } });
                        document.getElementById('tagsComponent').dispatchEvent(event);
                    } else { console.warn("Suggest Tags action succeeded but returned no text."); }
                } else {
                     // Handle single target update including 'rewrite_title'
                    const targetId = Array.isArray(targetElementIdOrIds) ? targetElementIdOrIds[0] : targetElementIdOrIds;
                    if (data.text && targetElements[targetId]) {
                        targetElements[targetId].value = data.text;
                        targetElements[targetId].dispatchEvent(new Event('input', { bubbles: true }));
                    } else if (!data.text) { console.warn('API success but no text received for action:', action); }
                }
            } else { console.error('API Error:', data.error || 'Unknown API error'); alert('AI request failed: ' + (data.error || 'An unknown error occurred.')); }
        })
        .catch(error => {
             console.error('Fetch Error:', error);
             let alertMessage = 'An error occurred while contacting the AI service.';
             if (error.message.includes('Failed to fetch')) { alertMessage = 'Network error. Could not connect to the AI service.'; }
             else if (error.message.includes('JSON')) { alertMessage = 'Received an unexpected response from the server.'; }
             else if (error.message.includes('HTTP error')) { alertMessage = 'The server returned an error. ' + error.message.split('message: ')[1]; }
             else { alertMessage += ' ' + error.message; }
             alert(alertMessage);
        })
        .finally(() => { btnTextSpan.style.display = 'inline-block'; loadingSpinner.style.display = 'none'; button.disabled = false; });
    }

    // --- START: Markdown Toolbar JS ---
    function insertMarkdown(prefix, suffix = '', placeholder = 'text') {
        const textarea = document.getElementById('content');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        const textToInsert = prefix + (selectedText || placeholder) + suffix;

        // Insert the text
        textarea.value = textarea.value.substring(0, start) + textToInsert + textarea.value.substring(end);

        // Adjust cursor position
        if (selectedText) {
            // If text was selected, place cursor after the inserted block
            textarea.selectionStart = textarea.selectionEnd = start + textToInsert.length;
        } else {
            // If no text was selected, place cursor inside the placeholder/tags
            textarea.selectionStart = start + prefix.length;
            textarea.selectionEnd = start + prefix.length + placeholder.length;
        }
        textarea.focus(); // Keep focus on the textarea
    }

    function insertLink() {
        const textarea = document.getElementById('content');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        const url = prompt("Enter the URL:", "https://");

        if (url) { // Proceed only if a URL was entered
            const linkText = selectedText || "link text";
            const textToInsert = '[' + linkText + '](' + url + ')';

            // Insert the text
            textarea.value = textarea.value.substring(0, start) + textToInsert + textarea.value.substring(end);

            // Adjust cursor position
            textarea.selectionStart = textarea.selectionEnd = start + textToInsert.length;
            textarea.focus();
        }
    }
    // --- END: Markdown Toolbar JS ---

    // Handle paste events to automatically upload images from copied content
    document.getElementById('content').addEventListener('paste', function(e) {
        // Get the current cursor position for insertion
        const cursorPosition = this.selectionStart;
        const editor = this;

        // We need to use setTimeout because innerHTML isn't updated immediately on paste
        setTimeout(function() {
            const pastedContent = editor.value;

            // Check if the pasted content contains image references
            if (pastedContent.indexOf('<img') === -1) {
                return; // No images found, exit
            }

            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = pastedContent;

            // Find all image elements in the pasted content
            const images = tempDiv.querySelectorAll('img');

            if (images.length > 0) {
                // Show a loading indicator
                const loadingMessage = document.createElement('div');
                loadingMessage.id = 'image-upload-status';
                loadingMessage.className = 'fixed bottom-4 right-4 bg-primary text-white px-4 py-2 rounded-md shadow-lg z-50';
                loadingMessage.innerHTML = `Uploading ${images.length} image(s)...`;
                document.body.appendChild(loadingMessage);

                let uploadedCount = 0;
                let failedCount = 0;

                // Process each image
                images.forEach(function(img) {
                    const sourceUrl = img.src;

                    // Skip data URLs - they're already embedded
                    if (sourceUrl.startsWith('data:')) {
                        uploadedCount++;
                        updateStatus();
                        return;
                    }

                    // Skip relative URLs that might be already on your site
                    if (sourceUrl.startsWith('/') || !sourceUrl.startsWith('http')) {
                        uploadedCount++;
                        updateStatus();
                        return;
                    }

                    // Upload the image via AJAX
                    const formData = new FormData();
                    formData.append('action', 'upload_from_url');
                    formData.append('image_url', sourceUrl);

                    fetch('image_upload_handler.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Replace the old URL with the new local URL
                            editor.value = editor.value.replace(sourceUrl, data.url);
                        } else {
                            failedCount++;
                            console.error('Failed to upload image:', data.error);
                        }
                        uploadedCount++;
                        updateStatus();
                    })
                    .catch(error => {
                        failedCount++;
                        uploadedCount++;
                        console.error('Error uploading image:', error);
                        updateStatus();
                    });
                });

                function updateStatus() {
                    if (uploadedCount === images.length) {
                        if (failedCount > 0) {
                            loadingMessage.innerHTML = `Uploaded ${uploadedCount - failedCount}/${images.length} images. ${failedCount} failed.`;
                            loadingMessage.className = 'fixed bottom-4 right-4 bg-orange-500 text-white px-4 py-2 rounded-md shadow-lg z-50';
                        } else {
                            loadingMessage.innerHTML = `Successfully uploaded ${images.length} images!`;
                            loadingMessage.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50';
                        }

                        // Remove the status message after a delay
                        setTimeout(function() {
                            loadingMessage.remove();
                        }, 3000);
                    } else {
                        loadingMessage.innerHTML = `Uploading images: ${uploadedCount}/${images.length}`;
                    }
                }
            }
        }, 100);
    });
</script>

<?php
// Include the admin footer
include 'includes/footer.php';
?>