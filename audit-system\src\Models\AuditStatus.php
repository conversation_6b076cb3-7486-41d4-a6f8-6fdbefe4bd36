<?php

namespace AuditSystem\Models;

use DateTime;

/**
 * Represents current audit status and progress information
 */
class AuditStatus
{
    public string $phase;
    public float $completionPercentage;
    public int $totalFiles;
    public int $processedFiles;
    public int $remainingFiles;
    public AuditStatistics $statistics;
    public DateTime $lastUpdate;
    public bool $isRunning;
    public ?string $currentFile;

    public function __construct()
    {
        $this->phase = 'not_started';
        $this->completionPercentage = 0.0;
        $this->totalFiles = 0;
        $this->processedFiles = 0;
        $this->remainingFiles = 0;
        $this->statistics = new AuditStatistics();
        $this->lastUpdate = new DateTime();
        $this->isRunning = false;
        $this->currentFile = null;
    }

    /**
     * Update status from audit progress
     *
     * @param AuditProgress $progress
     * @return void
     */
    public function updateFromProgress(AuditProgress $progress): void
    {
        $this->phase = $progress->currentPhase;
        $this->completionPercentage = $progress->getCompletionPercentage();
        $this->totalFiles = $progress->statistics['totalFiles'];
        $this->processedFiles = $progress->statistics['processedFiles'];
        $this->remainingFiles = $this->totalFiles - $this->processedFiles;
        $this->lastUpdate = $progress->lastUpdate;
        
        // Update statistics from findings
        $this->statistics->totalFindings = $progress->statistics['totalFindings'];
        $this->statistics->criticalFindings = $progress->statistics['criticalFindings'];
        $this->statistics->priorityAreaFindings = $progress->statistics['priorityAreaFindings'];
    }

    /**
     * Convert to array for serialization
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'phase' => $this->phase,
            'completionPercentage' => $this->completionPercentage,
            'totalFiles' => $this->totalFiles,
            'processedFiles' => $this->processedFiles,
            'remainingFiles' => $this->remainingFiles,
            'statistics' => $this->statistics->toArray(),
            'lastUpdate' => $this->lastUpdate->format('Y-m-d H:i:s'),
            'isRunning' => $this->isRunning,
            'currentFile' => $this->currentFile
        ];
    }
}