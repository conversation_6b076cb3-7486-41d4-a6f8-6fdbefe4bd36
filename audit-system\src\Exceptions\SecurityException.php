<?php

namespace AuditSystem\Exceptions;

/**
 * Exception thrown when security-related operations fail
 */
class SecurityException extends AuditException
{
    /**
     * Create exception for unauthorized access attempt
     *
     * @param string $resource Resource that was accessed
     * @param string $action Action that was attempted
     * @return static
     */
    public static function unauthorizedAccess(string $resource, string $action): self
    {
        return new self("Unauthorized access attempt: {$action} on {$resource}");
    }

    /**
     * Create exception for path traversal attempt
     *
     * @param string $path Suspicious path
     * @return static
     */
    public static function pathTraversal(string $path): self
    {
        return new self("Path traversal attempt detected: {$path}");
    }

    /**
     * Create exception for file type restriction
     *
     * @param string $filePath Path to restricted file
     * @param string $fileType File type that is restricted
     * @return static
     */
    public static function restrictedFileType(string $filePath, string $fileType): self
    {
        return new self("Access to {$fileType} files is restricted: {$filePath}");
    }

    /**
     * Create exception for size limit exceeded
     *
     * @param string $resource Resource that exceeded limits
     * @param int $actualSize Actual size
     * @param int $maxSize Maximum allowed size
     * @return static
     */
    public static function sizeLimitExceeded(string $resource, int $actualSize, int $maxSize): self
    {
        return new self("Size limit exceeded for {$resource}: {$actualSize} bytes (max: {$maxSize} bytes)");
    }

    /**
     * Create exception for malicious content detection
     *
     * @param string $filePath Path to file with malicious content
     * @param string $threatType Type of threat detected
     * @return static
     */
    public static function maliciousContent(string $filePath, string $threatType): self
    {
        return new self("Malicious content detected in {$filePath}: {$threatType}");
    }
}