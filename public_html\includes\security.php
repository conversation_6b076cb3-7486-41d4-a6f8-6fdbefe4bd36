<?php
/**
 * Security Functions
 *
 * This file contains functions for enhancing website security.
 */

/**
 * Generate Content Security Policy (CSP) headers
 *
 * @param bool $reportOnly Whether to use report-only mode
 * @return string The CSP header string
 */
function generateCSP($reportOnly = false) {
    $headerName = $reportOnly ? 'Content-Security-Policy-Report-Only' : 'Content-Security-Policy';

    // Define CSP directives
    $csp = [
        "default-src" => ["'self'"],
        "script-src" => ["'self'", "https://cdn.tailwindcss.com", "https://cdnjs.cloudflare.com", "https://pagead2.googlesyndication.com", "https://www.googletagmanager.com", "https://connect.facebook.net", "https://googleads.g.doubleclick.net", "https://adservice.google.com", "https://www.google.com", "https://tpc.googlesyndication.com", "https://*.adtrafficquality.google", "'unsafe-inline'", "'unsafe-eval'"],
        "style-src" => ["'self'", "https://fonts.googleapis.com", "'unsafe-inline'"],
        "img-src" => ["'self'", "data:", "blob:", "https:", "http:"],
        "font-src" => ["'self'", "https://fonts.gstatic.com"],
        "connect-src" => ["'self'", "https://www.google-analytics.com", "https://googleads.g.doubleclick.net", "https://adservice.google.com", "https://pagead2.googlesyndication.com", "https://*.adtrafficquality.google"],
        "frame-src" => ["'self'", "https://www.youtube.com", "https://www.facebook.com", "https://pagead2.googlesyndication.com", "https://googleads.g.doubleclick.net", "https://tpc.googlesyndication.com", "https://www.google.com", "https://*.adtrafficquality.google"],
        "object-src" => ["'none'"],
        "base-uri" => ["'self'"],
        "form-action" => ["'self'"],
        "frame-ancestors" => ["'self'"],
        "upgrade-insecure-requests" => [],
    ];

    // Build the CSP header value
    $cspValue = '';
    foreach ($csp as $directive => $sources) {
        if (!empty($sources)) {
            $cspValue .= $directive . ' ' . implode(' ', $sources) . '; ';
        } else {
            $cspValue .= $directive . '; ';
        }
    }

    return trim($cspValue);
}

/**
 * Set security headers
 */
function setSecurityHeaders() {
    // X-Content-Type-Options
    header('X-Content-Type-Options: nosniff');

    // X-Frame-Options
    header('X-Frame-Options: SAMEORIGIN');

    // X-XSS-Protection
    header('X-XSS-Protection: 1; mode=block');

    // Referrer-Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');

    // Permissions-Policy (formerly Feature-Policy)
    header('Permissions-Policy: camera=(), microphone=(), geolocation=()');

    // Strict Transport Security (HSTS)
    header('Strict-Transport-Security: max-age=31536000; includeSubDomains');

    // Content-Security-Policy
    header('Content-Security-Policy: ' . generateCSP());
}

/**
 * Generate a CSRF token
 *
 * @return string The CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify a CSRF token
 *
 * @param string $token The token to verify
 * @return bool Whether the token is valid
 */
function verifyCSRFToken($token) {
    if (!isset($_SESSION['csrf_token']) || empty($token)) {
        return false;
    }

    return hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Sanitize user input
 *
 * @param string $input The input to sanitize
 * @return string The sanitized input
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Check if a request is an AJAX request
 *
 * @return bool Whether the request is an AJAX request
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Log a security event
 *
 * @param string $event The event to log
 * @param array $data Additional data to log
 */
function logSecurityEvent($event, $data = []) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $event,
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
        'data' => $data
    ];

    error_log('SECURITY EVENT: ' . json_encode($logEntry));
}

/**
 * Check for common attack patterns in request data
 *
 * @return bool Whether an attack was detected
 */
function detectAttackPatterns() {
    $patterns = [
        '/(<|%3C)script/i',                 // XSS
        '/union\s+select/i',                // SQL Injection
        '/\/etc\/passwd/i',                 // Path Traversal
        '/\.\.\//',                         // Directory Traversal
        '/exec\s*\(/i',                     // Command Injection
    ];

    // Check GET parameters
    foreach ($_GET as $key => $value) {
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                logSecurityEvent('Attack pattern detected in GET', [
                    'parameter' => $key,
                    'value' => $value,
                    'pattern' => $pattern
                ]);
                return true;
            }
        }
    }

    // Check POST parameters
    foreach ($_POST as $key => $value) {
        if (is_string($value)) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $value)) {
                    logSecurityEvent('Attack pattern detected in POST', [
                        'parameter' => $key,
                        'pattern' => $pattern
                    ]);
                    return true;
                }
            }
        }
    }

    return false;
}

// Apply security headers by default
setSecurityHeaders();

// Check for attack patterns
if (detectAttackPatterns()) {
    // Optionally block the request
    // header('HTTP/1.1 403 Forbidden');
    // exit('Access Denied');
}
