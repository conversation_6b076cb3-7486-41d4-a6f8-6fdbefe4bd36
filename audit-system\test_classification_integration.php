<?php

require_once 'vendor/autoload.php';

use AuditSystem\Services\FindingClassifier;
use AuditSystem\Services\FindingFilter;
use AuditSystem\Models\Finding;

echo "Testing Complete Finding Classification and Prioritization System...\n\n";

$classifier = new FindingClassifier();

// Simulate real audit findings from the Lako & Fino CMS
$rawFindings = [
    // Critical security issues in priority areas
    [
        'file' => 'public_html/config.php',
        'line' => 15,
        'type' => Finding::TYPE_SECURITY,
        'subtype' => 'sql_injection',
        'description' => 'Direct SQL concatenation in database connection',
        'codeSnippet' => '$query = "SELECT * FROM users WHERE id = " . $_GET["id"];'
    ],
    [
        'file' => 'public_html/smrsaj-deepseek-api.php',
        'line' => 45,
        'type' => Finding::TYPE_SECURITY,
        'subtype' => 'sensitive_data_exposure',
        'description' => 'API key hardcoded in source code',
        'codeSnippet' => '$api_key = "sk-1234567890abcdef";'
    ],
    
    // Performance issues in ad system
    [
        'file' => 'public_html/process_ad_impressions.php',
        'line' => 30,
        'type' => Finding::TYPE_PERFORMANCE,
        'subtype' => 'n_plus_one_query',
        'description' => 'Multiple database queries in impression tracking loop',
        'codeSnippet' => 'foreach ($ads as $ad) { $db->query("SELECT * FROM impressions WHERE ad_id = " . $ad->id); }'
    ],
    [
        'file' => 'public_html/record_impression.php',
        'line' => 20,
        'type' => Finding::TYPE_PERFORMANCE,
        'subtype' => 'missing_database_index',
        'description' => 'Missing index on frequently queried impression table',
        'context' => ['table' => 'impressions', 'column' => 'ad_id']
    ],
    
    // Frontend issues in user-facing areas
    [
        'file' => 'public_html/css/main.css',
        'line' => 100,
        'type' => Finding::TYPE_PERFORMANCE,
        'subtype' => 'blocking_resource',
        'description' => 'Large CSS file blocking page render',
        'context' => ['file_size' => '250KB']
    ],
    [
        'file' => 'public_html/js/app.js',
        'line' => 50,
        'type' => Finding::TYPE_QUALITY,
        'subtype' => 'high_complexity',
        'description' => 'Complex JavaScript function with high cyclomatic complexity',
        'context' => ['complexity' => 18]
    ],
    
    // Image handling issues
    [
        'file' => 'public_html/image.php',
        'line' => 75,
        'type' => Finding::TYPE_SECURITY,
        'subtype' => 'file_upload_vulnerability',
        'description' => 'Insufficient file type validation in image processor',
        'codeSnippet' => 'if ($_FILES["image"]["type"] == "image/jpeg") { // process }'
    ],
    [
        'file' => 'public_html/image.php',
        'line' => 120,
        'type' => Finding::TYPE_PERFORMANCE,
        'subtype' => 'unoptimized_image_loading',
        'description' => 'Missing WebP format support for modern browsers',
        'context' => ['formats_supported' => ['jpeg', 'png']]
    ],
    
    // Quality issues in non-priority areas
    [
        'file' => 'public_html/privacy-policy.php',
        'line' => 10,
        'type' => Finding::TYPE_QUALITY,
        'subtype' => 'naming_convention',
        'description' => 'Inconsistent variable naming convention',
        'codeSnippet' => '$userName = $_POST["user_name"];'
    ],
    [
        'file' => 'public_html/cookie-policy.php',
        'line' => 25,
        'type' => Finding::TYPE_ARCHITECTURE,
        'subtype' => 'mvc_violation',
        'description' => 'Business logic mixed with presentation in policy page',
        'codeSnippet' => 'echo "<p>Cookie expires: " . date("Y-m-d", time() + 3600) . "</p>";'
    ]
];

echo "Step 1: Classifying " . count($rawFindings) . " raw findings...\n";
$classifiedFindings = $classifier->classifyFindings($rawFindings);

echo "Step 2: Analyzing classification results...\n";
$stats = $classifier->getPriorityStatistics($classifiedFindings);
echo "  Total findings: " . $stats['total_findings'] . "\n";
echo "  Priority area: " . $stats['priority_area'] . "\n";
echo "  Non-priority: " . $stats['non_priority'] . "\n";
echo "  Critical severity: " . $stats['severity_breakdown'][Finding::SEVERITY_CRITICAL] . "\n";
echo "  High severity: " . $stats['severity_breakdown'][Finding::SEVERITY_HIGH] . "\n";
echo "  Security findings: " . $stats['type_breakdown'][Finding::TYPE_SECURITY] . "\n\n";

echo "Step 3: Sorting findings by importance...\n";
$sortedFindings = FindingFilter::sortByImportance($classifiedFindings);
echo "Top 5 most important findings:\n";
foreach (array_slice($sortedFindings, 0, 5) as $i => $finding) {
    echo "  " . ($i + 1) . ". " . $finding->formatForDisplay() . " (Score: " . $finding->getImportanceScore() . ")\n";
}
echo "\n";

echo "Step 4: Filtering critical security issues...\n";
$criticalSecurity = FindingFilter::applyFilters($classifiedFindings, [
    'types' => [Finding::TYPE_SECURITY],
    'severities' => [Finding::SEVERITY_CRITICAL, Finding::SEVERITY_HIGH],
    'sort_by' => 'importance'
]);
echo "Critical security issues requiring immediate attention:\n";
foreach ($criticalSecurity as $i => $finding) {
    echo "  " . ($i + 1) . ". " . basename($finding->getFile()) . ":" . $finding->getLine() . " - " . $finding->getDescription() . "\n";
    echo "      Recommendation: " . substr($finding->getRecommendation(), 0, 80) . "...\n";
}
echo "\n";

echo "Step 5: Analyzing priority area distribution...\n";
$priorityAreaFindings = FindingFilter::filterByPriority($classifiedFindings, true);
$priorityByFile = FindingFilter::groupByFile($priorityAreaFindings);
echo "Priority area files with issues:\n";
foreach ($priorityByFile as $file => $fileFindings) {
    $criticalCount = count(FindingFilter::filterBySeverity($fileFindings, [Finding::SEVERITY_CRITICAL]));
    $highCount = count(FindingFilter::filterBySeverity($fileFindings, [Finding::SEVERITY_HIGH]));
    echo "  " . basename($file) . ": " . count($fileFindings) . " issues (Critical: $criticalCount, High: $highCount)\n";
}
echo "\n";

echo "Step 6: Generating change log by priority...\n";
$changeLog = [
    'URGENT_FIXES' => [],
    'HIGH_PRIORITY' => [],
    'MEDIUM_PRIORITY' => [],
    'LOW_PRIORITY' => []
];

foreach ($sortedFindings as $finding) {
    $category = match ($finding->getSeverity()) {
        Finding::SEVERITY_CRITICAL => 'URGENT_FIXES',
        Finding::SEVERITY_HIGH => $finding->isPriorityArea() ? 'HIGH_PRIORITY' : 'MEDIUM_PRIORITY',
        Finding::SEVERITY_MEDIUM => $finding->isPriorityArea() ? 'HIGH_PRIORITY' : 'MEDIUM_PRIORITY',
        default => 'LOW_PRIORITY'
    };
    
    $changeLog[$category][] = $finding;
}

foreach ($changeLog as $category => $findings) {
    if (!empty($findings)) {
        echo "\n" . str_replace('_', ' ', $category) . " (" . count($findings) . " items):\n";
        foreach (array_slice($findings, 0, 3) as $finding) { // Show first 3 of each category
            echo "  • " . basename($finding->getFile()) . ":" . $finding->getLine() . " - " . $finding->getDescription() . "\n";
        }
        if (count($findings) > 3) {
            echo "  ... and " . (count($findings) - 3) . " more\n";
        }
    }
}

echo "\nStep 7: Generating audit summary report...\n";
$summaryStats = FindingFilter::getSummaryStats($classifiedFindings);
echo "=== AUDIT SUMMARY ===\n";
echo "Files Scanned: " . $summaryStats['files_affected'] . "\n";
echo "Total Issues Found: " . $summaryStats['total'] . "\n";
echo "Priority Area Issues: " . $summaryStats['priority_area'] . " (" . round(($summaryStats['priority_area'] / $summaryStats['total']) * 100, 1) . "%)\n";
echo "Critical Issues: " . $summaryStats['by_severity'][Finding::SEVERITY_CRITICAL] . "\n";
echo "Security Issues: " . $summaryStats['by_type'][Finding::TYPE_SECURITY] . "\n";
echo "Average Importance Score: " . $summaryStats['avg_importance_score'] . "\n";

$urgentCount = count($changeLog['URGENT_FIXES']);
$highPriorityCount = count($changeLog['HIGH_PRIORITY']);
echo "\nIMMEDIATE ACTION REQUIRED: " . ($urgentCount + $highPriorityCount) . " issues\n";

echo "\n=== RECOMMENDATIONS ===\n";
echo "1. Address " . $urgentCount . " URGENT security vulnerabilities immediately\n";
echo "2. Fix " . $highPriorityCount . " HIGH PRIORITY issues in ad system and core functionality\n";
echo "3. Plan remediation for " . count($changeLog['MEDIUM_PRIORITY']) . " medium priority issues\n";
echo "4. Schedule code quality improvements for " . count($changeLog['LOW_PRIORITY']) . " low priority items\n";

echo "\nComplete finding classification and prioritization system test completed successfully!\n";