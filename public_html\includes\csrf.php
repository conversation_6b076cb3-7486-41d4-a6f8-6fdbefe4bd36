<?php
/**
 * CSRF Protection Functions
 * Provides Cross-Site Request Forgery protection for forms
 */

/**
 * Generate a CSRF token and store it in session
 * @return string The generated token
 */
function generateCSRFToken() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Generate a random token
    $token = bin2hex(random_bytes(32));
    
    // Store in session with timestamp
    $_SESSION['csrf_token'] = $token;
    $_SESSION['csrf_token_time'] = time();
    
    return $token;
}

/**
 * Validate CSRF token from form submission
 * @param string $token The token to validate
 * @param int $maxAge Maximum age of token in seconds (default: 1 hour)
 * @return bool True if token is valid
 */
function validateCSRFToken($token, $maxAge = 3600) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Check if token exists in session
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }
    
    // Check token age
    if (time() - $_SESSION['csrf_token_time'] > $maxAge) {
        unset($_SESSION['csrf_token']);
        unset($_SESSION['csrf_token_time']);
        return false;
    }
    
    // Validate token using hash_equals to prevent timing attacks
    $isValid = hash_equals($_SESSION['csrf_token'], $token);
    
    // Regenerate token after successful validation (one-time use)
    if ($isValid) {
        unset($_SESSION['csrf_token']);
        unset($_SESSION['csrf_token_time']);
    }
    
    return $isValid;
}

/**
 * Get current CSRF token, generate if doesn't exist
 * @return string The current token
 */
function getCSRFToken() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return generateCSRFToken();
    }
    
    // Check if token is expired
    if (time() - $_SESSION['csrf_token_time'] > 3600) {
        return generateCSRFToken();
    }
    
    return $_SESSION['csrf_token'];
}

/**
 * Generate HTML for CSRF token hidden input field
 * @return string HTML input field
 */
function csrfTokenField() {
    $token = getCSRFToken();
    return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token, ENT_QUOTES, 'UTF-8') . '">';
}

/**
 * Verify CSRF token from POST request and die with error if invalid
 * @param string $errorMessage Custom error message
 */
function requireValidCSRFToken($errorMessage = 'Invalid security token. Please try again.') {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST['csrf_token'] ?? '';
        
        if (!validateCSRFToken($token)) {
            http_response_code(403);
            die($errorMessage);
        }
    }
}