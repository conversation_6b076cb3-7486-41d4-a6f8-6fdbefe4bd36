<?php

namespace AuditSystem\Analyzers;

use AuditSystem\Interfaces\AnalyzerInterface;
use AuditSystem\Models\Finding;

/**
 * Comprehensive security analyzer for PHP code
 */
class SecurityAnalyzer implements AnalyzerInterface
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'check_sql_injection' => true,
            'check_xss' => true,
            'check_file_uploads' => true,
            'check_csrf' => true,
            'check_authentication' => true,
            'check_input_validation' => true
        ], $config);
    }

    /**
     * Analyze a file and return findings
     *
     * @param string $filePath Path to the file to analyze
     * @param string $content File content to analyze
     * @return Finding[] Array of findings discovered in the file
     */
    public function analyze(string $filePath, string $content): array
    {
        $findings = [];

        if ($this->config['check_sql_injection']) {
            $findings = array_merge($findings, $this->checkSQLInjection($filePath, $content));
        }

        if ($this->config['check_xss']) {
            $findings = array_merge($findings, $this->checkXSS($filePath, $content));
        }

        if ($this->config['check_file_uploads']) {
            $findings = array_merge($findings, $this->checkFileUploads($filePath, $content));
        }

        if ($this->config['check_csrf']) {
            $findings = array_merge($findings, $this->checkCSRF($filePath, $content));
        }

        if ($this->config['check_authentication']) {
            $findings = array_merge($findings, $this->checkAuthentication($filePath, $content));
        }

        if ($this->config['check_input_validation']) {
            $findings = array_merge($findings, $this->checkInputValidation($filePath, $content));
        }

        // Always check for secrets and debug flags
        $findings = array_merge($findings, $this->checkSecretsAndDebug($filePath, $content));

        return $findings;
    }

    /**
     * Compatibility wrapper for tests expecting analyzeFile($filePath)
     * Reads content and delegates to analyze().
     *
     * @param string $filePath
     * @return Finding[]
     */
    public function analyzeFile(string $filePath): array
    {
        $content = @file_get_contents($filePath) ?: '';
        return $this->analyze($filePath, $content);
    }

    /**
     * Check for SQL injection vulnerabilities
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkSQLInjection(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++; // 1-based line numbers

            // Check for direct concatenation with user input
            if (preg_match('/\$\w+\s*=\s*["\'].*?["\'].*?\..*?\$_(GET|POST|REQUEST|COOKIE)\[/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_CRITICAL,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'SQL injection vulnerability: Direct concatenation of user input in SQL query',
                    'Use prepared statements with parameter binding instead of string concatenation',
                    trim($line),
                    ['https://owasp.org/www-community/attacks/SQL_Injection']
                );
            }

            // Check for mysql_query with user input
            if (preg_match('/mysql_query\s*\(.*?\$_(GET|POST|REQUEST|COOKIE)/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_CRITICAL,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'SQL injection vulnerability: mysql_query with unsanitized user input',
                    'Use PDO prepared statements instead of deprecated mysql_query',
                    trim($line),
                    ['https://www.php.net/manual/en/security.database.sql-injection.php']
                );
            }

            // Check for query building with user input
            if (preg_match('/SELECT.*?WHERE.*?\$_(GET|POST|REQUEST|COOKIE)\[/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Potential SQL injection: User input directly in WHERE clause',
                    'Use parameterized queries with PDO prepared statements',
                    trim($line),
                    ['https://owasp.org/www-community/attacks/SQL_Injection']
                );
            }
        }

        return $findings;
    }

    /**
     * Check for XSS vulnerabilities
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkXSS(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Check for direct output of user input
            if (preg_match('/echo\s+\$_(GET|POST|REQUEST|COOKIE)\[/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'XSS vulnerability: Direct output of user input without sanitization',
                    'Use htmlspecialchars() or htmlentities() to escape output',
                    trim($line),
                    ['https://owasp.org/www-community/attacks/xss/']
                );
            }

            // Check for print/printf with user input
            if (preg_match('/(print|printf)\s*\(.*?\$_(GET|POST|REQUEST|COOKIE)\[/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'XSS vulnerability: Unescaped user input in print statement',
                    'Escape output using htmlspecialchars() before printing',
                    trim($line),
                    ['https://owasp.org/www-community/attacks/xss/']
                );
            }

            // Check for innerHTML or similar JavaScript injections
            if (preg_match('/innerHTML.*?\$_(GET|POST|REQUEST|COOKIE)\[/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'XSS vulnerability: User input directly in innerHTML',
                    'Sanitize input and use textContent instead of innerHTML when possible',
                    trim($line),
                    ['https://owasp.org/www-community/attacks/xss/']
                );
            }
        }

        return $findings;
    }

    /**
     * Check for file upload security issues
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkFileUploads(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Check for file uploads without validation
            if (preg_match('/move_uploaded_file\s*\(/', $line)) {
                // Look for validation in surrounding lines
                $hasValidation = false;
                $startLine = max(0, $lineNumber - 10);
                $endLine = min(count($lines), $lineNumber + 5);
                
                for ($i = $startLine; $i < $endLine; $i++) {
                    if (preg_match('/(pathinfo|getimagesize|mime_content_type|finfo_file)/', $lines[$i])) {
                        $hasValidation = true;
                        break;
                    }
                }

                if (!$hasValidation) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_SECURITY,
                        Finding::SEVERITY_HIGH,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        'File upload without proper validation',
                        'Validate file type, size, and content before moving uploaded files',
                        trim($line),
                        ['https://owasp.org/www-community/vulnerabilities/Unrestricted_File_Upload']
                    );
                }
            }

            // Check for direct file inclusion from uploads
            if (preg_match('/(include|require).*?uploads/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_CRITICAL,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Critical: Direct inclusion of uploaded files',
                    'Never include uploaded files directly. Validate and sanitize all file operations',
                    trim($line),
                    ['https://owasp.org/www-community/vulnerabilities/Unrestricted_File_Upload']
                );
            }
        }

        return $findings;
    }

    /**
     * Check for CSRF protection
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkCSRF(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        // Check for forms without CSRF tokens
        $hasForm = false;
        $hasCSRFToken = false;

        foreach ($lines as $lineNumber => $line) {
            if (preg_match('/<form.*?method=["\']post["\']/i', $line)) {
                $hasForm = true;
            }
            if (preg_match('/(csrf|token|nonce)/', $line)) {
                $hasCSRFToken = true;
            }
        }

        if ($hasForm && !$hasCSRFToken) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_SECURITY,
                Finding::SEVERITY_MEDIUM,
                $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                'POST form without CSRF protection',
                'Add CSRF token validation to prevent cross-site request forgery',
                'Form detected without CSRF token',
                ['https://owasp.org/www-community/attacks/csrf']
            );
        }

        return $findings;
    }

    /**
     * Check authentication and session management
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkAuthentication(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Check for weak session configuration
            if (preg_match('/session_start\s*\(\s*\)/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Session started without security configuration',
                    'Configure session security settings (httponly, secure, samesite)',
                    trim($line),
                    ['https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet']
                );
            }

            // Check for hardcoded passwords
            if (preg_match('/password\s*=\s*["\'][^"\']{1,20}["\']/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Hardcoded password detected',
                    'Use environment variables or secure configuration files for passwords',
                    '[REDACTED - contains password]',
                    ['https://owasp.org/www-community/vulnerabilities/Use_of_hard-coded_password']
                );
            }
        }

        return $findings;
    }

    /**
     * Detect secrets, hardcoded credentials, and debug settings
     */
    private function checkSecretsAndDebug(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Hardcoded DB credentials (keep config.php as required by user, but still report as security hygiene)
            if (preg_match('/define\s*\(\s*["\']DB_(PASS|PASSWORD|USER|HOST)["\']\s*,\s*["\'](.+?)["\']\s*\)/i', $line, $m)) {
                $label = $m[1];
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Hardcoded database credential detected ({$label})",
                    'Consider centralizing secrets and limiting file exposure; restrict file permissions',
                    trim($line),
                    ['https://owasp.org/www-community/cryptography/Secret_Management']
                );
            }

            // Exposed API keys (e.g., DEEPSEEK_API_KEY, *_API_KEY, sk- prefix)
            if (preg_match('/define\s*\(\s*["\']([A-Z0-9_]*API_KEY)["\']\s*,\s*["\'](sk-[A-Za-z0-9_\-]{10,})["\']\s*\)/', $line, $m)) {
                $const = $m[1];
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Potential exposed API key in constant {$const}",
                    'Rotate the key if necessary and ensure repository access is controlled',
                    trim($line),
                    ['https://owasp.org/www-community/attacks/Source_Code_Disclosure']
                );
            }

            // Debug settings
            if (preg_match('/error_reporting\s*\(\s*E_ALL\s*\)/i', $line) || preg_match('/ini_set\s*\(\s*["\']display_errors["\']\s*,\s*1\s*\)/i', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Debug mode enabled (error reporting/display_errors) can leak sensitive info in production',
                    'Ensure debug is disabled on production environments',
                    trim($line),
                    ['https://owasp.org/www-project-top-ten/2017/A6_2017-Security_Misconfiguration']
                );
            }
        }

        return $findings;
    }

    /**
     * Check input validation
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkInputValidation(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Check for unvalidated file operations
            if (preg_match('/(file_get_contents|fopen|readfile)\s*\(.*?\$_(GET|POST|REQUEST)\[/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'File operation with unvalidated user input',
                    'Validate and sanitize file paths to prevent directory traversal',
                    trim($line),
                    ['https://owasp.org/www-community/attacks/Path_Traversal']
                );
            }

            // Check for eval with user input
            if (preg_match('/eval\s*\(.*?\$_(GET|POST|REQUEST|COOKIE)\[/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_CRITICAL,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Critical: eval() with user input - Remote Code Execution risk',
                    'Never use eval() with user input. Find alternative approaches',
                    trim($line),
                    ['https://owasp.org/www-community/attacks/Code_Injection']
                );
            }
        }

        return $findings;
    }

    /**
     * Get the types of files this analyzer can handle
     *
     * @return string[] Array of file extensions or patterns this analyzer supports
     */
    public function getSupportedFileTypes(): array
    {
        return ['php'];
    }

    /**
     * Get the analyzer name for identification
     *
     * @return string Name of the analyzer
     */
    public function getName(): string
    {
        return 'Security Analyzer';
    }

    /**
     * Check if file is in priority area
     *
     * @param string $filePath
     * @return bool
     */
    private function isPriorityArea(string $filePath): bool
    {
        $priorityPatterns = [
            'admin/',
            'includes/',
            'config.php',
            'ad_',
            'smrsaj'
        ];

        foreach ($priorityPatterns as $pattern) {
            if (strpos($filePath, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }
}