<?php
/**
 * admin/internal_links.php
 * Admin page for managing internal links
 */

require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/internal_links.php';
require_once 'includes/auth_check.php';

// Make sure getPDOConnection function is available
if (!function_exists('getPDOConnection')) {
    function getPDOConnection() {
        // Basic check for constants
        if (!defined('DB_HOST') || !defined('DB_NAME') || !defined('DB_USER') || !defined('DB_PASS') || !defined('DB_CHARSET')) {
            error_log("FATAL: Database configuration constants are missing in getPDOConnection().");
            throw new Exception("Database configuration constants are missing.");
        }
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $options = [ PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC, PDO::ATTR_EMULATE_PREPARES => false, ];
        try {
            return new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (\PDOException $e) {
            error_log("PDO Connection failed in getPDOConnection(): " . $e->getMessage());
            throw new Exception("Database connection failed during operation. Check server logs. PDO Error: " . $e->getMessage());
        }
    }
}

// Initialize variables
$pdo = getPDOConnection();
$message = '';
$error = '';
$links = [];
$editLink = null;
$editId = isset($_GET['edit']) ? (int)$_GET['edit'] : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    // Create or update link
    if ($action === 'save_link') {
        $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
        $keyword = trim($_POST['keyword'] ?? '');
        $url = trim($_POST['url'] ?? '');
        $targetArticleId = !empty($_POST['target_article_id']) ? (int)$_POST['target_article_id'] : null;
        $matchType = $_POST['match_type'] ?? 'case_insensitive';
        $maxReplacements = (int)($_POST['max_replacements'] ?? 1);
        $isActive = isset($_POST['is_active']) && $_POST['is_active'] === '1';

        // Validate input
        if (empty($keyword)) {
            $error = 'Keyword is required.';
        } elseif (empty($url)) {
            $error = 'URL is required.';
        } else {
            // Create or update the link
            if ($id > 0) {
                $result = updateInternalLink($pdo, $id, $keyword, $url, $targetArticleId, $matchType, $maxReplacements, $isActive);
            } else {
                $result = createInternalLink($pdo, $keyword, $url, $targetArticleId, $matchType, $maxReplacements);
            }

            if ($result['success']) {
                $message = $result['message'];
                // Reset edit mode
                $editId = 0;
            } else {
                $error = $result['error'];
            }
        }
    }

    // Delete link
    elseif ($action === 'delete_link' && isset($_POST['id'])) {
        $id = (int)$_POST['id'];
        $result = deleteInternalLink($pdo, $id);

        if ($result['success']) {
            $message = $result['message'];
        } else {
            $error = $result['error'];
        }
    }
}

// Get link for editing
if ($editId > 0) {
    $editLink = getInternalLinkById($pdo, $editId);
    if (!$editLink) {
        $error = 'Internal link not found.';
        $editId = 0;
    }
}

// Get all links
$links = getActiveInternalLinks($pdo);

// Get all published articles for the dropdown
$sql = "SELECT id, title FROM articles WHERE status = 'published' ORDER BY title ASC";
$stmt = $pdo->prepare($sql);
$stmt->execute();
$articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Page title
$pageTitle = 'Manage Internal Links';
?>

<?php include 'includes/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800"><?php echo $pageTitle; ?></h1>
        <?php if ($editId === 0): ?>
        <button id="add-link-btn" class="btn bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors">
            <i class="fas fa-plus mr-2"></i> Add New Link
        </button>
        <?php else: ?>
        <a href="internal_links.php" class="btn bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
            <i class="fas fa-arrow-left mr-2"></i> Cancel Editing
        </a>
        <?php endif; ?>
    </div>

    <?php if (!empty($message)): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
        <?php echo $message; ?>
    </div>
    <?php endif; ?>

    <?php if (!empty($error)): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <?php echo $error; ?>
    </div>
    <?php endif; ?>

    <!-- Link Form -->
    <div id="link-form" class="bg-white shadow-md rounded-lg p-6 mb-8 <?php echo ($editId > 0) ? '' : 'hidden'; ?>">
        <h2 class="text-xl font-semibold mb-4"><?php echo ($editId > 0) ? 'Edit Internal Link' : 'Add New Internal Link'; ?></h2>
        <form action="internal_links.php" method="post">
            <input type="hidden" name="action" value="save_link">
            <input type="hidden" name="id" value="<?php echo $editId; ?>">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="keyword" class="block text-sm font-medium text-gray-700 mb-1">Keyword or Phrase</label>
                    <input type="text" id="keyword" name="keyword" value="<?php echo escape($editLink['keyword'] ?? ''); ?>"
                           class="w-full border border-gray-300 rounded-md shadow-sm p-2" required>
                    <p class="text-xs text-gray-500 mt-1">The text that will be linked in your articles</p>
                </div>

                <div>
                    <label for="url" class="block text-sm font-medium text-gray-700 mb-1">URL</label>
                    <input type="text" id="url" name="url" value="<?php echo escape($editLink['url'] ?? ''); ?>"
                           class="w-full border border-gray-300 rounded-md shadow-sm p-2" required>
                    <p class="text-xs text-gray-500 mt-1">The destination URL (e.g., /article/slug/)</p>
                </div>

                <div>
                    <label for="target_article_id" class="block text-sm font-medium text-gray-700 mb-1">Target Article (Optional)</label>
                    <select id="target_article_id" name="target_article_id" class="w-full border border-gray-300 rounded-md shadow-sm p-2">
                        <option value="">-- Select Article --</option>
                        <?php foreach ($articles as $article): ?>
                        <option value="<?php echo $article['id']; ?>" <?php if (isset($editLink['target_article_id']) && $editLink['target_article_id'] == $article['id']) echo 'selected'; ?>>
                            <?php echo escape($article['title']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Select an article to link to (URL will be updated automatically)</p>
                </div>

                <div>
                    <label for="match_type" class="block text-sm font-medium text-gray-700 mb-1">Match Type</label>
                    <select id="match_type" name="match_type" class="w-full border border-gray-300 rounded-md shadow-sm p-2">
                        <option value="exact" <?php if (isset($editLink['match_type']) && $editLink['match_type'] === 'exact') echo 'selected'; ?>>Exact Match</option>
                        <option value="case_insensitive" <?php if (!isset($editLink['match_type']) || $editLink['match_type'] === 'case_insensitive') echo 'selected'; ?>>Case Insensitive</option>
                        <option value="partial" <?php if (isset($editLink['match_type']) && $editLink['match_type'] === 'partial') echo 'selected'; ?>>Partial Match</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">How the keyword should be matched in the content</p>
                </div>

                <div>
                    <label for="max_replacements" class="block text-sm font-medium text-gray-700 mb-1">Max Replacements</label>
                    <input type="number" id="max_replacements" name="max_replacements" value="<?php echo isset($editLink['max_replacements']) ? (int)$editLink['max_replacements'] : 1; ?>"
                           min="1" max="10" class="w-full border border-gray-300 rounded-md shadow-sm p-2">
                    <p class="text-xs text-gray-500 mt-1">Maximum number of times this keyword will be linked in an article</p>
                </div>

                <div>
                    <label for="is_active" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <div class="flex items-center mt-2">
                        <input type="checkbox" id="is_active" name="is_active" value="1" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                               <?php if (!isset($editLink['is_active']) || $editLink['is_active'] == 1) echo 'checked'; ?>>
                        <label for="is_active" class="ml-2 block text-sm text-gray-900">Active</label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Only active links will be applied to articles</p>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <button type="submit" class="btn bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save mr-2"></i> Save Link
                </button>
            </div>
        </form>
    </div>

    <!-- Links Table -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Keyword</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">URL</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Match Type</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php if (empty($links)): ?>
                <tr>
                    <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">No internal links found. Add your first link using the button above.</td>
                </tr>
                <?php else: ?>
                <?php foreach ($links as $link): ?>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?php echo escape($link['keyword']); ?></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo escape($link['url']); ?></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <?php
                        $matchTypes = [
                            'exact' => 'Exact',
                            'case_insensitive' => 'Case Insensitive',
                            'partial' => 'Partial'
                        ];
                        echo $matchTypes[$link['match_type']] ?? $link['match_type'];
                        ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo (int)$link['max_replacements']; ?></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <?php if ($link['is_active'] == 1): ?>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                        <?php else: ?>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Inactive</span>
                        <?php endif; ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <a href="internal_links.php?edit=<?php echo $link['id']; ?>" class="text-primary hover:text-primary-dark mr-3">Edit</a>
                        <form action="internal_links.php" method="post" class="inline" onsubmit="return confirm('Are you sure you want to delete this link?');">
                            <input type="hidden" name="action" value="delete_link">
                            <input type="hidden" name="id" value="<?php echo $link['id']; ?>">
                            <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide the link form
    const addLinkBtn = document.getElementById('add-link-btn');
    const linkForm = document.getElementById('link-form');

    if (addLinkBtn) {
        addLinkBtn.addEventListener('click', function() {
            linkForm.classList.remove('hidden');
            document.getElementById('keyword').focus();
        });
    }

    // Auto-update URL when target article is selected
    const targetArticleSelect = document.getElementById('target_article_id');
    const urlInput = document.getElementById('url');

    if (targetArticleSelect && urlInput) {
        targetArticleSelect.addEventListener('change', function() {
            const articleId = this.value;
            if (articleId) {
                // Find the selected option
                const selectedOption = this.options[this.selectedIndex];
                const articleTitle = selectedOption.textContent.trim();

                // Generate a slug from the article title
                const slug = articleTitle.toLowerCase()
                    .replace(/[^\w\s-]/g, '') // Remove special characters
                    .replace(/\s+/g, '-')     // Replace spaces with hyphens
                    .replace(/-+/g, '-');     // Replace multiple hyphens with a single one

                // Update the URL field
                urlInput.value = `/article/${slug}/`;
            }
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
