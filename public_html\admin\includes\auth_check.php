<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if the admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    // If not logged in, destroy the session and redirect to login page
    session_unset();
    session_destroy();
    // Adjust path to login.php if necessary
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// Optional: Add activity timeout check
$timeout_duration = 1800; // 30 minutes
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > $timeout_duration) {
    // Last activity was too long ago
    session_unset();
    session_destroy();
    header('Location: login.php?timeout=1'); // Redirect with a timeout flag
    exit;
}
$_SESSION['last_activity'] = time(); // Update last activity time stamp

?>
