<?php
// Start the session
session_start();

// Include configuration and function files
require_once '../config.php'; // Adjust path if needed
require_once '../includes/functions.php'; // Adjust path if needed

$error_message = '';

// Check if the user is already logged in, redirect to dashboard if so
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: index.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';

    // Basic Validation
    if (empty($email) || empty($password)) {
        $error_message = 'Molimo unesite email i lozinku.';
    } else {
        try {
            // Find the author by email
            $sql = "SELECT id, name, email, password, status FROM authors WHERE email = :email LIMIT 1";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':email', $email, PDO::PARAM_STR);
            $stmt->execute();
            $author = $stmt->fetch(PDO::FETCH_ASSOC);

            // Verify author exists, password is correct, and status is active
            if ($author && !empty($author['password']) && password_verify($password, $author['password'])) {

                if ($author['status'] !== 'active') {
                     $error_message = 'Vaš nalog nije aktivan. Molimo kontaktirajte administratora.';
                } else {
                    // Login successful
                    $_SESSION['admin_logged_in'] = true;
                    $_SESSION['admin_user_id'] = $author['id']; // Store user ID
                    $_SESSION['admin_user_name'] = $author['name']; // Store user name
                    $_SESSION['admin_email'] = $author['email'];
                    $_SESSION['last_activity'] = time(); // For session timeout

                    // Regenerate session ID for security
                    session_regenerate_id(true);

                    // Redirect to the dashboard
                    header('Location: index.php');
                    exit;
                }

            } else {
                // Login failed
                $error_message = 'Pogrešan email ili lozinka.';
            }
        } catch (PDOException $e) {
            // Database error
            $error_message = 'Greška pri povezivanju s bazom podataka. Pokušajte ponovo kasnije.';
            // Log the error in a real application: error_log('Login DB Error: ' . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="bs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="theme-color" content="#ff6481">
    <title>Admin Prijava - <?php echo defined('SITE_NAME') ? SITE_NAME : 'CMS'; ?> CMS</title>

    <link rel="preconnect" href="https://cdn.tailwindcss.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700;800&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">

    <script src="https://cdn.tailwindcss.com"></script>

    <script>
        // Tailwind configuration
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        background: '#FFF4F5',
                        primary: '#ff6481',
                        secondary: '#ffd6dd',
                        dark: '#333333',
                        light: '#ffffff',
                        border: '#feeaec',
                        danger: '#ef4444',
                    },
                    fontFamily: {
                        sans: ['Open Sans', 'sans-serif'],
                        montserrat: ['Montserrat', 'sans-serif'],
                    },
                    borderRadius: {
                        'xl': '1rem',
                        '2xl': '1.5rem',
                    },
                }
            }
        };
    </script>

    <style type="text/tailwindcss">
        @layer components {
            .btn {
                @apply bg-primary text-white px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 font-montserrat w-full shadow-md hover:shadow-lg;
            }

            .form-input {
                @apply w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200;
            }

            .pattern-background {
                background-image: radial-gradient(#ff6481 1px, transparent 1px), radial-gradient(#ff6481 1px, transparent 1px);
                background-size: 40px 40px;
                background-position: 0 0, 20px 20px;
                opacity: 0.15;
            }
        }
    </style>
</head>
<body class="bg-background font-sans text-dark flex flex-col items-center justify-center min-h-screen p-4">
    <div class="fixed inset-0 pattern-background -z-10"></div>

    <div class="w-full max-w-md relative z-10">
        <div class="text-center mb-8">
            <div class="inline-block">
                 <span class="font-montserrat text-3xl font-extrabold text-primary">
                    <span class="relative inline-block">
                        Lako <span class="text-[#333] italic font-light">&</span> <span class="relative">Fino
                            <span class="absolute -bottom-1 left-0 w-full h-1 bg-primary rounded-full"></span>
                        </span>
                    </span>
                </span>
                <span class="text-sm font-medium text-gray-500 ml-2">CMS</span>
            </div>
        </div>

        <div class="bg-white rounded-2xl shadow-lg border border-border p-8 mb-6">
            <h1 class="text-2xl font-montserrat font-bold text-dark mb-6 text-center">Admin Prijava</h1>

            <?php if (!empty($error_message)): ?>
            <div class="mb-6 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg flex items-center text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span><?php echo escape($error_message); ?></span>
            </div>
            <?php endif; ?>

            <form method="POST" action="login.php">
                <div class="mb-5">
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                            </svg>
                        </div>
                        <input id="email" name="email" type="email" class="form-input pl-10" placeholder="Unesite vaš email" required value="<?php echo isset($_POST['email']) ? escape($_POST['email']) : ''; ?>">
                    </div>
                </div>

                <div class="mb-6">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Lozinka</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                        <input id="password" name="password" type="password" class="form-input pl-10" placeholder="Unesite vašu lozinku" required>
                        </div>
                </div>

                <button type="submit" class="btn">
                    Prijavi se
                </button>
            </form>
        </div>
    </div>

</body>
</html>
