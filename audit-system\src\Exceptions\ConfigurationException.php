<?php

namespace AuditSystem\Exceptions;

/**
 * Exception thrown when configuration-related operations fail
 */
class ConfigurationException extends AuditException
{
    /**
     * Create exception for missing configuration
     *
     * @param string $configKey Configuration key that is missing
     * @return static
     */
    public static function missingConfiguration(string $configKey): self
    {
        return new self("Missing required configuration: {$configKey}");
    }

    /**
     * Create exception for invalid configuration value
     *
     * @param string $configKey Configuration key with invalid value
     * @param mixed $value The invalid value
     * @param string $expectedType Expected type or format
     * @return static
     */
    public static function invalidConfiguration(string $configKey, $value, string $expectedType): self
    {
        $valueStr = is_scalar($value) ? (string)$value : gettype($value);
        return new self("Invalid configuration for {$configKey}: got '{$valueStr}', expected {$expectedType}");
    }

    /**
     * Create exception for configuration file not found
     *
     * @param string $configPath Path to the configuration file
     * @return static
     */
    public static function configFileNotFound(string $configPath): self
    {
        return new self("Configuration file not found: {$configPath}");
    }

    /**
     * Create exception for invalid configuration file format
     *
     * @param string $configPath Path to the configuration file
     * @param string $expectedFormat Expected file format
     * @return static
     */
    public static function invalidConfigFormat(string $configPath, string $expectedFormat): self
    {
        return new self("Invalid configuration file format in {$configPath}, expected {$expectedFormat}");
    }
}