/**
 * avg-time-trick.css - Styling for the AVG Time Trick
 *
 * This file contains the CSS styles for the AVG Time Trick banner and popup.
 * Designed to match the Mercislike.art website design.
 */

/* Banner styles */
#avg-time-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to right, #ff6481, #ff64c3);
    color: white;
    padding: 1rem;
    box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(100%);
    transition: transform 0.5s ease-in-out;
    z-index: 40;
    text-align: center;
    font-family: 'Open Sans', sans-serif;
}

#avg-time-banner.visible {
    transform: translateY(0);
}

#avg-time-banner h3 {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    font-family: 'Montserrat', sans-serif;
}

#avg-time-banner p {
    margin-bottom: 0.75rem;
}

#countdown-timer {
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.progress-container {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 9999px;
    height: 0.5rem;
    overflow: hidden;
}

#countdown-progress {
    background-color: white;
    height: 100%;
    border-radius: 9999px;
    transition: width 1s linear;
}

/* Popup styles */
#avg-time-popup {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    font-family: 'Open Sans', sans-serif;
}

#avg-time-popup.visible {
    opacity: 1;
    pointer-events: auto;
}

#avg-time-popup .popup-content {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    max-width: 28rem;
    margin: 0 1rem;
    transform: scale(0.95);
    transition: transform 0.3s ease;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #feeaec;
}

#avg-time-popup.visible .popup-content {
    transform: scale(1);
}

#avg-time-popup .close-button {
    text-align: right;
    margin-bottom: 0.5rem;
}

#avg-time-popup .close-button button {
    color: #6b7280;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
}

#avg-time-popup .close-button button:hover {
    color: #374151;
    background-color: #f3f4f6;
}

#avg-time-popup .icon-container {
    width: 4rem;
    height: 4rem;
    background-color: #feeaec;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

#avg-time-popup .icon-container svg {
    width: 2rem;
    height: 2rem;
    color: #ff6481;
}

#avg-time-popup h3 {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333333;
    margin-bottom: 0.75rem;
    font-family: 'Montserrat', sans-serif;
}

#avg-time-popup p {
    color: #4b5563;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

#avg-time-popup .facebook-button {
    display: block;
    width: 100%;
    background-color: #ff6481;
    color: white;
    text-align: center;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: background-color 0.2s;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
}

#avg-time-popup .facebook-button:hover {
    background-color: #ff4d6e;
}
