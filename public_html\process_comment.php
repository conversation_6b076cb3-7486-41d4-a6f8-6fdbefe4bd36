<?php
/**
 * Process Comment Submission
 * Handles adding new comments via AJAX.
 * v1.1: Added error reporting suppression for clean JSON output.
 */

// --- Error Reporting & Header ---
// !! IMPORTANT: Suppress errors in output for AJAX endpoints !!
// Errors will still be logged based on your server/PHP configuration.
error_reporting(0);
ini_set('display_errors', 0);

// Set header to return JSON *before* any potential output
header('Content-Type: application/json');

// --- Configuration & Functions ---
// Ensure this path is correct relative to process_comment.php
require_once __DIR__ . '/config.php'; // Includes functions.php which now has addComment

// --- Input Validation ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
    exit;
}

$action = $_POST['action'] ?? '';

if ($action !== 'add_comment') {
    echo json_encode(['success' => false, 'error' => 'Invalid action.']);
    exit;
}

$articleId = filter_input(INPUT_POST, 'article_id', FILTER_VALIDATE_INT);
$parentId = filter_input(INPUT_POST, 'parent_id', FILTER_VALIDATE_INT);
$userName = trim($_POST['name'] ?? '');
// Remove potentially harmful tags, allow basic formatting if desired
$allowed_tags = '<br><p><a><strong><em><ul><ol><li><blockquote><code>'; // Example allowed tags
$commentText = trim(strip_tags($_POST['comment'] ?? '', $allowed_tags));


// Re-validate server-side
if (empty($userName) || empty($commentText) || $articleId === false || $articleId <= 0) {
    echo json_encode(['success' => false, 'error' => 'Molimo popunite ime i komentar.']);
    exit;
}
// Ensure parentId is either a positive integer or null
if ($parentId !== null && ($parentId === false || $parentId <= 0)) {
    $parentId = null; // Treat invalid parent ID as null (top-level comment)
}

// --- Add Comment to Database ---
// Assuming $pdo is available from config.php
if (!isset($pdo) || !($pdo instanceof PDO)) {
     // Log the critical error server-side
     error_log("PDO connection not available in process_comment.php");
     // Send a generic error back to the client
     echo json_encode(['success' => false, 'error' => 'Greška na serveru. Molimo pokušajte kasnije.']);
     exit;
}

// Call the addComment function (assuming default approval status is 1)
// Pass the already sanitized/stripped comment text
$result = addComment($pdo, $articleId, $userName, $commentText, $parentId, 1);

if ($result['success']) {
    // Fetch the newly added comment to return it with all details (like created_at)
    try {
        $stmtNewComment = $pdo->prepare("SELECT * FROM comments WHERE id = :comment_id");
        $stmtNewComment->bindParam(':comment_id', $result['comment_id'], PDO::PARAM_INT);
        $stmtNewComment->execute();
        $newCommentData = $stmtNewComment->fetch(PDO::FETCH_ASSOC);

        if ($newCommentData) {
             // Add a formatted date field for immediate display
             // Ensure the formatCommentDate function is available (included via config.php -> functions.php)
             if (function_exists('formatCommentDate')) {
                $newCommentData['time_ago'] = formatCommentDate($newCommentData['created_at']);
             } else {
                 $newCommentData['time_ago'] = $newCommentData['created_at']; // Fallback
             }
            // Escape user-generated content before sending back in JSON
            $newCommentData['user_name'] = htmlspecialchars($newCommentData['user_name'] ?? '', ENT_QUOTES, 'UTF-8');
            // Comment text was already processed, but maybe re-escape just in case? Or trust strip_tags
            // $newCommentData['comment_text'] = htmlspecialchars($newCommentData['comment_text'] ?? '', ENT_QUOTES, 'UTF-8');

            echo json_encode(['success' => true, 'comment' => $newCommentData]);
        } else {
             error_log("Failed to fetch newly added comment ID {$result['comment_id']}");
             echo json_encode(['success' => false, 'error' => 'Komentar je dodan, ali ga nije moguće odmah prikazati.']);
        }
    } catch (PDOException $e) {
         error_log("Error fetching newly added comment ID {$result['comment_id']}: " . $e->getMessage());
         echo json_encode(['success' => false, 'error' => 'Komentar je dodan, ali je došlo do greške pri prikazu.']);
    }
} else {
    // Log the specific error from addComment function
    error_log("addComment failed: " . ($result['error'] ?? 'Unknown error'));
    echo json_encode(['success' => false, 'error' => $result['error'] ?? 'Došlo je do nepoznate greške prilikom spremanja komentara.']);
}

exit;
?>