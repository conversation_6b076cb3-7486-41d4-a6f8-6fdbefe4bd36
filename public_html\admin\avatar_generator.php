<?php

/**
 * avatar_generator.php
 *
 * Generates a simple SVG avatar image with initials and a background color.
 *
 * Parameters (via GET request):
 * - initials: The initials to display (e.g., ?initials=EH). Max 2 chars, defaults to '??'.
 * - size: The width and height of the avatar in pixels (e.g., ?size=100). Defaults to 100. Min 20, Max 500.
 * - bg: Background color as a hex code (without #) (e.g., ?bg=E8E8E8). Defaults to E8E8E8.
 * - fg: Foreground (text) color as a hex code (without #) (e.g., ?fg=BDBDBD). Defaults to BDBDBD.
 *
 * Example URL: /avatar_generator.php?initials=JD&size=80&bg=BBDEFB&fg=333333
 */

// --- Input Sanitization & Defaults ---

// Get initials, sanitize (allow only letters), ensure uppercase, limit to 2 chars
$initials = isset($_GET['initials']) ? strtoupper(preg_replace('/[^a-zA-Z]/', '', $_GET['initials'])) : '??';
if (mb_strlen($initials) > 2) {
    $initials = mb_substr($initials, 0, 2); // Take only the first two characters if more are provided
}
if (empty($initials)) {
    $initials = '??'; // Fallback if initials become empty after sanitization
}

// Get size, ensure it's an integer, clamp between 20 and 500
$size = isset($_GET['size']) ? (int)$_GET['size'] : 100;
$size = max(20, min(500, $size));

// Get background color, validate hex format (3 or 6 digits), add '#', provide default
$bgColorInput = isset($_GET['bg']) ? trim($_GET['bg']) : 'E8E8E8';
$bgColor = preg_match('/^[a-fA-F0-9]{3,6}$/', $bgColorInput) ? '#' . $bgColorInput : '#E8E8E8';

// Get foreground color, validate hex format (3 or 6 digits), add '#', provide default
$fgColorInput = isset($_GET['fg']) ? trim($_GET['fg']) : 'BDBDBD';
$fgColor = preg_match('/^[a-fA-F0-9]{3,6}$/', $fgColorInput) ? '#' . $fgColorInput : '#BDBDBD';


// --- SVG Generation ---

// Calculate font size relative to the avatar size (adjust multiplier for best look)
$fontSize = round($size * 0.45);

// Set Content-Type header to indicate SVG image
header("Content-type: image/svg+xml");
// Optional: Add caching headers if desired (e.g., cache for 1 day)
// header("Cache-Control: public, max-age=86400");
// header("Expires: " . gmdate('D, d M Y H:i:s', time() + 86400) . ' GMT');

// Start SVG output (using echo for clarity)
echo '<?xml version="1.0" encoding="UTF-8" standalone="no"?>';
echo '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 ' . $size . ' ' . $size . '" version="1.1" xmlns="http://www.w3.org/2000/svg">';

// Background rectangle - covers the entire SVG area
echo '<rect width="' . $size . '" height="' . $size . '" fill="' . htmlspecialchars($bgColor) . '"/>';

// Text element for initials
// - x="50%" y="50%": Center the text horizontally and vertically
// - dominant-baseline="central": Aligns the vertical center of the text to the y coordinate
// - text-anchor="middle": Aligns the horizontal center of the text to the x coordinate
// - font-family: Basic sans-serif fallback
echo '<text x="50%" y="50%" font-family="Arial, Helvetica, sans-serif" font-weight="bold" font-size="' . $fontSize . 'px" fill="' . htmlspecialchars($fgColor) . '" dominant-baseline="central" text-anchor="middle">';
echo htmlspecialchars($initials); // Output the sanitized initials
echo '</text>';

// End SVG output
echo '</svg>';

exit; // Stop script execution after outputting SVG

?>
