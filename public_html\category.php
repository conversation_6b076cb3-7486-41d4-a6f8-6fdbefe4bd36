<?php
// ========================================================================= //
//                         CATEGORY PAGE (category.php)                      //
// ========================================================================= //
// This file displays articles belonging to a specific category.             //
// It fetches category details based on the slug from the URL, retrieves     //
// associated articles, sets meta tags, and displays the content.            //
// ========================================================================= //

// --- Core Includes & Setup ---
require_once 'config.php'; // Includes functions.php
require_once 'includes/ad_display.php'; // Include ad display functions
require_once 'includes/ad_manager.php'; // Include ad manager if needed
require_once 'includes/ad_tracking.php'; // Include ad tracking if needed

// Initialize variables
$category = null;
$articles = [];
$error_message = null;

// --- Fetch Category Data ---
// Get the slug from the URL query parameter (e.g., category.php?slug=recepti)
$slug = $_GET['slug'] ?? null;

if (!$slug) {
    // Redirect to homepage if no slug is provided
    header('Location: ' . SITE_URL);
    exit;
}

// Sanitize the slug
$slug = htmlspecialchars($slug, ENT_QUOTES, 'UTF-8');

// Fetch the category data using the sanitized slug
// Ensure the function getCategoryBySlug exists in functions.php
if (function_exists('getCategoryBySlug')) {
    try {
        $category = getCategoryBySlug($pdo, $slug);
    } catch (PDOException $e) {
        $error_message = "Database error fetching category: " . $e->getMessage();
        error_log($error_message);
        // Don't exit here, handle the $category being null later
    }
} else {
    $error_message = "Error: getCategoryBySlug function not found.";
    error_log($error_message);
    // Handle missing function - perhaps show a generic error or redirect
    http_response_code(500);
    echo "An internal error occurred. Please try again later.";
    exit;
}


// --- Handle Category Not Found ---
if (!$category) {
    http_response_code(404);
    // Set meta tags for the 404 page
    $page_title = '404 - Kategorija nije pronađena';
    $meta_description = 'Tražena kategorija nije pronađena.';
    $og_title = $page_title;
    $og_description = $meta_description;
    $ogImageData = getDefaultOgImageData();
    $og_image_url = $ogImageData['url'];
    $og_image_width = $ogImageData['width'];
    $og_image_height = $ogImageData['height'];
    $og_image_alt = $ogImageData['og_alt'];
    $og_url = SITE_URL . '/category.php?slug=' . $slug; // Use the requested URL structure
    $og_type = 'website';
    $final_focus_keyword = '';
    $final_tags_string = '';

    // Include header, display 404 message, include footer, and exit
    include 'includes/header.php'; // Header needs the meta vars
    echo '<div class="text-center py-20">';
    echo '<h1 class="text-4xl font-bold mb-4">404</h1>';
    echo '<p class="text-gray-600">Tražena kategorija nije pronađena.</p>';
    echo '<a href="' . SITE_URL . '" class="btn mt-6 inline-block">Vrati se na početnu</a>';
    echo '</div>';
    include 'includes/footer.php';
    exit;
}

// --- Fetch Articles for the Category ---
// Ensure the function getArticlesByCategory exists in functions.php
if (function_exists('getArticlesByCategory')) {
    try {
        // Fetch initial set of articles for this category
        $articles = getArticlesByCategory($pdo, $category['id'], ARTICLES_PER_PAGE);
    } catch (PDOException $e) {
        $error_message = "Database error fetching articles for category: " . $e->getMessage();
        error_log($error_message);
        // Articles will remain an empty array, handled below
    }
} else {
     $error_message = "Error: getArticlesByCategory function not found.";
     error_log($error_message);
     $articles = []; // Ensure articles is empty if function missing
}

// --- Set Meta Data for Category Page ---
$page_title = escape($category['name']) . ' - ' . SITE_NAME;
$meta_description = 'Pregled članaka u kategoriji ' . escape($category['name']) . ' na ' . SITE_NAME . '.';
// Add category description from DB if available: $meta_description = $category['description'] ?? $default_description;
$og_title = escape($category['name']) . ' - ' . SITE_NAME;
$og_description = $meta_description;
$ogImageData = getDefaultOgImageData(); // Use default OG image for category pages for now
$og_image_url = $ogImageData['url'];
$og_image_width = $ogImageData['width'];
$og_image_height = $ogImageData['height'];
$og_image_alt = $ogImageData['og_alt']; // Use default OG alt
$og_url = SITE_URL . '/category.php?slug=' . escape($category['slug']); // URL of the current category page
$og_type = 'website'; // Or 'object' if more appropriate

// Include header AFTER setting meta variables
include 'includes/header.php';

// --- Define Ad Context for Category Page ---
$adContext = [
    'page_type' => 'category', // Source page type
    'source_page_id' => $category['id'], // Category ID as source ID
    'category_id' => $category['id'] // Also pass category ID for targeting rules
];

?>

<div class="flex flex-col md:flex-row gap-8">

    <?php // --- Main Content Area (Articles) --- ?>
    <div class="md:w-3/4 order-2 md:order-1">

        <div class="text-center mb-8 border-b border-border pb-4">
             <span class="text-sm text-gray-500 uppercase tracking-wider">Kategorija</span>
            <h1 class="text-3xl md:text-4xl font-montserrat font-extrabold text-primary mt-1">
                <?php echo escape($category['name']); ?>
            </h1>
            <?php // Optional: Display category description if you add it to the DB/fetch logic ?>
            <?php /* if (!empty($category['description'])): ?>
                <p class="mt-2 text-gray-600 max-w-2xl mx-auto"><?php echo escape($category['description']); ?></p>
            <?php endif; */ ?>
        </div>

        <?php if (!empty($error_message)): ?>
            <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm">
                <?php echo escape($error_message); ?>
            </div>
        <?php endif; ?>

        <div class="space-y-6" id="articlesContainer"> <?php // Container for articles, same ID as index.php for potential JS reuse ?>
            <?php if (empty($articles)): ?>
                <p class="text-center text-gray-darker py-10">Trenutno nema članaka u ovoj kategoriji.</p>
            <?php else: ?>
                <?php foreach ($articles as $article):
                    // Re-use the article card rendering logic from index.php or article.php if possible
                    // For simplicity, duplicating similar structure here:
                    $randomReaders = rand(30, 120); // Random reader count for visual effect
                    $articleUrl = SITE_URL . '/' . escape($article['slug']) . '/';
                    $authorAvatarData = getFeaturedImageUrl($article['author_avatar'], 'ss', 'articles', escape($article['author_name'] ?? 'Autor'));
                    $authorAvatarUrl = $authorAvatarData['url'];
                ?>
                    <?php // Desktop Card ?>
                    <article class="card p-0 overflow-hidden max-h-[245px] w-full article-card-desktop" x-data="{ readers: <?php echo $randomReaders; ?>, animating: false }" x-init="setTimeout(() => { readers += Math.floor(Math.random() * 5) + 1; animating = true; setTimeout(() => animating = false, 1500) }, Math.random() * 8000 + 3000)">
                        <a href="<?php echo $articleUrl; ?>" class="w-[200px] relative overflow-hidden m-[10px] z-[1] min-h-[200px] flex-shrink-0 rounded-lg block bg-gray-100">
                           <?php if (!empty($article['featured_image'])): ?>
                               <?php
                               // Use our responsive image helper for article thumbnails
                               echo getResponsiveImageHtml(
                                   $article['featured_image'],
                                   'articles',
                                   escape($article['title']),
                                   [
                                       'widths' => ['xs', 'ss'], // Use xs and ss sizes for article thumbnails
                                       'isPrimary' => false,
                                       'lazyLoad' => true,
                                       'containerClass' => '',
                                       'imgClass' => 'absolute inset-0 w-full h-full object-cover',
                                       'sizesAttr' => '200px',
                                       'usePicture' => false
                                   ]
                               );
                               ?>
                           <?php else: ?>
                            <div class="absolute inset-0 flex items-center justify-center bg-gray-200"><span class="text-gray-500 text-xs italic">Slika nedostupna</span></div>
                           <?php endif; ?>
                        </a>
                        <div class="w-3/5 p-5 flex flex-col justify-between">
                            <div>
                                <div class="flex justify-between items-center mb-2"><span class="text-xs text-gray-medium"><?php echo formatDate($article['published_at'] ?? $article['created_at']); ?></span></div>
                                <h3 class="text-xl font-montserrat font-extrabold mb-2 line-clamp-2"><a href="<?php echo $articleUrl; ?>" class="hover:text-primary transition-colors article-title text-dark-contrast"><?php echo escape($article['title']); ?></a></h3>
                                <?php if (!empty($article['excerpt'])): ?><p class="text-gray-darker text-sm line-clamp-3"><?php echo escape($article['excerpt']); ?></p><?php endif; ?>
                            </div>
                            <div class="mt-3 flex justify-between items-center">
                                <div class="flex items-center">
                                     <div class="w-8 h-8 rounded-full overflow-hidden mr-2 bg-gray-200 flex items-center justify-center">
                                        <?php if ($authorAvatarUrl): ?>
                                            <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo $authorAvatarData['alt']; ?>" class="w-full h-full object-cover" loading="lazy" width="32" height="32"/>
                                        <?php else: ?>
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                                        <?php endif; ?>
                                    </div>
                                    <span class="text-sm font-montserrat font-semibold text-dark-contrast"><?php echo escape($article['author_name'] ?? 'Nepoznat autor'); ?></span>
                                </div>
                                <div class="flex items-center text-xs text-gray-medium gap-4 flex-nowrap whitespace-nowrap">
                                     <?php if (!empty($article['reading_time'])): ?>
                                    <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span><?php echo escape($article['reading_time']); ?> min</span></div>
                                    <?php endif; ?>
                                     <div class="flex items-center text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg><span x-text="readers + ' trenutno čita'" :class="{'reader-pulse': animating}" class="relative inline-block"></span></div>
                                </div>
                            </div>
                        </div>
                    </article>
                    <?php // Mobile Card ?>
                     <article class="card article-card-mobile" x-data="{ readers: <?php echo $randomReaders; ?>, animating: false }" x-init="setTimeout(() => { readers += Math.floor(Math.random() * 5) + 1; animating = true; setTimeout(() => animating = false, 1500) }, Math.random() * 8000 + 3000)">
                        <a href="<?php echo $articleUrl; ?>" class="mobile-article-image rounded-t-xl block bg-gray-100">
                            <?php if (!empty($article['featured_image'])): ?>
                                <?php
                                // Use our responsive image helper for mobile article thumbnails
                                echo getResponsiveImageHtml(
                                    $article['featured_image'],
                                    'articles',
                                    escape($article['title']),
                                    [
                                        'widths' => ['xs', 'ss'], // Use xs and ss sizes for mobile thumbnails
                                        'isPrimary' => false,
                                        'lazyLoad' => true,
                                        'containerClass' => '',
                                        'imgClass' => 'absolute inset-0 w-full h-full object-cover',
                                        'sizesAttr' => '100vw',
                                        'usePicture' => false
                                    ]
                                );
                                ?>
                            <?php else: ?>
                                <div class="absolute inset-0 flex items-center justify-center bg-gray-200"><span class="text-gray-500 text-xs italic">Slika nedostupna</span></div>
                            <?php endif; ?>
                        </a>
                        <div class="mobile-article-content">
                            <div>
                                <div class="flex justify-between items-center mb-2"><span class="text-xs text-gray-medium"><?php echo formatDate($article['published_at'] ?? $article['created_at']); ?></span></div>
                                <h3 class="text-lg font-montserrat font-extrabold mb-2 line-clamp-2"><a href="<?php echo $articleUrl; ?>" class="hover:text-primary transition-colors article-title text-dark-contrast"><?php echo escape($article['title']); ?></a></h3>
                                <?php if (!empty($article['excerpt'])): ?><p class="text-gray-darker text-xs md:text-sm line-clamp-2"><?php echo escape($article['excerpt']); ?></p><?php endif; ?>
                            </div>
                            <div class="mt-3 flex flex-wrap justify-between items-center">
                                <div class="flex items-center mb-2 xs:mb-0">
                                    <div class="w-6 h-6 rounded-full overflow-hidden mr-2 bg-gray-200 flex items-center justify-center">
                                        <?php if ($authorAvatarUrl): ?>
                                            <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo $authorAvatarData['alt']; ?>" class="w-full h-full object-cover" loading="lazy" width="24" height="24"/>
                                        <?php else: ?>
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                                        <?php endif; ?>
                                    </div>
                                    <span class="text-xs font-montserrat font-semibold text-dark-contrast"><?php echo escape($article['author_name'] ?? 'Nepoznat autor'); ?></span>
                                </div>
                                <div class="flex items-center text-xs text-gray-medium gap-3">
                                     <?php if (!empty($article['reading_time'])): ?>
                                    <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span><?php echo escape($article['reading_time']); ?> min</span></div>
                                    <?php endif; ?>
                                    <div class="flex items-center text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg><span x-text="readers" :class="{'reader-pulse': animating}" class="relative inline-block"></span></div>
                                </div>
                            </div>
                        </div>
                    </article>
                <?php endforeach; ?>
            <?php endif; ?>

            <?php // --- Placeholder for Load More Button --- ?>
            <?php // TODO: Implement pagination for category pages if needed ?>
            <?php /*
            <div class="text-center mt-10" id="loadMoreContainer">
                <button id="loadMoreBtn"
                        data-next-page="2"
                        data-category-id="<?php echo $category['id']; ?>"
                        class="btn px-8 font-montserrat font-bold <?php // echo count($articles) < ARTICLES_PER_PAGE ? 'hidden' : ''; ?>"
                        >
                    Učitaj više članaka
                </button>
                <p id="loadingIndicator" class="text-gray-500 italic mt-4 hidden">Učitavanje...</p>
            </div>
            */ ?>

        </div> <?php // End #articlesContainer ?>
    </div> <?php // End Main Content Area ?>

    <?php // --- Sidebar --- ?>
    <div class="md:w-1/4 order-1 md:order-2">
        <div class="sidebar-content md:sticky md:top-32 space-y-6">

            <?php // Display Ads/Promos in Sidebar ?>
            <div class="mb-6">
                <?php echo displayAdsForPlacement($pdo, 'sidebar_middle', $adContext, 1, 1); ?>
            </div>

            <div class="mb-6">
                <?php echo displayAdsForPlacement($pdo, 'sidebar_bottom', $adContext, 1, 1); ?>
            </div>

            <?php // Popular Categories ?>
            <div class="card p-4">
                <h3 class="font-montserrat font-extrabold text-lg mb-4 text-dark-contrast">Popularne kategorije</h3>
                <div class="space-y-2">
                    <a href="<?php echo SITE_URL; ?>/category/recepti/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Recepti</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/prirodni-lijekovi/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Prirodni lijekovi</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/slana-jela/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Slana jela</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/slatka-jela/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Slatka jela</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>

            <?php // You might want to fetch popular articles *within this category* here later ?>

        </div> <?php // End .sidebar-content ?>
    </div> <?php // End Sidebar ?>
</div> <?php // End Flex container ?>

<?php
// --- Include Footer ---
include 'includes/footer.php';
?>