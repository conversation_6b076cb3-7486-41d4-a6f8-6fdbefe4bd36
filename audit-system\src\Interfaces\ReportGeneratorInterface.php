<?php

namespace AuditSystem\Interfaces;

use AuditSystem\Models\AuditResult;

/**
 * Interface for generating audit reports
 */
interface ReportGeneratorInterface
{
    /**
     * Generate a comprehensive audit report
     *
     * @param AuditResult $auditResult The audit results to generate report from
     * @return string Generated report content
     */
    public function generateReport(AuditResult $auditResult): string;

    /**
     * Export report in specified format
     *
     * @param AuditResult $auditResult The audit results to export
     * @param string $format Export format (json, html, markdown)
     * @param string $outputPath Path to save the exported report
     * @return bool True if export was successful
     */
    public function exportReport(AuditResult $auditResult, string $format, string $outputPath): bool;

    /**
     * Get supported export formats
     *
     * @return string[] Array of supported formats
     */
    public function getSupportedFormats(): array;
}