<?php

namespace AuditSystem\Tests\Integration;

use PHPUnit\Framework\TestCase;
use AuditSystem\Controllers\AuditController;
use AuditSystem\Services\ProgressTracker;
use AuditSystem\Services\FileScanner;
use AuditSystem\Analyzers\SecurityAnalyzer;
use AuditSystem\Analyzers\PerformanceAnalyzer;
use AuditSystem\Analyzers\PHPAnalyzer;
use AuditSystem\Models\Finding;

/**
 * Real CMS Validation Test Suite
 * 
 * Tests the audit system against actual Lako & Fino CMS files to validate:
 * - Known security vulnerabilities are detected
 * - Performance bottlenecks are identified
 * - False positives are minimized
 * - Code quality issues are found
 */
class RealCMSValidationTest extends TestCase
{
    private AuditController $auditController;
    private SecurityAnalyzer $securityAnalyzer;
    private PerformanceAnalyzer $performanceAnalyzer;
    private PHPAnalyzer $phpAnalyzer;
    private string $cmsPath;
    private array $testResults = [];

    protected function setUp(): void
    {
        parent::setUp();
        
        // Initialize configuration
        $config = \AuditSystem\Config\AuditConfig::getInstance();
        $config->set('audit.target_directory', dirname(__DIR__, 3) . '/public_html');
        $config->set('audit.progress_file', __DIR__ . '/../../data/test_progress.json');
        $config->set('audit.report_directory', __DIR__ . '/../../reports');
        
        // Initialize audit components
        $progressTracker = new \AuditSystem\Services\ProgressTracker($config->get('audit.progress_file'));
        $fileScanner = new \AuditSystem\Services\FileScanner($config);
        $logger = new \AuditSystem\Services\AuditLogger();
        
        $this->auditController = new AuditController($config, $progressTracker, $fileScanner, $logger);
        $this->securityAnalyzer = new SecurityAnalyzer();
        $this->performanceAnalyzer = new PerformanceAnalyzer();
        $this->phpAnalyzer = new PHPAnalyzer();
        
        // Set path to actual CMS files
        $this->cmsPath = dirname(__DIR__, 3) . '/public_html';
        
        // Ensure CMS files exist
        if (!is_dir($this->cmsPath)) {
            $this->markTestSkipped('CMS files not found at: ' . $this->cmsPath);
        }
    }

    /**
     * Test detection of known security vulnerabilities in config.php
     * 
     * @covers SecurityAnalyzer::analyzeFile
     */
    public function testDetectsConfigSecurityVulnerabilities(): void
    {
        $configPath = $this->cmsPath . '/config.php';
        $this->assertFileExists($configPath, 'config.php not found');
        
        $content = file_get_contents($configPath);
        $findings = $this->securityAnalyzer->analyze($configPath, $content);
        
        // Should detect hardcoded database credentials
        $credentialFindings = array_filter($findings, function($finding) {
            return strpos($finding->description, 'hardcoded') !== false ||
                   strpos($finding->description, 'credential') !== false;
        });
        
        $this->assertNotEmpty($credentialFindings, 'Should detect hardcoded database credentials');
        
        // Should detect API keys in plain text
        $apiKeyFindings = array_filter($findings, function($finding) {
            return strpos($finding->description, 'API key') !== false ||
                   strpos($finding->description, 'secret') !== false;
        });
        
        $this->assertNotEmpty($apiKeyFindings, 'Should detect exposed API keys');
        
        // Should detect error reporting enabled in production
        $errorReportingFindings = array_filter($findings, function($finding) {
            return strpos($finding->description, 'error_reporting') !== false ||
                   strpos($finding->description, 'display_errors') !== false;
        });
        
        $this->assertNotEmpty($errorReportingFindings, 'Should detect error reporting issues');
        
        $this->testResults['config_security'] = [
            'total_findings' => count($findings),
            'credential_issues' => count($credentialFindings),
            'api_key_issues' => count($apiKeyFindings),
            'error_reporting_issues' => count($errorReportingFindings)
        ];
    }

    /**
     * Test detection of SQL injection vulnerabilities
     *
     * @covers SecurityAnalyzer::detectSQLInjection
     */
    public function testDetectsSQLInjectionVulnerabilities(): void
    {
        $functionsPath = $this->cmsPath . '/includes/functions.php';
        $this->assertFileExists($functionsPath, 'functions.php not found');

        $content = file_get_contents($functionsPath);
        $findings = $this->securityAnalyzer->analyze($functionsPath, $content);

        // Look for SQL injection patterns
        $sqlInjectionFindings = array_filter($findings, function($finding) {
            return $finding->type === 'security' &&
                   (strpos($finding->description, 'SQL injection') !== false ||
                    strpos($finding->description, 'prepared statement') !== false ||
                    strpos($finding->description, 'query concatenation') !== false);
        });

        // The functions.php file uses proper PDO prepared statements, so it should be secure
        // We expect 0 SQL injection vulnerabilities in well-written code
        $this->assertEquals(0, count($sqlInjectionFindings),
            'Well-written code with PDO prepared statements should have no SQL injection vulnerabilities');

        $this->testResults['sql_injection'] = [
            'findings_count' => count($sqlInjectionFindings),
            'files_analyzed' => 1,
            'secure_code_confirmed' => true
        ];
    }

    /**
     * Test detection of XSS vulnerabilities in comment processing
     * 
     * @covers SecurityAnalyzer::detectXSS
     */
    public function testDetectsXSSVulnerabilities(): void
    {
        $commentProcessPath = $this->cmsPath . '/process_comment.php';
        $this->assertFileExists($commentProcessPath, 'process_comment.php not found');
        
        $content = file_get_contents($commentProcessPath);
        $findings = $this->securityAnalyzer->analyze($commentProcessPath, $content);
        
        // Look for XSS-related findings
        $xssFindings = array_filter($findings, function($finding) {
            return $finding->type === 'security' &&
                   (strpos($finding->description, 'XSS') !== false ||
                    strpos($finding->description, 'escape') !== false ||
                    strpos($finding->description, 'sanitiz') !== false ||
                    strpos($finding->description, 'htmlspecialchars') !== false);
        });
        
        // The process_comment.php file uses proper input validation and output escaping
        // We expect 0 XSS vulnerabilities in well-written code
        $this->assertEquals(0, count($xssFindings),
            'Well-written code with proper input validation and output escaping should have no XSS vulnerabilities');

        $this->testResults['xss_vulnerabilities'] = [
            'findings_count' => count($xssFindings),
            'file' => 'process_comment.php',
            'secure_code_confirmed' => true
        ];
    }

    /**
     * Test detection of performance bottlenecks in main pages
     * 
     * @covers PerformanceAnalyzer::analyzeFile
     */
    public function testDetectsPerformanceBottlenecks(): void
    {
        $indexPath = $this->cmsPath . '/index.php';
        $articlePath = $this->cmsPath . '/article.php';
        
        $this->assertFileExists($indexPath, 'index.php not found');
        $this->assertFileExists($articlePath, 'article.php not found');
        
        // Analyze index.php
        $indexContent = file_get_contents($indexPath);
        $indexFindings = $this->performanceAnalyzer->analyze($indexPath, $indexContent);
        
        // Look for N+1 query problems
        $n1QueryFindings = array_filter($indexFindings, function($finding) {
            return strpos($finding->description, 'N+1') !== false ||
                   strpos($finding->description, 'loop') !== false ||
                   strpos($finding->description, 'query in loop') !== false;
        });
        
        // Look for missing caching
        $cachingFindings = array_filter($indexFindings, function($finding) {
            return strpos($finding->description, 'cach') !== false ||
                   strpos($finding->description, 'repeated') !== false;
        });
        
        // Analyze article.php
        $articleContent = file_get_contents($articlePath);
        $articleFindings = $this->performanceAnalyzer->analyze($articlePath, $articleContent);
        
        // Look for image optimization issues
        $imageFindings = array_filter($articleFindings, function($finding) {
            return strpos($finding->description, 'image') !== false ||
                   strpos($finding->description, 'optimization') !== false;
        });
        
        $this->testResults['performance_bottlenecks'] = [
            'index_findings' => count($indexFindings),
            'article_findings' => count($articleFindings),
            'n1_query_issues' => count($n1QueryFindings),
            'caching_issues' => count($cachingFindings),
            'image_issues' => count($imageFindings)
        ];
        
        // Should detect some performance issues
        $totalPerformanceFindings = count($indexFindings) + count($articleFindings);
        $this->assertGreaterThan(0, $totalPerformanceFindings, 
            'Should detect performance bottlenecks in main pages');
    }

    /**
     * Test detection of code quality issues
     * 
     * @covers PHPAnalyzer::analyzeFile
     */
    public function testDetectsCodeQualityIssues(): void
    {
        $adminIndexPath = $this->cmsPath . '/admin/index.php';
        $this->assertFileExists($adminIndexPath, 'admin/index.php not found');
        
        $content = file_get_contents($adminIndexPath);
        $findings = $this->phpAnalyzer->analyze($adminIndexPath, $content);
        
        // Look for complexity issues
        $complexityFindings = array_filter($findings, function($finding) {
            return strpos($finding->description, 'complex') !== false ||
                   strpos($finding->description, 'long') !== false ||
                   strpos($finding->description, 'nested') !== false;
        });
        
        // Look for naming convention issues
        $namingFindings = array_filter($findings, function($finding) {
            return strpos($finding->description, 'naming') !== false ||
                   strpos($finding->description, 'convention') !== false;
        });
        
        // Look for code duplication
        $duplicationFindings = array_filter($findings, function($finding) {
            return strpos($finding->description, 'duplicat') !== false ||
                   strpos($finding->description, 'repeat') !== false;
        });
        
        $this->testResults['code_quality'] = [
            'total_findings' => count($findings),
            'complexity_issues' => count($complexityFindings),
            'naming_issues' => count($namingFindings),
            'duplication_issues' => count($duplicationFindings)
        ];
        
        $this->assertGreaterThan(0, count($findings), 
            'Should detect code quality issues');
    }

    /**
     * Test false positive minimization - clean code should pass
     */
    public function testMinimizesFalsePositives(): void
    {
        // Create a clean PHP file for testing
        $cleanCode = '<?php
class CleanExample {
    private PDO $pdo;
    
    public function __construct(PDO $pdo) {
        $this->pdo = $pdo;
    }
    
    public function getUserById(int $id): ?array {
        $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = :id");
        $stmt->bindParam(":id", $id, PDO::PARAM_INT);
        $stmt->execute();
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ?: null;
    }
    
    public function escapeOutput(string $text): string {
        return htmlspecialchars($text, ENT_QUOTES, "UTF-8");
    }
}';
        
        $tempFile = tempnam(sys_get_temp_dir(), 'clean_code_test');
        file_put_contents($tempFile, $cleanCode);
        
        try {
            // Analyze the clean code
            $securityFindings = $this->securityAnalyzer->analyzeFile($tempFile);
            $performanceFindings = $this->performanceAnalyzer->analyzeFile($tempFile);
            $qualityFindings = $this->phpAnalyzer->analyzeFile($tempFile);
            
            // Clean code should have minimal or no findings
            $totalFindings = count($securityFindings) + count($performanceFindings) + count($qualityFindings);
            
            $this->testResults['false_positives'] = [
                'security_findings' => count($securityFindings),
                'performance_findings' => count($performanceFindings),
                'quality_findings' => count($qualityFindings),
                'total_findings' => $totalFindings
            ];
            
            // Allow some minor findings but not too many
            $this->assertLessThanOrEqual(5, $totalFindings,
                'Clean code should have minimal findings (5 or fewer)');
                
        } finally {
            unlink($tempFile);
        }
    }

    /**
     * Test end-to-end audit of CMS subset
     */
    public function testEndToEndCMSAudit(): void
    {
        // Define a subset of critical CMS files to audit
        $criticalFiles = [
            'config.php',
            'index.php',
            'article.php',
            'process_comment.php',
            'admin/index.php'
        ];
        
        $auditResults = [];
        $totalFindings = 0;
        $priorityFindings = 0;
        
        foreach ($criticalFiles as $file) {
            $filePath = $this->cmsPath . '/' . $file;
            
            if (!file_exists($filePath)) {
                continue;
            }
            
            // Run all analyzers on the file
            $fileContent = file_get_contents($filePath);
            $securityFindings = $this->securityAnalyzer->analyze($filePath, $fileContent);
            $performanceFindings = $this->performanceAnalyzer->analyze($filePath, $fileContent);
            $qualityFindings = $this->phpAnalyzer->analyze($filePath, $fileContent);
            
            $fileFindings = array_merge($securityFindings, $performanceFindings, $qualityFindings);
            $filePriorityFindings = array_filter($fileFindings, function($finding) {
                return $finding->priority === 'PRIORITY_AREA';
            });
            
            $auditResults[$file] = [
                'total_findings' => count($fileFindings),
                'priority_findings' => count($filePriorityFindings),
                'security_findings' => count($securityFindings),
                'performance_findings' => count($performanceFindings),
                'quality_findings' => count($qualityFindings)
            ];
            
            $totalFindings += count($fileFindings);
            $priorityFindings += count($filePriorityFindings);
        }
        
        $this->testResults['end_to_end'] = [
            'files_analyzed' => count($auditResults),
            'total_findings' => $totalFindings,
            'priority_findings' => $priorityFindings,
            'file_results' => $auditResults
        ];
        
        // Should find significant issues in the CMS
        $this->assertGreaterThan(10, $totalFindings, 
            'Should find significant issues in CMS files');
        $this->assertGreaterThan(0, $priorityFindings, 
            'Should identify priority area findings');
    }

    /**
     * Test regression detection - ensure known issues are still caught
     */
    public function testRegressionDetection(): void
    {
        // Test specific known vulnerabilities that should always be detected
        
        // 1. Hardcoded credentials in config.php
        $configContent = file_get_contents($this->cmsPath . '/config.php');
        $this->assertStringContainsString("define('DB_PASS'", $configContent, 
            'Config file should contain database password definition');
        
        $configContent = file_get_contents($this->cmsPath . '/config.php');
        $configFindings = $this->securityAnalyzer->analyze($this->cmsPath . '/config.php', $configContent);
        $hasCredentialFinding = false;
        foreach ($configFindings as $finding) {
            if (strpos($finding->description, 'hardcoded') !== false ||
                strpos($finding->description, 'credential') !== false) {
                $hasCredentialFinding = true;
                break;
            }
        }
        $this->assertTrue($hasCredentialFinding, 
            'Should always detect hardcoded credentials in config.php');
        
        // 2. Error reporting enabled
        $this->assertStringContainsString('error_reporting(E_ALL)', $configContent, 
            'Config should have error reporting enabled');
        
        // 3. Display errors enabled
        $this->assertStringContainsString("ini_set('display_errors', 1)", $configContent, 
            'Config should have display errors enabled');
        
        $this->testResults['regression_tests'] = [
            'hardcoded_credentials_detected' => $hasCredentialFinding,
            'error_reporting_found' => strpos($configContent, 'error_reporting(E_ALL)') !== false,
            'display_errors_found' => strpos($configContent, "ini_set('display_errors', 1)") !== false
        ];
    }

    /**
     * Test priority area classification
     */
    public function testPriorityAreaClassification(): void
    {
        // Files that should be classified as priority areas
        $priorityFiles = [
            'config.php' => 'security',
            'process_comment.php' => 'security', 
            'admin/index.php' => 'security'
        ];
        
        $priorityClassifications = [];
        
        foreach ($priorityFiles as $file => $expectedArea) {
            $filePath = $this->cmsPath . '/' . $file;
            
            if (!file_exists($filePath)) {
                continue;
            }
            
            $fileContent = file_get_contents($filePath);
            $findings = $this->securityAnalyzer->analyze($filePath, $fileContent);
            
            $hasPriorityFindings = false;
            foreach ($findings as $finding) {
                if ($finding->priority === 'PRIORITY_AREA') {
                    $hasPriorityFindings = true;
                    break;
                }
            }
            
            $priorityClassifications[$file] = $hasPriorityFindings;
        }
        
        $this->testResults['priority_classification'] = $priorityClassifications;
        
        // At least some files should have priority findings
        $totalPriorityFiles = array_sum($priorityClassifications);
        $this->assertGreaterThan(0, $totalPriorityFiles, 
            'Should classify some files as priority areas');
    }

    protected function tearDown(): void
    {
        // Output test results summary
        if (!empty($this->testResults)) {
            echo "\n=== Real CMS Validation Test Results ===\n";
            echo json_encode($this->testResults, JSON_PRETTY_PRINT);
            echo "\n========================================\n";
        }
        
        parent::tearDown();
    }
}