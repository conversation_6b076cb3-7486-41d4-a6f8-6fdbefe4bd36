<?php
/**
 * Admin image upload handler
 * Handles ajax requests for image uploads from URLs
 */

require_once '../config.php';
require_once 'includes/auth_check.php';
require_once '../includes/functions.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Check for valid request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    exit;
}

// Check for required parameters
if (empty($_POST['action'])) {
    echo json_encode(['success' => false, 'error' => 'Missing action parameter']);
    exit;
}

// Handle image upload from URL
if ($_POST['action'] === 'upload_from_url') {
    if (empty($_POST['image_url'])) {
        echo json_encode(['success' => false, 'error' => 'Missing image URL']);
        exit;
    }
    
    $imageUrl = trim($_POST['image_url']);
    
    // Validate URL
    if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
        echo json_encode(['success' => false, 'error' => 'Invalid image URL']);
        exit;
    }
    
    // Upload the image using your existing function
    $uploadResult = handleImageUpload($imageUrl, 'articles');
    
    if (is_array($uploadResult) && isset($uploadResult['error'])) {
        // Error occurred during upload
        echo json_encode(['success' => false, 'error' => $uploadResult['error']]);
        exit;
    } elseif (is_string($uploadResult)) {
        // Success - get the URL for the uploaded image
        $imageData = getFeaturedImageUrl($uploadResult, 'original', 'articles');
        
        if (!empty($imageData['url'])) {
            echo json_encode([
                'success' => true, 
                'url' => $imageData['url'],
                'width' => $imageData['width'],
                'height' => $imageData['height']
            ]);
            exit;
        } else {
            echo json_encode(['success' => false, 'error' => 'Failed to get URL for uploaded image']);
            exit;
        }
    } else {
        echo json_encode(['success' => false, 'error' => 'Unknown error during image upload']);
        exit;
    }
}

// No valid action matched
echo json_encode(['success' => false, 'error' => 'Invalid action']);
exit;