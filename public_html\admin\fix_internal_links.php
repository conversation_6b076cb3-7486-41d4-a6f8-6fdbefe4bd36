<?php
/**
 * admin/fix_internal_links.php
 * <PERSON><PERSON><PERSON> to fix internal links by removing the /article/ prefix
 */

require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/internal_links.php';
require_once 'includes/auth_check.php';

// Initialize variables
$message = '';
$error = '';

// Make sure getPDOConnection function is available
if (!function_exists('getPDOConnection')) {
    function getPDOConnection() {
        // Basic check for constants
        if (!defined('DB_HOST') || !defined('DB_NAME') || !defined('DB_USER') || !defined('DB_PASS') || !defined('DB_CHARSET')) {
            error_log("FATAL: Database configuration constants are missing in getPDOConnection().");
            throw new Exception("Database configuration constants are missing.");
        }
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $options = [ PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC, PDO::ATTR_EMULATE_PREPARES => false, ];
        try {
            return new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (\PDOException $e) {
            error_log("PDO Connection failed in getPDOConnection(): " . $e->getMessage());
            throw new Exception("Database connection failed during operation. Check server logs. PDO Error: " . $e->getMessage());
        }
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_links'])) {
    try {
        $pdo = getPDOConnection();
        $result = fixInternalLinks($pdo);
        
        if ($result['success']) {
            $message = $result['message'];
        } else {
            $error = $result['error'];
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get current internal links
try {
    $pdo = getPDOConnection();
    $sql = "SELECT id, keyword, url FROM internal_links WHERE url LIKE '%/article/%'";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $links = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error = "Error fetching links: " . $e->getMessage();
    $links = [];
}

// Include header
$page_title = "Fix Internal Links";
include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Fix Internal Links</h1>
        <a href="internal_links.php" class="btn-secondary">Back to Internal Links</a>
    </div>
    
    <?php if (!empty($message)): ?>
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
            <p><?php echo $message; ?></p>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($error)): ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
            <p><?php echo $error; ?></p>
        </div>
    <?php endif; ?>
    
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Internal Links with /article/ Prefix</h2>
        
        <?php if (empty($links)): ?>
            <p class="text-gray-600">No internal links with /article/ prefix found.</p>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead>
                        <tr>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">ID</th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Keyword</th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Current URL</th>
                            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Fixed URL</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($links as $link): ?>
                            <tr>
                                <td class="py-2 px-4 border-b border-gray-200"><?php echo $link['id']; ?></td>
                                <td class="py-2 px-4 border-b border-gray-200"><?php echo htmlspecialchars($link['keyword']); ?></td>
                                <td class="py-2 px-4 border-b border-gray-200"><?php echo htmlspecialchars($link['url']); ?></td>
                                <td class="py-2 px-4 border-b border-gray-200"><?php echo htmlspecialchars(str_replace('/article/', '/', $link['url'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <form method="post" class="mt-6">
                <button type="submit" name="fix_links" class="btn-primary">Fix Internal Links</button>
            </form>
        <?php endif; ?>
    </div>
    
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">About This Tool</h2>
        <p class="text-gray-600 mb-2">This tool fixes internal links in the database by removing the '/article/' prefix from URLs.</p>
        <p class="text-gray-600 mb-2">The system has been updated to automatically handle URLs correctly, but existing links in the database may still have the incorrect format.</p>
        <p class="text-gray-600">Use this tool to fix all existing internal links at once.</p>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
