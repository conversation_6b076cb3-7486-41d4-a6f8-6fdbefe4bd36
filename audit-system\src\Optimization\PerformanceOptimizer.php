<?php

namespace AuditSystem\Optimization;

use AuditSystem\Config\AuditConfig;
use AuditSystem\Services\AuditLogger;

/**
 * Performance Optimizer for Large Codebase Scanning
 * 
 * Optimizes audit system performance for scanning large codebases by:
 * - Memory management and garbage collection
 * - File processing batching
 * - Analyzer optimization
 * - Progress checkpointing
 */
class PerformanceOptimizer
{
    private AuditConfig $config;
    private AuditLogger $logger;
    private array $performanceMetrics = [];
    private int $processedFiles = 0;
    private float $startTime;
    private int $memoryThreshold;
    private int $batchSize;

    public function __construct(AuditConfig $config, ?AuditLogger $logger = null)
    {
        $this->config = $config;
        $this->logger = $logger ?? new AuditLogger();
        $this->startTime = microtime(true);
        
        // Configure performance parameters
        $this->memoryThreshold = $this->config->get('performance.memory_threshold', 128 * 1024 * 1024); // 128MB
        $this->batchSize = $this->config->get('performance.batch_size', 50);
        
        $this->logger->info('Performance optimizer initialized', [
            'memory_threshold' => $this->formatBytes($this->memoryThreshold),
            'batch_size' => $this->batchSize
        ]);
    }

    /**
     * Optimize system configuration for large codebase scanning
     *
     * @return void
     */
    public function optimizeForLargeCodebase(): void
    {
        $this->logger->info('Optimizing system for large codebase scanning');

        // Increase memory limit if needed
        $currentLimit = ini_get('memory_limit');
        $currentBytes = $this->parseMemoryLimit($currentLimit);
        $recommendedBytes = 256 * 1024 * 1024; // 256MB

        if ($currentBytes < $recommendedBytes) {
            ini_set('memory_limit', '256M');
            $this->logger->info('Increased memory limit', [
                'from' => $currentLimit,
                'to' => '256M'
            ]);
        }

        // Optimize garbage collection
        gc_enable();
        gc_collect_cycles();
        
        // Set optimal timeout for large files
        $this->config->set('audit.timeout', 600); // 10 minutes
        
        // Increase max file size for comprehensive analysis
        $this->config->set('audit.max_file_size', 5 * 1024 * 1024); // 5MB
        
        // Enable progress checkpointing more frequently
        $this->config->set('audit.checkpoint_frequency', 25); // Every 25 files
        
        $this->logger->info('System optimized for large codebase scanning');
    }

    /**
     * Monitor and manage memory usage during audit
     *
     * @return void
     */
    public function monitorMemoryUsage(): void
    {
        $currentUsage = memory_get_usage(true);
        $peakUsage = memory_get_peak_usage(true);
        
        $this->performanceMetrics['memory'] = [
            'current' => $currentUsage,
            'peak' => $peakUsage,
            'threshold' => $this->memoryThreshold
        ];

        if ($currentUsage > $this->memoryThreshold) {
            $this->logger->warning('Memory usage approaching threshold', [
                'current' => $this->formatBytes($currentUsage),
                'threshold' => $this->formatBytes($this->memoryThreshold),
                'peak' => $this->formatBytes($peakUsage)
            ]);
            
            // Trigger garbage collection
            $this->performMemoryCleanup();
        }
    }

    /**
     * Perform memory cleanup and garbage collection
     *
     * @return void
     */
    public function performMemoryCleanup(): void
    {
        $beforeCleanup = memory_get_usage(true);
        
        // Force garbage collection
        gc_collect_cycles();
        
        // Clear any internal caches if available
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
        
        $afterCleanup = memory_get_usage(true);
        $freed = $beforeCleanup - $afterCleanup;
        
        $this->logger->info('Memory cleanup performed', [
            'before' => $this->formatBytes($beforeCleanup),
            'after' => $this->formatBytes($afterCleanup),
            'freed' => $this->formatBytes($freed)
        ]);
    }

    /**
     * Optimize file processing batch size based on performance
     *
     * @param float $averageProcessingTime Average time per file in seconds
     * @return int Optimized batch size
     */
    public function optimizeBatchSize(float $averageProcessingTime): int
    {
        // Adjust batch size based on processing speed
        if ($averageProcessingTime > 2.0) {
            // Slow processing - reduce batch size
            $optimizedBatchSize = max(10, $this->batchSize / 2);
        } elseif ($averageProcessingTime < 0.5) {
            // Fast processing - increase batch size
            $optimizedBatchSize = min(100, $this->batchSize * 2);
        } else {
            // Normal processing - keep current batch size
            $optimizedBatchSize = $this->batchSize;
        }

        if ($optimizedBatchSize !== $this->batchSize) {
            $this->logger->info('Optimized batch size', [
                'from' => $this->batchSize,
                'to' => $optimizedBatchSize,
                'average_processing_time' => $averageProcessingTime
            ]);
            $this->batchSize = $optimizedBatchSize;
        }

        return $optimizedBatchSize;
    }

    /**
     * Track file processing performance
     *
     * @param string $filePath
     * @param float $processingTime
     * @param int $findingsCount
     * @return void
     */
    public function trackFileProcessing(string $filePath, float $processingTime, int $findingsCount): void
    {
        $this->processedFiles++;
        
        $this->performanceMetrics['files'][] = [
            'path' => basename($filePath),
            'processing_time' => $processingTime,
            'findings_count' => $findingsCount,
            'memory_usage' => memory_get_usage(true)
        ];

        // Monitor memory every batch
        if ($this->processedFiles % $this->batchSize === 0) {
            $this->monitorMemoryUsage();
            $this->logBatchPerformance();
        }
    }

    /**
     * Log performance metrics for current batch
     *
     * @return void
     */
    private function logBatchPerformance(): void
    {
        $recentFiles = array_slice($this->performanceMetrics['files'], -$this->batchSize);
        
        $totalTime = array_sum(array_column($recentFiles, 'processing_time'));
        $averageTime = $totalTime / count($recentFiles);
        $totalFindings = array_sum(array_column($recentFiles, 'findings_count'));
        
        $this->logger->info('Batch performance metrics', [
            'batch_size' => count($recentFiles),
            'total_processing_time' => round($totalTime, 2),
            'average_processing_time' => round($averageTime, 3),
            'total_findings' => $totalFindings,
            'files_processed' => $this->processedFiles
        ]);

        // Optimize batch size based on performance
        $this->optimizeBatchSize($averageTime);
    }

    /**
     * Get comprehensive performance report
     *
     * @return array
     */
    public function getPerformanceReport(): array
    {
        $currentTime = microtime(true);
        $totalDuration = $currentTime - $this->startTime;
        
        $report = [
            'summary' => [
                'total_duration' => round($totalDuration, 2),
                'files_processed' => $this->processedFiles,
                'files_per_second' => $this->processedFiles > 0 ? round($this->processedFiles / $totalDuration, 2) : 0,
                'current_memory_usage' => memory_get_usage(true),
                'peak_memory_usage' => memory_get_peak_usage(true)
            ],
            'configuration' => [
                'memory_threshold' => $this->memoryThreshold,
                'batch_size' => $this->batchSize,
                'timeout' => $this->config->get('audit.timeout'),
                'max_file_size' => $this->config->get('audit.max_file_size')
            ]
        ];

        if (!empty($this->performanceMetrics['files'])) {
            $processingTimes = array_column($this->performanceMetrics['files'], 'processing_time');
            $findingsCounts = array_column($this->performanceMetrics['files'], 'findings_count');
            
            $report['file_processing'] = [
                'average_processing_time' => round(array_sum($processingTimes) / count($processingTimes), 3),
                'min_processing_time' => round(min($processingTimes), 3),
                'max_processing_time' => round(max($processingTimes), 3),
                'total_findings' => array_sum($findingsCounts),
                'average_findings_per_file' => round(array_sum($findingsCounts) / count($findingsCounts), 1)
            ];
        }

        return $report;
    }

    /**
     * Optimize analyzer configuration for performance
     *
     * @param array $analyzers
     * @return void
     */
    public function optimizeAnalyzers(array $analyzers): void
    {
        $this->logger->info('Optimizing analyzer configurations for performance');

        foreach ($analyzers as $name => $analyzer) {
            if (method_exists($analyzer, 'optimizeForPerformance')) {
                $analyzer->optimizeForPerformance();
                $this->logger->debug("Optimized {$name} analyzer for performance");
            }

            // Set timeout for individual analyzers
            if (method_exists($analyzer, 'setTimeout')) {
                $analyzer->setTimeout($this->config->get('audit.timeout', 300));
            }

            // Enable caching if supported
            if (method_exists($analyzer, 'enableCaching')) {
                $analyzer->enableCaching(true);
            }
        }
    }

    /**
     * Check if system should pause for resource management
     *
     * @return bool
     */
    public function shouldPauseForResources(): bool
    {
        $currentMemory = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        // Pause if using more than 80% of memory limit
        $memoryUsagePercent = ($currentMemory / $memoryLimit) * 100;
        
        if ($memoryUsagePercent > 80) {
            $this->logger->warning('High memory usage detected, pausing for resource management', [
                'memory_usage_percent' => round($memoryUsagePercent, 1),
                'current_memory' => $this->formatBytes($currentMemory),
                'memory_limit' => $this->formatBytes($memoryLimit)
            ]);
            return true;
        }

        return false;
    }

    /**
     * Pause execution for resource management
     *
     * @param int $pauseSeconds
     * @return void
     */
    public function pauseForResources(int $pauseSeconds = 5): void
    {
        $this->logger->info("Pausing execution for {$pauseSeconds} seconds for resource management");
        
        // Perform cleanup during pause
        $this->performMemoryCleanup();
        
        // Brief pause to allow system recovery
        sleep($pauseSeconds);
        
        $this->logger->info('Resuming execution after resource management pause');
    }

    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * Parse memory limit string to bytes
     *
     * @param string $memoryLimit
     * @return int
     */
    private function parseMemoryLimit(string $memoryLimit): int
    {
        if ($memoryLimit === '-1') {
            return PHP_INT_MAX;
        }
        
        $unit = strtoupper(substr($memoryLimit, -1));
        $value = (int) substr($memoryLimit, 0, -1);
        
        switch ($unit) {
            case 'G':
                return $value * 1024 * 1024 * 1024;
            case 'M':
                return $value * 1024 * 1024;
            case 'K':
                return $value * 1024;
            default:
                return (int) $memoryLimit;
        }
    }

    /**
     * Generate performance optimization recommendations
     *
     * @return array
     */
    public function generateOptimizationRecommendations(): array
    {
        $recommendations = [];
        $report = $this->getPerformanceReport();
        
        // Memory recommendations
        if ($report['summary']['peak_memory_usage'] > $this->memoryThreshold) {
            $recommendations[] = [
                'type' => 'memory',
                'priority' => 'high',
                'message' => 'Consider increasing memory limit or reducing batch size',
                'current_peak' => $this->formatBytes($report['summary']['peak_memory_usage']),
                'threshold' => $this->formatBytes($this->memoryThreshold)
            ];
        }

        // Performance recommendations
        if (isset($report['file_processing']['average_processing_time'])) {
            $avgTime = $report['file_processing']['average_processing_time'];
            
            if ($avgTime > 2.0) {
                $recommendations[] = [
                    'type' => 'performance',
                    'priority' => 'medium',
                    'message' => 'File processing is slow, consider optimizing analyzers or increasing timeout',
                    'average_time' => $avgTime
                ];
            }
        }

        // Throughput recommendations
        if ($report['summary']['files_per_second'] < 1.0) {
            $recommendations[] = [
                'type' => 'throughput',
                'priority' => 'medium',
                'message' => 'Low file processing throughput, consider performance optimizations',
                'files_per_second' => $report['summary']['files_per_second']
            ];
        }

        return $recommendations;
    }
}