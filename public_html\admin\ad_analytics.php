<?php
// Core configuration and authentication
require_once '../config.php'; // Adjust path as needed
require_once 'includes/auth_check.php'; // Check if admin is logged in
require_once '../includes/functions.php'; // General functions like escape(), formatDate()

// --- Page Setup ---
$admin_page_title = 'Ads Analytics';
$error_message = $GLOBALS['error_message'] ?? null; // Use global error message if set by functions
$selected_period = $_GET['period'] ?? '30d'; // Default to 30 days
$limit = 10; // Limit for tables

// --- Helper function to get date interval string for SQL ---
if (!function_exists('getSqlDateInterval')) {
    function getSqlDateInterval(string $period): string {
        return match ($period) {
            '7d' => 'INTERVAL 7 DAY',
            '90d' => 'INTERVAL 90 DAY',
            'year' => 'INTERVAL 1 YEAR',
            'all' => 'INTERVAL 10 YEAR', // Effectively all time for recent data
            default => 'INTERVAL 30 DAY', // Default is 30d
        };
    }
}
$date_interval_sql = getSqlDateInterval($selected_period);

// --- Ad Specific Data Fetching Functions (Now using new tables) ---

function getAdPerformanceStats(PDO $pdo, string $date_interval_sql): array {
    $stats = [
        'total_affiliate_views' => 0,
        'total_affiliate_clicks' => 0,
        'total_adsense_views' => 0,
        'overall_ctr' => 0,
    ];
    $fallback_needed = false;
    $detailed_clicks_exists = $pdo->query("SHOW TABLES LIKE 'detailed_ad_clicks'")->rowCount() > 0;
    $ad_impressions_exists = $pdo->query("SHOW TABLES LIKE 'ad_impressions'")->rowCount() > 0;

    try {
        if ($detailed_clicks_exists) {
            // Affiliate Clicks (from detailed table)
            $sql_clicks = "SELECT COUNT(*) FROM detailed_ad_clicks
                           WHERE ad_type = 'affiliate' AND created_at >= DATE_SUB(NOW(), $date_interval_sql)";
            $stats['total_affiliate_clicks'] = (int)$pdo->query($sql_clicks)->fetchColumn();
        } else {
            $fallback_needed = true; // Need fallback if detailed clicks missing
        }

        if ($ad_impressions_exists) {
            // Affiliate Impressions (from detailed table)
            $sql_views = "SELECT COUNT(*) FROM ad_impressions
                          WHERE ad_type = 'affiliate' AND created_at >= DATE_SUB(NOW(), $date_interval_sql)";
            $stats['total_affiliate_views'] = (int)$pdo->query($sql_views)->fetchColumn();

            // AdSense Impressions (from detailed table)
             $sql_adsense_views = "SELECT COUNT(*) FROM ad_impressions
                                   WHERE ad_type = 'adsense' AND created_at >= DATE_SUB(NOW(), $date_interval_sql)";
            $stats['total_adsense_views'] = (int)$pdo->query($sql_adsense_views)->fetchColumn();
        } else {
            $fallback_needed = true; // Need fallback if impressions missing
        }


        // Overall CTR (Affiliate Only using detailed data if available)
        if (!$fallback_needed && $stats['total_affiliate_views'] > 0) {
            $stats['overall_ctr'] = round(($stats['total_affiliate_clicks'] / $stats['total_affiliate_views']) * 100, 2);
        } elseif ($fallback_needed) {
            // If we need fallback, calculate CTR from summary table later
            $stats['overall_ctr'] = 0; // Initialize
        }

    } catch (PDOException $e) {
         error_log("Error fetching detailed ad stats: " . $e->getMessage());
         $fallback_needed = true; // Attempt fallback on any error
         $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Error fetching detailed stats.";
    }

    // Fallback to summary tables if needed
    if ($fallback_needed) {
        try {
             $stmt_affiliate_summary = $pdo->query("SELECT SUM(views) as total_views, SUM(clicks) as total_clicks FROM affiliate_ads WHERE status = 'active'");
             $affiliate_totals_summary = $stmt_affiliate_summary->fetch(PDO::FETCH_ASSOC);
             // Only overwrite if detailed data wasn't fetched
             if ($stats['total_affiliate_views'] == 0) $stats['total_affiliate_views'] = (int)($affiliate_totals_summary['total_views'] ?? 0);
             if ($stats['total_affiliate_clicks'] == 0) $stats['total_affiliate_clicks'] = (int)($affiliate_totals_summary['total_clicks'] ?? 0);

             // Only overwrite if detailed data wasn't fetched
             if ($stats['total_adsense_views'] == 0) {
                $stmt_adsense_summary = $pdo->query("SELECT SUM(views) as total_views FROM adsense_units WHERE status = 'active'");
                $stats['total_adsense_views'] = (int)$stmt_adsense_summary->fetchColumn();
             }

              // Recalculate CTR using potentially mixed data
              if ($stats['total_affiliate_views'] > 0) {
                 $stats['overall_ctr'] = round(($stats['total_affiliate_clicks'] / $stats['total_affiliate_views']) * 100, 2);
             }
             $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Warning: Displaying summary data as detailed tracking tables are missing or empty.";
        } catch (PDOException $e) {
             error_log("Error fetching summary ad stats: " . $e->getMessage());
             $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Database error fetching summary stats.";
        }
    }

    return $stats;
}

function getTopAffiliateAds(PDO $pdo, string $sortBy = 'clicks', string $date_interval_sql = 'INTERVAL 30 DAY', int $limit = 5): array {
     $orderByClause = match ($sortBy) {
        'views' => 'impression_count DESC',
        'ctr' => '(click_count / NULLIF(impression_count, 0)) DESC',
        default => 'click_count DESC',
    };
    $detailed_clicks_exists = $pdo->query("SHOW TABLES LIKE 'detailed_ad_clicks'")->rowCount() > 0;
    $ad_impressions_exists = $pdo->query("SHOW TABLES LIKE 'ad_impressions'")->rowCount() > 0;

    if ($detailed_clicks_exists && $ad_impressions_exists) {
        // Use detailed tables if they exist
        try {
            $sql = "SELECT
                        a.id,
                        a.title,
                        COUNT(DISTINCT c.id) as click_count,
                        COUNT(DISTINCT i.id) as impression_count
                    FROM affiliate_ads a
                    LEFT JOIN detailed_ad_clicks c ON a.id = c.ad_id AND c.ad_type = 'affiliate' AND c.created_at >= DATE_SUB(NOW(), $date_interval_sql)
                    LEFT JOIN ad_impressions i ON a.id = i.ad_id AND i.ad_type = 'affiliate' AND i.created_at >= DATE_SUB(NOW(), $date_interval_sql)
                    WHERE a.status = 'active'
                    GROUP BY a.id, a.title
                    HAVING click_count > 0 OR impression_count > 0
                    ORDER BY $orderByClause
                    LIMIT :limit";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error fetching top affiliate ads (detailed): " . $e->getMessage());
            $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Error fetching detailed top affiliate ads.";
            // Fall through to fallback if query fails
        }
    }

    // Fallback to summary data if tables missing or query failed
    error_log("Warning: Falling back to summary data for top affiliate ads.");
    $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Warning: Displaying summary data for top affiliate ads.";
    try {
        $orderBySummary = ($sortBy == 'views') ? 'views DESC' : 'clicks DESC';
        // Add CTR calculation for fallback sorting if needed
        if ($sortBy == 'ctr') {
             $orderBySummary = '(clicks / NULLIF(views, 0)) DESC, clicks DESC'; // Sort by CTR then clicks
        }
        $sql_fallback = "SELECT id, title, views as impression_count, clicks as click_count FROM affiliate_ads WHERE status='active' ORDER BY $orderBySummary LIMIT :limit";
        $stmt_fallback = $pdo->prepare($sql_fallback);
        $stmt_fallback->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt_fallback->execute();
        return $stmt_fallback->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
         error_log("Error fetching summary top affiliate ads: " . $e->getMessage());
         $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Database error fetching top affiliate ads.";
         return [];
    }
}

function getAdSensePerformance(PDO $pdo, string $date_interval_sql = 'INTERVAL 30 DAY', int $limit = 10): array {
     $ad_impressions_exists = $pdo->query("SHOW TABLES LIKE 'ad_impressions'")->rowCount() > 0;

     if ($ad_impressions_exists) {
         // Use detailed table if it exists
         try {
            $sql = "SELECT
                        u.id,
                        u.name,
                        u.placement,
                        COUNT(DISTINCT i.id) as impression_count
                    FROM adsense_units u
                    LEFT JOIN ad_impressions i ON u.id = i.ad_id AND i.ad_type = 'adsense' AND i.created_at >= DATE_SUB(NOW(), $date_interval_sql)
                    WHERE u.status = 'active'
                    GROUP BY u.id, u.name, u.placement
                    ORDER BY impression_count DESC
                    LIMIT :limit";
            $stmt = $pdo->prepare($sql);
             $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            $adsense_performance = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($adsense_performance as &$unit) {
                if (isset($unit['placement'])) { $placementsArray = json_decode($unit['placement'], true); $unit['placements_display'] = (json_last_error() === JSON_ERROR_NONE && is_array($placementsArray)) ? implode(', ', array_map('ucfirst', $placementsArray)) : ucfirst($unit['placement']); } else { $unit['placements_display'] = 'N/A'; }
            }
            unset($unit);
            return $adsense_performance;
        } catch (PDOException $e) {
            error_log("Error fetching AdSense performance (detailed): " . $e->getMessage());
            $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Error fetching detailed AdSense performance.";
            // Fall through to fallback
        }
     }

     // Fallback to summary data if table missing or query failed
     error_log("Warning: Falling back to summary data for AdSense performance.");
     $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Warning: Displaying summary data for AdSense performance.";
     try {
         $sql_fallback = "SELECT id, name, placement, views as impression_count FROM adsense_units WHERE status='active' ORDER BY views DESC LIMIT :limit";
         $stmt_fallback = $pdo->prepare($sql_fallback);
         $stmt_fallback->bindParam(':limit', $limit, PDO::PARAM_INT);
         $stmt_fallback->execute();
         $adsense_performance = $stmt_fallback->fetchAll(PDO::FETCH_ASSOC);
         foreach ($adsense_performance as &$unit) {
             if (isset($unit['placement'])) { $placementsArray = json_decode($unit['placement'], true); $unit['placements_display'] = (json_last_error() === JSON_ERROR_NONE && is_array($placementsArray)) ? implode(', ', array_map('ucfirst', $placementsArray)) : ucfirst($unit['placement']); } else { $unit['placements_display'] = 'N/A'; }
         } unset($unit); return $adsense_performance;
     } catch (PDOException $e) {
         error_log("Error fetching summary AdSense performance: " . $e->getMessage());
         $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Database error fetching AdSense performance.";
     }
    return [];
}

function getAdPerformanceByPlacement(PDO $pdo, string $date_interval_sql = 'INTERVAL 30 DAY', int $limit = 10): array {
    // Uses detailed tables
    $sql = "SELECT
                COALESCE(c.placement, i.placement, 'unknown') as placement, -- Handle cases where placement might be null in one table
                COUNT(DISTINCT i.id) as total_impressions,
                COUNT(DISTINCT c.id) as total_clicks
            FROM ad_impressions i
            LEFT JOIN detailed_ad_clicks c ON i.ad_id = c.ad_id AND i.ad_type = c.ad_type AND i.placement = c.placement AND DATE(i.created_at) = DATE(c.created_at) -- Approximate join for CTR
            WHERE i.created_at >= DATE_SUB(NOW(), $date_interval_sql)
            GROUP BY COALESCE(c.placement, i.placement)
            HAVING total_impressions > 0 OR total_clicks > 0
            ORDER BY total_clicks DESC, total_impressions DESC
            LIMIT :limit";
     try {
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
     } catch (PDOException $e) {
         error_log("Error fetching performance by placement: " . $e->getMessage());
         $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Error fetching placement performance. Ensure detailed tracking tables exist.";
         return []; // Return empty on error
     }
}


function getTopUtmSources(PDO $pdo, string $date_interval_sql = 'INTERVAL 30 DAY', int $limit = 5): array {
    // Querying the new detailed table
    $sql = "SELECT utm_source, COUNT(*) as click_count
            FROM detailed_ad_clicks
            WHERE created_at >= DATE_SUB(NOW(), $date_interval_sql)
              AND utm_source IS NOT NULL AND utm_source != ''
            GROUP BY utm_source
            ORDER BY click_count DESC
            LIMIT :limit";
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
         error_log("Error fetching UTM sources: " . $e->getMessage());
         $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Error fetching UTM sources. Ensure 'detailed_ad_clicks' table exists.";
         return [];
    }
}

function getTopUtmCampaigns(PDO $pdo, string $date_interval_sql = 'INTERVAL 30 DAY', int $limit = 5): array {
    // Querying the new detailed table
     $sql = "SELECT utm_campaign, COUNT(*) as click_count
            FROM detailed_ad_clicks
            WHERE created_at >= DATE_SUB(NOW(), $date_interval_sql)
              AND utm_campaign IS NOT NULL AND utm_campaign != ''
            GROUP BY utm_campaign
            ORDER BY click_count DESC
            LIMIT :limit";
     try {
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
         error_log("Error fetching UTM campaigns: " . $e->getMessage());
         $GLOBALS['error_message'] = ($GLOBALS['error_message'] ?? '') . " Error fetching UTM campaigns. Ensure 'detailed_ad_clicks' table exists.";
         return [];
    }
}

// --- Fetch Data ---
$stats = getAdPerformanceStats($pdo, $date_interval_sql);
$top_affiliate_ads = getTopAffiliateAds($pdo, 'clicks', $date_interval_sql, 5);
$adsense_performance = getAdSensePerformance($pdo, $date_interval_sql, 10);
$placement_performance = getAdPerformanceByPlacement($pdo, $date_interval_sql, 10);
$topSources = getTopUtmSources($pdo, $date_interval_sql, 5);
$topCampaigns = getTopUtmCampaigns($pdo, $date_interval_sql, 5);


// Include the admin header
include 'includes/header.php';
?>

<script defer src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.13.5/cdn.min.js"></script>

<header class="h-16 bg-white border-b border-border flex items-center justify-between px-6 flex-shrink-0 sticky top-0 z-30">
    <div class="flex items-center">
        <h1 class="text-xl font-montserrat font-bold text-dark"><?php echo escape($admin_page_title); ?></h1>
    </div>
    <div class="flex items-center space-x-4">
        <form method="GET" action="ad_analytics.php" class="relative">
            <select class="form-select h-10 pr-10 pl-3 py-2 text-sm appearance-none" name="period" onchange="this.form.submit()">
                <option value="7d" <?php selected($selected_period, '7d'); ?>>Last 7 days</option>
                <option value="30d" <?php selected($selected_period, '30d'); ?>>Last 30 days</option>
                <option value="90d" <?php selected($selected_period, '90d'); ?>>Last 90 days</option>
                <option value="year" <?php selected($selected_period, 'year'); ?>>This year</option>
                <option value="all" <?php selected($selected_period, 'all'); ?>>All time</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>
            </div>
        </form>
        <button class="btn-secondary text-sm" disabled title="Export Not Implemented">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" /></svg>
            Export Data
        </button>
    </div>
</header>

<div class="flex-1 overflow-y-auto bg-background">
    <div class="p-6 space-y-6">

        <?php if (!empty($error_message)): ?>
        <div class="mb-4 bg-yellow-100 border border-yellow-300 text-yellow-800 px-4 py-3 rounded-lg text-sm shadow-sm">
            <?php echo escape($error_message); ?>
        </div>
        <?php endif; ?>

        <div class="mb-6">
            <h2 class="text-lg font-montserrat font-bold mb-4 text-gray-700">Ads Performance Overview (<?php echo escape(ucfirst($selected_period)); ?>)</h2>
             <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="metric-card"> <div class="flex items-center justify-between mb-2"> <h3 class="text-sm font-semibold text-gray-500">AFFILIATE VIEWS</h3> <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Detailed</span> </div> <span class="metric-value"><?php echo number_format($stats['total_affiliate_views']); ?></span> <span class="metric-label">total impressions</span> </div>
                <div class="metric-card"> <div class="flex items-center justify-between mb-2"> <h3 class="text-sm font-semibold text-gray-500">AFFILIATE CLICKS</h3> <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Detailed</span> </div> <span class="metric-value"><?php echo number_format($stats['total_affiliate_clicks']); ?></span> <span class="metric-label">total clicks</span> </div>
                <div class="metric-card"> <div class="flex items-center justify-between mb-2"> <h3 class="text-sm font-semibold text-gray-500">ADSENSE VIEWS</h3> <span class="bg-indigo-100 text-indigo-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Detailed</span> </div> <span class="metric-value"><?php echo number_format($stats['total_adsense_views']); ?></span> <span class="metric-label">total impressions</span> </div>
                <div class="metric-card"> <div class="flex items-center justify-between mb-2"> <h3 class="text-sm font-semibold text-gray-500">AFFILIATE CTR</h3> <span class="bg-pink-100 text-pink-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Calculated</span> </div> <span class="metric-value"><?php echo $stats['overall_ctr']; ?>%</span> <span class="metric-label">clicks / views</span> </div>
            </div>
        </div>

        <div class="mb-6">
            <h2 class="text-lg font-montserrat font-bold mb-4 text-gray-700">UTM Performance (Ad Clicks)</h2>
             <p class="text-xs text-gray-500 mb-4 italic">Based on data from `detailed_ad_clicks` table for the selected period.</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="card"> <h3 class="text-md font-semibold text-gray-800 mb-3">Top UTM Sources</h3> <?php if (empty($topSources)): ?> <p class="text-sm text-gray-500">No UTM source data found.</p> <?php else: ?> <ul class="space-y-2"> <?php foreach ($topSources as $source): ?> <li class="flex justify-between items-center text-sm"> <span class="text-gray-700 truncate" title="<?php echo escape($source['utm_source']); ?>"><?php echo escape($source['utm_source']); ?></span> <span class="font-medium text-gray-900"><?php echo number_format($source['click_count']); ?> clicks</span> </li> <?php endforeach; ?> </ul> <?php endif; ?> </div>
                <div class="card"> <h3 class="text-md font-semibold text-gray-800 mb-3">Top UTM Campaigns</h3> <?php if (empty($topCampaigns)): ?> <p class="text-sm text-gray-500">No UTM campaign data found.</p> <?php else: ?> <ul class="space-y-2"> <?php foreach ($topCampaigns as $campaign): ?> <li class="flex justify-between items-center text-sm"> <span class="text-gray-700 truncate" title="<?php echo escape($campaign['utm_campaign']); ?>"><?php echo escape($campaign['utm_campaign']); ?></span> <span class="font-medium text-gray-900"><?php echo number_format($campaign['click_count']); ?> clicks</span> </li> <?php endforeach; ?> </ul> <?php endif; ?> </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-border overflow-hidden mb-6">
             <div class="px-6 py-4 border-b border-border flex justify-between items-center"> <h2 class="text-lg font-montserrat font-bold text-gray-800">Top Performing Ads & Units</h2> </div>
            <div class="p-4"> <div class="overflow-x-auto">
                    <h3 class="text-md font-semibold text-gray-700 mb-3 pl-2">Affiliate Ads (by Clicks)</h3>
                    <table class="min-w-full table"> <thead> <tr><th>Ad Title</th><th>Impressions</th><th>Clicks</th><th>CTR</th><th>Actions</th></tr> </thead> <tbody class="divide-y divide-border"> <?php if (empty($top_affiliate_ads)): ?> <tr><td colspan="5" class="text-center text-gray-500 py-4">No affiliate ad data found.</td></tr> <?php else: ?> <?php foreach ($top_affiliate_ads as $ad): $ctr = (($ad['impression_count'] ?? 0) > 0) ? round(($ad['click_count'] / $ad['impression_count']) * 100, 2) : 0; ?> <tr> <td class="font-medium text-gray-900 max-w-xs truncate"><a href="ad_form.php?id=<?php echo $ad['id']; ?>" title="<?php echo escape($ad['title']); ?>" class="hover:text-primary"><?php echo escape($ad['title']); ?></a></td> <td><?php echo number_format($ad['impression_count'] ?? 0); ?></td> <td><?php echo number_format($ad['click_count'] ?? 0); ?></td> <td><?php echo $ctr; ?>%</td> <td><a href="ad_form.php?id=<?php echo $ad['id']; ?>" class="text-primary hover:text-primary/80 text-sm font-medium">Edit</a></td> </tr> <?php endforeach; ?> <?php endif; ?> </tbody> </table>
                    <h3 class="text-md font-semibold text-gray-700 mb-3 mt-6 pl-2">AdSense Units (by Impressions)</h3>
                     <table class="min-w-full table"> <thead> <tr><th>Unit Name</th><th>Placements</th><th>Impressions</th><th>Actions</th></tr> </thead> <tbody class="divide-y divide-border"> <?php if (empty($adsense_performance)): ?> <tr><td colspan="4" class="text-center text-gray-500 py-4">No AdSense unit data found.</td></tr> <?php else: ?> <?php foreach ($adsense_performance as $unit): ?> <tr> <td class="font-medium text-gray-900 max-w-xs truncate"><a href="adsense_form.php?id=<?php echo $unit['id']; ?>" title="<?php echo escape($unit['name']); ?>" class="hover:text-primary"><?php echo escape($unit['name']); ?></a></td> <td class="text-xs"><?php echo escape($unit['placements_display']); ?></td> <td><?php echo number_format($unit['impression_count'] ?? 0); ?></td> <td> <a href="adsense_form.php?id=<?php echo $unit['id']; ?>" class="text-primary hover:text-primary/80 text-sm font-medium">Edit</a> <a href="https://www.google.com/adsense/" target="_blank" class="ml-2 text-blue-600 hover:text-blue-800 text-sm font-medium">View in AdSense</a> </td> </tr> <?php endforeach; ?> <?php endif; ?> </tbody> </table>
                </div> </div>
        </div>

        <div class="card">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-100 pb-2">Performance by Placement</h3>
            <p class="text-xs text-gray-500 mb-4 italic">Based on data from `detailed_ad_clicks` and `ad_impressions` tables.</p>
            <?php if (empty($placement_performance)): ?>
                <p class="text-gray-600">No placement data available for the selected period.</p>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full table"> <thead> <tr><th>Placement</th><th>Impressions</th><th>Clicks</th><th>CTR</th></tr> </thead> <tbody class="divide-y divide-border"> <?php foreach ($placement_performance as $placement): ?> <?php $impressions = (int)($placement['total_impressions'] ?? 0); $clicks = (int)($placement['total_clicks'] ?? 0); $ctr = ($impressions > 0) ? round(($clicks / $impressions) * 100, 2) : 0; ?> <tr> <td class="font-medium text-gray-900"><?php echo escape($placement['placement'] ?? 'Unknown'); ?></td> <td><?php echo number_format($impressions); ?></td> <td><?php echo number_format($clicks); ?></td> <td><?php echo $ctr; ?>%</td> </tr> <?php endforeach; ?> </tbody> </table>
                </div>
            <?php endif; ?>
        </div>

         <div class="card">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-100 pb-2">Performance Over Time (<?php echo escape(ucfirst($selected_period)); ?>)</h3>
            <p class="text-sm text-gray-500 mb-4 italic">Note: Time-based analytics require querying the `detailed_ad_clicks` and `ad_impressions` tables.</p>
            <div class="bg-gray-100 p-6 rounded-lg text-center text-gray-500"> Chart placeholder - Implement using a library like Chart.js or ApexCharts. Query `detailed_ad_clicks` and `ad_impressions` grouping by DATE(created_at). </div>
        </div>

    </div> </div> <?php
// Include the admin footer
include 'includes/footer.php';
?>

