<?php

namespace AuditSystem\Analyzers;

use AuditSystem\Interfaces\AnalyzerInterface;
use AuditSystem\Models\Finding;

/**
 * Configuration file analyzer for security and performance settings
 */
class ConfigurationAnalyzer implements AnalyzerInterface
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'check_security_config' => true,
            'check_performance_settings' => true,
            'check_environment_config' => true,
            'check_dependency_management' => true,
            'check_sensitive_data' => true
        ], $config);
    }

    /**
     * Analyze a configuration file and return findings
     *
     * @param string $filePath Path to the file to analyze
     * @param string $content File content to analyze
     * @return Finding[] Array of findings discovered in the file
     */
    public function analyze(string $filePath, string $content): array
    {
        $findings = [];

        // Determine file type and apply appropriate analysis
        if (str_ends_with($filePath, 'config.php')) {
            $findings = array_merge($findings, $this->analyzePhpConfig($filePath, $content));
        } elseif (str_ends_with($filePath, '.htaccess')) {
            $findings = array_merge($findings, $this->analyzeHtaccess($filePath, $content));
        } elseif (str_ends_with($filePath, 'composer.json')) {
            $findings = array_merge($findings, $this->analyzeComposerConfig($filePath, $content));
        }

        return $findings;
    }

    /**
     * Analyze PHP configuration file (config.php)
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function analyzePhpConfig(string $filePath, string $content): array
    {
        $findings = [];

        if ($this->config['check_security_config']) {
            $findings = array_merge($findings, $this->checkPhpSecurityConfig($filePath, $content));
        }

        if ($this->config['check_performance_settings']) {
            $findings = array_merge($findings, $this->checkPhpPerformanceSettings($filePath, $content));
        }

        if ($this->config['check_environment_config']) {
            $findings = array_merge($findings, $this->checkEnvironmentConfig($filePath, $content));
        }

        if ($this->config['check_sensitive_data']) {
            $findings = array_merge($findings, $this->checkSensitiveData($filePath, $content));
        }

        return $findings;
    }

    /**
     * Check PHP configuration for security issues
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkPhpSecurityConfig(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++; // 1-based line numbers

            // Check for hardcoded credentials
            if (preg_match('/define\s*\(\s*[\'"]DB_PASS[\'"],\s*[\'"]([^\'"]+)[\'"]\s*\)/', $line, $matches)) {
                if (strlen($matches[1]) < 12) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_SECURITY,
                        Finding::SEVERITY_HIGH,
                        Finding::PRIORITY_AREA,
                        'Weak database password detected',
                        'Use a strong password with at least 12 characters, including uppercase, lowercase, numbers, and special characters',
                        '[REDACTED - contains password]',
                        ['https://owasp.org/www-community/controls/Password_Management_Cheat_Sheet']
                    );
                }
            }

            // Check for API keys in plain text
            if (preg_match('/define\s*\(\s*[\'"][^\'"]*(API_KEY|SECRET|TOKEN)[\'"],\s*[\'"]([^\'"]+)[\'"]\s*\)/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_HIGH,
                    Finding::PRIORITY_AREA,
                    'API key or secret stored in plain text',
                    'Move sensitive credentials to environment variables or encrypted configuration files',
                    '[REDACTED - contains API key]',
                    ['https://owasp.org/www-community/vulnerabilities/Use_of_hard-coded_password']
                );
            }

            // Check for debug mode enabled
            if (preg_match('/ini_set\s*\(\s*[\'"]display_errors[\'"],\s*1\s*\)/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_MEDIUM,
                    Finding::PRIORITY_AREA,
                    'Debug mode enabled in production configuration',
                    'Set display_errors to 0 in production and use proper logging instead',
                    trim($line),
                    ['https://owasp.org/www-community/vulnerabilities/Information_Exposure_Through_Debug_Information']
                );
            }

            // Check for error reporting set to show all errors
            if (preg_match('/error_reporting\s*\(\s*E_ALL\s*\)/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_MEDIUM,
                    Finding::PRIORITY_AREA,
                    'Full error reporting enabled',
                    'Disable detailed error reporting in production to prevent information disclosure',
                    trim($line),
                    ['https://owasp.org/www-community/vulnerabilities/Information_Exposure_Through_Debug_Information']
                );
            }

            // Check for insecure session configuration
            if (preg_match('/session_start\s*\(\s*\)/', $line)) {
                // Look for security settings in surrounding lines
                $hasHttpOnly = false;
                $hasSecure = false;
                $hasSameSite = false;
                
                $startLine = max(0, $lineNumber - 10);
                $endLine = min(count($lines), $lineNumber + 10);
                
                for ($i = $startLine; $i < $endLine; $i++) {
                    if (preg_match('/session\.cookie_httponly.*1/', $lines[$i])) {
                        $hasHttpOnly = true;
                    }
                    if (preg_match('/session\.cookie_secure.*1/', $lines[$i])) {
                        $hasSecure = true;
                    }
                    if (preg_match('/session\.cookie_samesite/', $lines[$i])) {
                        $hasSameSite = true;
                    }
                }

                if (!$hasHttpOnly) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_SECURITY,
                        Finding::SEVERITY_MEDIUM,
                        Finding::PRIORITY_AREA,
                        'Session cookies not configured as HttpOnly',
                        'Set session.cookie_httponly = 1 to prevent XSS attacks on session cookies',
                        trim($line),
                        ['https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet']
                    );
                }

                if (!$hasSameSite) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_SECURITY,
                        Finding::SEVERITY_LOW,
                        Finding::PRIORITY_AREA,
                        'Session SameSite attribute not configured',
                        'Set session.cookie_samesite to "Strict" or "Lax" to prevent CSRF attacks',
                        trim($line),
                        ['https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet']
                    );
                }
            }
        }

        return $findings;
    }

    /**
     * Check PHP configuration for performance issues
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkPhpPerformanceSettings(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Check for missing CDN configuration
            if (preg_match('/define\s*\(\s*[\'"]CDN_ENABLED[\'"],\s*false\s*\)/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_MEDIUM,
                    Finding::NON_PRIORITY,
                    'CDN is disabled',
                    'Enable CDN for better performance and reduced server load',
                    trim($line),
                    ['https://developer.mozilla.org/en-US/docs/Glossary/CDN']
                );
            }

            // Check for image quality settings
            if (preg_match('/define\s*\(\s*[\'"]IMAGE_QUALITY[\'"],\s*(\d+)\s*\)/', $line, $matches)) {
                $quality = (int)$matches[1];
                if ($quality > 85) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_LOW,
                        Finding::NON_PRIORITY,
                        'Image quality setting too high',
                        'Consider reducing image quality to 75-85 for better performance without significant quality loss',
                        trim($line),
                        ['https://web.dev/optimize-images/']
                    );
                }
            }

            // Check for large file upload limits
            if (preg_match('/define\s*\(\s*[\'"]MAX_FILE_SIZE[\'"],\s*(\d+(?:\s*\*\s*\d+\s*\*\s*\d+)?)/', $line, $matches)) {
                // Handle expressions like "20 * 1024 * 1024"
                $expression = $matches[1];
                $expression = preg_replace('/\s+/', '', $expression); // Remove spaces
                eval('$maxSize = ' . $expression . ';'); // Safe since we control the input pattern
                if ($maxSize > 10 * 1024 * 1024) { // 10MB
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_LOW,
                        Finding::NON_PRIORITY,
                        'Large file upload limit may impact performance',
                        'Consider implementing chunked uploads or reducing max file size for better server performance',
                        trim($line),
                        ['https://web.dev/optimize-images/']
                    );
                }
            }

            // Check for missing caching configuration
            if (preg_match('/ARTICLES_PER_PAGE/', $line) && !preg_match('/CACHE/', $content)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_MEDIUM,
                    Finding::NON_PRIORITY,
                    'No caching configuration detected',
                    'Implement caching mechanisms (Redis, Memcached, or file-based) to improve performance',
                    trim($line),
                    ['https://web.dev/http-cache/']
                );
            }
        }

        return $findings;
    }

    /**
     * Check environment-specific configuration
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkEnvironmentConfig(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        // Check for environment detection
        $hasEnvironmentDetection = preg_match('/(getenv|_ENV|\$_SERVER\[.*ENVIRONMENT.*\])/', $content);
        
        if (!$hasEnvironmentDetection) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_ARCHITECTURE,
                Finding::SEVERITY_MEDIUM,
                Finding::NON_PRIORITY,
                'No environment detection mechanism found',
                'Implement environment detection to use different configurations for development, staging, and production',
                'Configuration file lacks environment detection',
                ['https://12factor.net/config']
            );
        }

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Check for localhost hardcoded in production
            if (preg_match('/define\s*\(\s*[\'"]DB_HOST[\'"],\s*[\'"]localhost[\'"]\s*\)/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_ARCHITECTURE,
                    Finding::SEVERITY_LOW,
                    Finding::NON_PRIORITY,
                    'Database host hardcoded to localhost',
                    'Use environment variables for database host to support different environments',
                    trim($line),
                    ['https://12factor.net/config']
                );
            }

            // Check for development database names in production
            if (preg_match('/define\s*\(\s*[\'"]DB_NAME[\'"],\s*[\'"].*(_dev|_test|_local).*[\'"]\s*\)/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_ARCHITECTURE,
                    Finding::SEVERITY_HIGH,
                    Finding::PRIORITY_AREA,
                    'Development database name detected',
                    'Ensure production configuration uses production database names',
                    trim($line),
                    ['https://12factor.net/config']
                );
            }
        }

        return $findings;
    }

    /**
     * Check for sensitive data exposure
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkSensitiveData(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Check for commented out credentials
            if (preg_match('/\/\/.*(?:password|pass|pwd|secret|key|token).*[:=].*\w+/i', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_MEDIUM,
                    Finding::PRIORITY_AREA,
                    'Sensitive information in comments',
                    'Remove commented credentials and sensitive information from configuration files',
                    '[REDACTED - contains sensitive data]',
                    ['https://owasp.org/www-community/vulnerabilities/Information_Exposure_Through_Debug_Information']
                );
            }

            // Check for TODO/FIXME with sensitive info
            if (preg_match('/(TODO|FIXME|HACK).*(?:password|secret|key|token)/i', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_LOW,
                    Finding::PRIORITY_AREA,
                    'TODO/FIXME comment contains sensitive information references',
                    'Remove or secure TODO comments that reference sensitive information',
                    trim($line),
                    ['https://owasp.org/www-community/vulnerabilities/Information_Exposure_Through_Debug_Information']
                );
            }
        }

        return $findings;
    }

    /**
     * Analyze .htaccess configuration file
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function analyzeHtaccess(string $filePath, string $content): array
    {
        $findings = [];

        if ($this->config['check_security_config']) {
            $findings = array_merge($findings, $this->checkHtaccessSecurity($filePath, $content));
        }

        if ($this->config['check_performance_settings']) {
            $findings = array_merge($findings, $this->checkHtaccessPerformance($filePath, $content));
        }

        return $findings;
    }

    /**
     * Check .htaccess for security configurations
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkHtaccessSecurity(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        // Check for HTTPS enforcement
        $hasHttpsRedirect = preg_match('/RewriteRule.*https.*\[.*R=301.*\]/', $content);
        if (!$hasHttpsRedirect) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_SECURITY,
                Finding::SEVERITY_HIGH,
                Finding::PRIORITY_AREA,
                'HTTPS redirect not configured',
                'Add HTTPS redirect rules to enforce secure connections',
                'Missing HTTPS enforcement',
                ['https://owasp.org/www-community/controls/Transport_Layer_Security_Cheat_Sheet']
            );
        }

        // Check for directory listing protection
        $hasDirectoryProtection = preg_match('/Options\s+-Indexes/', $content);
        if (!$hasDirectoryProtection) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_SECURITY,
                Finding::SEVERITY_MEDIUM,
                Finding::PRIORITY_AREA,
                'Directory listing not disabled',
                'Add "Options -Indexes" to prevent directory listing',
                'Missing directory listing protection',
                ['https://owasp.org/www-community/attacks/Forced_browsing']
            );
        }

        // Check for sensitive file protection
        $protectedFiles = ['config.php', '.htaccess'];
        foreach ($protectedFiles as $file) {
            if (!preg_match('/<Files\s+' . preg_quote($file, '/') . '>.*Require all denied/s', $content)) {
                $findings[] = new Finding(
                    $filePath,
                    1,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_HIGH,
                    Finding::PRIORITY_AREA,
                    "Sensitive file $file not protected",
                    "Add access restriction for $file to prevent direct access",
                    "Missing protection for $file",
                    ['https://owasp.org/www-community/attacks/Forced_browsing']
                );
            }
        }

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++;

            // Check for weak file permissions
            if (preg_match('/Require\s+all\s+granted/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_MEDIUM,
                    Finding::PRIORITY_AREA,
                    'Overly permissive file access granted',
                    'Review and restrict file access permissions to only necessary files',
                    trim($line),
                    ['https://httpd.apache.org/docs/2.4/howto/access.html']
                );
            }

            // Check for server signature exposure
            if (preg_match('/ServerSignature\s+On/i', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_LOW,
                    Finding::NON_PRIORITY,
                    'Server signature enabled',
                    'Disable server signature to reduce information disclosure',
                    trim($line),
                    ['https://owasp.org/www-community/vulnerabilities/Information_Exposure_Through_Debug_Information']
                );
            }
        }

        return $findings;
    }

    /**
     * Check .htaccess for performance configurations
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkHtaccessPerformance(string $filePath, string $content): array
    {
        $findings = [];

        // Check for compression
        $hasCompression = preg_match('/mod_deflate|mod_gzip/', $content);
        if (!$hasCompression) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_PERFORMANCE,
                Finding::SEVERITY_MEDIUM,
                Finding::NON_PRIORITY,
                'GZIP compression not configured',
                'Enable GZIP compression to reduce bandwidth usage and improve load times',
                'Missing compression configuration',
                ['https://web.dev/reduce-network-payloads-using-text-compression/']
            );
        }

        // Check for browser caching
        $hasCaching = preg_match('/mod_expires|ExpiresActive|Cache-Control/', $content);
        if (!$hasCaching) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_PERFORMANCE,
                Finding::SEVERITY_MEDIUM,
                Finding::NON_PRIORITY,
                'Browser caching not configured',
                'Configure browser caching headers to improve repeat visitor performance',
                'Missing caching configuration',
                ['https://web.dev/http-cache/']
            );
        }

        // Check for excessive redirects
        $redirectCount = preg_match_all('/RewriteRule.*\[.*R=/', $content);
        if ($redirectCount > 10) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_PERFORMANCE,
                Finding::SEVERITY_LOW,
                Finding::NON_PRIORITY,
                'High number of redirect rules detected',
                'Review and optimize redirect rules to minimize redirect chains',
                "Found $redirectCount redirect rules",
                ['https://web.dev/redirects/']
            );
        }

        return $findings;
    }

    /**
     * Analyze composer.json for dependency management
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function analyzeComposerConfig(string $filePath, string $content): array
    {
        $findings = [];

        if (!$this->config['check_dependency_management']) {
            return $findings;
        }

        $composerData = json_decode($content, true);
        if (!$composerData) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_ARCHITECTURE,
                Finding::SEVERITY_HIGH,
                Finding::NON_PRIORITY,
                'Invalid JSON in composer.json',
                'Fix JSON syntax errors in composer.json',
                'JSON parsing failed',
                ['https://getcomposer.org/doc/04-schema.md']
            );
            return $findings;
        }

        // Check for outdated PHP version requirement
        if (isset($composerData['require']['php'])) {
            $phpVersion = $composerData['require']['php'];
            if (preg_match('/\^?(\d+\.\d+)/', $phpVersion, $matches)) {
                $version = (float)$matches[1];
                if ($version < 8.0) {
                    $findings[] = new Finding(
                        $filePath,
                        1,
                        Finding::TYPE_SECURITY,
                        Finding::SEVERITY_MEDIUM,
                        Finding::NON_PRIORITY,
                        'Outdated PHP version requirement',
                        'Update PHP version requirement to 8.0+ for better security and performance',
                        "PHP version: $phpVersion",
                        ['https://www.php.net/supported-versions.php']
                    );
                }
            }
        }

        // Check for missing security-related packages
        $securityPackages = ['roave/security-advisories'];
        foreach ($securityPackages as $package) {
            if (!isset($composerData['require-dev'][$package]) && !isset($composerData['require'][$package])) {
                $findings[] = new Finding(
                    $filePath,
                    1,
                    Finding::TYPE_SECURITY,
                    Finding::SEVERITY_LOW,
                    Finding::NON_PRIORITY,
                    "Missing security package: $package",
                    "Add $package to prevent installation of packages with known vulnerabilities",
                    "Missing $package",
                    ['https://github.com/Roave/SecurityAdvisories']
                );
            }
        }

        return $findings;
    }

    /**
     * Get the types of files this analyzer can handle
     *
     * @return string[] Array of file extensions or patterns this analyzer supports
     */
    public function getSupportedFileTypes(): array
    {
        return ['php', 'htaccess', 'json'];
    }

    /**
     * Get the analyzer name for identification
     *
     * @return string Name of the analyzer
     */
    public function getName(): string
    {
        return 'Configuration Analyzer';
    }

    /**
     * Check if file is in priority area
     *
     * @param string $filePath
     * @return bool
     */
    private function isPriorityArea(string $filePath): bool
    {
        $priorityPatterns = [
            'config.php',
            '.htaccess',
            'composer.json',
            'admin/',
            'includes/'
        ];

        foreach ($priorityPatterns as $pattern) {
            if (strpos($filePath, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }
}