-- Add comment_count field to articles table
ALTER TABLE articles ADD COLUMN comment_count INT DEFAULT 5 AFTER auto_link_keywords;

-- Add generate_comments field to articles table if it doesn't exist
ALTER TABLE articles ADD COLUMN generate_comments TINYINT(1) DEFAULT 1 AFTER auto_link_keywords;

-- Update existing articles to have default values
UPDATE articles SET comment_count = 5, generate_comments = 1 WHERE comment_count IS NULL OR generate_comments IS NULL;
