/**
 * Engagement Tricks - Keep users engaged with articles
 *
 * This script implements various engagement tricks to keep users on articles longer
 * without violating AdSense and Facebook rules.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize engagement tricks if they're enabled for this article
    initEngagementTricks();
});

/**
 * Initialize engagement tricks based on article settings
 */
function initEngagementTricks() {
    // Get article data from the body data attributes
    const articleElement = document.querySelector('.article-content');
    if (!articleElement) return;

    const body = document.body;
    const trickType = body.dataset.engagementTrick;

    // If no trick is set for this article, exit
    if (!trickType) return;

    // Initialize the appropriate trick based on the type
    switch(trickType) {
        case 'reward_popup':
            initRewardPopup();
            break;
        // Add more trick types here in the future
        default:
            console.log('Unknown engagement trick type:', trickType);
    }
}

/**
 * Initialize the reward popup engagement trick
 */
function initRewardPopup() {
    const articleContent = document.querySelector('.article-content');
    if (!articleContent) return;

    // Get settings from data attribute (JSON string)
    const settingsJson = document.body.dataset.engagementTrickSettings || '{}';
    let settings;

    try {
        // First try to parse as is
        settings = JSON.parse(settingsJson);
    } catch (e) {
        // If that fails, try to clean the string (remove any potential HTML entities)
        try {
            // Create a temporary div to decode HTML entities
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = settingsJson;
            const decodedJson = tempDiv.textContent || tempDiv.innerText;
            settings = JSON.parse(decodedJson);
        } catch (e2) {
            console.error('Error parsing engagement trick settings:', e2);
            // Use default settings
            settings = {
                scrollThreshold: 70,
                countdownSeconds: 60,
                popupTitle: 'Kvalifikovali ste se za nagradnu igru',
                popupMessage: 'Učitavanje uputa za nagradnu igru za',
                confirmMessage: 'Čestitamo! Za potvrdu učestvovanja u nagradnoj igri, pošaljite poruku na našu Facebook stranicu sa tekstom "NAGRADNA IGRA".'
            };
        }
    }

    // Default settings
    const config = {
        scrollThreshold: settings.scrollThreshold || 70, // Default 70% of article
        countdownSeconds: settings.countdownSeconds || 60, // Default 60 seconds
        popupTitle: settings.popupTitle || 'Kvalifikovali ste se za nagradnu igru',
        popupMessage: settings.popupMessage || 'Učitavanje uputa za nagradnu igru za',
        confirmMessage: settings.confirmMessage || 'Čestitamo! Za potvrdu učestvovanja u nagradnoj igri, pošaljite poruku na našu Facebook stranicu sa tekstom "NAGRADNA IGRA".'
    };

    // Track scroll position
    let popupShown = false;
    let countdownStarted = false;

    // Create the popup elements but keep them hidden
    createRewardPopup(config);

    // Add scroll event listener
    window.addEventListener('scroll', function() {
        if (popupShown) return; // Only show popup once

        const scrollPosition = window.scrollY || document.documentElement.scrollTop;
        const articleHeight = articleContent.offsetHeight;
        const windowHeight = window.innerHeight;
        const scrollableDistance = Math.max(0, articleHeight - windowHeight);

        // Calculate how far through the article the user has scrolled (as a percentage)
        let scrollPercentage = 0;
        if (scrollableDistance > 0) {
            scrollPercentage = (scrollPosition / scrollableDistance) * 100;
        }

        // If user has scrolled past the threshold, show the popup
        if (scrollPercentage >= config.scrollThreshold) {
            showRewardPopup();
            popupShown = true;
        }
    }, { passive: true });
}

/**
 * Create the reward popup elements
 * @param {Object} config Configuration options
 */
function createRewardPopup(config) {
    // Create the banner element
    const banner = document.createElement('div');
    banner.id = 'reward-popup-banner';

    // Create the gift icon
    const giftIcon = `
        <div class="reward-gift-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#ff6481">
                <path d="M20 6h-2.18c.11-.31.18-.65.18-1a2.996 2.996 0 0 0-5.5-1.65l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .35.07.69.18 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm11 15H4v-2h16v2zm0-5H4V8h5.08L7 10.83 8.62 12 12 7.4l3.38 4.6L17 10.83 14.92 8H20v6z"/>
            </svg>
        </div>
    `;

    // Calculate the circumference for the SVG circle
    const radius = 25;
    const circumference = 2 * Math.PI * radius;

    // Create the banner content with circular countdown
    banner.innerHTML = `
        ${giftIcon}
        <div class="reward-banner-content">
            <h4>${config.popupTitle}</h4>
            <p>${config.popupMessage}</p>

            <div class="reward-countdown-container">
                <div class="reward-countdown-circle">
                    <svg width="60" height="60" viewBox="0 0 60 60">
                        <circle class="reward-countdown-bg" cx="30" cy="30" r="${radius}" />
                        <circle class="reward-countdown-progress" cx="30" cy="30" r="${radius}"
                                stroke-dasharray="${circumference}"
                                stroke-dashoffset="0" />
                    </svg>
                    <div class="reward-countdown-number" id="reward-countdown">${config.countdownSeconds}</div>
                </div>
                <div class="reward-countdown-text">
                    <p class="countdown-title">Učitavanje uputa za nagradnu igru</p>
                    <p>Molimo sačekajte da se upute učitaju...</p>
                </div>
            </div>

            <div id="reward-button-container" class="hidden mt-3">
                <button id="reward-popup-btn">
                    Saznaj više o nagradnoj igri
                </button>
            </div>
        </div>
    `;

    // Create the modal element with improved design
    const modal = document.createElement('div');
    modal.id = 'reward-popup-modal';
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center hidden';
    modal.innerHTML = `
        <div class="modal-overlay absolute inset-0" id="reward-modal-overlay"></div>
        <div class="modal-container relative z-10">
            <div class="modal-gift-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="white">
                    <path d="M20 6h-2.18c.11-.31.18-.65.18-1a2.996 2.996 0 0 0-5.5-1.65l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .35.07.69.18 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm11 15H4v-2h16v2zm0-5H4V8h5.08L7 10.83 8.62 12 12 7.4l3.38 4.6L17 10.83 14.92 8H20v6z"/>
                </svg>
            </div>

            <button id="reward-modal-close" aria-label="Close modal">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>

            <div class="modal-header">
                <h3>Nagradna igra</h3>
            </div>

            <div class="modal-body">
                <div class="reward-message">
                    <p>${config.confirmMessage.replace('NAGRADNA IGRA', '<span class="reward-highlight">NAGRADNA IGRA</span>')}</p>
                </div>
            </div>

            <div class="modal-footer">
                <a href="https://www.facebook.com/messages/t/mercislike.art" target="_blank" class="reward-action-button">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2.04C6.5 2.04 2 6.13 2 11.22c0 2.61 1.08 4.94 2.8 6.58v3.16l2.57-1.29c1.4.39 2.9.6 4.44.6 5.5 0 10-4.09 10-9.18S17.5 2.04 12 2.04zm1 12.3l-1.92-2.04L6.5 14.33l4.95-5.27 1.97 2.04 4.54-2.03-4.96 5.27z"/>
                    </svg>
                    Pošalji poruku
                </a>
            </div>
        </div>
    `;

    // Append elements to the body
    document.body.appendChild(banner);
    document.body.appendChild(modal);

    // Add event listeners
    document.getElementById('reward-popup-btn').addEventListener('click', function() {
        document.getElementById('reward-popup-modal').classList.remove('hidden');
        document.body.style.overflow = 'hidden'; // Prevent scrolling
    });

    document.getElementById('reward-modal-close').addEventListener('click', function() {
        document.getElementById('reward-popup-modal').classList.add('hidden');
        document.body.style.overflow = ''; // Re-enable scrolling
    });

    document.getElementById('reward-modal-overlay').addEventListener('click', function() {
        document.getElementById('reward-popup-modal').classList.add('hidden');
        document.body.style.overflow = ''; // Re-enable scrolling
    });
}

/**
 * Show the reward popup and start the countdown
 */
function showRewardPopup() {
    const banner = document.getElementById('reward-popup-banner');
    if (!banner) return;

    // Show the banner with animation
    banner.classList.remove('translate-y-full');

    // Start the countdown
    const countdownElement = document.getElementById('reward-countdown');
    if (!countdownElement) return;

    const countdownSeconds = parseInt(countdownElement.textContent);
    let secondsLeft = countdownSeconds;

    // Get the progress circle element
    const progressCircle = document.querySelector('.reward-countdown-progress');
    if (!progressCircle) return;

    // Calculate the circumference of the circle
    const radius = parseInt(progressCircle.getAttribute('r'));
    const circumference = 2 * Math.PI * radius;

    // Set the initial stroke-dashoffset
    progressCircle.style.strokeDashoffset = '0';

    const countdownInterval = setInterval(function() {
        secondsLeft--;
        countdownElement.textContent = secondsLeft;

        // Update the progress circle
        const offset = circumference - (secondsLeft / countdownSeconds) * circumference;
        progressCircle.style.strokeDashoffset = offset;

        if (secondsLeft <= 0) {
            clearInterval(countdownInterval);

            // Show the button container when countdown reaches zero
            const buttonContainer = document.getElementById('reward-button-container');
            if (buttonContainer) {
                buttonContainer.classList.remove('hidden');
                buttonContainer.classList.add('animate-fadeIn');
            }

            // Add pulse animation to the button
            const button = document.getElementById('reward-popup-btn');
            if (button) {
                button.classList.add('animate-pulse');
            }

            // Update the countdown text
            const countdownTextElement = document.querySelector('.reward-countdown-text');
            if (countdownTextElement) {
                countdownTextElement.innerHTML = `
                    <p class="countdown-title">Nagradna igra je spremna!</p>
                    <p>Kliknite na dugme ispod da saznate više o nagradnoj igri.</p>
                `;
            }

            // Add a success class to the countdown circle
            const countdownCircle = document.querySelector('.reward-countdown-circle');
            if (countdownCircle) {
                countdownCircle.classList.add('success');

                // Change the content of the countdown number to a checkmark
                countdownElement.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                `;
            }
        }
    }, 1000);
}
