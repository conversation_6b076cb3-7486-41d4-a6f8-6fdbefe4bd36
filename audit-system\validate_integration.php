<?php

/**
 * Integration Validation Script
 * 
 * Simple validation script that tests the integrated system without PHPUnit
 */

require_once __DIR__ . '/vendor/autoload.php';

use AuditSystem\Integration\SystemIntegrator;
use AuditSystem\Config\AuditConfig;

echo "=== AUDIT SYSTEM INTEGRATION VALIDATION ===\n";
echo "Testing complete system integration...\n\n";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest(string $testName, callable $testFunction): bool
{
    global $totalTests, $passedTests, $testResults;
    
    $totalTests++;
    echo "Running: {$testName}... ";
    
    try {
        $result = $testFunction();
        if ($result) {
            echo "✅ PASS\n";
            $passedTests++;
            $testResults[$testName] = 'PASS';
            return true;
        } else {
            echo "❌ FAIL\n";
            $testResults[$testName] = 'FAIL';
            return false;
        }
    } catch (Exception $e) {
        echo "❌ ERROR: " . $e->getMessage() . "\n";
        $testResults[$testName] = 'ERROR: ' . $e->getMessage();
        return false;
    }
}

// Test 1: System Initialization
runTest('System Initialization', function() {
    $config = AuditConfig::getInstance();
    $config->set('audit.target_directory', dirname(__DIR__) . '/public_html');
    $config->set('audit.report_directory', __DIR__ . '/reports');
    $config->set('audit.progress_file', __DIR__ . '/data/validation_progress.json');
    
    $integrator = new SystemIntegrator($config);
    $integrator->initialize();
    
    $status = $integrator->getSystemStatus();
    return $status['initialized'] && $status['analyzers']['count'] > 0;
});

// Test 2: Health Check
runTest('System Health Check', function() {
    $config = AuditConfig::getInstance();
    $integrator = new SystemIntegrator($config);
    $integrator->initialize();
    
    $healthCheck = $integrator->performHealthCheck();
    return in_array($healthCheck['overall_status'], ['healthy', 'degraded']);
});

// Test 3: Audit Execution
runTest('Audit Execution', function() {
    $config = AuditConfig::getInstance();
    $cmsPath = dirname(__DIR__) . '/public_html';
    
    if (!is_dir($cmsPath)) {
        return false; // Skip if CMS not available
    }
    
    $integrator = new SystemIntegrator($config);
    $integrator->initialize();
    
    $controller = $integrator->getAuditController();
    $result = $controller->startAudit([
        'audit.target_directory' => $cmsPath,
        'audit.timeout' => 60,
        'audit.max_file_size' => 1024 * 1024 // 1MB
    ]);
    
    return count($result->fileStatus) > 0 && $result->statistics->totalFindings >= 0;
});

// Test 4: Report Generation
runTest('Report Generation', function() {
    $config = AuditConfig::getInstance();
    $cmsPath = dirname(__DIR__) . '/public_html';
    
    if (!is_dir($cmsPath)) {
        return false; // Skip if CMS not available
    }
    
    $integrator = new SystemIntegrator($config);
    $integrator->initialize();
    
    $controller = $integrator->getAuditController();
    $result = $controller->startAudit([
        'audit.target_directory' => $cmsPath,
        'audit.timeout' => 30
    ]);
    
    $reportGenerator = $integrator->getService('report_generator');
    $reportDir = $config->get('audit.report_directory');
    
    if (!is_dir($reportDir)) {
        mkdir($reportDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d_H-i-s');
    $testReportPath = "{$reportDir}/validation_test_{$timestamp}.json";
    
    $success = $reportGenerator->exportReport($result, 'json', $testReportPath);
    
    // Cleanup test report
    if (file_exists($testReportPath)) {
        unlink($testReportPath);
    }
    
    return $success;
});

// Test 5: Error Handling
runTest('Error Handling', function() {
    $config = AuditConfig::getInstance();
    $integrator = new SystemIntegrator($config);
    $integrator->initialize();
    
    $controller = $integrator->getAuditController();
    
    try {
        // Try to audit non-existent directory
        $result = $controller->startAudit([
            'audit.target_directory' => '/nonexistent/directory',
            'audit.timeout' => 10
        ]);
        return false; // Should have thrown an exception
    } catch (Exception $e) {
        return true; // Exception was properly thrown and caught
    }
});

// Test 6: Performance Optimization
runTest('Performance Optimization', function() {
    $config = AuditConfig::getInstance();
    $optimizer = new \AuditSystem\Optimization\PerformanceOptimizer($config);
    
    $optimizer->optimizeForLargeCodebase();
    
    // Check if memory limit was increased
    $memoryLimit = ini_get('memory_limit');
    
    // Simple check - should be at least 256M
    return strpos($memoryLimit, '256M') !== false || 
           strpos($memoryLimit, '512M') !== false ||
           strpos($memoryLimit, '1G') !== false ||
           $memoryLimit === '-1';
});

// Test 7: Analyzer Integration
runTest('Analyzer Integration', function() {
    $config = AuditConfig::getInstance();
    $integrator = new SystemIntegrator($config);
    $integrator->initialize();
    
    $analyzers = $integrator->getAnalyzers();
    
    // Should have at least security and performance analyzers
    return count($analyzers) >= 2 && 
           isset($analyzers['security']) && 
           isset($analyzers['performance']);
});

// Test 8: Configuration Validation
runTest('Configuration Validation', function() {
    $config = AuditConfig::getInstance();
    
    // Test setting and getting configuration
    $config->set('test.value', 'test123');
    $value = $config->get('test.value');
    
    return $value === 'test123';
});

echo "\n" . str_repeat("=", 60) . "\n";
echo "VALIDATION RESULTS\n";
echo str_repeat("=", 60) . "\n";

foreach ($testResults as $testName => $result) {
    $status = $result === 'PASS' ? '✅' : '❌';
    echo "{$status} {$testName}: {$result}\n";
}

echo "\n";
echo "Summary: {$passedTests}/{$totalTests} tests passed\n";

if ($passedTests === $totalTests) {
    echo "🎉 ALL TESTS PASSED! System integration is successful.\n";
    echo "\nThe audit system is fully integrated and ready for production use.\n";
    echo "\nNext steps:\n";
    echo "1. Run 'php bin/audit.php' to perform a complete audit\n";
    echo "2. Review generated reports in the reports/ directory\n";
    echo "3. Use 'php bin/audit.php validate' for system health checks\n";
    exit(0);
} else {
    $failedTests = $totalTests - $passedTests;
    echo "❌ {$failedTests} tests failed. Please review the issues above.\n";
    echo "\nTroubleshooting:\n";
    echo "1. Ensure CMS files are available at ../public_html\n";
    echo "2. Check file permissions for data/ and reports/ directories\n";
    echo "3. Verify PHP memory limit is sufficient (256MB recommended)\n";
    exit(1);
}