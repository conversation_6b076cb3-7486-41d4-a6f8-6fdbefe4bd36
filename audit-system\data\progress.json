{"completedFiles": ["C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_001.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_002.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_003.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_004.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_005.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_006.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_007.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_008.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_009.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_010.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_011.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_012.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_013.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_014.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_015.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_016.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_017.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_018.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_019.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_020.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\script_1.js", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\script_2.js", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\script_3.js", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\script_4.js", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\style_1.css", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\style_2.css", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\style_3.css", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\style_4.css"], "pendingFiles": [], "findings": {"C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_001.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_001.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table1 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_001.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table1 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_001.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_002.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_002.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table2 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_002.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table2 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_002.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_003.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_003.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table3 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_003.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table3 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_003.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_004.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_004.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table4 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_004.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table4 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_004.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_005.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_005.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table5 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_005.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table5 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_005.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_006.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_006.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table6 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_006.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table6 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_006.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_007.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_007.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table7 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_007.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table7 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_007.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_008.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_008.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table8 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_008.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table8 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_008.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_009.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_009.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table9 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_009.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table9 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_009.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_010.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_010.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table10 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_010.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table10 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_010.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_011.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_011.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table11 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_011.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table11 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_011.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_012.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_012.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table12 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_012.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table12 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_012.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_013.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_013.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table13 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_013.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table13 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_013.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_014.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_014.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table14 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_014.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table14 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_014.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_015.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_015.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table15 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_015.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table15 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_015.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_016.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_016.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table16 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_016.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table16 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_016.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_017.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_017.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table17 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_017.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table17 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_017.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_018.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_018.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table18 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_018.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table18 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_018.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_019.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_019.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table19 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_019.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table19 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_019.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_020.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_020.php", "line": 12, "type": "security", "severity": "critical", "priority": "NON_PRIORITY", "description": "SQL injection vulnerability: Direct concatenation of user input in SQL query", "recommendation": "Use prepared statements with parameter binding instead of string concatenation", "codeSnippet": "$query = \"SELECT * FROM table20 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_020.php", "line": 12, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "Potential SQL injection: User input directly in WHERE clause", "recommendation": "Use parameterized queries with PDO prepared statements", "codeSnippet": "$query = \"SELECT * FROM table20 WHERE id = \" . $_GET['id'];", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\perf_test_020.php", "line": 17, "type": "security", "severity": "high", "priority": "NON_PRIORITY", "description": "XSS vulnerability: Direct output of user input without sanitization", "recommendation": "Use htmlspecialchars() or htmlentities() to escape output", "codeSnippet": "echo $_POST['data'];", "references": ["https://owasp.org/www-community/attacks/xss/"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\script_1.js": [], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\script_2.js": [], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\script_3.js": [], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\script_4.js": [], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\style_1.css": [], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\style_2.css": [], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\style_3.css": [], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/performance-test\\style_4.css": []}, "lastUpdate": "2025-08-11 18:47:48", "currentPhase": "completed", "statistics": {"totalFiles": 28, "processedFiles": 28, "totalFindings": 60, "criticalFindings": 20, "priorityAreaFindings": 0}}