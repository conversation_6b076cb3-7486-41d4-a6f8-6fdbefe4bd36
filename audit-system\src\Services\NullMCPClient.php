<?php

namespace AuditSystem\Services;

use AuditSystem\Interfaces\MCPClientInterface;

/**
 * Null MCP Client for fallback operation
 * 
 * This client provides a no-op implementation of the MCP interface
 * for use when MCP server is not available or not configured.
 */
class NullMCPClient implements MCPClientInterface
{
    /**
     * Test connection to MCP server
     *
     * @return bool Always returns false for null client
     */
    public function testConnection(): bool
    {
        return false;
    }

    /**
     * Query best practices (returns empty result)
     *
     * @param string $query
     * @param array $context
     * @return array Empty array for null client
     */
    public function queryBestPractices(string $query, array $context = []): array
    {
        return [];
    }

    /**
     * Get security guidelines (returns empty result)
     *
     * @param string $language
     * @return array Empty array for null client
     */
    public function getSecurityGuidelines(string $language = 'php'): array
    {
        return [];
    }

    /**
     * Get performance recommendations (returns empty result)
     *
     * @param string $codeType
     * @return array Empty array for null client
     */
    public function getPerformanceRecommendations(string $codeType = 'php'): array
    {
        return [];
    }

    /**
     * Validate code against standards (returns empty result)
     *
     * @param string $code
     * @param string $language
     * @return array Empty array for null client
     */
    public function validateCodeStandards(string $code, string $language = 'php'): array
    {
        return [];
    }

    /**
     * Check if client is available
     *
     * @return bool Always returns false for null client
     */
    public function isAvailable(): bool
    {
        return false;
    }

    /**
     * Get client status
     *
     * @return array Status information
     */
    public function getStatus(): array
    {
        return [
            'connected' => false,
            'type' => 'null',
            'message' => 'MCP client not configured or available'
        ];
    }
}