<?php

require_once 'vendor/autoload.php';

use AuditSystem\Models\Finding;
use AuditSystem\Services\FindingFilter;

echo "Testing FindingFilter...\n\n";

// Create test findings
$findings = [
    new Finding(
        'public_html/config.php', 25, Finding::TYPE_SECURITY, 
        Finding::SEVERITY_CRITICAL, Finding::PRIORITY_AREA,
        'SQL injection vulnerability', 'Use prepared statements'
    ),
    new Finding(
        'public_html/ad_display.php', 30, Finding::TYPE_PERFORMANCE, 
        Finding::SEVERITY_HIGH, Finding::PRIORITY_AREA,
        'N+1 query problem', 'Optimize queries'
    ),
    new Finding(
        'public_html/article.php', 45, Finding::TYPE_SECURITY, 
        Finding::SEVERITY_MEDIUM, Finding::PRIORITY_AREA,
        'XSS vulnerability', 'Escape output'
    ),
    new Finding(
        'public_html/privacy-policy.php', 10, Finding::TYPE_QUALITY, 
        Finding::SEVERITY_LOW, Finding::NON_PRIORITY,
        'Naming convention issue', 'Follow PSR standards'
    ),
    new Finding(
        'public_html/includes/functions.php', 100, Finding::TYPE_ARCHITECTURE, 
        Finding::SEVERITY_MEDIUM, Finding::NON_PRIORITY,
        'MVC violation', 'Separate concerns'
    )
];

echo "Total findings: " . count($findings) . "\n\n";

// Test 1: Filter by priority
echo "Test 1: Filter by priority\n";
$priorityFindings = FindingFilter::filterByPriority($findings, true);
$nonPriorityFindings = FindingFilter::filterByPriority($findings, false);
echo "Priority area findings: " . count($priorityFindings) . "\n";
echo "Non-priority findings: " . count($nonPriorityFindings) . "\n\n";

// Test 2: Filter by severity
echo "Test 2: Filter by severity\n";
$criticalHighFindings = FindingFilter::filterBySeverity($findings, [
    Finding::SEVERITY_CRITICAL, Finding::SEVERITY_HIGH
]);
echo "Critical/High severity findings: " . count($criticalHighFindings) . "\n\n";

// Test 3: Filter by type
echo "Test 3: Filter by type\n";
$securityFindings = FindingFilter::filterByType($findings, [Finding::TYPE_SECURITY]);
echo "Security findings: " . count($securityFindings) . "\n\n";

// Test 4: Get critical findings
echo "Test 4: Get critical findings\n";
$critical = FindingFilter::getCriticalFindings($findings);
echo "Critical findings: " . count($critical) . "\n\n";

// Test 5: Get security findings
echo "Test 5: Get security findings\n";
$security = FindingFilter::getSecurityFindings($findings);
echo "Security findings: " . count($security) . "\n\n";

// Test 6: Sort by importance
echo "Test 6: Sort by importance\n";
$sortedByImportance = FindingFilter::sortByImportance($findings);
echo "Sorted findings by importance:\n";
foreach ($sortedByImportance as $i => $finding) {
    echo ($i + 1) . ". " . $finding->formatForDisplay() . " (Score: " . $finding->getImportanceScore() . ")\n";
}
echo "\n";

// Test 7: Group by file
echo "Test 7: Group by file\n";
$groupedByFile = FindingFilter::groupByFile($findings);
echo "Files with findings: " . count($groupedByFile) . "\n";
foreach ($groupedByFile as $file => $fileFindings) {
    echo "  " . basename($file) . ": " . count($fileFindings) . " findings\n";
}
echo "\n";

// Test 8: Group by type
echo "Test 8: Group by type\n";
$groupedByType = FindingFilter::groupByType($findings);
foreach ($groupedByType as $type => $typeFindings) {
    echo "  " . ucfirst($type) . ": " . count($typeFindings) . " findings\n";
}
echo "\n";

// Test 9: Group by severity
echo "Test 9: Group by severity\n";
$groupedBySeverity = FindingFilter::groupBySeverity($findings);
foreach ($groupedBySeverity as $severity => $severityFindings) {
    if (count($severityFindings) > 0) {
        echo "  " . ucfirst($severity) . ": " . count($severityFindings) . " findings\n";
    }
}
echo "\n";

// Test 10: Summary statistics
echo "Test 10: Summary statistics\n";
$stats = FindingFilter::getSummaryStats($findings);
echo "Total: " . $stats['total'] . "\n";
echo "Priority area: " . $stats['priority_area'] . "\n";
echo "Non-priority: " . $stats['non_priority'] . "\n";
echo "Files affected: " . $stats['files_affected'] . "\n";
echo "Average importance score: " . $stats['avg_importance_score'] . "\n";
echo "Critical severity: " . $stats['by_severity'][Finding::SEVERITY_CRITICAL] . "\n";
echo "Security type: " . $stats['by_type'][Finding::TYPE_SECURITY] . "\n\n";

// Test 11: Get top critical
echo "Test 11: Get top critical (limit 3)\n";
$topCritical = FindingFilter::getTopCritical($findings, 3);
echo "Top 3 critical findings:\n";
foreach ($topCritical as $i => $finding) {
    echo ($i + 1) . ". " . $finding->formatForDisplay() . "\n";
}
echo "\n";

// Test 12: Apply multiple filters
echo "Test 12: Apply multiple filters\n";
$filters = [
    'priority_area_only' => true,
    'severities' => [Finding::SEVERITY_CRITICAL, Finding::SEVERITY_HIGH],
    'sort_by' => 'importance',
    'limit' => 2
];
$filtered = FindingFilter::applyFilters($findings, $filters);
echo "Filtered results (priority area, critical/high severity, top 2):\n";
foreach ($filtered as $i => $finding) {
    echo ($i + 1) . ". " . $finding->formatForDisplay() . "\n";
}

echo "\nAll FindingFilter tests completed successfully!\n";