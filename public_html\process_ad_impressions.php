<?php
/**
 * process_ad_impressions.php
 *
 * <PERSON>ript to aggregate ad impressions from the buffer table into the main aggregated table
 * AND update the summary view counts in the affiliate_ads and adsense_units tables.
 * This script should be run periodically via a cron job (e.g., every 5-10 minutes).
 */

// Prevent direct web access if needed
// if (php_sapi_name() !== 'cli' && !isset($_GET['cron_secret'])) { die('Access denied.'); }

// Set a longer execution time limit
set_time_limit(300); // 5 minutes

// Get the absolute path to the config file
$configPath = __DIR__ . '/config.php';

// Include configuration and database connection
if (file_exists($configPath)) {
    require_once $configPath;
} else {
    error_log("process_ad_impressions.php: Failed to include config.php at path: " . $configPath);
    die("Configuration file not found.");
}

// Check if $pdo is available
if (!isset($pdo) || !$pdo instanceof PDO) {
    error_log("process_ad_impressions.php: PDO database connection is not available.");
    die("Database connection error.");
}

echo "Starting ad impression aggregation process...\n";
$processed_count = 0;
$buffer_entries = [];
$buffer_ids = [];

try {
    // Start transaction
    $pdo->beginTransaction();

    // Select entries from the buffer table and lock them
    $stmt_select = $pdo->query("SELECT id, view_date, view_hour, ad_id, ad_type, placement, view_count FROM ad_impressions_buffer FOR UPDATE");
    $buffer_entries = $stmt_select->fetchAll(PDO::FETCH_ASSOC);
    $processed_count = count($buffer_entries);

    if ($processed_count > 0) {
        echo "Found " . $processed_count . " ad impression entries in the buffer.\n";

        // --- Prepare data for aggregated table AND summary counts ---
        $aggregated_data = [];
        $summary_counts = ['affiliate' => [], 'adsense' => []]; // Separate counts for summary updates

        foreach ($buffer_entries as $entry) {
            $buffer_ids[] = $entry['id']; // Collect IDs for deletion
            $key = $entry['view_date'] . '_' . $entry['view_hour'] . '_' . $entry['ad_id'] . '_' . $entry['ad_type'] . '_' . ($entry['placement'] ?? 'NULL');

            // Prepare for detailed aggregated table
            if (!isset($aggregated_data[$key])) {
                $aggregated_data[$key] = [
                    'view_date' => $entry['view_date'],
                    'view_hour' => $entry['view_hour'],
                    'ad_id'     => $entry['ad_id'],
                    'ad_type'   => $entry['ad_type'],
                    'placement' => $entry['placement'],
                    'view_count'=> 0
                ];
            }
            $aggregated_data[$key]['view_count'] += $entry['view_count'];

            // Prepare for summary table updates (affiliate_ads, adsense_units)
            $adTypeLower = strtolower($entry['ad_type']);
            $adId = $entry['ad_id'];
            if ($adTypeLower === 'affiliate' || $adTypeLower === 'adsense') {
                 if (!isset($summary_counts[$adTypeLower][$adId])) {
                     $summary_counts[$adTypeLower][$adId] = 0;
                 }
                 $summary_counts[$adTypeLower][$adId] += $entry['view_count'];
            }
        }

        // --- Update ad_impressions_aggregated Table ---
        $sql_aggregate = "INSERT INTO ad_impressions_aggregated (view_date, view_hour, ad_id, ad_type, placement, view_count)
                          VALUES (:view_date, :view_hour, :ad_id, :ad_type, :placement, :view_count)
                          ON DUPLICATE KEY UPDATE view_count = view_count + VALUES(view_count)";
        $stmt_aggregate = $pdo->prepare($sql_aggregate);

        echo "Updating ad_impressions_aggregated table...\n";
        foreach ($aggregated_data as $data) {
            $stmt_aggregate->execute([
                ':view_date' => $data['view_date'],
                ':view_hour' => $data['view_hour'],
                ':ad_id'     => $data['ad_id'],
                ':ad_type'   => $data['ad_type'],
                ':placement' => $data['placement'],
                ':view_count'=> $data['view_count']
            ]);
        }
        echo "Finished updating ad_impressions_aggregated.\n";

        // --- Update Summary View Counts in Main Ad Tables ---
        echo "Updating summary view counts in main ad tables...\n";

        // Update affiliate_ads views
        if (!empty($summary_counts['affiliate'])) {
            $sql_update_affiliate = "UPDATE affiliate_ads SET views = views + :count WHERE id = :id";
            $stmt_update_affiliate = $pdo->prepare($sql_update_affiliate);
            foreach ($summary_counts['affiliate'] as $ad_id => $count) {
                if ($count > 0) { // Only update if there's a change
                    $stmt_update_affiliate->execute([':count' => $count, ':id' => $ad_id]);
                }
            }
            echo "Updated " . count($summary_counts['affiliate']) . " affiliate ad summary counts.\n";
        }

        // Update adsense_units views
        if (!empty($summary_counts['adsense'])) {
            $sql_update_adsense = "UPDATE adsense_units SET views = views + :count WHERE id = :id";
            $stmt_update_adsense = $pdo->prepare($sql_update_adsense);
            foreach ($summary_counts['adsense'] as $ad_id => $count) {
                 if ($count > 0) { // Only update if there's a change
                    $stmt_update_adsense->execute([':count' => $count, ':id' => $ad_id]);
                 }
            }
             echo "Updated " . count($summary_counts['adsense']) . " adsense unit summary counts.\n";
        }
        echo "Finished updating summary counts.\n";


        // --- Delete processed entries from the buffer table ---
        if (!empty($buffer_ids)) {
            echo "Deleting processed entries from ad_impressions_buffer...\n";
            $placeholders = implode(',', array_fill(0, count($buffer_ids), '?'));
            $sql_delete = "DELETE FROM ad_impressions_buffer WHERE id IN ($placeholders)";
            $stmt_delete = $pdo->prepare($sql_delete);
            foreach ($buffer_ids as $k => $id) {
                $stmt_delete->bindValue(($k + 1), $id, PDO::PARAM_INT);
            }
            $stmt_delete->execute();
            echo "Deleted " . $stmt_delete->rowCount() . " entries from buffer.\n";
        }

        // Commit transaction
        $pdo->commit();
        echo "Ad impression aggregation successful. Processed " . $processed_count . " buffer entries.\n";

    } else {
        // No entries to process, just commit (which does nothing)
        $pdo->commit();
        echo "No ad impression entries found in the buffer to process.\n";
    }

} catch (PDOException $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("process_ad_impressions.php: Aggregation failed: " . $e->getMessage());
    echo "Error during ad impression aggregation: " . $e->getMessage() . "\n";
    die("Aggregation process failed.");
} catch (Exception $e) {
     // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("process_ad_impressions.php: General error during aggregation: " . $e->getMessage());
    echo "General Error during ad impression aggregation: " . $e->getMessage() . "\n";
    die("Aggregation process failed due to general error.");
}

echo "Ad impression aggregation process finished.\n";

?>