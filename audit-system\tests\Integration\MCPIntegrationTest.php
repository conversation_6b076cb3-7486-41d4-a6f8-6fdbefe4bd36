<?php

namespace AuditSystem\Tests\Integration;

use PHPUnit\Framework\TestCase;
use AuditSystem\Services\MCPClient;
use AuditSystem\Services\BestPracticesChecker;
use AuditSystem\Config\AuditConfig;
use AuditSystem\Models\Finding;
use AuditSystem\Exceptions\MCPConnectionException;

/**
 * Integration tests for MCP server connectivity and best practices validation
 */
class MCPIntegrationTest extends TestCase
{
    private MCPClient $mcpClient;
    private BestPracticesChecker $checker;
    private AuditConfig $config;

    protected function setUp(): void
    {
        $this->config = AuditConfig::getInstance();
        $this->mcpClient = new MCPClient($this->config);
        $this->checker = new BestPracticesChecker($this->mcpClient, $this->config);
    }

    protected function tearDown(): void
    {
        // Clean up cache after tests
        $this->checker->clearCache();
        if ($this->mcpClient->isConnected()) {
            $this->mcpClient->disconnect();
        }
    }

    public function testMCPServerConnectivity(): void
    {
        // Test basic connectivity
        $this->assertTrue($this->mcpClient->connect());
        $this->assertTrue($this->mcpClient->isConnected());

        // Test capabilities
        $capabilities = $this->mcpClient->getCapabilities();
        $this->assertIsArray($capabilities);
        $this->assertArrayHasKey('name', $capabilities);
        $this->assertEquals('context7', $capabilities['name']);
    }

    public function testEndToEndBestPracticesValidation(): void
    {
        // Connect to MCP server
        $this->mcpClient->connect();

        // Test PHP security validation
        $phpCode = '<?php echo $_GET["user_input"]; ?>';
        $result = $this->checker->checkCode($phpCode, 'php', 'security validation');

        $this->assertIsArray($result);
        $this->assertEquals('php', $result['language']);
        $this->assertArrayHasKey('issues', $result);
        $this->assertArrayHasKey('score', $result);

        // Should detect security issue
        $this->assertNotEmpty($result['issues']);
        $this->assertLessThan(100, $result['score']);
    }

    public function testBestPracticesRetrieval(): void
    {
        $this->mcpClient->connect();

        // Test getting PHP security best practices
        $practices = $this->checker->getBestPractices('php-security', 'CMS security audit');

        $this->assertIsArray($practices);
        $this->assertEquals('php-security', $practices['technology']);
        $this->assertArrayHasKey('practices', $practices);
        $this->assertIsArray($practices['practices']);
        $this->assertNotEmpty($practices['practices']);
    }

    public function testFindingEnhancementWithBestPractices(): void
    {
        $this->mcpClient->connect();

        // Create a security finding
        $finding = new Finding(
            'public_html/config.php',
            25,
            Finding::TYPE_SECURITY,
            Finding::SEVERITY_HIGH,
            Finding::PRIORITY_AREA,
            'Unescaped user input in database query',
            'Use prepared statements to prevent SQL injection',
            'mysql_query("SELECT * FROM users WHERE name = \'" . $_POST["name"] . "\'");'
        );

        // Enhance findings with best practices
        $enhancedFindings = $this->checker->validateFindings([$finding]);

        $this->assertCount(1, $enhancedFindings);
        $enhancedFinding = $enhancedFindings[0];
        
        $this->assertInstanceOf(Finding::class, $enhancedFinding);
        $this->assertEquals($finding->getFile(), $enhancedFinding->getFile());
        $this->assertEquals($finding->getDescription(), $enhancedFinding->getDescription());
        
        // Should have enhanced references
        $references = $enhancedFinding->getReferences();
        $this->assertNotEmpty($references);
    }

    public function testCachingBehavior(): void
    {
        $this->mcpClient->connect();

        $code = '<?php echo htmlspecialchars($_GET["safe_input"]); ?>';
        
        // First request
        $start1 = microtime(true);
        $result1 = $this->checker->checkCode($code, 'php');
        $time1 = microtime(true) - $start1;

        // Second request (should be cached)
        $start2 = microtime(true);
        $result2 = $this->checker->checkCode($code, 'php');
        $time2 = microtime(true) - $start2;

        // Results should be identical
        $this->assertEquals($result1, $result2);
        
        // Second request should be faster (cached)
        $this->assertLessThan($time1, $time2);

        // Check cache stats
        $stats = $this->checker->getCacheStats();
        $this->assertGreaterThan(0, $stats['entries']);
    }

    public function testFallbackWhenMCPUnavailable(): void
    {
        // Don't connect to MCP server to simulate unavailability
        $code = '<?php mysql_query("SELECT * FROM users WHERE id = " . $_GET["id"]); ?>';
        
        $result = $this->checker->checkCode($code, 'php');

        $this->assertIsArray($result);
        $this->assertEquals('php', $result['language']);
        $this->assertEquals('fallback', $result['source']);
        $this->assertNotEmpty($result['issues']);
        
        // Should still detect SQL injection in fallback mode
        $hasSQLInjection = false;
        foreach ($result['issues'] as $issue) {
            if ($issue['type'] === 'security' && strpos($issue['message'], 'SQL injection') !== false) {
                $hasSQLInjection = true;
                break;
            }
        }
        $this->assertTrue($hasSQLInjection);
    }

    public function testMultipleTechnologyValidation(): void
    {
        $this->mcpClient->connect();

        // Test different technologies
        $testCases = [
            ['<?php echo $_GET["test"]; ?>', 'php'],
            ['eval(userInput);', 'javascript'],
            ['.a { .b { .c { .d { color: red; } } } }', 'css']
        ];

        foreach ($testCases as [$code, $language]) {
            $result = $this->checker->checkCode($code, $language);
            
            $this->assertIsArray($result);
            $this->assertEquals($language, $result['language']);
            $this->assertArrayHasKey('issues', $result);
            $this->assertArrayHasKey('score', $result);
            
            // All test cases should have issues
            $this->assertNotEmpty($result['issues']);
            $this->assertLessThan(100, $result['score']);
        }
    }

    public function testBestPracticesForDifferentTechnologies(): void
    {
        $this->mcpClient->connect();

        $technologies = [
            'php-security',
            'php-performance',
            'javascript-performance',
            'css-structure'
        ];

        foreach ($technologies as $technology) {
            $practices = $this->checker->getBestPractices($technology);
            
            $this->assertIsArray($practices);
            $this->assertEquals($technology, $practices['technology']);
            $this->assertArrayHasKey('practices', $practices);
            $this->assertIsArray($practices['practices']);
        }
    }

    public function testConfigurationIntegration(): void
    {
        // Test that MCP configuration is properly loaded
        $mcpEnabled = $this->config->get('mcp.enabled');
        $serverUrl = $this->config->get('mcp.server_url');
        $timeout = $this->config->get('mcp.timeout');
        $fallbackEnabled = $this->config->get('mcp.fallback_enabled');

        $this->assertTrue($mcpEnabled);
        $this->assertEquals('context7', $serverUrl);
        $this->assertEquals(30, $timeout);
        $this->assertTrue($fallbackEnabled);
    }

    public function testErrorHandlingAndRecovery(): void
    {
        $this->mcpClient->connect();

        // Test with invalid method
        try {
            $this->mcpClient->request('invalid_method');
            $this->fail('Expected MCPConnectionException');
        } catch (MCPConnectionException $e) {
            $this->assertStringContainsString('Unknown method', $e->getMessage());
        }

        // MCP client should still be connected after error
        $this->assertTrue($this->mcpClient->isConnected());

        // Should still be able to make valid requests
        $result = $this->mcpClient->getBestPractices('php-security');
        $this->assertIsArray($result);
    }

    public function testLargeBatchValidation(): void
    {
        $this->mcpClient->connect();

        // Create multiple findings to test batch processing
        $findings = [];
        $testFiles = [
            'public_html/index.php',
            'public_html/article.php',
            'public_html/admin/settings.php',
            'public_html/includes/functions.php',
            'public_html/assets/js/main.js'
        ];

        foreach ($testFiles as $file) {
            $findings[] = new Finding(
                $file,
                rand(1, 100),
                Finding::TYPE_SECURITY,
                Finding::SEVERITY_MEDIUM,
                Finding::PRIORITY_AREA,
                'Test security issue',
                'Test recommendation'
            );
        }

        $start = microtime(true);
        $enhancedFindings = $this->checker->validateFindings($findings);
        $duration = microtime(true) - $start;

        $this->assertCount(count($findings), $enhancedFindings);
        $this->assertLessThan(5.0, $duration); // Should complete within 5 seconds

        // All findings should be enhanced
        foreach ($enhancedFindings as $finding) {
            $this->assertInstanceOf(Finding::class, $finding);
        }
    }
}