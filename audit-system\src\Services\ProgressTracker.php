<?php

namespace AuditSystem\Services;

use AuditSystem\Interfaces\ProgressTrackerInterface;
use AuditSystem\Models\AuditProgress;
use AuditSystem\Models\Finding;

/**
 * JSON-based progress tracking and persistence system
 */
class ProgressTracker implements ProgressTrackerInterface
{
    private string $progressFile;

    /**
     * Backward-compatible constructor.
     * - If given string, treat as progress file path.
     * - If given AuditConfig, pull audit.progress_file.
     * - If given (AuditConfig, LoggerInterface) from tests, accept first arg and ignore the rest.
     */
    public function __construct($arg1 = 'data/progress.json', $arg2 = null)
    {
        if ($arg1 instanceof \AuditSystem\Config\AuditConfig) {
            $this->progressFile = $arg1->get('audit.progress_file', 'audit-system/data/progress.json');
        } elseif (is_string($arg1)) {
            $this->progressFile = $arg1;
        } else {
            // Fallback
            $this->progressFile = 'audit-system/data/progress.json';
        }

        // Ensure directory exists
        $dir = dirname($this->progressFile);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }

    /**
     * Load existing audit progress from storage
     *
     * @return AuditProgress|null Existing progress or null if none exists
     */
    public function loadProgress(): ?AuditProgress
    {
        if (!file_exists($this->progressFile)) {
            return null;
        }

        $data = file_get_contents($this->progressFile);
        if ($data === false) {
            return null;
        }

        $progressData = json_decode($data, true);
        if ($progressData === null) {
            return null;
        }

        try {
            return AuditProgress::fromArray($progressData);
        } catch (\Exception $e) {
            // If progress file is corrupted, return null to start fresh
            return null;
        }
    }

    /**
     * Save current audit progress to storage
     *
     * @param AuditProgress $progress Progress data to save
     * @return bool True if save was successful
     */
    public function saveProgress(AuditProgress $progress): bool
    {
        $data = json_encode($progress->toArray(), JSON_PRETTY_PRINT);
        if ($data === false) {
            return false;
        }

        $result = file_put_contents($this->progressFile, $data, LOCK_EX);
        return $result !== false;
    }

    /**
     * Mark a file as completed in the progress tracker
     *
     * @param string $filePath Path of the completed file
     * @param array $findings Findings discovered in the file
     * @return void
     */
    public function markFileCompleted(string $filePath, array $findings): void
    {
        $progress = $this->loadProgress();
        if ($progress === null) {
            $progress = new AuditProgress();
        }

        $progress->markFileCompleted($filePath, $findings);
        $this->saveProgress($progress);
    }

    /**
     * Check if a file has already been processed
     *
     * @param string $filePath Path of the file to check
     * @return bool True if file has been processed
     */
    public function isFileCompleted(string $filePath): bool
    {
        $progress = $this->loadProgress();
        if ($progress === null) {
            return false;
        }

        return $progress->isFileCompleted($filePath);
    }

    /**
     * Reset progress tracker to start fresh audit
     *
     * @return void
     */
    public function resetProgress(): void
    {
        if (file_exists($this->progressFile)) {
            unlink($this->progressFile);
        }
    }

    /**
     * Initialize progress with list of files to process
     *
     * @param array $files List of files to be audited
     * @return void
     */
    public function initializeProgress(array $files): void
    {
        $progress = new AuditProgress();
        $progress->setPendingFiles($files);
        $progress->currentPhase = 'scanning';
        $this->saveProgress($progress);
    }

    /**
     * Update current phase of the audit
     *
     * @param string $phase Current phase name
     * @return void
     */
    public function updatePhase(string $phase): void
    {
        $progress = $this->loadProgress();
        if ($progress !== null) {
            $progress->currentPhase = $phase;
            $this->saveProgress($progress);
        }
    }

    /**
     * Get progress statistics
     *
     * @return array Progress statistics
     */
    public function getStatistics(): array
    {
        $progress = $this->loadProgress();
        if ($progress === null) {
            return [
                'totalFiles' => 0,
                'processedFiles' => 0,
                'completionPercentage' => 0.0,
                'totalFindings' => 0,
                'criticalFindings' => 0,
                'priorityAreaFindings' => 0
            ];
        }

        return array_merge($progress->statistics, [
            'completionPercentage' => $progress->getCompletionPercentage()
        ]);
    }

    /**
     * Get list of remaining files to process
     *
     * @return array List of pending files
     */
    public function getPendingFiles(): array
    {
        $progress = $this->loadProgress();
        if ($progress === null) {
            return [];
        }

        return $progress->pendingFiles;
    }

    /**
     * Backup current progress to a timestamped file
     *
     * @return bool True if backup was successful
     */
    public function backupProgress(): bool
    {
        if (!file_exists($this->progressFile)) {
            return false;
        }

        $timestamp = date('Y-m-d_H-i-s');
        $backupFile = dirname($this->progressFile) . "/progress_backup_{$timestamp}.json";
        
        return copy($this->progressFile, $backupFile);
    }

    /**
     * Restore progress from a backup file
     *
     * @param string $backupFile Path to backup file
     * @return bool True if restore was successful
     */
    public function restoreProgress(string $backupFile): bool
    {
        if (!file_exists($backupFile)) {
            return false;
        }

        return copy($backupFile, $this->progressFile);
    }
}