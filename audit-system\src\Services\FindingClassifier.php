<?php

namespace AuditSystem\Services;

use AuditSystem\Models\Finding;

/**
 * Classifies and prioritizes audit findings based on severity and business impact
 */
class FindingClassifier
{
    /**
     * Priority area file patterns that indicate high business impact
     */
    private const PRIORITY_PATTERNS = [
        // Ad system files
        '/ad[_-]/',
        '/adsense/',
        '/advertising/',
        '/process_ad_impressions/',
        '/record_impression/',
        '/track_click/',
        
        // Design and UI files
        '/css/',
        '/js/',
        '/assets/',
        '/images/',
        '/image\.php/',
        
        // AI features
        '/deepseek/',
        '/smrsaj/',
        '/ai[_-]/',
        
        // Image handling
        '/upload/',
        '/image/',
        '/media/',
        
        // Security critical files
        '/config\.php/',
        '/\.htaccess/',
        '/security/',
        '/auth/',
        '/login/',
        '/register/',
        '/process_/',
    ];

    /**
     * Security finding types that require immediate attention
     */
    private const CRITICAL_SECURITY_TYPES = [
        'sql_injection',
        'xss_vulnerability',
        'file_upload_vulnerability',
        'authentication_bypass',
        'csrf_missing',
        'sensitive_data_exposure'
    ];

    /**
     * Performance finding types that significantly impact user experience
     */
    private const HIGH_PERFORMANCE_IMPACT = [
        'n_plus_one_query',
        'missing_database_index',
        'unoptimized_image_loading',
        'blocking_resource',
        'memory_leak'
    ];

    /**
     * Classify a finding with appropriate severity and priority
     *
     * @param string $file File path where finding was detected
     * @param int $line Line number of the finding
     * @param string $type Type of finding (security, performance, quality, architecture)
     * @param string $subtype Specific subtype of the finding
     * @param string $description Description of the finding
     * @param string|null $codeSnippet Code snippet showing the issue
     * @param array $context Additional context about the finding
     * @return Finding
     */
    public function classifyFinding(
        string $file,
        int $line,
        string $type,
        string $subtype,
        string $description,
        ?string $codeSnippet = null,
        array $context = []
    ): Finding {
        $severity = $this->determineSeverity($type, $subtype, $file, $context);
        $priority = $this->determinePriority($file, $type, $severity);
        $recommendation = $this->generateRecommendation($type, $subtype, $severity);
        $references = $this->generateReferences($type, $subtype);

        return new Finding(
            $file,
            $line,
            $type,
            $severity,
            $priority,
            $description,
            $recommendation,
            $codeSnippet,
            $references
        );
    }

    /**
     * Determine severity based on finding type, subtype, and context
     *
     * @param string $type
     * @param string $subtype
     * @param string $file
     * @param array $context
     * @return string
     */
    private function determineSeverity(string $type, string $subtype, string $file, array $context): string
    {
        // Critical security vulnerabilities
        if ($type === Finding::TYPE_SECURITY && in_array($subtype, self::CRITICAL_SECURITY_TYPES)) {
            return Finding::SEVERITY_CRITICAL;
        }

        // High-impact performance issues
        if ($type === Finding::TYPE_PERFORMANCE && in_array($subtype, self::HIGH_PERFORMANCE_IMPACT)) {
            return Finding::SEVERITY_HIGH;
        }

        // Security issues in priority areas
        if ($type === Finding::TYPE_SECURITY && $this->isPriorityArea($file)) {
            return Finding::SEVERITY_HIGH;
        }

        // Performance issues in user-facing areas
        if ($type === Finding::TYPE_PERFORMANCE && $this->isUserFacingArea($file)) {
            return Finding::SEVERITY_HIGH;
        }

        // Architecture issues in core files
        if ($type === Finding::TYPE_ARCHITECTURE && $this->isCoreFile($file)) {
            return Finding::SEVERITY_MEDIUM;
        }

        // Quality issues based on complexity
        if ($type === Finding::TYPE_QUALITY) {
            return $this->determineQualitySeverity($subtype, $context);
        }

        // Default severity mapping
        return match ($type) {
            Finding::TYPE_SECURITY => Finding::SEVERITY_HIGH,
            Finding::TYPE_PERFORMANCE => Finding::SEVERITY_MEDIUM,
            Finding::TYPE_ARCHITECTURE => Finding::SEVERITY_MEDIUM,
            Finding::TYPE_QUALITY => Finding::SEVERITY_LOW,
            default => Finding::SEVERITY_LOW
        };
    }

    /**
     * Determine priority based on file location and finding characteristics
     *
     * @param string $file
     * @param string $type
     * @param string $severity
     * @return string
     */
    private function determinePriority(string $file, string $type, string $severity): string
    {
        // Critical and high severity security issues are always priority
        if ($type === Finding::TYPE_SECURITY && 
            in_array($severity, [Finding::SEVERITY_CRITICAL, Finding::SEVERITY_HIGH])) {
            return Finding::PRIORITY_AREA;
        }

        // Files in priority areas
        if ($this->isPriorityArea($file)) {
            return Finding::PRIORITY_AREA;
        }

        // High severity performance issues in user-facing areas
        if ($type === Finding::TYPE_PERFORMANCE && 
            $severity === Finding::SEVERITY_HIGH && 
            $this->isUserFacingArea($file)) {
            return Finding::PRIORITY_AREA;
        }

        return Finding::NON_PRIORITY;
    }

    /**
     * Check if file is in a priority area
     *
     * @param string $file
     * @return bool
     */
    private function isPriorityArea(string $file): bool
    {
        $normalizedFile = strtolower($file);
        
        foreach (self::PRIORITY_PATTERNS as $pattern) {
            if (preg_match($pattern, $normalizedFile)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if file is user-facing (affects user experience)
     *
     * @param string $file
     * @return bool
     */
    private function isUserFacingArea(string $file): bool
    {
        $userFacingPatterns = [
            '/index\.php/',
            '/article\.php/',
            '/category\.php/',
            '/search\.php/',
            '/css/',
            '/js/',
            '/image\.php/',
            '/load_/',
        ];

        $normalizedFile = strtolower($file);
        
        foreach ($userFacingPatterns as $pattern) {
            if (preg_match($pattern, $normalizedFile)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if file is a core system file
     *
     * @param string $file
     * @return bool
     */
    private function isCoreFile(string $file): bool
    {
        $corePatterns = [
            '/config\.php/',
            '/functions\.php/',
            '/security\.php/',
            '/includes\//',
        ];

        $normalizedFile = strtolower($file);
        
        foreach ($corePatterns as $pattern) {
            if (preg_match($pattern, $normalizedFile)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine quality issue severity based on subtype and context
     *
     * @param string $subtype
     * @param array $context
     * @return string
     */
    private function determineQualitySeverity(string $subtype, array $context): string
    {
        // High complexity or duplication
        if (in_array($subtype, ['high_complexity', 'code_duplication', 'large_function'])) {
            $complexity = $context['complexity'] ?? 0;
            if ($complexity > 20) {
                return Finding::SEVERITY_HIGH;
            } elseif ($complexity > 10) {
                return Finding::SEVERITY_MEDIUM;
            }
        }

        // Naming convention issues
        if ($subtype === 'naming_convention') {
            return Finding::SEVERITY_LOW;
        }

        // Documentation issues
        if ($subtype === 'missing_documentation') {
            return Finding::SEVERITY_LOW;
        }

        return Finding::SEVERITY_LOW;
    }

    /**
     * Generate specific recommendations based on finding type and subtype
     *
     * @param string $type
     * @param string $subtype
     * @param string $severity
     * @return string
     */
    private function generateRecommendation(string $type, string $subtype, string $severity): string
    {
        $recommendations = [
            'security' => [
                'sql_injection' => 'Use PDO prepared statements with parameter binding. Replace direct SQL concatenation with parameterized queries.',
                'xss_vulnerability' => 'Implement proper output escaping using htmlspecialchars() or similar functions. Validate and sanitize all user input.',
                'file_upload_vulnerability' => 'Add file type validation, size limits, and store uploads outside web root. Implement virus scanning if possible.',
                'authentication_bypass' => 'Implement proper session management and authentication checks. Use secure session configuration.',
                'csrf_missing' => 'Add CSRF tokens to all forms and validate them on submission. Use secure random token generation.',
                'sensitive_data_exposure' => 'Remove sensitive information from code. Use environment variables for credentials and API keys.',
            ],
            'performance' => [
                'n_plus_one_query' => 'Implement eager loading or batch queries to reduce database calls. Consider using JOIN statements or query optimization.',
                'missing_database_index' => 'Add appropriate database indexes for frequently queried columns. Analyze query execution plans.',
                'unoptimized_image_loading' => 'Implement lazy loading, WebP format, and responsive images. Use CDN for static assets.',
                'blocking_resource' => 'Move JavaScript to bottom of page or use async/defer attributes. Optimize CSS delivery.',
                'memory_leak' => 'Review variable scope and unset large variables when no longer needed. Optimize memory usage patterns.',
            ],
            'quality' => [
                'high_complexity' => 'Break down complex functions into smaller, single-purpose methods. Apply single responsibility principle.',
                'code_duplication' => 'Extract common code into reusable functions or classes. Follow DRY (Don\'t Repeat Yourself) principle.',
                'naming_convention' => 'Use consistent, descriptive naming following PHP PSR standards. Rename variables and functions for clarity.',
                'missing_documentation' => 'Add PHPDoc comments for classes, methods, and complex logic. Document API endpoints and parameters.',
            ],
            'architecture' => [
                'mvc_violation' => 'Separate business logic from presentation. Move database queries to model layer and HTML to view templates.',
                'tight_coupling' => 'Implement dependency injection and use interfaces to reduce coupling between components.',
                'missing_error_handling' => 'Add try-catch blocks for error-prone operations. Implement proper error logging and user feedback.',
            ]
        ];

        $typeRecommendations = $recommendations[$type] ?? [];
        $recommendation = $typeRecommendations[$subtype] ?? 'Review and address this issue according to best practices.';

        // Add severity-specific urgency
        $urgencyPrefix = match ($severity) {
            Finding::SEVERITY_CRITICAL => 'URGENT: ',
            Finding::SEVERITY_HIGH => 'HIGH PRIORITY: ',
            Finding::SEVERITY_MEDIUM => 'MEDIUM PRIORITY: ',
            default => ''
        };

        return $urgencyPrefix . $recommendation;
    }

    /**
     * Generate relevant references for finding type and subtype
     *
     * @param string $type
     * @param string $subtype
     * @return array
     */
    private function generateReferences(string $type, string $subtype): array
    {
        $references = [
            'security' => [
                'sql_injection' => [
                    'https://owasp.org/www-community/attacks/SQL_Injection',
                    'https://www.php.net/manual/en/pdo.prepared-statements.php'
                ],
                'xss_vulnerability' => [
                    'https://owasp.org/www-community/attacks/xss/',
                    'https://www.php.net/manual/en/function.htmlspecialchars.php'
                ],
                'file_upload_vulnerability' => [
                    'https://owasp.org/www-community/vulnerabilities/Unrestricted_File_Upload',
                    'https://www.php.net/manual/en/features.file-upload.php'
                ],
                'csrf_missing' => [
                    'https://owasp.org/www-community/attacks/csrf',
                    'https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html'
                ]
            ],
            'performance' => [
                'n_plus_one_query' => [
                    'https://secure.phabricator.com/book/phabcontrib/article/n_plus_one/',
                    'https://www.doctrine-project.org/projects/doctrine-orm/en/2.9/reference/dql-doctrine-query-language.html#joins'
                ],
                'missing_database_index' => [
                    'https://dev.mysql.com/doc/refman/8.0/en/mysql-indexes.html',
                    'https://use-the-index-luke.com/'
                ]
            ],
            'quality' => [
                'high_complexity' => [
                    'https://www.php-fig.org/psr/psr-12/',
                    'https://refactoring.guru/refactoring/smells/long-method'
                ],
                'code_duplication' => [
                    'https://refactoring.guru/refactoring/smells/duplicate-code',
                    'https://www.php-fig.org/psr/psr-4/'
                ]
            ]
        ];

        $typeReferences = $references[$type] ?? [];
        return $typeReferences[$subtype] ?? [];
    }

    /**
     * Batch classify multiple findings
     *
     * @param array $findingData Array of finding data to classify
     * @return array Array of classified Finding objects
     */
    public function classifyFindings(array $findingData): array
    {
        $findings = [];
        
        foreach ($findingData as $data) {
            $findings[] = $this->classifyFinding(
                $data['file'],
                $data['line'],
                $data['type'],
                $data['subtype'],
                $data['description'],
                $data['codeSnippet'] ?? null,
                $data['context'] ?? []
            );
        }

        return $findings;
    }

    /**
     * Get priority area statistics
     *
     * @param array $findings Array of Finding objects
     * @return array Statistics about priority areas
     */
    public function getPriorityStatistics(array $findings): array
    {
        $stats = [
            'total_findings' => count($findings),
            'priority_area' => 0,
            'non_priority' => 0,
            'severity_breakdown' => [
                Finding::SEVERITY_CRITICAL => 0,
                Finding::SEVERITY_HIGH => 0,
                Finding::SEVERITY_MEDIUM => 0,
                Finding::SEVERITY_LOW => 0
            ],
            'type_breakdown' => [
                Finding::TYPE_SECURITY => 0,
                Finding::TYPE_PERFORMANCE => 0,
                Finding::TYPE_QUALITY => 0,
                Finding::TYPE_ARCHITECTURE => 0
            ]
        ];

        foreach ($findings as $finding) {
            // Priority breakdown
            if ($finding->getPriority() === Finding::PRIORITY_AREA) {
                $stats['priority_area']++;
            } else {
                $stats['non_priority']++;
            }

            // Severity breakdown
            $stats['severity_breakdown'][$finding->getSeverity()]++;

            // Type breakdown
            $stats['type_breakdown'][$finding->getType()]++;
        }

        return $stats;
    }
}