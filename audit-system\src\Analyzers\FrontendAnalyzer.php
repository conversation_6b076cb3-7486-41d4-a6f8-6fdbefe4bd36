<?php

namespace AuditSystem\Analyzers;

use AuditSystem\Interfaces\AnalyzerInterface;
use AuditSystem\Models\Finding;

/**
 * Comprehensive frontend code analyzer for CSS, JavaScript, HTML, and accessibility
 */
class FrontendAnalyzer implements AnalyzerInterface
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'check_css_structure' => true,
            'check_js_performance' => true,
            'check_responsive_design' => true,
            'check_accessibility' => true,
            'check_asset_loading' => true,
            'max_css_selector_depth' => 4,
            'max_js_function_length' => 30,
            'required_viewport_meta' => true,
            'require_alt_attributes' => true
        ], $config);
    }

    /**
     * Analyze a file and return findings
     *
     * @param string $filePath Path to the file to analyze
     * @param string $content File content to analyze
     * @return Finding[] Array of findings discovered in the file
     */
    public function analyze(string $filePath, string $content): array
    {
        $findings = [];
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        switch ($extension) {
            case 'css':
                $findings = array_merge($findings, $this->analyzeCss($filePath, $content));
                break;
            case 'js':
                $findings = array_merge($findings, $this->analyzeJavaScript($filePath, $content));
                break;
            case 'html':
            case 'php':
                $findings = array_merge($findings, $this->analyzeHtml($filePath, $content));
                if ($this->config['check_asset_loading']) {
                    $findings = array_merge($findings, $this->analyzeAssetLoading($filePath, $content));
                }
                break;
        }

        return $findings;
    }

    /**
     * Analyze CSS structure and organization
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function analyzeCss(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++; // 1-based line numbers
            $trimmedLine = trim($line);

            // Check for overly specific selectors
            if (preg_match('/^([^{]+)\s*{/', $trimmedLine, $matches)) {
                $selector = trim($matches[1]);
                $depth = substr_count($selector, ' ') + substr_count($selector, '>') + substr_count($selector, '+') + substr_count($selector, '~');
                
                if ($depth > $this->config['max_css_selector_depth']) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "CSS selector too specific (depth: $depth > {$this->config['max_css_selector_depth']})",
                        "Simplify selector to improve maintainability and performance",
                        $trimmedLine,
                        ['https://developer.mozilla.org/en-US/docs/Web/CSS/Specificity']
                    );
                }

                // Check for ID selectors in CSS (should prefer classes)
                if (preg_match('/#[a-zA-Z]/', $selector)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_LOW,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "ID selector used in CSS",
                        "Consider using class selectors for better reusability",
                        $trimmedLine,
                        ['https://cssguidelin.es/#ids-in-css']
                    );
                }
            }

            // Check for !important usage
            if (strpos($trimmedLine, '!important') !== false) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "!important declaration found",
                    "Avoid !important by improving CSS specificity and organization",
                    $trimmedLine,
                    ['https://developer.mozilla.org/en-US/docs/Web/CSS/important']
                );
            }

            // Check for inline styles in CSS (vendor prefixes without fallbacks)
            if (preg_match('/(-webkit-|-moz-|-ms-|-o-)([^:]+):/', $trimmedLine, $matches)) {
                $property = $matches[2];
                // Look for standard property in next few lines
                $hasStandard = false;
                for ($i = $lineNumber; $i < min($lineNumber + 5, count($lines)); $i++) {
                    if (isset($lines[$i]) && preg_match("/\\b$property\\s*:/", $lines[$i])) {
                        $hasStandard = true;
                        break;
                    }
                }
                
                if (!$hasStandard) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_LOW,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Vendor prefix without standard property fallback",
                        "Add standard property after vendor-prefixed versions",
                        $trimmedLine,
                        ['https://developer.mozilla.org/en-US/docs/Glossary/Vendor_Prefix']
                    );
                }
            }

            // Check for responsive design patterns
            if (preg_match('/font-size\s*:\s*(\d+)px/', $trimmedLine, $matches)) {
                $fontSize = (int)$matches[1];
                if ($fontSize < 16) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Font size too small for accessibility ({$fontSize}px < 16px)",
                        "Use minimum 16px font size for better readability",
                        $trimmedLine,
                        ['https://www.w3.org/WAI/WCAG21/Understanding/resize-text.html']
                    );
                }
            }

            // Check for fixed widths that might break responsiveness
            if (preg_match('/width\s*:\s*(\d+)px/', $trimmedLine, $matches)) {
                $width = (int)$matches[1];
                if ($width > 1200) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_LOW,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Fixed width may break responsive design ({$width}px)",
                        "Consider using max-width or relative units (%, vw, rem)",
                        $trimmedLine,
                        ['https://developer.mozilla.org/en-US/docs/Learn/CSS/CSS_layout/Responsive_Design']
                    );
                }
            }
        }

        // Check for missing media queries in CSS files
        if (!preg_match('/@media/', $content)) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_QUALITY,
                Finding::SEVERITY_MEDIUM,
                $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                "No media queries found in CSS file",
                "Add responsive breakpoints using @media queries",
                "/* No @media queries found */",
                ['https://developer.mozilla.org/en-US/docs/Web/CSS/Media_Queries']
            );
        }

        return $findings;
    }

    /**
     * Analyze JavaScript performance and compatibility
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function analyzeJavaScript(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++; // 1-based line numbers
            $trimmedLine = trim($line);

            // Check for console.log statements (should be removed in production)
            if (preg_match('/console\.(log|debug|info|warn|error)/', $trimmedLine)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_LOW,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Console statement found",
                    "Remove console statements from production code",
                    $trimmedLine,
                    ['https://eslint.org/docs/rules/no-console']
                );
            }

            // Check for var usage (prefer let/const)
            if (preg_match('/^\s*var\s+/', $trimmedLine)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_LOW,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Use of 'var' keyword",
                    "Use 'let' or 'const' instead of 'var' for better scoping",
                    $trimmedLine,
                    ['https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/let']
                );
            }

            // Check for == usage (prefer ===)
            if (preg_match('/[^=!]==[^=]/', $trimmedLine)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Loose equality operator (==) used",
                    "Use strict equality (===) to avoid type coercion issues",
                    $trimmedLine,
                    ['https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Strict_equality']
                );
            }

            // Check for global variable assignments
            if (preg_match('/^\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*=/', $trimmedLine) && 
                !preg_match('/^\s*(var|let|const)\s+/', $trimmedLine)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Potential global variable assignment",
                    "Declare variables with var, let, or const to avoid global scope pollution",
                    $trimmedLine,
                    ['https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/var']
                );
            }

            // Check for deprecated jQuery methods
            $deprecatedJQuery = ['live', 'die', 'toggle', 'browser'];
            foreach ($deprecatedJQuery as $method) {
                if (preg_match("/\\.$method\\s*\\(/", $trimmedLine)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_HIGH,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Deprecated jQuery method '$method' used",
                        "Replace with modern jQuery alternatives or vanilla JavaScript",
                        $trimmedLine,
                        ['https://api.jquery.com/category/deprecated/']
                    );
                }
            }

            // Check for synchronous AJAX calls
            if (preg_match('/ajax.*async\s*:\s*false/', $trimmedLine)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Synchronous AJAX call detected",
                    "Use asynchronous AJAX calls to prevent UI blocking",
                    $trimmedLine,
                    ['https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Synchronous_and_Asynchronous_Requests']
                );
            }

            // Check for document.write usage
            if (preg_match('/document\.write/', $trimmedLine)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_PERFORMANCE,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "document.write() usage detected",
                    "Use DOM manipulation methods instead of document.write()",
                    $trimmedLine,
                    ['https://developer.mozilla.org/en-US/docs/Web/API/Document/write']
                );
            }
        }

        // Check for missing 'use strict'
        if (!preg_match('/["\']use strict["\']/', $content)) {
            $findings[] = new Finding(
                $filePath,
                1,
                Finding::TYPE_QUALITY,
                Finding::SEVERITY_LOW,
                $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                "Missing 'use strict' directive",
                "Add 'use strict'; at the beginning of the file for better error handling",
                "/* Missing 'use strict'; */",
                ['https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode']
            );
        }

        return $findings;
    }

    /**
     * Analyze HTML structure and accessibility
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function analyzeHtml(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++; // 1-based line numbers

            // Check for images without alt attributes
            if (preg_match('/<img[^>]*>/', $line, $matches)) {
                $imgTag = $matches[0];
                if (!preg_match('/alt\s*=/', $imgTag)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_HIGH,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Image missing alt attribute",
                        "Add descriptive alt attribute for accessibility",
                        trim($line),
                        ['https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html']
                    );
                } elseif (preg_match('/alt\s*=\s*["\']["\']/', $imgTag)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Image has empty alt attribute",
                        "Provide descriptive alt text or use alt='' for decorative images",
                        trim($line),
                        ['https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html']
                    );
                }
            }

            // Check for links without descriptive text
            if (preg_match('/<a[^>]*>([^<]*)<\/a>/', $line, $matches)) {
                $linkText = trim($matches[1]);
                $genericTexts = ['click here', 'read more', 'more', 'here', 'link'];
                if (in_array(strtolower($linkText), $genericTexts)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Non-descriptive link text: '$linkText'",
                        "Use descriptive link text that explains the destination or purpose",
                        trim($line),
                        ['https://www.w3.org/WAI/WCAG21/Understanding/link-purpose-in-context.html']
                    );
                }
            }

            // Check for form inputs without labels
            if (preg_match('/<input[^>]*type\s*=\s*["\'](?!hidden)[^"\']*["\'][^>]*>/', $line, $matches)) {
                $inputTag = $matches[0];
                if (!preg_match('/id\s*=\s*["\']([^"\']+)["\']/', $inputTag, $idMatch)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Form input without ID for label association",
                        "Add ID attribute to associate with label element",
                        trim($line),
                        ['https://www.w3.org/WAI/WCAG21/Understanding/labels-or-instructions.html']
                    );
                }
            }

            // Check for missing viewport meta tag in head
            if (preg_match('/<head[^>]*>/', $line) && $this->config['required_viewport_meta']) {
                // Look for viewport meta in next 20 lines
                $hasViewport = false;
                for ($i = $lineNumber; $i < min($lineNumber + 20, count($lines)); $i++) {
                    if (isset($lines[$i]) && preg_match('/meta[^>]*name\s*=\s*["\']viewport["\']/', $lines[$i])) {
                        $hasViewport = true;
                        break;
                    }
                }
                
                if (!$hasViewport) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_HIGH,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Missing viewport meta tag",
                        "Add <meta name='viewport' content='width=device-width, initial-scale=1'> for responsive design",
                        trim($line),
                        ['https://developer.mozilla.org/en-US/docs/Web/HTML/Viewport_meta_tag']
                    );
                }
            }

            // Check for inline styles
            if (preg_match('/style\s*=\s*["\'][^"\']+["\']/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_LOW,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Inline style attribute used",
                    "Move styles to external CSS file for better maintainability",
                    trim($line),
                    ['https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/style']
                );
            }

            // Check for missing lang attribute on html tag
            if (preg_match('/<html[^>]*>/', $line, $matches)) {
                $htmlTag = $matches[0];
                if (!preg_match('/lang\s*=/', $htmlTag)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "HTML element missing lang attribute",
                        "Add lang attribute (e.g., lang='bs' for Bosnian) for accessibility",
                        trim($line),
                        ['https://www.w3.org/WAI/WCAG21/Understanding/language-of-page.html']
                    );
                }
            }

            // Check for tables without proper headers
            if (preg_match('/<table[^>]*>/', $line)) {
                // Look for th elements in next 10 lines
                $hasHeaders = false;
                for ($i = $lineNumber; $i < min($lineNumber + 10, count($lines)); $i++) {
                    if (isset($lines[$i]) && preg_match('/<th[^>]*>/', $lines[$i])) {
                        $hasHeaders = true;
                        break;
                    }
                }
                
                if (!$hasHeaders) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Table without proper headers",
                        "Use <th> elements for table headers to improve accessibility",
                        trim($line),
                        ['https://www.w3.org/WAI/WCAG21/Understanding/info-and-relationships.html']
                    );
                }
            }
        }

        return $findings;
    }

    /**
     * Analyze asset loading order and optimization
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function analyzeAssetLoading(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);
        
        $cssInHead = false;
        $jsInHead = false;
        $inHead = false;

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++; // 1-based line numbers

            // Track if we're in head section
            if (preg_match('/<head[^>]*>/', $line)) {
                $inHead = true;
            } elseif (preg_match('/<\/head>/', $line)) {
                $inHead = false;
            }

            // Check CSS loading
            if (preg_match('/<link[^>]*rel\s*=\s*["\']stylesheet["\'][^>]*>/', $line)) {
                if ($inHead) {
                    $cssInHead = true;
                } else {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "CSS loaded outside of <head> section",
                        "Move CSS links to <head> section for optimal loading",
                        trim($line),
                        ['https://developer.mozilla.org/en-US/docs/Web/HTML/Element/link']
                    );
                }
            }

            // Check JavaScript loading
            if (preg_match('/<script[^>]*src[^>]*>/', $line)) {
                if ($inHead) {
                    $jsInHead = true;
                    // Check if it has async or defer
                    if (!preg_match('/(async|defer)/', $line)) {
                        $findings[] = new Finding(
                            $filePath,
                            $lineNumber,
                            Finding::TYPE_PERFORMANCE,
                            Finding::SEVERITY_MEDIUM,
                            $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                            "JavaScript in <head> without async or defer",
                            "Add async or defer attribute, or move script to end of body",
                            trim($line),
                            ['https://developer.mozilla.org/en-US/docs/Web/HTML/Element/script']
                        );
                    }
                }
            }

            // Check for multiple CSS/JS files (could be concatenated)
            if (preg_match_all('/<link[^>]*rel\s*=\s*["\']stylesheet["\'][^>]*>/', $content, $matches)) {
                if (count($matches[0]) > 3) {
                    $findings[] = new Finding(
                        $filePath,
                        1,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_LOW,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Multiple CSS files detected (" . count($matches[0]) . " files)",
                        "Consider concatenating CSS files to reduce HTTP requests",
                        "/* " . count($matches[0]) . " CSS files found */",
                        ['https://developer.mozilla.org/en-US/docs/Web/Performance/Optimizing_content_efficiency']
                    );
                    break; // Only report once
                }
            }

            // Check for images without loading optimization
            if (preg_match('/<img[^>]*src[^>]*>/', $line, $matches)) {
                $imgTag = $matches[0];
                if (!preg_match('/loading\s*=\s*["\']lazy["\']/', $imgTag)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_LOW,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Image without lazy loading",
                        "Add loading='lazy' attribute for better performance",
                        trim($line),
                        ['https://developer.mozilla.org/en-US/docs/Web/Performance/Lazy_loading']
                    );
                }
            }

            // Check for missing preload for critical resources
            if (preg_match('/<link[^>]*href\s*=\s*["\'][^"\']*\.(woff2?|ttf)["\'][^>]*>/', $line)) {
                if (!preg_match('/rel\s*=\s*["\']preload["\']/', $line)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_PERFORMANCE,
                        Finding::SEVERITY_LOW,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Font file without preload",
                        "Add rel='preload' for critical font files",
                        trim($line),
                        ['https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types/preload']
                    );
                }
            }
        }

        return $findings;
    }

    /**
     * Get the types of files this analyzer can handle
     *
     * @return string[] Array of file extensions or patterns this analyzer supports
     */
    public function getSupportedFileTypes(): array
    {
        return ['css', 'js', 'html', 'php'];
    }

    /**
     * Get the analyzer name for identification
     *
     * @return string Name of the analyzer
     */
    public function getName(): string
    {
        return 'Frontend Code Analyzer';
    }

    /**
     * Check if file is in priority area
     *
     * @param string $filePath
     * @return bool
     */
    private function isPriorityArea(string $filePath): bool
    {
        $priorityPatterns = [
            'css/',
            'js/',
            'assets/',
            'admin/',
            'includes/',
            'index.php',
            'article.php',
            'category.php'
        ];

        foreach ($priorityPatterns as $pattern) {
            if (strpos($filePath, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }
}