<?php
require_once '../config.php'; // Includes DB constants, functions.php, session_start() etc.
require_once 'includes/auth_check.php'; // Check if admin is logged in
require_once '../includes/functions.php'; // General functions (like escape)

// --- Page Setup ---
$admin_page_title = 'Manage Categories & Tags';
$error_message = $_SESSION['error_message'] ?? null;
$success_message = $_SESSION['success_message'] ?? null;
unset($_SESSION['error_message'], $_SESSION['success_message']); // Clear messages after displaying

// --- Fetch Existing Data ---
$categories = [];
$tags = [];
$pdo = null; // Initialize PDO variable

try {
    // Ensure PDO connection exists
    if (!isset($pdo) || !$pdo instanceof PDO) {
         $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
         $options = [ PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC, PDO::ATTR_EMULATE_PREPARES => false, ];
         $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
    }

    // Fetch Categories
    $stmt_cat = $pdo->query("SELECT id, name FROM categories ORDER BY name ASC");
    $categories = $stmt_cat->fetchAll();

    // Fetch Tags
    $stmt_tag = $pdo->query("SELECT id, name FROM tags ORDER BY name ASC");
    $tags = $stmt_tag->fetchAll();

} catch (PDOException $e) {
    $error_message = "Database error fetching data: " . $e->getMessage();
    error_log("DB Error in manage_categories_tags.php: " . $e->getMessage());
} finally {
    $pdo = null; // Close connection
}


// Include the admin header
include 'includes/header.php';
?>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.13.5/cdn.min.js"></script>

<header class="h-16 bg-white border-b border-border flex items-center justify-between px-6 flex-shrink-0 sticky top-0 z-30">
    <h1 class="text-xl font-montserrat font-bold text-dark"><?php echo escape($admin_page_title); ?></h1>
    <div class="text-sm text-gray-600">
        Logged in as: <?php echo isset($_SESSION['admin_email']) ? escape($_SESSION['admin_email']) : 'Admin'; ?>
    </div>
</header>

<div class="p-6 space-y-6">

    <?php // --- Display Messages --- ?>
    <?php if ($success_message): ?>
        <div class="bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded-lg text-sm mb-4">
            <?php echo escape($success_message); ?>
        </div>
    <?php endif; ?>
    <?php if ($error_message): ?>
        <div class="bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm mb-4">
            <?php echo escape($error_message); ?>
        </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

        <?php // --- Categories Management --- ?>
        <div class="card">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-100 pb-2">Manage Categories</h2>

            <?php // Add Category Form ?>
            <form action="process_category_tag.php" method="POST" class="mb-6">
                <input type="hidden" name="action" value="add_category">
                <div class="mb-4">
                    <label for="category_name" class="block text-sm font-medium text-gray-700 mb-1">New Category Name:</label>
                    <input type="text" id="category_name" name="category_name" required class="form-input" placeholder="e.g., Recepti">
                </div>
                <button type="submit" class="btn">
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>
                    Add Category
                </button>
            </form>

            <?php // List Categories ?>
            <h3 class="text-md font-semibold text-gray-700 mb-3">Existing Categories</h3>
            <?php if (empty($categories)): ?>
                <p class="text-gray-500 text-sm">No categories found.</p>
            <?php else: ?>
                <ul class="space-y-2">
                    <?php foreach ($categories as $category): ?>
                        <li class="flex items-center justify-between bg-gray-50 p-2 rounded">
                            <span class="text-sm text-gray-800"><?php echo escape($category['name']); ?></span>
                            <form action="process_category_tag.php" method="POST" onsubmit="return confirm('Are you sure you want to delete this category? This cannot be undone.');">
                                <input type="hidden" name="action" value="delete_category">
                                <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                <button type="submit" class="btn-danger btn-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                                </button>
                            </form>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>

        <?php // --- Tags Management --- ?>
        <div class="card">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-100 pb-2">Manage Tags</h2>

             <?php // Add Tag Form ?>
            <form action="process_category_tag.php" method="POST" class="mb-6">
                <input type="hidden" name="action" value="add_tag">
                <div class="mb-4">
                    <label for="tag_name" class="block text-sm font-medium text-gray-700 mb-1">New Tag Name:</label>
                    <input type="text" id="tag_name" name="tag_name" required class="form-input" placeholder="e.g., brzi ručak">
                </div>
                <button type="submit" class="btn">
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>
                    Add Tag
                </button>
            </form>

            <?php // List Tags ?>
            <h3 class="text-md font-semibold text-gray-700 mb-3">Existing Tags</h3>
             <?php if (empty($tags)): ?>
                <p class="text-gray-500 text-sm">No tags found.</p>
            <?php else: ?>
                <ul class="space-y-2">
                    <?php foreach ($tags as $tag): ?>
                        <li class="flex items-center justify-between bg-gray-50 p-2 rounded">
                            <span class="text-sm text-gray-800"><?php echo escape($tag['name']); ?></span>
                             <form action="process_category_tag.php" method="POST" onsubmit="return confirm('Are you sure you want to delete this tag? This will remove it from all articles.');">
                                <input type="hidden" name="action" value="delete_tag">
                                <input type="hidden" name="tag_id" value="<?php echo $tag['id']; ?>">
                                <button type="submit" class="btn-danger btn-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                                </button>
                            </form>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>

    </div>

</div>

<?php
// Include the admin footer
include 'includes/footer.php';
?>