{"findings": {"../public_html\\image.php": [{"file": "../public_html\\image.php", "line": 21, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Debug mode enabled (error reporting/display_errors) can leak sensitive info in production", "recommendation": "Ensure debug is disabled on production environments", "codeSnippet": "ini_set('display_errors', 1);", "references": ["https://owasp.org/www-project-top-ten/2017/A6_2017-Security_Misconfiguration"]}, {"file": "../public_html\\image.php", "line": 22, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Debug mode enabled (error reporting/display_errors) can leak sensitive info in production", "recommendation": "Ensure debug is disabled on production environments", "codeSnippet": "error_reporting(E_ALL);", "references": ["https://owasp.org/www-project-top-ten/2017/A6_2017-Security_Misconfiguration"]}], "../public_html\\config.php": [{"file": "../public_html\\config.php", "line": 172, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\config.php", "line": 17, "type": "security", "severity": "high", "priority": "PRIORITY_AREA", "description": "Hardcoded database credential detected (HOST)", "recommendation": "Consider centralizing secrets and limiting file exposure; restrict file permissions", "codeSnippet": "define('DB_HOST', 'localhost'); define('DB_NAME', 'lakofino_cms'); define('DB_USER', 'lakofino_cms'); define('DB_PASS', 'zH_0$2t$3=rE[D]_'); define('DB_CHARSET', 'utf8mb4');", "references": ["https://owasp.org/www-community/cryptography/Secret_Management"]}, {"file": "../public_html\\config.php", "line": 40, "type": "security", "severity": "high", "priority": "PRIORITY_AREA", "description": "Potential exposed API key in constant DEEPSEEK_API_KEY", "recommendation": "Rotate the key if necessary and ensure repository access is controlled", "codeSnippet": "define('DEEPSEEK_API_KEY', '***********************************'); define('DEEPSEEK_API_URL', 'https://api.deepseek.com/chat/completions'); define('DEEPSEEK_MODEL', 'deepseek-chat');", "references": ["https://owasp.org/www-community/attacks/Source_Code_Disclosure"]}, {"file": "../public_html\\config.php", "line": 160, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Debug mode enabled (error reporting/display_errors) can leak sensitive info in production", "recommendation": "Ensure debug is disabled on production environments", "codeSnippet": "error_reporting(E_ALL); ini_set('display_errors', 1); // Keep 1 for dev, 0 for production", "references": ["https://owasp.org/www-project-top-ten/2017/A6_2017-Security_Misconfiguration"]}], "../public_html\\404.php": [], "../public_html\\admin\\adsense_form.php": [{"file": "../public_html\\admin\\adsense_form.php", "line": 1, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "POST form without CSRF protection", "recommendation": "Add CSRF token validation to prevent cross-site request forgery", "codeSnippet": "Form detected without CSRF token", "references": ["https://owasp.org/www-community/attacks/csrf"]}], "../public_html\\admin\\advertising.php": [{"file": "../public_html\\admin\\advertising.php", "line": 1, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "POST form without CSRF protection", "recommendation": "Add CSRF token validation to prevent cross-site request forgery", "codeSnippet": "Form detected without CSRF token", "references": ["https://owasp.org/www-community/attacks/csrf"]}], "../public_html\\admin\\ad_analytics.php": [], "../public_html\\admin\\ad_form.php": [], "../public_html\\admin\\analytics.php": [], "../public_html\\admin\\articles.php": [{"file": "../public_html\\admin\\articles.php", "line": 1, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "POST form without CSRF protection", "recommendation": "Add CSRF token validation to prevent cross-site request forgery", "codeSnippet": "Form detected without CSRF token", "references": ["https://owasp.org/www-community/attacks/csrf"]}], "../public_html\\admin\\article_form.php": [{"file": "../public_html\\admin\\article_form.php", "line": 1, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "POST form without CSRF protection", "recommendation": "Add CSRF token validation to prevent cross-site request forgery", "codeSnippet": "Form detected without CSRF token", "references": ["https://owasp.org/www-community/attacks/csrf"]}], "../public_html\\admin\\avatar_generator.php": [], "../public_html\\admin\\bulk_delete_articles.php": [], "../public_html\\admin\\deepseek_handler.php": [{"file": "../public_html\\admin\\deepseek_handler.php", "line": 27, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}], "../public_html\\admin\\delete_article.php": [], "../public_html\\admin\\fix_internal_links.php": [{"file": "../public_html\\admin\\fix_internal_links.php", "line": 1, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "POST form without CSRF protection", "recommendation": "Add CSRF token validation to prevent cross-site request forgery", "codeSnippet": "Form detected without CSRF token", "references": ["https://owasp.org/www-community/attacks/csrf"]}], "../public_html\\admin\\image_upload_handler.php": [], "../public_html\\admin\\includes\\auth_check.php": [{"file": "../public_html\\admin\\includes\\auth_check.php", "line": 4, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}], "../public_html\\admin\\includes\\footer.php": [], "../public_html\\admin\\includes\\header.php": [], "../public_html\\admin\\index.php": [], "../public_html\\admin\\internal_links.php": [{"file": "../public_html\\admin\\internal_links.php", "line": 1, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "POST form without CSRF protection", "recommendation": "Add CSRF token validation to prevent cross-site request forgery", "codeSnippet": "Form detected without CSRF token", "references": ["https://owasp.org/www-community/attacks/csrf"]}], "../public_html\\admin\\login.php": [{"file": "../public_html\\admin\\login.php", "line": 1, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "POST form without CSRF protection", "recommendation": "Add CSRF token validation to prevent cross-site request forgery", "codeSnippet": "Form detected without CSRF token", "references": ["https://owasp.org/www-community/attacks/csrf"]}, {"file": "../public_html\\admin\\login.php", "line": 3, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}], "../public_html\\admin\\logout.php": [{"file": "../public_html\\admin\\logout.php", "line": 2, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start(); // Start the session to access session variables", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}], "../public_html\\admin\\manage_categories_tags.php": [{"file": "../public_html\\admin\\manage_categories_tags.php", "line": 1, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "POST form without CSRF protection", "recommendation": "Add CSRF token validation to prevent cross-site request forgery", "codeSnippet": "Form detected without CSRF token", "references": ["https://owasp.org/www-community/attacks/csrf"]}, {"file": "../public_html\\admin\\manage_categories_tags.php", "line": 2, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "require_once '../config.php'; // Includes DB constants, functions.php, session_start() etc.", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}], "../public_html\\admin\\process_ad.php": [], "../public_html\\admin\\process_adsense.php": [], "../public_html\\admin\\process_article.php": [{"file": "../public_html\\admin\\process_article.php", "line": 19, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "require_once '../config.php'; // Includes DB constants, functions.php, session_start() etc.", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\admin\\process_article.php", "line": 52, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "if (session_status() == PHP_SESSION_NONE) session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\admin\\process_article.php", "line": 61, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "if (session_status() == PHP_SESSION_NONE) session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\admin\\process_article.php", "line": 78, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "if (session_status() == PHP_SESSION_NONE) { session_start(); }", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\admin\\process_article.php", "line": 430, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\admin\\process_article.php", "line": 498, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\admin\\process_article.php", "line": 531, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\admin\\process_article.php", "line": 554, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "if (session_status() == PHP_SESSION_NONE) session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\admin\\process_article.php", "line": 569, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "if (session_status() == PHP_SESSION_NONE) session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\admin\\process_article.php", "line": 12, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Debug mode enabled (error reporting/display_errors) can leak sensitive info in production", "recommendation": "Ensure debug is disabled on production environments", "codeSnippet": "// error_reporting(E_ALL);", "references": ["https://owasp.org/www-project-top-ten/2017/A6_2017-Security_Misconfiguration"]}, {"file": "../public_html\\admin\\process_article.php", "line": 13, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Debug mode enabled (error reporting/display_errors) can leak sensitive info in production", "recommendation": "Ensure debug is disabled on production environments", "codeSnippet": "// ini_set('display_errors', 1);", "references": ["https://owasp.org/www-project-top-ten/2017/A6_2017-Security_Misconfiguration"]}], "../public_html\\admin\\process_category_tag.php": [{"file": "../public_html\\admin\\process_category_tag.php", "line": 2, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "require_once '../config.php'; // Includes DB constants, functions.php, session_start() etc.", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\admin\\process_category_tag.php", "line": 14, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}], "../public_html\\admin\\process_settings.php": [], "../public_html\\admin\\settings.php": [], "../public_html\\admin\\update_db_engagement_tricks.php": [], "../public_html\\article.php": [], "../public_html\\category.php": [], "../public_html\\cookie-policy.php": [], "../public_html\\debug-timer.php": [], "../public_html\\includes\\ad_display.php": [], "../public_html\\includes\\ad_manager.php": [], "../public_html\\includes\\ad_targeting.php": [], "../public_html\\includes\\ad_tracking.php": [], "../public_html\\includes\\csrf.php": [{"file": "../public_html\\includes\\csrf.php", "line": 13, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\includes\\csrf.php", "line": 34, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}, {"file": "../public_html\\includes\\csrf.php", "line": 67, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}], "../public_html\\includes\\facebook-integration.php": [], "../public_html\\includes\\footer.php": [], "../public_html\\includes\\functions.php": [], "../public_html\\includes\\header.php": [{"file": "../public_html\\includes\\header.php", "line": 36, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "Session started without security configuration", "recommendation": "Configure session security settings (httponly, secure, samesite)", "codeSnippet": "session_start();", "references": ["https://owasp.org/www-community/controls/Session_Management_Cheat_Sheet"]}], "../public_html\\includes\\internal_links.php": [], "../public_html\\includes\\Parsedown.php": [], "../public_html\\includes\\security.php": [], "../public_html\\index.php": [], "../public_html\\kontakt.php": [{"file": "../public_html\\kontakt.php", "line": 1, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "POST form without CSRF protection", "recommendation": "Add CSRF token validation to prevent cross-site request forgery", "codeSnippet": "Form detected without CSRF token", "references": ["https://owasp.org/www-community/attacks/csrf"]}], "../public_html\\loading.php": [], "../public_html\\load_articles.php": [], "../public_html\\load_comments.php": [], "../public_html\\o_nama.php": [], "../public_html\\politika-kolacica.php": [], "../public_html\\politika-privatnosti.php": [], "../public_html\\popularno.php": [], "../public_html\\privacy-policy.php": [], "../public_html\\process_ad_impressions.php": [], "../public_html\\process_comment.php": [], "../public_html\\process_like.php": [], "../public_html\\process_page_views.php": [], "../public_html\\record_impression.php": [], "../public_html\\register.php": [{"file": "../public_html\\register.php", "line": 1, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "POST form without CSRF protection", "recommendation": "Add CSRF token validation to prevent cross-site request forgery", "codeSnippet": "Form detected without CSRF token", "references": ["https://owasp.org/www-community/attacks/csrf"]}], "../public_html\\search.php": [], "../public_html\\sitemap.php": [], "../public_html\\smrsaj-deepseek-api.php": [{"file": "../public_html\\smrsaj-deepseek-api.php", "line": 10, "type": "security", "severity": "high", "priority": "PRIORITY_AREA", "description": "Potential exposed API key in constant DEEPSEEK_API_KEY", "recommendation": "Rotate the key if necessary and ensure repository access is controlled", "codeSnippet": "define('DEEPSEEK_API_KEY', '***********************************');", "references": ["https://owasp.org/www-community/attacks/Source_Code_Disclosure"]}], "../public_html\\smrsaj.php": [], "../public_html\\track_click.php": [], "../public_html\\uslovi_koristenja.php": [], "../public_html\\wp-import\\cms-import.php": [{"file": "../public_html\\wp-import\\cms-import.php", "line": 1, "type": "security", "severity": "medium", "priority": "NON_PRIORITY", "description": "POST form without CSRF protection", "recommendation": "Add CSRF token validation to prevent cross-site request forgery", "codeSnippet": "Form detected without CSRF token", "references": ["https://owasp.org/www-community/attacks/csrf"]}], "../public_html\\wp-import\\image-helper.php": [], "../public_html\\assets\\css\\contest-timer.css": [], "../public_html\\assets\\css\\engagement-tricks.css": [], "../public_html\\assets\\css\\internal-links.css": [], "../public_html\\assets\\js\\contest-timer.js": [], "../public_html\\css\\avg-time-trick.css": [], "../public_html\\js\\ads.js": [], "../public_html\\js\\adsense-init.js": [], "../public_html\\js\\avg-time-trick.js": [], "../public_html\\js\\cookie-consent.js": [], "../public_html\\js\\engagement-tricks.js": [], "../public_html\\js\\reward-popup.js": [], "../public_html\\.htaccess": [], "../public_html\\includes\\.htaccess": []}, "statistics": {"totalFindings": 40, "criticalFindings": 0, "highFindings": 3, "mediumFindings": 37, "lowFindings": 0, "priorityAreaFindings": 5, "nonPriorityFindings": 35, "securityFindings": 40, "performanceFindings": 0, "qualityFindings": 0, "architectureFindings": 0, "filesWithIssues": 21, "optimalFiles": 63}, "fileStatus": {"../public_html\\image.php": "needs_change", "../public_html\\config.php": "needs_change", "../public_html\\404.php": "optimal", "../public_html\\admin\\adsense_form.php": "needs_change", "../public_html\\admin\\advertising.php": "needs_change", "../public_html\\admin\\ad_analytics.php": "optimal", "../public_html\\admin\\ad_form.php": "optimal", "../public_html\\admin\\analytics.php": "optimal", "../public_html\\admin\\articles.php": "needs_change", "../public_html\\admin\\article_form.php": "needs_change", "../public_html\\admin\\avatar_generator.php": "optimal", "../public_html\\admin\\bulk_delete_articles.php": "optimal", "../public_html\\admin\\deepseek_handler.php": "needs_change", "../public_html\\admin\\delete_article.php": "optimal", "../public_html\\admin\\fix_internal_links.php": "needs_change", "../public_html\\admin\\image_upload_handler.php": "optimal", "../public_html\\admin\\includes\\auth_check.php": "needs_change", "../public_html\\admin\\includes\\footer.php": "optimal", "../public_html\\admin\\includes\\header.php": "optimal", "../public_html\\admin\\index.php": "optimal", "../public_html\\admin\\internal_links.php": "needs_change", "../public_html\\admin\\login.php": "needs_change", "../public_html\\admin\\logout.php": "needs_change", "../public_html\\admin\\manage_categories_tags.php": "needs_change", "../public_html\\admin\\process_ad.php": "optimal", "../public_html\\admin\\process_adsense.php": "optimal", "../public_html\\admin\\process_article.php": "needs_change", "../public_html\\admin\\process_category_tag.php": "needs_change", "../public_html\\admin\\process_settings.php": "optimal", "../public_html\\admin\\settings.php": "optimal", "../public_html\\admin\\update_db_engagement_tricks.php": "optimal", "../public_html\\article.php": "optimal", "../public_html\\category.php": "optimal", "../public_html\\cookie-policy.php": "optimal", "../public_html\\debug-timer.php": "optimal", "../public_html\\includes\\ad_display.php": "optimal", "../public_html\\includes\\ad_manager.php": "optimal", "../public_html\\includes\\ad_targeting.php": "optimal", "../public_html\\includes\\ad_tracking.php": "optimal", "../public_html\\includes\\csrf.php": "needs_change", "../public_html\\includes\\facebook-integration.php": "optimal", "../public_html\\includes\\footer.php": "optimal", "../public_html\\includes\\functions.php": "optimal", "../public_html\\includes\\header.php": "needs_change", "../public_html\\includes\\internal_links.php": "optimal", "../public_html\\includes\\Parsedown.php": "optimal", "../public_html\\includes\\security.php": "optimal", "../public_html\\index.php": "optimal", "../public_html\\kontakt.php": "needs_change", "../public_html\\loading.php": "optimal", "../public_html\\load_articles.php": "optimal", "../public_html\\load_comments.php": "optimal", "../public_html\\o_nama.php": "optimal", "../public_html\\politika-kolacica.php": "optimal", "../public_html\\politika-privatnosti.php": "optimal", "../public_html\\popularno.php": "optimal", "../public_html\\privacy-policy.php": "optimal", "../public_html\\process_ad_impressions.php": "optimal", "../public_html\\process_comment.php": "optimal", "../public_html\\process_like.php": "optimal", "../public_html\\process_page_views.php": "optimal", "../public_html\\record_impression.php": "optimal", "../public_html\\register.php": "needs_change", "../public_html\\search.php": "optimal", "../public_html\\sitemap.php": "optimal", "../public_html\\smrsaj-deepseek-api.php": "needs_change", "../public_html\\smrsaj.php": "optimal", "../public_html\\track_click.php": "optimal", "../public_html\\uslovi_koristenja.php": "optimal", "../public_html\\wp-import\\cms-import.php": "needs_change", "../public_html\\wp-import\\image-helper.php": "optimal", "../public_html\\assets\\css\\contest-timer.css": "optimal", "../public_html\\assets\\css\\engagement-tricks.css": "optimal", "../public_html\\assets\\css\\internal-links.css": "optimal", "../public_html\\assets\\js\\contest-timer.js": "optimal", "../public_html\\css\\avg-time-trick.css": "optimal", "../public_html\\js\\ads.js": "optimal", "../public_html\\js\\adsense-init.js": "optimal", "../public_html\\js\\avg-time-trick.js": "optimal", "../public_html\\js\\cookie-consent.js": "optimal", "../public_html\\js\\engagement-tricks.js": "optimal", "../public_html\\js\\reward-popup.js": "optimal", "../public_html\\.htaccess": "optimal", "../public_html\\includes\\.htaccess": "optimal"}, "completedAt": "2025-08-11 17:11:51", "version": "1.0.0"}