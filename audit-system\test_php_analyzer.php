<?php

require_once 'vendor/autoload.php';

use AuditSystem\Analyzers\PHPAnalyzer;
use AuditSystem\Models\Finding;

echo "Testing PHP Analyzer...\n\n";

$analyzer = new PHPAnalyzer();

// Test 1: Basic functionality
echo "Test 1: Basic functionality\n";
echo "Supported file types: " . implode(', ', $analyzer->getSupportedFileTypes()) . "\n";
echo "Analyzer name: " . $analyzer->getName() . "\n\n";

// Test 2: Naming conventions
echo "Test 2: Naming conventions\n";
$badNamingCode = '<?php
class badClassName {
    function BadFunctionName() {
        $BadVariableName = "test";
        const badConstant = "value";
    }
}';

$findings = $analyzer->analyze('test.php', $badNamingCode);
$namingFindings = array_filter($findings, fn($f) => $f->type === Finding::TYPE_QUALITY && 
    strpos($f->description, 'should use') !== false);

echo "Found " . count($namingFindings) . " naming convention violations:\n";
foreach ($namingFindings as $finding) {
    echo "- Line {$finding->line}: {$finding->description}\n";
}
echo "\n";

// Test 3: Complexity
echo "Test 3: Function complexity\n";
$complexCode = '<?php
function complexFunction($a, $b, $c, $d, $e, $f) {
    if ($a > 0) {
        if ($b > 0) {
            if ($c > 0) {
                if ($d > 0) {
                    if ($e > 0) {
                        if ($f > 0) {
                            for ($i = 0; $i < 10; $i++) {
                                while ($i < 5) {
                                    foreach ($array as $item) {
                                        switch ($item) {
                                            case 1: echo "one"; break;
                                            case 2: echo "two"; break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}';

$findings = $analyzer->analyze('test.php', $complexCode);
$complexityFindings = array_filter($findings, fn($f) => 
    strpos($f->description, 'too many parameters') !== false ||
    strpos($f->description, 'cyclomatic complexity') !== false
);

echo "Found " . count($complexityFindings) . " complexity violations:\n";
foreach ($complexityFindings as $finding) {
    echo "- Line {$finding->line}: {$finding->description}\n";
}
echo "\n";

// Test 4: Architecture patterns
echo "Test 4: Architecture patterns\n";
$badArchitectureCode = '<?php
function badFunction() {
    global $database;
    $data = $_POST["data"];
    
    echo "<html><body>";
    echo "<h1>Results</h1>";
    echo "</body></html>";
}

include $_GET["file"] . ".php";
';

$findings = $analyzer->analyze('test.php', $badArchitectureCode);
$architectureFindings = array_filter($findings, fn($f) => 
    $f->type === Finding::TYPE_ARCHITECTURE
);

echo "Found " . count($architectureFindings) . " architecture violations:\n";
foreach ($architectureFindings as $finding) {
    echo "- Line {$finding->line}: {$finding->description}\n";
}
echo "\n";

// Test 5: Maintainability
echo "Test 5: Maintainability\n";
$maintainabilityCode = '<?php
// TODO: Fix this later
function undocumentedFunction() {
    @file_get_contents("somefile.txt"); // FIXME: Handle errors properly
    
    // Using deprecated function
    $result = mysql_connect("localhost", "user", "pass");
    
    return $result;
}

class UndocumentedClass {
    public function method() {
        return "test";
    }
}
';

$findings = $analyzer->analyze('test.php', $maintainabilityCode);
$maintainabilityFindings = array_filter($findings, fn($f) => 
    strpos($f->description, 'lacks documentation') !== false ||
    strpos($f->description, 'Technical debt') !== false ||
    strpos($f->description, 'Error suppression') !== false ||
    strpos($f->description, 'Deprecated function') !== false
);

echo "Found " . count($maintainabilityFindings) . " maintainability issues:\n";
foreach ($maintainabilityFindings as $finding) {
    echo "- Line {$finding->line}: {$finding->description}\n";
}
echo "\n";

// Test 6: Priority area detection
echo "Test 6: Priority area detection\n";
$priorityContent = '<?php class badClassName {}';

$priorityFindings = $analyzer->analyze('admin/test.php', $priorityContent);
$nonPriorityFindings = $analyzer->analyze('other/test.php', $priorityContent);

echo "Priority area file (admin/test.php): " . $priorityFindings[0]->priority . "\n";
echo "Non-priority file (other/test.php): " . $nonPriorityFindings[0]->priority . "\n\n";

// Test 7: Clean code
echo "Test 7: Clean code (should produce minimal findings)\n";
$cleanCode = '<?php
/**
 * Well-documented class following best practices
 */
class GoodExample
{
    private string $property;

    /**
     * Constructor with proper documentation
     * @param string $property The property value
     */
    public function __construct(string $property)
    {
        $this->property = $property;
    }

    /**
     * Simple method with clear purpose
     * @return string The property value
     */
    public function getProperty(): string
    {
        return $this->property;
    }
}
';

$findings = $analyzer->analyze('test.php', $cleanCode);
echo "Clean code findings: " . count($findings) . " (should be 0 or very few)\n\n";

echo "PHP Analyzer testing completed!\n";
echo "The analyzer successfully detects:\n";
echo "- Naming convention violations\n";
echo "- Function complexity issues\n";
echo "- Architecture pattern violations\n";
echo "- Maintainability problems\n";
echo "- Priority area classification\n";
echo "- Handles clean code appropriately\n";