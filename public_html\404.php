<?php
require_once 'config.php'; // Includes functions.php

// Set HTTP Response Code to 404
http_response_code(404);

// --- Set Meta Data for 404 Page ---
$page_title = '404 - Stranica nije pronađena';
$meta_description = 'Ups! Stranica koju ste tražili nije pronađena na ' . SITE_NAME . '.';
$og_title = $page_title;
$og_description = $meta_description;

// Use default OG image data for 404 pages
$ogImageData = getDefaultOgImageData();
$og_image_url = $ogImageData['url'];
$og_image_width = $ogImageData['width'];
$og_image_height = $ogImageData['height'];
$og_image_alt = $ogImageData['og_alt']; // Use the default OG alt

// Construct the URL that was requested (best guess)
$request_uri = $_SERVER['REQUEST_URI'] ?? '';
$og_url = rtrim(SITE_URL, '/') . '/' . ltrim($request_uri, '/');
$og_type = 'website'; // Standard type for error pages

// No specific keywords or tags for 404
$final_focus_keyword = '';
$final_tags_string = '';

// --- Include Header ---
// Make sure all variables needed by header.php are defined above
include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-16 md:py-24 text-center max-w-3xl flex flex-col items-center">

    <?php // Removed the SVG icon block ?>

    <h1 class="text-7xl md:text-9xl font-montserrat font-extrabold text-primary mb-6 leading-none">
        404
    </h1>

    <h2 class="text-2xl md:text-3xl font-montserrat font-semibold text-dark-contrast mb-6">
        Ups! Izgleda da se ovaj recept izgubio.
    </h2>

    <p class="text-base md:text-lg text-gray-darker mb-8">
        Ne možemo pronaći stranicu koju tražite. Možda je premještena, obrisana, ili ste unijeli pogrešnu adresu. Molimo provjerite URL ili se vratite na početnu stranicu.
    </p>

    <?php // Adding a visual divider ?>
    <hr class="w-1/4 md:w-1/5 border-t-2 border-secondary my-8">

    <a href="<?php echo SITE_URL; ?>" class="btn px-8 py-3 text-lg inline-flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
        Vrati se na početnu
    </a>

    <?php // Optional: Add a search form here if desired ?>
    <?php /*
    <div class="mt-12 max-w-md mx-auto w-full">
        <form action="<?php echo SITE_URL; ?>/search.php" method="GET" class="relative"> <?php // Update action URL if needed ?>
            <input type="text" name="query" placeholder="Ili pretražite ponovo..."
                   class="w-full bg-white border border-gray-300 rounded-full py-3 px-6 pr-12 focus:outline-none focus:ring-2 focus:ring-primary text-base text-dark-contrast shadow-sm">
            <button type="submit" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-primary" aria-label="Pretraži">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
            </button>
        </form>
    </div>
    */ ?>

</div>

<?php
// --- Include Footer ---
include 'includes/footer.php';
?>