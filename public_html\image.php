<?php
/**
 * Advanced Dynamic Image Processing Script
 *
 * This script handles on-demand image processing, resizing, format conversion,
 * and caching for optimal performance.
 *
 * URL Parameters:
 * - src: Path to the original image (required)
 * - w: Width (optional)
 * - h: Height (optional)
 * - q: Quality (optional, default: 85)
 * - f: Format (optional, default: auto-detect best format)
 * - fit: Fit method (optional: cover, contain, fill, default: cover)
 *
 * Example usage:
 * /image.php?src=/uploads/articles/my-image.jpg&w=800&h=600&q=85&f=webp
 */

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Basic configuration
define('CACHE_DIR', __DIR__ . '/cache/images');
define('UPLOAD_DIR', __DIR__ . '/uploads/images');

define('DEFAULT_QUALITY', 70); // Aggressively reduced quality for better compression
define('MAX_WIDTH', 2000);
define('MAX_HEIGHT', 2000);
define('ALLOWED_FORMATS', ['jpg', 'jpeg', 'png', 'webp', 'avif']);

// Create cache directory if it doesn't exist
if (!is_dir(CACHE_DIR)) {
    $result = @mkdir(CACHE_DIR, 0777, true);
    if (!$result) {
        error_log("Failed to create cache directory: " . CACHE_DIR);
        // Try to create with different permissions
        $result = @mkdir(CACHE_DIR, 0755, true);
        if (!$result) {
            error_log("Failed again to create cache directory with 755 permissions");
        }
    }
}

// Check if cache directory is writable
if (!is_writable(CACHE_DIR)) {
    error_log("Cache directory is not writable: " . CACHE_DIR);
}

// Helper function to check if WebP is supported by the browser
function isWebPSupported() {
    if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false) {
        return true;
    }
    return false;
}

// Helper function to check if AVIF is supported by the browser
function isAVIFSupported() {
    if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'image/avif') !== false) {
        return true;
    }
    return false;
}

// Helper function to determine the best output format
function getBestOutputFormat($originalFormat) {
    // If AVIF is supported and we have the capability, use it
    if (isAVIFSupported() && function_exists('imageavif')) {
        return 'avif';
    }

    // If WebP is supported and we have the capability, use it
    if (isWebPSupported() && function_exists('imagewebp')) {
        return 'webp';
    }

    // Otherwise, use the original format or default to JPEG
    return in_array(strtolower($originalFormat), ALLOWED_FORMATS) ? strtolower($originalFormat) : 'jpeg';
}

// Helper function to get the content type based on format
function getContentType($format) {
    switch (strtolower($format)) {
        case 'jpg':
        case 'jpeg':
            return 'image/jpeg';
        case 'png':
            return 'image/png';
        case 'webp':
            return 'image/webp';
        case 'avif':
            return 'image/avif';
        default:
            return 'image/jpeg';
    }
}

// Helper function to generate a cache key
function generateCacheKey($params) {
    return md5(json_encode($params));
}

// Helper function to get the file extension from a path
function getExtension($path) {
    return strtolower(pathinfo($path, PATHINFO_EXTENSION));
}

// Find the actual image file based on your website's structure
function findImageFile($src) {
    // Parse the source path
    $parts = explode('/', $src);

    // Check if we have at least a directory and a filename
    if (count($parts) < 2) {
        error_log("Invalid source path format: $src");
        return null;
    }

    // Get the directory (e.g., 'articles') and the base filename
    $directory = $parts[0];
    $baseFilename = $parts[1];

    // Check if the baseFilename contains size suffix (e.g., _xs, _ms)
    // If it does, we need to strip it to get the actual base filename
    $baseFilenameWithoutSuffix = preg_replace('/_[a-z]{2}$/', '', $baseFilename);

    // Define the common size suffixes used in your system
    $sizeSuffixes = ['xs', 'ss', 'ms', 'ls', 'fb'];

    // Define common extensions to check
    $extensions = ['webp', 'jpg', 'jpeg', 'png', 'gif'];

    // First, try to find the exact file as specified
    $exactPath = UPLOAD_DIR . '/' . $src;
    if (file_exists($exactPath)) {
        error_log("Found exact file: $exactPath");
        return $exactPath;
    }

    // Next, try to find any file with the base filename and any size suffix
    foreach ($sizeSuffixes as $suffix) {
        foreach ($extensions as $ext) {
            $testPath = UPLOAD_DIR . '/' . $directory . '/' . $baseFilenameWithoutSuffix . '_' . $suffix . '.' . $ext;
            if (file_exists($testPath)) {
                error_log("Found file with suffix: $testPath");
                return $testPath;
            }
        }
    }

    // If still not found, try looking for the file without any suffix
    foreach ($extensions as $ext) {
        $testPath = UPLOAD_DIR . '/' . $directory . '/' . $baseFilenameWithoutSuffix . '.' . $ext;
        if (file_exists($testPath)) {
            error_log("Found file without suffix: $testPath");
            return $testPath;
        }
    }

    // If we still haven't found the file, log the paths we tried
    error_log("Image not found: $src");
    error_log("Tried looking in: " . UPLOAD_DIR . '/' . $directory);

    return null;
}

// Main processing function
function processImage($params) {
    // Validate source
    if (empty($params['src'])) {
        header('HTTP/1.1 400 Bad Request');
        echo 'Error: Missing source image parameter';
        exit;
    }

    // Log the request for debugging
    error_log("Image request: " . json_encode($params));

    // Clean the source path to prevent directory traversal
    $src = str_replace('../', '', $params['src']);
    $src = ltrim($src, '/');

    // Find the actual image file
    $originalPath = findImageFile($src);

    if (!$originalPath || !file_exists($originalPath)) {
        header('HTTP/1.1 404 Not Found');
        echo 'Error: Image not found. Please check the path.';
        exit;
    }

    // Get original image info
    $originalInfo = getimagesize($originalPath);
    if ($originalInfo === false) {
        header('HTTP/1.1 400 Bad Request');
        echo 'Error: Invalid image file';
        exit;
    }

    $originalWidth = $originalInfo[0];
    $originalHeight = $originalInfo[1];
    $originalMime = $originalInfo['mime'];
    $originalFormat = getExtension($originalPath);

    // Determine target dimensions
    $width = isset($params['w']) ? min((int)$params['w'], MAX_WIDTH) : null;
    $height = isset($params['h']) ? min((int)$params['h'], MAX_HEIGHT) : null;

    // If neither width nor height is specified, use original dimensions
    if ($width === null && $height === null) {
        $width = $originalWidth;
        $height = $originalHeight;
    }
    // If only width is specified, calculate height to maintain aspect ratio
    else if ($height === null) {
        $height = round($width * ($originalHeight / $originalWidth));
    }
    // If only height is specified, calculate width to maintain aspect ratio
    else if ($width === null) {
        $width = round($height * ($originalWidth / $originalHeight));
    }

    // Determine quality
    $quality = isset($params['q']) ? min(max((int)$params['q'], 10), 100) : DEFAULT_QUALITY;

    // Determine output format
    $format = isset($params['f']) ? strtolower($params['f']) : getBestOutputFormat($originalFormat);
    if (!in_array($format, ALLOWED_FORMATS)) {
        $format = getBestOutputFormat($originalFormat);
    }

    // Determine fit method
    $fit = isset($params['fit']) ? strtolower($params['fit']) : 'cover';
    if (!in_array($fit, ['cover', 'contain', 'fill'])) {
        $fit = 'cover';
    }

    // Generate a cache key based on all parameters
    $cacheKey = generateCacheKey([
        'src' => $src,
        'w' => $width,
        'h' => $height,
        'q' => $quality,
        'f' => $format,
        'fit' => $fit
    ]);

    // Define cache path
    $cachePath = CACHE_DIR . '/' . $cacheKey . '.' . $format;

    // Check if the cached version exists
    if (file_exists($cachePath)) {
        // Set appropriate headers
        header('Content-Type: ' . getContentType($format));
        header('Content-Length: ' . filesize($cachePath));
        header('Cache-Control: public, max-age=31536000'); // Cache for 1 year
        header('Expires: ' . gmdate('D, d M Y H:i:s \G\M\T', time() + 31536000));

        // Output the cached file
        readfile($cachePath);
        exit;
    }

    // Load the original image
    $sourceImage = null;
    switch ($originalMime) {
        case 'image/jpeg':
            $sourceImage = imagecreatefromjpeg($originalPath);
            break;
        case 'image/png':
            $sourceImage = imagecreatefrompng($originalPath);
            break;
        case 'image/webp':
            if (function_exists('imagecreatefromwebp')) {
                $sourceImage = imagecreatefromwebp($originalPath);
            }
            break;
        case 'image/avif':
            if (function_exists('imagecreatefromavif')) {
                $sourceImage = imagecreatefromavif($originalPath);
            }
            break;
    }

    if (!$sourceImage) {
        header('HTTP/1.1 400 Bad Request');
        echo 'Error: Unsupported image format';
        exit;
    }

    // Create a new true color image
    $targetImage = imagecreatetruecolor($width, $height);

    // Preserve transparency for PNG images
    if ($originalMime === 'image/png') {
        imagealphablending($targetImage, false);
        imagesavealpha($targetImage, true);
        $transparent = imagecolorallocatealpha($targetImage, 255, 255, 255, 127);
        imagefilledrectangle($targetImage, 0, 0, $width, $height, $transparent);
    }

    // Calculate dimensions based on fit method
    $srcX = 0;
    $srcY = 0;
    $srcWidth = $originalWidth;
    $srcHeight = $originalHeight;
    $dstX = 0;
    $dstY = 0;
    $dstWidth = $width;
    $dstHeight = $height;

    if ($fit === 'cover') {
        // Cover: Scale and crop to fill the target dimensions
        $scale = max($width / $originalWidth, $height / $originalHeight);
        $newWidth = round($originalWidth * $scale);
        $newHeight = round($originalHeight * $scale);
        $srcX = 0;
        $srcY = 0;
        $srcWidth = $originalWidth;
        $srcHeight = $originalHeight;
        $dstX = round(($width - $newWidth) / 2);
        $dstY = round(($height - $newHeight) / 2);
        $dstWidth = $newWidth;
        $dstHeight = $newHeight;

        // Adjust source coordinates for cropping
        if ($newWidth > $width) {
            $srcX = round(($newWidth - $width) / $scale / 2);
            $srcWidth = round($width / $scale);
        }
        if ($newHeight > $height) {
            $srcY = round(($newHeight - $height) / $scale / 2);
            $srcHeight = round($height / $scale);
        }

        $dstX = 0;
        $dstY = 0;
        $dstWidth = $width;
        $dstHeight = $height;
    } else if ($fit === 'contain') {
        // Contain: Scale to fit within the target dimensions
        $scale = min($width / $originalWidth, $height / $originalHeight);
        $newWidth = round($originalWidth * $scale);
        $newHeight = round($originalHeight * $scale);
        $dstX = round(($width - $newWidth) / 2);
        $dstY = round(($height - $newHeight) / 2);
        $dstWidth = $newWidth;
        $dstHeight = $newHeight;
    }
    // 'fill' just stretches the image to the target dimensions

    // Resize the image
    imagecopyresampled(
        $targetImage, $sourceImage,
        $dstX, $dstY, $srcX, $srcY,
        $dstWidth, $dstHeight, $srcWidth, $srcHeight
    );

    // Save the processed image to cache
    switch ($format) {
        case 'jpg':
        case 'jpeg':
            imagejpeg($targetImage, $cachePath, $quality);
            break;
        case 'png':
            // PNG quality is 0-9, convert from 0-100
            $pngQuality = round(9 - (($quality / 100) * 9));
            imagepng($targetImage, $cachePath, $pngQuality);
            break;
        case 'webp':
            if (function_exists('imagewebp')) {
                imagewebp($targetImage, $cachePath, $quality);
            } else {
                // Fallback to JPEG if WebP is not supported
                $format = 'jpeg';
                $cachePath = CACHE_DIR . '/' . $cacheKey . '.jpg';
                imagejpeg($targetImage, $cachePath, $quality);
            }
            break;
        case 'avif':
            if (function_exists('imageavif')) {
                imageavif($targetImage, $cachePath, $quality);
            } else {
                // Fallback to WebP or JPEG if AVIF is not supported
                if (function_exists('imagewebp')) {
                    $format = 'webp';
                    $cachePath = CACHE_DIR . '/' . $cacheKey . '.webp';
                    imagewebp($targetImage, $cachePath, $quality);
                } else {
                    $format = 'jpeg';
                    $cachePath = CACHE_DIR . '/' . $cacheKey . '.jpg';
                    imagejpeg($targetImage, $cachePath, $quality);
                }
            }
            break;
    }

    // Free up memory
    imagedestroy($sourceImage);
    imagedestroy($targetImage);

    // Set appropriate headers
    header('Content-Type: ' . getContentType($format));
    header('Content-Length: ' . filesize($cachePath));
    header('Cache-Control: public, max-age=31536000'); // Cache for 1 year
    header('Expires: ' . gmdate('D, d M Y H:i:s \G\M\T', time() + 31536000));

    // Output the processed image
    readfile($cachePath);
    exit;
}

// Process the image
processImage($_GET);

