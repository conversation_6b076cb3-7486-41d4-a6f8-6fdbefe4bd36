{"completedFiles": ["C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/test-cms\\config.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/test-cms\\test_security.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/test-cms\\script.js", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/test-cms\\style.css"], "pendingFiles": [], "findings": {"C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/test-cms\\config.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/test-cms\\config.php", "line": 2, "type": "security", "severity": "high", "priority": "PRIORITY_AREA", "description": "Hardcoded database credential detected (HOST)", "recommendation": "Consider centralizing secrets and limiting file exposure; restrict file permissions", "codeSnippet": "define(\"DB_HOST\", \"localhost\");", "references": ["https://owasp.org/www-community/cryptography/Secret_Management"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/test-cms\\config.php", "line": 3, "type": "security", "severity": "high", "priority": "PRIORITY_AREA", "description": "Hardcoded database credential detected (USER)", "recommendation": "Consider centralizing secrets and limiting file exposure; restrict file permissions", "codeSnippet": "define(\"DB_USER\", \"root\");", "references": ["https://owasp.org/www-community/cryptography/Secret_Management"]}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/test-cms\\config.php", "line": 4, "type": "security", "severity": "high", "priority": "PRIORITY_AREA", "description": "Hardcoded database credential detected (PASS)", "recommendation": "Consider centralizing secrets and limiting file exposure; restrict file permissions", "codeSnippet": "define(\"DB_PASS\", \"password123\");", "references": ["https://owasp.org/www-community/cryptography/Secret_Management"]}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/test-cms\\test_security.php": [], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/test-cms\\script.js": [], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../fixtures/test-cms\\style.css": []}, "lastUpdate": "2025-08-11 17:47:05", "currentPhase": "completed", "statistics": {"totalFiles": 4, "processedFiles": 4, "totalFindings": 3, "criticalFindings": 0, "priorityAreaFindings": 3}}