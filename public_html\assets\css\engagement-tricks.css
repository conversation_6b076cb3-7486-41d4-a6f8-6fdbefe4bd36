/*
 * Engagement Tricks CSS
 * Styles for various engagement tricks to keep users on articles longer
 */

/* Reward Popup Banner */
#reward-popup-banner {
    box-shadow: 0 -10px 25px -5px rgba(0, 0, 0, 0.1), 0 -8px 10px -6px rgba(0, 0, 0, 0.05);
    transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1);
    background: linear-gradient(135deg, #ff6481 0%, #ff8c9e 100%);
    z-index: 50;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    transform: translateY(100%);
    overflow: hidden;
}

#reward-popup-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.5) 100%);
    z-index: 1;
}

#reward-popup-banner::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
    pointer-events: none;
}

#reward-popup-banner.show {
    transform: translateY(0);
}

.reward-banner-content {
    position: relative;
    z-index: 2;
    padding: 1rem 1.5rem;
}

#reward-popup-banner h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 800;
    color: #ffffff;
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.01em;
}

#reward-popup-banner p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.reward-countdown-container {
    display: flex;
    align-items: center;
    margin: 0.75rem 0;
}

.reward-countdown-circle {
    position: relative;
    width: 60px;
    height: 60px;
    margin-right: 1rem;
}

.reward-countdown-circle svg {
    transform: rotate(-90deg);
    overflow: visible;
}

.reward-countdown-circle circle {
    fill: none;
    stroke-width: 5;
    stroke-linecap: round;
}

.reward-countdown-bg {
    stroke: rgba(255, 255, 255, 0.2);
}

.reward-countdown-progress {
    stroke: #ffffff;
    transition: stroke-dashoffset 1s linear;
}

.reward-countdown-circle.success .reward-countdown-progress {
    stroke: #4ade80; /* Green color for success */
}

.reward-countdown-number {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 1.25rem;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.reward-countdown-circle.success .reward-countdown-number {
    color: #4ade80; /* Green color for success */
}

.reward-countdown-text {
    flex: 1;
}

.reward-countdown-text p {
    font-size: 0.875rem;
    margin: 0;
    opacity: 0.9;
}

.reward-countdown-text .countdown-title {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

#reward-button-container {
    transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
    opacity: 0;
    transform: translateY(20px);
    display: none;
}

#reward-button-container.animate-fadeIn {
    opacity: 1;
    transform: translateY(0);
    display: block;
    animation: bounceIn 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}

#reward-popup-btn {
    background-color: #ffffff;
    color: #ff6481;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 1rem;
    transition: all 0.3s ease-in-out;
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: none;
    outline: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

#reward-popup-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: -1;
}

#reward-popup-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

#reward-popup-btn:hover::before {
    transform: translateX(100%);
}

#reward-popup-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

#reward-popup-btn.animate-pulse {
    animation: glowPulse 2s infinite;
}

.reward-gift-icon {
    position: absolute;
    top: -15px;
    right: -15px;
    width: 50px;
    height: 50px;
    background-color: #ffffff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    animation: float 3s ease-in-out infinite;
    z-index: 3;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

@keyframes glowPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    20% {
        transform: scale(1.1);
    }
    40% {
        transform: scale(0.9);
    }
    60% {
        opacity: 1;
        transform: scale(1.03);
    }
    80% {
        transform: scale(0.97);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Reward Popup Modal */
#reward-popup-modal {
    z-index: 60;
}

#reward-popup-modal .modal-overlay {
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease-out;
}

#reward-popup-modal .modal-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    position: relative;
    max-width: 450px;
    width: 90%;
    animation: modalSlideIn 0.5s cubic-bezier(0.16, 1, 0.3, 1);
    transform: translateY(0);
}

#reward-popup-modal .modal-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #ff6481 0%, #ff8c9e 100%);
}

#reward-popup-modal .modal-header {
    position: relative;
    padding: 1.5rem 1.5rem 0.5rem;
    text-align: center;
}

#reward-popup-modal .modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #ff6481 0%, #ff8c9e 100%);
    border-radius: 3px;
}

#reward-popup-modal .modal-gift-icon {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6481 0%, #ff8c9e 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(255, 100, 129, 0.3);
    z-index: 2;
    border: 3px solid white;
}

#reward-popup-modal h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 800;
    color: #333333;
    font-size: 1.75rem;
    margin: 1.5rem 0 0.5rem;
    position: relative;
    display: inline-block;
}

#reward-popup-modal .modal-body {
    padding: 1rem 1.5rem 1.5rem;
    text-align: center;
}

#reward-popup-modal .reward-message {
    background-color: rgba(255, 100, 129, 0.05);
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    position: relative;
    border: 1px dashed rgba(255, 100, 129, 0.2);
}

#reward-popup-modal .reward-message::before,
#reward-popup-modal .reward-message::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: #ff6481;
    opacity: 0.1;
    border-radius: 50%;
}

#reward-popup-modal .reward-message::before {
    top: -10px;
    left: -10px;
}

#reward-popup-modal .reward-message::after {
    bottom: -10px;
    right: -10px;
}

#reward-popup-modal p {
    color: #555555;
    line-height: 1.6;
    font-size: 1.0625rem;
    margin: 0;
}

#reward-popup-modal .reward-highlight {
    color: #ff6481;
    font-weight: 600;
}

#reward-popup-modal .modal-footer {
    padding: 0 1.5rem 1.5rem;
}

#reward-popup-modal a.reward-action-button {
    background: linear-gradient(135deg, #ff6481 0%, #ff8c9e 100%);
    color: white;
    transition: all 0.3s ease-in-out;
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    padding: 1rem 2rem;
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(255, 100, 129, 0.3);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    font-size: 1.125rem;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

#reward-popup-modal a.reward-action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    z-index: -1;
}

#reward-popup-modal a.reward-action-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 100, 129, 0.4);
}

#reward-popup-modal a.reward-action-button:hover::before {
    transform: translateX(100%);
}

#reward-popup-modal a.reward-action-button:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(255, 100, 129, 0.3);
}

#reward-popup-modal a.reward-action-button svg {
    margin-right: 0.75rem;
    width: 24px;
    height: 24px;
}

#reward-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: none;
    outline: none;
    cursor: pointer;
    z-index: 3;
}

#reward-modal-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: rotate(90deg);
}

#reward-modal-close svg {
    width: 16px;
    height: 16px;
    color: #666666;
}

@keyframes modalSlideIn {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Responsive Adjustments */
@media (max-width: 640px) {
    /* Banner adjustments */
    .reward-banner-content {
        padding: 0.75rem 1rem;
    }

    #reward-popup-banner h4 {
        font-size: 1.125rem;
        margin-bottom: 0.25rem;
    }

    #reward-popup-banner p {
        font-size: 0.875rem;
        line-height: 1.3;
    }

    .reward-countdown-circle {
        width: 50px;
        height: 50px;
        margin-right: 0.75rem;
    }

    .reward-countdown-number {
        font-size: 1.125rem;
    }

    .reward-countdown-text .countdown-title {
        font-size: 0.9375rem;
    }

    .reward-countdown-text p {
        font-size: 0.8125rem;
    }

    #reward-popup-btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.9375rem;
    }

    .reward-gift-icon {
        width: 40px;
        height: 40px;
        top: -10px;
        right: -10px;
    }

    /* Modal adjustments */
    #reward-popup-modal .modal-container {
        max-width: 320px;
        width: 95%;
    }

    #reward-popup-modal .modal-header {
        padding: 1.25rem 1.25rem 0.5rem;
    }

    #reward-popup-modal .modal-gift-icon {
        width: 50px;
        height: 50px;
        top: -25px;
    }

    #reward-popup-modal h3 {
        font-size: 1.5rem;
        margin: 1.25rem 0 0.5rem;
    }

    #reward-popup-modal .modal-body {
        padding: 0.75rem 1.25rem 1.25rem;
    }

    #reward-popup-modal .reward-message {
        padding: 1rem;
        margin-bottom: 1.25rem;
    }

    #reward-popup-modal p {
        font-size: 0.9375rem;
    }

    #reward-popup-modal .modal-footer {
        padding: 0 1.25rem 1.25rem;
    }

    #reward-popup-modal a.reward-action-button {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }

    #reward-modal-close {
        top: 0.75rem;
        right: 0.75rem;
        width: 28px;
        height: 28px;
    }

    #reward-modal-close svg {
        width: 14px;
        height: 14px;
    }
}
