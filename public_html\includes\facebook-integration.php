<?php
/**
 * Facebook Integration Functions
 * 
 * This file contains functions for integrating with Facebook.
 */

/**
 * Generate Facebook Pixel code
 * 
 * @param string $pixelId The Facebook Pixel ID
 * @return string The Facebook Pixel code
 */
function generateFacebookPixelCode($pixelId) {
    if (empty($pixelId)) {
        return '';
    }
    
    $code = "
<!-- Facebook Pixel Code -->
<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '{$pixelId}');
fbq('track', 'PageView');
</script>
<noscript>
<img height=\"1\" width=\"1\" style=\"display:none\"
src=\"https://www.facebook.com/tr?id={$pixelId}&ev=PageView&noscript=1\"/>
</noscript>
<!-- End Facebook Pixel Code -->
";
    
    return $code;
}

/**
 * Generate Facebook SDK code
 * 
 * @param string $appId The Facebook App ID
 * @return string The Facebook SDK code
 */
function generateFacebookSDKCode($appId) {
    if (empty($appId)) {
        return '';
    }
    
    $code = "
<!-- Facebook SDK -->
<div id=\"fb-root\"></div>
<script async defer crossorigin=\"anonymous\" src=\"https://connect.facebook.net/bs_BA/sdk.js#xfbml=1&version=v18.0&appId={$appId}\" nonce=\"random_nonce\"></script>
<!-- End Facebook SDK -->
";
    
    return $code;
}

/**
 * Generate Facebook Share Button
 * 
 * @param string $url The URL to share
 * @param string $text The button text
 * @return string The Facebook Share Button HTML
 */
function generateFacebookShareButton($url, $text = 'Podijeli na Facebook') {
    $html = "
<a href=\"https://www.facebook.com/sharer/sharer.php?u=" . urlencode($url) . "\" 
   target=\"_blank\" 
   rel=\"noopener noreferrer\" 
   class=\"fb-share-button flex items-center justify-center px-4 py-2.5 md:py-3 rounded-lg border border-blue-300 bg-blue-600 text-white hover:bg-blue-700 transition-all duration-300 shadow-sm text-sm\">
    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 md:h-5 md:w-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">
        <path d=\"M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z\"/>
    </svg>
    " . htmlspecialchars($text) . "
</a>";
    
    return $html;
}

/**
 * Generate Facebook Comments Plugin
 * 
 * @param string $url The URL for comments
 * @param int $width The width of the comments plugin
 * @param int $numPosts The number of posts to show
 * @return string The Facebook Comments Plugin HTML
 */
function generateFacebookCommentsPlugin($url, $width = 100, $numPosts = 5) {
    $html = "
<div class=\"fb-comments\" 
     data-href=\"" . htmlspecialchars($url) . "\" 
     data-width=\"" . (int)$width . "%\" 
     data-numposts=\"" . (int)$numPosts . "\"
     data-order-by=\"social\"
     data-colorscheme=\"light\"></div>";
    
    return $html;
}

/**
 * Track a Facebook event
 * 
 * @param string $eventName The event name
 * @param array $parameters The event parameters
 * @return string The JavaScript code to track the event
 */
function trackFacebookEvent($eventName, $parameters = []) {
    $parametersJson = json_encode($parameters);
    
    $js = "
<script>
if (typeof fbq !== 'undefined') {
    fbq('track', '{$eventName}', {$parametersJson});
}
</script>";
    
    return $js;
}
