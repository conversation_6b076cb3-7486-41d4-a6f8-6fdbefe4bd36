<?php
require_once 'config.php'; // Includes functions.php
require_once 'includes/ad_display.php'; // Include ad display functions if needed
require_once 'includes/ad_manager.php'; // Include ad manager if needed
require_once 'includes/ad_tracking.php'; // Include ad tracking if needed

// --- Input Handling ---
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;
$limit = ARTICLES_PER_PAGE;
$offset = ($page - 1) * $limit;

// Define Ad Context for Loaded Articles
// Adjust 'page_type' if this script is used on different page types (e.g., category archives)
$adContext = [
    'page_type' => 'archive_load', // Type of page where these loaded articles appear
    'source_page_id' => null, // Set if loading within a specific category/tag page
];

$articles = [];
$htmlOutput = '';
$moreArticlesAvailable = false;
$error_message = null;

// Helper function (ideally move to functions.php)
function renderArticleCard($article, $adContext) {
    $randomReaders = rand(50, 150);
    $articleUrl = SITE_URL . '/' . escape($article['slug']) . '/';
    $authorAvatarData = getFeaturedImageUrl($article['author_avatar'], 'ss', 'articles', escape($article['author_name'] ?? 'Autor'));
    $authorAvatarUrl = $authorAvatarData['url'];
    ob_start();
    ?>
     <article class="card p-0 overflow-hidden max-h-[245px] w-full article-card-desktop loaded-item" x-data="{ readers: <?php echo $randomReaders; ?>, animating: false }" x-init="setTimeout(() => { readers += Math.floor(Math.random() * 5) + 1; animating = true; setTimeout(() => animating = false, 1500) }, Math.random() * 8000 + 3000)">
         <a href="<?php echo $articleUrl; ?>" class="w-[200px] relative overflow-hidden m-[10px] z-[1] min-h-[200px] flex-shrink-0 rounded-lg block bg-gray-100">
            <?php if (!empty($article['featured_image'])): ?>
                <?php
                // Use our responsive image helper for article thumbnails
                echo getResponsiveImageHtml(
                    $article['featured_image'],
                    'articles',
                    escape($article['title']),
                    [
                        'widths' => ['xs', 'ss'], // Use xs and ss sizes for article thumbnails
                        'isPrimary' => false,
                        'lazyLoad' => true,
                        'containerClass' => '',
                        'imgClass' => 'absolute inset-0 w-full h-full object-cover',
                        'sizesAttr' => '200px',
                        'usePicture' => false
                    ]
                );
                ?>
            <?php else: ?>
             <div class="absolute inset-0 flex items-center justify-center bg-gray-200"><span class="text-gray-500 text-xs italic">Slika nedostupna</span></div>
            <?php endif; ?>
         </a>
         <div class="w-3/5 p-5 flex flex-col justify-between">
             <div>
                 <div class="flex justify-between items-center mb-2"><span class="text-xs text-gray-medium"><?php echo formatDate($article['published_at'] ?? $article['created_at']); ?></span></div>
                 <h3 class="text-xl font-montserrat font-extrabold mb-2 line-clamp-2"><a href="<?php echo $articleUrl; ?>" class="hover:text-primary transition-colors article-title text-dark-contrast"><?php echo escape($article['title']); ?></a></h3>
                 <?php if (!empty($article['excerpt'])): ?><p class="text-gray-darker text-sm line-clamp-3"><?php echo escape($article['excerpt']); ?></p><?php endif; ?>
             </div>
             <div class="mt-3 flex justify-between items-center">
                 <div class="flex items-center">
                     <div class="w-8 h-8 rounded-full overflow-hidden mr-2 bg-gray-200 flex items-center justify-center">
                         <?php if ($authorAvatarUrl): ?>
                             <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo $authorAvatarData['alt']; ?>" class="w-full h-full object-cover" loading="lazy" width="32" height="32"/>
                         <?php else: ?>
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                         <?php endif; ?>
                     </div>
                     <span class="text-sm font-montserrat font-semibold text-dark-contrast"><?php echo escape($article['author_name'] ?? 'Nepoznat autor'); ?></span>
                 </div>
                 <div class="flex items-center text-xs text-gray-medium gap-4 flex-nowrap whitespace-nowrap">
                     <?php if (!empty($article['reading_time'])): ?>
                     <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span><?php echo escape($article['reading_time']); ?> min</span></div>
                     <?php endif; ?>
                     <div class="flex items-center text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg><span x-text="readers + ' trenutno čita'" :class="{'reader-pulse': animating}" class="relative inline-block"></span></div>
                 </div>
             </div>
         </div>
     </article>

     <article class="card article-card-mobile loaded-item" x-data="{ readers: <?php echo $randomReaders; ?>, animating: false }" x-init="setTimeout(() => { readers += Math.floor(Math.random() * 5) + 1; animating = true; setTimeout(() => animating = false, 1500) }, Math.random() * 8000 + 3000)">
         <a href="<?php echo $articleUrl; ?>" class="mobile-article-image rounded-t-xl block bg-gray-100">
             <?php if (!empty($article['featured_image'])): ?>
                 <?php
                 // Use our responsive image helper for mobile article thumbnails
                 echo getResponsiveImageHtml(
                     $article['featured_image'],
                     'articles',
                     escape($article['title']),
                     [
                         'widths' => ['xs', 'ss'], // Use xs and ss sizes for mobile thumbnails
                         'isPrimary' => false,
                         'lazyLoad' => true,
                         'containerClass' => '',
                         'imgClass' => 'absolute inset-0 w-full h-full object-cover',
                         'sizesAttr' => '100vw',
                         'usePicture' => false
                     ]
                 );
                 ?>
             <?php else: ?>
                 <div class="absolute inset-0 flex items-center justify-center bg-gray-200"><span class="text-gray-500 text-xs italic">Slika nedostupna</span></div>
             <?php endif; ?>
         </a>
         <div class="mobile-article-content">
             <div>
                 <div class="flex justify-between items-center mb-2"><span class="text-xs text-gray-medium"><?php echo formatDate($article['published_at'] ?? $article['created_at']); ?></span></div>
                 <h3 class="text-lg font-montserrat font-extrabold mb-2 line-clamp-2"><a href="<?php echo $articleUrl; ?>" class="hover:text-primary transition-colors article-title text-dark-contrast"><?php echo escape($article['title']); ?></a></h3>
                 <?php if (!empty($article['excerpt'])): ?><p class="text-gray-darker text-xs md:text-sm line-clamp-2"><?php echo escape($article['excerpt']); ?></p><?php endif; ?>
             </div>
             <div class="mt-3 flex flex-wrap justify-between items-center">
                 <div class="flex items-center mb-2 xs:mb-0">
                     <div class="w-6 h-6 rounded-full overflow-hidden mr-2 bg-gray-200 flex items-center justify-center">
                         <?php if ($authorAvatarUrl): ?>
                             <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo $authorAvatarData['alt']; ?>" class="w-full h-full object-cover" loading="lazy" width="24" height="24"/>
                         <?php else: ?>
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                         <?php endif; ?>
                     </div>
                     <span class="text-xs font-montserrat font-semibold text-dark-contrast"><?php echo escape($article['author_name'] ?? 'Nepoznat autor'); ?></span>
                 </div>
                 <div class="flex items-center text-xs text-gray-medium gap-3">
                     <?php if (!empty($article['reading_time'])): ?>
                     <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span><?php echo escape($article['reading_time']); ?> min</span></div>
                     <?php endif; ?>
                     <div class="flex items-center text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg><span x-text="readers" :class="{'reader-pulse': animating}" class="relative inline-block"></span></div>
                 </div>
             </div>
         </div>
     </article>
    <?php
    return ob_get_clean();
}


try {
    // Fetch articles (SQL remains the same)
    $sql = "SELECT
                a.id, a.title, a.slug, a.excerpt, a.featured_image, a.created_at, a.published_at, a.reading_time,
                au.name as author_name, au.avatar_url as author_avatar,
                c.name as category_name, c.slug as category_slug
            FROM articles a
            LEFT JOIN authors au ON a.author_id = au.id
            LEFT JOIN categories c ON a.category_id = c.id
            WHERE a.status = 'published' AND (a.published_at IS NULL OR a.published_at <= NOW())
            ORDER BY COALESCE(a.published_at, a.created_at) DESC
            LIMIT :limit OFFSET :offset";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // --- Prepare Data for JSON Response ---
    if (!empty($articles)) {
        foreach ($articles as $article) {
            // Pass the ad context to the rendering function
            $htmlOutput .= renderArticleCard($article, $adContext);

            // ** OPTIONAL: Insert ads between loaded articles **
            // Example: Insert an ad after every 3rd loaded article
            // static $articleCounter = 0;
            // $articleCounter++;
            // if ($articleCounter % 3 === 0) {
            //     // Define a specific click position for this ad
            //     $betweenAdContext = $adContext;
            //     $betweenAdContext['click_position'] = 'between_loaded_' . $articleCounter;
            //     $htmlOutput .= '<div class="my-6">'; // Add some spacing
            //     $htmlOutput .= displayAdsForPlacement($pdo, 'between_posts_ad', $betweenAdContext, 1);
            //     $htmlOutput .= '</div>';
            // }
        }
    }

    // Check if more articles might exist
    $moreArticlesAvailable = count($articles) === $limit;

} catch (PDOException $e) {
    error_log("Load More Articles DB Error: " . $e->getMessage());
    $error_message = 'Greška pri učitavanju članaka.';
}

// --- Send JSON Response ---
header('Content-Type: application/json');
if ($error_message && empty($articles)) {
     http_response_code(500);
}
echo json_encode([
    'html' => $htmlOutput,
    'moreAvailable' => $moreArticlesAvailable,
    'error' => $error_message
]);
exit;
?>

