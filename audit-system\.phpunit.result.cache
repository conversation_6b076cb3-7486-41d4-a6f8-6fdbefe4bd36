{"version": 1, "defects": {"AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testDetectsConfigSecurityVulnerabilities": 3, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testDetectsSQLInjectionVulnerabilities": 3, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testDetectsXSSVulnerabilities": 3, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testDetectsPerformanceBottlenecks": 4, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testDetectsCodeQualityIssues": 4, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testMinimizesFalsePositives": 3, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testEndToEndCMSAudit": 4, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testRegressionDetection": 3, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testPriorityAreaClassification": 4, "AuditLoggerTest::testBasicLogging": 4, "AuditLoggerTest::testErrorLogSeparation": 4, "AuditLoggerTest::testLoggingWithContext": 4, "AuditLoggerTest::testPerformanceLogging": 4, "AuditLoggerTest::testProgressLogging": 4, "AuditLoggerTest::testFileAnalysisLogging": 4, "AuditLoggerTest::testAnalyzerFailureLogging": 4, "AuditLoggerTest::testExceptionLogging": 4, "AuditLoggerTest::testErrorRecoveryLogging": 4, "AuditLoggerTest::testResourceUsageLogging": 4, "AuditLoggerTest::testCheckpointLogging": 4, "AuditLoggerTest::testGracefulDegradationLogging": 4, "AuditLoggerTest::testRecentLogsRetrieval": 4, "ErrorRecoveryServiceTest::testSkipFileStrategy": 3, "AuditSystem\\Tests\\Services\\FileScannerTest::testScanDirectory": 3, "AuditSystem\\Tests\\Services\\FileScannerTest::testFileCategories": 3, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testClassifyArchitectureIssueInCoreFile": 3, "AuditSystem\\Tests\\Services\\ProgressTrackerTest::testGetPendingFiles": 3, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testCssOverlySpecificSelectors": 3, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testJavaScriptSynchronousAjax": 3, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testHtmlTablesWithoutHeaders": 3, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testConfigurableOptions": 3, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerRealCodeTest::testConfigurationFileAnalysis": 4, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerRealCodeTest::testAdminFileAnalysis": 3, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerRealCodeTest::testLegacyCodeAnalysis": 3, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerRealCodeTest::testWellStructuredCodeProducesFewerFindings": 3, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsLargeImageDimensions": 4, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsJpegWithoutCompressionQuality": 4, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsLoopWithoutMemoryCleanup": 4, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testComplexPerformanceScenario": 4, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testStartAuditSuccess": 4, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testResumeAuditSuccess": 4, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testGetAuditStatusWithProgress": 4, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testValidateAuditEnvironmentWithoutAnalyzers": 3, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testFileAnalysisErrorHandling": 3, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testConfigurationOverrides": 4, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testProgressLogging": 4, "ExceptionHierarchyTest::testFileAccessExceptionFactoryMethods": 4, "ExceptionHierarchyTest::testAnalysisExceptionFactoryMethods": 4, "ExceptionHierarchyTest::testConfigurationExceptionFactoryMethods": 4, "ExceptionHierarchyTest::testMCPConnectionExceptionFactoryMethods": 4, "ExceptionHierarchyTest::testProgressExceptionFactoryMethods": 4, "ExceptionHierarchyTest::testReportExceptionFactoryMethods": 4, "ExceptionHierarchyTest::testSecurityExceptionFactoryMethods": 4, "ExceptionHierarchyTest::testGeneralAuditExceptionFactoryMethods": 4, "AuditSystem\\Tests\\Integration\\CompleteSystemIntegrationTest::testManualValidationAccuracy": 4, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testMemoryUsageDuringAudit": 4, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testValidateCommand": 3, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testArgumentParsing": 3, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testCustomConfigFile": 3, "AuditSystem\\Tests\\Integration\\CLIPerformanceTest::testLargeCodebasePerformance": 1, "AuditSystem\\Tests\\Integration\\CLIPerformanceTest::testMemoryUsage": 1, "ErrorHandlingIntegrationTest::testFileAccessErrorRecovery": 3, "ErrorHandlingIntegrationTest::testAnalysisErrorRecovery": 3, "ErrorHandlingIntegrationTest::testFileSizeLimitErrorRecovery": 4, "ErrorHandlingIntegrationTest::testConfigurationErrorHandling": 4, "ErrorHandlingIntegrationTest::testProgressCorruptionRecovery": 3, "ErrorHandlingIntegrationTest::testMCPConnectionErrorGracefulDegradation": 4, "ErrorHandlingIntegrationTest::testAuditResumeAfterFailure": 4, "ErrorHandlingIntegrationTest::testErrorRecoveryStatistics": 4, "ErrorHandlingIntegrationTest::testLogRotationDuringAudit": 4, "ErrorHandlingIntegrationTest::testResourceMonitoringDuringErrors": 4, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testCleanSecureCodeMinimalFindings": 3, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testProperPDOUsageNotFlagged": 4, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testProperInputValidationNotFlagged": 4, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testOptimizedCodeNotFlagged": 4, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testWellStructuredCodeNotFlagged": 4, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testConfigurationConstantsHandledCorrectly": 4, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testLegitimateFileOperationsNotOverFlagged": 4, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testComprehensiveFalsePositiveAnalysis": 4, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testFallbackWhenMCPUnavailable": 3, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testMultipleTechnologyValidation": 3, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testErrorHandlingAndRecovery": 4, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsN1QueryProblems": 1, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsMissingDatabaseIndexes": 4, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsInefficiientImageProcessing": 4, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsMissingCaching": 4, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsInefficiientAssetLoading": 4, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsMemoryUsageIssues": 4, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsSlowDatabaseQueries": 4, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsInefficiientFileOperations": 4, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testComprehensivePerformanceAnalysis": 4, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testPerformanceRegressionDetection": 4, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsHardcodedDatabaseCredentials": 3, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsExposedAPIKeys": 4, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsDebugModeEnabled": 4, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsInsufficientInputValidation": 3, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsXSSVulnerabilities": 3, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsSessionSecurityIssues": 4, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsFileInclusionVulnerabilities": 4, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsAuthenticationIssues": 4, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsCSRFVulnerabilities": 3, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testComprehensiveSecurityScan": 3, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testConflictingFlags": 4, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testAuditResumeWorkflow": 4, "AuditSystem\\Tests\\Integration\\CLIPerformanceTest::testProgressMonitoringAccuracy": 4, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testCheckCodeWithMCPSuccess": 3, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testCheckCodeWithMCPFailureFallback": 3, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testCheckCodeCaching": 3, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testFallbackValidationPHP": 3, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testFallbackValidationJavaScript": 3, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testFallbackValidationCSS": 3}, "times": {"AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testDetectsConfigSecurityVulnerabilities": 0.001, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testDetectsSQLInjectionVulnerabilities": 0.003, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testDetectsXSSVulnerabilities": 0, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testDetectsPerformanceBottlenecks": 0.059, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testDetectsCodeQualityIssues": 0.102, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testMinimizesFalsePositives": 0.006, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testEndToEndCMSAudit": 4.761, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testRegressionDetection": 0.001, "AuditSystem\\Tests\\Integration\\RealCMSValidationTest::testPriorityAreaClassification": 0.001, "AuditSystem\\Tests\\Integration\\CompleteSystemIntegrationTest::testSystemInitialization": 0.01, "AuditSystem\\Tests\\Integration\\CompleteSystemIntegrationTest::testSystemHealthCheck": 0.006, "AuditSystem\\Tests\\Integration\\CompleteSystemIntegrationTest::testCompleteAuditExecution": 36.228, "AuditSystem\\Tests\\Models\\FindingTest::testFindingCreation": 0, "AuditSystem\\Tests\\Models\\FindingTest::testFindingArrayConversion": 0, "AuditLoggerTest::testBasicLogging": 0.012, "AuditLoggerTest::testErrorLogSeparation": 0.01, "AuditLoggerTest::testLoggingWithContext": 0.007, "AuditLoggerTest::testPerformanceLogging": 0.007, "AuditLoggerTest::testProgressLogging": 0.007, "AuditLoggerTest::testFileAnalysisLogging": 0.013, "AuditLoggerTest::testAnalyzerFailureLogging": 0.008, "AuditLoggerTest::testExceptionLogging": 0.009, "AuditLoggerTest::testErrorRecoveryLogging": 0.011, "AuditLoggerTest::testResourceUsageLogging": 0.007, "AuditLoggerTest::testCheckpointLogging": 0.007, "AuditLoggerTest::testGracefulDegradationLogging": 0.008, "AuditLoggerTest::testLogRotation": 0.008, "AuditLoggerTest::testLogStatistics": 0.01, "AuditLoggerTest::testArchiveOldLogs": 0.007, "AuditLoggerTest::testRecentLogsRetrieval": 0.01, "AuditLoggerTest::testClearLogs": 0.007, "AuditLoggerTest::testLogDirectoryCreation": 0.002, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testCheckCodeWithMCPSuccess": 0.001, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testCheckCodeWithMCPFailureFallback": 0.001, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testCheckCodeCaching": 0.001, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testGetBestPractices": 0.001, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testGetBestPracticesWithFallback": 0, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testValidateFindings": 0.001, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testValidateFindingsWithNonFindingObjects": 0, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testClearCache": 0.001, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testGetCacheStats": 0, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testFallbackValidationPHP": 0.001, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testFallbackValidationJavaScript": 0.001, "AuditSystem\\Tests\\Services\\BestPracticesCheckerTest::testFallbackValidationCSS": 0.001, "ErrorRecoveryServiceTest::testFileAccessExceptionRecovery": 1.013, "ErrorRecoveryServiceTest::testAnalysisExceptionRecovery": 0.007, "ErrorRecoveryServiceTest::testMCPConnectionExceptionRecovery": 0.006, "ErrorRecoveryServiceTest::testConfigurationExceptionRecovery": 0.005, "ErrorRecoveryServiceTest::testProgressExceptionRecovery": 0.006, "ErrorRecoveryServiceTest::testReportExceptionRecovery": 0.006, "ErrorRecoveryServiceTest::testSecurityExceptionRecovery": 0.005, "ErrorRecoveryServiceTest::testMaxRetryAttemptsExceeded": 7.027, "ErrorRecoveryServiceTest::testGracefulDegradation": 0.002, "ErrorRecoveryServiceTest::testRecoveryStatistics": 3.032, "ErrorRecoveryServiceTest::testResetRecoveryCounters": 1.008, "ErrorRecoveryServiceTest::testRetryWithDelayStrategy": 1.017, "ErrorRecoveryServiceTest::testSkipFileStrategy": 0.008, "ErrorRecoveryServiceTest::testAlternativePathStrategy": 1.017, "ErrorRecoveryServiceTest::testMCPFallbackStrategy": 0.007, "AuditSystem\\Tests\\Services\\FileScannerTest::testScanDirectory": 0.023, "AuditSystem\\Tests\\Services\\FileScannerTest::testFileCategories": 0.025, "AuditSystem\\Tests\\Services\\FileScannerTest::testFileStatistics": 0.025, "AuditSystem\\Tests\\Services\\FileScannerTest::testFilterUnprocessedFiles": 0.029, "AuditSystem\\Tests\\Services\\FileScannerTest::testInvalidDirectory": 0.023, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testClassifySecurityFindingInPriorityArea": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testClassifyXSSVulnerabilityInUserFacingArea": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testClassifyPerformanceIssueInAdSystem": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testClassifyQualityIssueWithHighComplexity": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testClassifyArchitectureIssueInCoreFile": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testPriorityAreaDetection": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testNonPriorityAreaDetection": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testSeverityEscalationForSecurityInPriorityArea": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testPerformanceIssueInUserFacingArea": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testBatchClassifyFindings": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testGetPriorityStatistics": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testRecommendationGeneration": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testReferenceGeneration": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testQualitySeverityBasedOnComplexity": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testCriticalSecurityAlwaysPriority": 0, "AuditSystem\\Tests\\Services\\FindingClassifierTest::testUrgencyPrefixInRecommendations": 0, "AuditSystem\\Tests\\Services\\MCPClientTest::testConnection": 0, "AuditSystem\\Tests\\Services\\MCPClientTest::testDisconnection": 0, "AuditSystem\\Tests\\Services\\MCPClientTest::testGetCapabilities": 0, "AuditSystem\\Tests\\Services\\MCPClientTest::testGetBestPractices": 0, "AuditSystem\\Tests\\Services\\MCPClientTest::testValidateCode": 0, "AuditSystem\\Tests\\Services\\MCPClientTest::testValidateSecureCode": 0, "AuditSystem\\Tests\\Services\\MCPClientTest::testValidateInsecureCode": 0, "AuditSystem\\Tests\\Services\\MCPClientTest::testRequestWithoutConnection": 0, "AuditSystem\\Tests\\Services\\MCPClientTest::testUnknownMethod": 0, "AuditSystem\\Tests\\Services\\MCPClientTest::testSecurityGuidelines": 0, "AuditSystem\\Tests\\Services\\MCPClientTest::testPerformanceRecommendations": 0, "AuditSystem\\Tests\\Services\\ProgressTrackerTest::testLoadProgressReturnsNullWhenFileDoesNotExist": 0, "AuditSystem\\Tests\\Services\\ProgressTrackerTest::testSaveAndLoadProgress": 0.006, "AuditSystem\\Tests\\Services\\ProgressTrackerTest::testMarkFileCompleted": 0.01, "AuditSystem\\Tests\\Services\\ProgressTrackerTest::testResetProgress": 0.001, "AuditSystem\\Tests\\Services\\ProgressTrackerTest::testGetStatistics": 0.005, "AuditSystem\\Tests\\Services\\ProgressTrackerTest::testUpdatePhase": 0.009, "AuditSystem\\Tests\\Services\\ProgressTrackerTest::testGetPendingFiles": 0.009, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testGetSupportedFileTypes": 0.001, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testGetName": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzePhpConfigWithWeakPassword": 0.001, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzePhpConfigWithApiKeyInPlainText": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzePhpConfigWithDebugModeEnabled": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzePhpConfigWithInsecureSessionConfig": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzePhpConfigWithSecureSessionConfig": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzePhpConfigPerformanceSettings": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzePhpConfigEnvironmentIssues": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzePhpConfigSensitiveDataInComments": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzeHtaccessSecurityIssues": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzeHtaccessWithSecureConfig": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzeHtaccessPerformanceIssues": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzeHtaccessWithPerformanceOptimizations": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzeComposerJsonWithOutdatedPhp": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzeComposerJsonWithMissingSecurityPackage": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzeComposerJsonWithInvalidJson": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzeWithDisabledChecks": 0, "AuditSystem\\Tests\\Analyzers\\ConfigurationAnalyzerTest::testAnalyzeUnsupportedFileType": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testGetSupportedFileTypes": 0.001, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testGetName": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testCssOverlySpecificSelectors": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testCssIdSelectors": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testCssImportantUsage": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testCssVendorPrefixes": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testCssAccessibilityFontSize": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testCssResponsiveDesign": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testCssMissingMediaQueries": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testJavaScriptConsoleStatements": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testJavaScriptVarUsage": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testJavaScriptLooseEquality": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testJavaScriptGlobalVariables": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testJavaScriptDeprecatedJQuery": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testJavaScriptSynchronousAjax": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testJavaScriptDocumentWrite": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testJavaScriptMissingUseStrict": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testHtmlMissingAltAttributes": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testHtmlNonDescriptiveLinkText": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testHtmlFormInputsWithoutIds": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testHtmlMissingViewportMeta": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testHtmlInlineStyles": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testHtmlMissingLangAttribute": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testHtmlTablesWithoutHeaders": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testAssetLoadingCssOutsideHead": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testAssetLoadingJavaScriptInHead": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testAssetLoadingMultipleCssFiles": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testAssetLoadingImagesWithoutLazyLoading": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testAssetLoadingFontWithoutPreload": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testPriorityAreaDetection": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testCleanCssProducesMinimalFindings": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testCleanJavaScriptProducesMinimalFindings": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testCleanHtmlProducesMinimalFindings": 0, "AuditSystem\\Tests\\Analyzers\\FrontendAnalyzerTest::testConfigurableOptions": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerRealCodeTest::testConfigurationFileAnalysis": 0.001, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerRealCodeTest::testDatabaseFunctionAnalysis": 0.001, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerRealCodeTest::testAdminFileAnalysis": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerRealCodeTest::testImageProcessingAnalysis": 0.001, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerRealCodeTest::testLegacyCodeAnalysis": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerRealCodeTest::testWellStructuredCodeProducesFewerFindings": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerTest::testGetSupportedFileTypes": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerTest::testGetName": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerTest::testNamingConventions": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerTest::testComplexityChecks": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerTest::testLongFunction": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerTest::testCodeDuplication": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerTest::testArchitecturePatterns": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerTest::testMaintainabilityChecks": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerTest::testPriorityAreaDetection": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerTest::testCleanCodeProducesNoFindings": 0, "AuditSystem\\Tests\\Analyzers\\PHPAnalyzerTest::testConfigurableOptions": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testGetSupportedFileTypes": 0.001, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testGetName": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsNPlusOneQueryProblem": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsSelectStarQueries": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsQueriesWithoutWhereClause": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsInefficientsLikePatterns": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsOrderByWithoutLimit": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsHighQueryCount": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsBlockingJavaScript": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsMultipleCssFiles": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsLargeInlineStyles": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsImagesWithoutLazyLoading": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsFileOperationsWithoutCaching": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsApiCallsWithoutCaching": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsMissingHttpCacheHeaders": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsExpensiveOperationsWithoutCaching": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsImageProcessingWithoutWebP": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsLargeImageDimensions": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsImageUploadWithoutSizeValidation": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsJpegWithoutCompressionQuality": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsMultipleArrayMerge": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsFileGetContentsMemoryIssue": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsStringConcatenationInLoop": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsMemoryLimitModification": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testDetectsLoopWithoutMemoryCleanup": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testPriorityAreaDetection": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testNonPriorityAreaDetection": 0, "AuditSystem\\Tests\\Analyzers\\PerformanceAnalyzerTest::testComplexPerformanceScenario": 0, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testStartAuditSuccess": 0.005, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testStartAuditWithInvalidOptions": 0.001, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testStartAuditWhenAlreadyRunning": 0, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testResumeAuditSuccess": 0.001, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testResumeAuditWithoutProgress": 0, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testGetAuditStatusWithProgress": 0, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testGetAuditStatusWithoutProgress": 0, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testAnalyzerRegistration": 0, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testValidateAuditEnvironmentWithoutAnalyzers": 0, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testFileAnalysisErrorHandling": 0.001, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testAnalysisTimeout": 0, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testMemoryLimitHandling": 0, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testConfigurationOverrides": 0, "AuditSystem\\Tests\\Controllers\\AuditControllerTest::testProgressLogging": 0, "ExceptionHierarchyTest::testAllExceptionsExtendAuditException": 0.001, "ExceptionHierarchyTest::testAuditExceptionProvidesContext": 0, "ExceptionHierarchyTest::testFileAccessExceptionFactoryMethods": 0, "ExceptionHierarchyTest::testAnalysisExceptionFactoryMethods": 0, "ExceptionHierarchyTest::testConfigurationExceptionFactoryMethods": 0, "ExceptionHierarchyTest::testMCPConnectionExceptionFactoryMethods": 0, "ExceptionHierarchyTest::testProgressExceptionFactoryMethods": 0, "ExceptionHierarchyTest::testReportExceptionFactoryMethods": 0, "ExceptionHierarchyTest::testSecurityExceptionFactoryMethods": 0, "ExceptionHierarchyTest::testGeneralAuditExceptionFactoryMethods": 0, "ExceptionHierarchyTest::testExceptionChaining": 0, "AuditSystem\\Tests\\Integration\\CompleteSystemIntegrationTest::testReportGenerationAndAccuracy": 35.923, "AuditSystem\\Tests\\Integration\\CompleteSystemIntegrationTest::testAuditResumeCapability": 72.19, "AuditSystem\\Tests\\Integration\\CompleteSystemIntegrationTest::testPerformanceOptimization": 77.595, "AuditSystem\\Tests\\Integration\\CompleteSystemIntegrationTest::testErrorHandlingAndRecovery": 36.103, "AuditSystem\\Tests\\Integration\\CompleteSystemIntegrationTest::testAnalyzerIntegration": 0.153, "AuditSystem\\Tests\\Integration\\CompleteSystemIntegrationTest::testManualValidationAccuracy": 36.892, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testCompleteAuditWorkflow": 0.391, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testAuditResumeWorkflow": 0.054, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testAuditErrorHandling": 0.024, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testConfigurationValidation": 0.01, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testConcurrentAuditPrevention": 0.005, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testResumeWithoutProgress": 0.01, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testAuditWithInvalidTargetDirectory": 0.011, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testProgressTrackingAccuracy": 0.017, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testLoggingFunctionality": 0.023, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testPerformanceMetricsLogging": 0.017, "AuditSystem\\Tests\\Integration\\AuditControllerIntegrationTest::testMemoryUsageDuringAudit": 0.379, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testHelpCommand": 0.077, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testVersionCommand": 0.069, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testValidateCommand": 0.069, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testConfigCommand": 0.069, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testAuditExecution": 0.105, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testAuditResume": 0.827, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testStatusCommand": 0.077, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testVerboseOutput": 0.132, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testArgumentParsing": 0.068, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testInvalidCommand": 0.069, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testInvalidTargetDirectory": 0.07, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testConflictingFlags": 0.077, "AuditSystem\\Tests\\Integration\\CLIIntegrationTest::testCustomConfigFile": 0.07, "AuditSystem\\Tests\\Integration\\CLIPerformanceTest::testLargeCodebasePerformance": 0.002, "AuditSystem\\Tests\\Integration\\CLIPerformanceTest::testMemoryUsage": 0.003, "AuditSystem\\Tests\\Integration\\CLIPerformanceTest::testProgressMonitoringAccuracy": 0.326, "AuditSystem\\Tests\\Integration\\CLIPerformanceTest::testConcurrentStatusChecking": 0.13, "AuditSystem\\Tests\\Integration\\CLIPerformanceTest::testArgumentParsingPerformance": 0.383, "ErrorHandlingIntegrationTest::testFileAccessErrorRecovery": 0.055, "ErrorHandlingIntegrationTest::testAnalysisErrorRecovery": 0.047, "ErrorHandlingIntegrationTest::testFileSizeLimitErrorRecovery": 0.046, "ErrorHandlingIntegrationTest::testConfigurationErrorHandling": 0.023, "ErrorHandlingIntegrationTest::testProgressCorruptionRecovery": 0.046, "ErrorHandlingIntegrationTest::testMCPConnectionErrorGracefulDegradation": 0.059, "ErrorHandlingIntegrationTest::testAuditResumeAfterFailure": 0.106, "ErrorHandlingIntegrationTest::testErrorRecoveryStatistics": 0.047, "ErrorHandlingIntegrationTest::testLogRotationDuringAudit": 0.059, "ErrorHandlingIntegrationTest::testResourceMonitoringDuringErrors": 0.051, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testCleanSecureCodeMinimalFindings": 0.004, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testProperPDOUsageNotFlagged": 0.019, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testProperInputValidationNotFlagged": 0.018, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testOptimizedCodeNotFlagged": 0.002, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testWellStructuredCodeNotFlagged": 0.003, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testConfigurationConstantsHandledCorrectly": 0.015, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testLegitimateFileOperationsNotOverFlagged": 0.002, "AuditSystem\\Tests\\Integration\\FalsePositiveMinimizationTest::testComprehensiveFalsePositiveAnalysis": 0.005, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testMCPServerConnectivity": 0, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testEndToEndBestPracticesValidation": 0.001, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testBestPracticesRetrieval": 0.001, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testFindingEnhancementWithBestPractices": 0.001, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testCachingBehavior": 0.001, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testFallbackWhenMCPUnavailable": 0.001, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testMultipleTechnologyValidation": 0.007, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testBestPracticesForDifferentTechnologies": 0.013, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testConfigurationIntegration": 0.001, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testErrorHandlingAndRecovery": 0, "AuditSystem\\Tests\\Integration\\MCPIntegrationTest::testLargeBatchValidation": 0.007, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsN1QueryProblems": 0, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsMissingDatabaseIndexes": 0.046, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsInefficiientImageProcessing": 0.056, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsMissingCaching": 0.058, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsInefficiientAssetLoading": 0.017, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsMemoryUsageIssues": 0.048, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsSlowDatabaseQueries": 0.049, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testDetectsInefficiientFileOperations": 0.046, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testComprehensivePerformanceAnalysis": 0.106, "AuditSystem\\Tests\\Integration\\PerformanceBottleneckValidationTest::testPerformanceRegressionDetection": 0.054, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsHardcodedDatabaseCredentials": 0.001, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsExposedAPIKeys": 0, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsDebugModeEnabled": 0, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsInsufficientInputValidation": 0, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsXSSVulnerabilities": 0.001, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsSessionSecurityIssues": 0.001, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsFileInclusionVulnerabilities": 0.004, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsAuthenticationIssues": 0.001, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testDetectsCSRFVulnerabilities": 0, "AuditSystem\\Tests\\Integration\\SecurityVulnerabilityValidationTest::testComprehensiveSecurityScan": 0.004}}