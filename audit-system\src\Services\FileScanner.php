<?php

namespace AuditSystem\Services;

use AuditSystem\Config\AuditConfig;

/**
 * File discovery and categorization system
 */
class FileScanner
{
    private AuditConfig $config;
    private array $supportedExtensions;
    private array $excludePatterns;
    private int $maxFileSize;

    public function __construct(AuditConfig $config)
    {
        $this->config = $config;
        $this->supportedExtensions = $config->get('file_filters.include_extensions', ['php', 'html', 'css', 'js', 'htaccess']);
        $this->excludePatterns = $config->get('file_filters.exclude_patterns', []);
        $this->maxFileSize = $config->get('file_filters.max_file_size', 1048576);
    }

    /**
     * Scan directory recursively and return categorized files
     *
     * @param string $directory Target directory to scan
     * @return array Categorized file list
     */
    public function scanDirectory(string $directory): array
    {
        if (!is_dir($directory)) {
            throw new \InvalidArgumentException("Directory does not exist: {$directory}");
        }

        $files = $this->discoverFiles($directory);
        $categorized = $this->categorizeFiles($files);
        $prioritized = $this->identifyPriorityAreas($categorized);

        return $prioritized;
    }

    /**
     * Recursively discover all files in directory
     *
     * @param string $directory
     * @return array List of file paths
     */
    private function discoverFiles(string $directory): array
    {
        $files = [];
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($iterator as $file) {
            /** @var \SplFileInfo $file */
            if ($this->shouldIncludeFile($file)) {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * Check if file should be included in scan
     *
     * @param \SplFileInfo $file
     * @return bool
     */
    private function shouldIncludeFile(\SplFileInfo $file): bool
    {
        // Check file size
        if ($file->getSize() > $this->maxFileSize) {
            return false;
        }

        // Check extension
        $extension = strtolower($file->getExtension());
        if ($file->getFilename() === '.htaccess') {
            $extension = 'htaccess';
        }
        
        if (!in_array($extension, $this->supportedExtensions)) {
            return false;
        }

        // Check exclude patterns
        $relativePath = $this->getRelativePath($file->getPathname());
        foreach ($this->excludePatterns as $pattern) {
            if ($this->matchesPattern($relativePath, $pattern)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Categorize files by type and purpose
     *
     * @param array $files
     * @return array Categorized files
     */
    private function categorizeFiles(array $files): array
    {
        $categorized = [
            'php' => [],
            'frontend' => [],
            'config' => [],
            'assets' => [],
            'admin' => [],
            'includes' => [],
            'templates' => []
        ];

        foreach ($files as $file) {
            $category = $this->determineFileCategory($file);
            $categorized[$category][] = $file;
        }

        return $categorized;
    }

    /**
     * Determine the category of a file
     *
     * @param string $filePath
     * @return string Category name
     */
    private function determineFileCategory(string $filePath): string
    {
        $relativePath = $this->getRelativePath($filePath);
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $filename = basename($filePath);

        // Configuration files
        if ($filename === '.htaccess' || $filename === 'config.php' || $filename === 'robots.txt') {
            return 'config';
        }

        // Admin area
        if (strpos($relativePath, 'admin/') === 0) {
            return 'admin';
        }

        // Includes directory
        if (strpos($relativePath, 'includes/') === 0) {
            return 'includes';
        }

        // Assets
        if (strpos($relativePath, 'assets/') === 0) {
            return 'assets';
        }

        // By extension
        switch ($extension) {
            case 'php':
                return 'php';
            case 'css':
            case 'js':
            case 'html':
                return 'frontend';
            default:
                return 'assets';
        }
    }

    /**
     * Identify priority areas based on configuration
     *
     * @param array $categorizedFiles
     * @return array Files with priority marking
     */
    private function identifyPriorityAreas(array $categorizedFiles): array
    {
        $priorityPatterns = $this->config->get('priority_areas.patterns', []);
        $priorityDirectories = $this->config->get('priority_areas.directories', []);
        
        $result = [
            'priority_area' => [],
            'non_priority' => [],
            'categories' => $categorizedFiles
        ];

        foreach ($categorizedFiles as $category => $files) {
            foreach ($files as $file) {
                $relativePath = $this->getRelativePath($file);
                $isPriority = $this->isPriorityArea($relativePath, $priorityPatterns, $priorityDirectories);
                
                if ($isPriority) {
                    $result['priority_area'][] = [
                        'file' => $file,
                        'category' => $category,
                        'relativePath' => $relativePath
                    ];
                } else {
                    $result['non_priority'][] = [
                        'file' => $file,
                        'category' => $category,
                        'relativePath' => $relativePath
                    ];
                }
            }
        }

        return $result;
    }

    /**
     * Check if file is in priority area
     *
     * @param string $relativePath
     * @param array $patterns
     * @param array $directories
     * @return bool
     */
    private function isPriorityArea(string $relativePath, array $patterns, array $directories): bool
    {
        // Check patterns
        foreach ($patterns as $pattern) {
            if ($this->matchesPattern($relativePath, $pattern)) {
                return true;
            }
        }

        // Check directories
        foreach ($directories as $directory) {
            if (strpos($relativePath, $directory . '/') === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if path matches pattern (supports wildcards)
     *
     * @param string $path
     * @param string $pattern
     * @return bool
     */
    private function matchesPattern(string $path, string $pattern): bool
    {
        // Convert glob pattern to regex
        $regex = str_replace(
            ['*', '?'],
            ['.*', '.'],
            preg_quote($pattern, '/')
        );
        
        return preg_match("/^{$regex}$/", $path) === 1;
    }

    /**
     * Get relative path from absolute path
     *
     * @param string $absolutePath
     * @return string
     */
    private function getRelativePath(string $absolutePath): string
    {
        $targetDir = $this->config->get('audit.target_directory', 'public_html');
        $targetDir = realpath($targetDir);
        
        if ($targetDir && strpos($absolutePath, $targetDir) === 0) {
            return ltrim(substr($absolutePath, strlen($targetDir)), '/\\');
        }
        
        return basename($absolutePath);
    }

    /**
     * Get file statistics
     *
     * @param array $categorizedFiles
     * @return array Statistics
     */
    public function getFileStatistics(array $categorizedFiles): array
    {
        $stats = [
            'totalFiles' => 0,
            'priorityFiles' => count($categorizedFiles['priority_area'] ?? []),
            'nonPriorityFiles' => count($categorizedFiles['non_priority'] ?? []),
            'categories' => []
        ];

        if (isset($categorizedFiles['categories'])) {
            foreach ($categorizedFiles['categories'] as $category => $files) {
                $stats['categories'][$category] = count($files);
                $stats['totalFiles'] += count($files);
            }
        }

        return $stats;
    }

    /**
     * Filter files that haven't been processed yet
     *
     * @param array $files
     * @param array $processedFiles
     * @return array Unprocessed files
     */
    public function filterUnprocessedFiles(array $files, array $processedFiles): array
    {
        $filtered = [
            'priority_area' => [],
            'non_priority' => [],
            'categories' => []
        ];

        // Filter priority area files
        if (isset($files['priority_area'])) {
            foreach ($files['priority_area'] as $fileInfo) {
                if (!in_array($fileInfo['file'], $processedFiles)) {
                    $filtered['priority_area'][] = $fileInfo;
                }
            }
        }

        // Filter non-priority files
        if (isset($files['non_priority'])) {
            foreach ($files['non_priority'] as $fileInfo) {
                if (!in_array($fileInfo['file'], $processedFiles)) {
                    $filtered['non_priority'][] = $fileInfo;
                }
            }
        }

        // Filter categorized files
        if (isset($files['categories'])) {
            foreach ($files['categories'] as $category => $categoryFiles) {
                $filtered['categories'][$category] = array_filter(
                    $categoryFiles,
                    fn($file) => !in_array($file, $processedFiles)
                );
            }
        }

        return $filtered;
    }
}