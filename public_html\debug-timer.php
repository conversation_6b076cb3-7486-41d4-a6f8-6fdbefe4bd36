<?php
// Include configuration
require_once 'config.php';

// Set page variables
$page_title = 'Debug Contest Timer';
$meta_description = 'This is a debug page for the contest timer feature.';
$og_title = $page_title;
$og_description = $meta_description;
$current_page_type = 'article';

// Create a mock article array with the contest timer enabled
$article = [
    'id' => 9999,
    'title' => 'Debug Contest Timer',
    'enable_contest_timer' => 1 // Must be exactly 1 (integer), not true or any other value
];

// Include header
include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">Debug Contest Timer</h1>
    
    <div class="article-content">
        <p>This is a debug page for the contest timer feature. The timer should appear immediately.</p>
        
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
            <p class="font-bold">Debug Information</p>
            <p>Contest timer enabled: <?php echo $article['enable_contest_timer']; ?></p>
            <p>Data attribute value: <?php echo (isset($article['enable_contest_timer']) && $article['enable_contest_timer'] == 1) ? '1' : '0'; ?></p>
        </div>
        
        <div class="mb-4">
            <button id="force-show-timer" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Force Show Timer
            </button>
        </div>
        
        <div id="timer-debug" class="bg-gray-100 p-4 rounded mb-4">
            <h3 class="font-bold mb-2">Timer Debug Info:</h3>
            <pre id="debug-output" class="whitespace-pre-wrap bg-white p-2 rounded border">Waiting for timer...</pre>
        </div>
    </div>
</div>

<script>
// Add CSS and JS for the contest timer
document.addEventListener('DOMContentLoaded', function() {
    // Add CSS
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '<?php echo SITE_URL; ?>/assets/css/contest-timer.css';
    link.setAttribute('data-purpose', 'reader-engagement');
    document.head.appendChild(link);
    
    // Add JS with a delay
    setTimeout(function() {
        var script = document.createElement('script');
        script.src = '<?php echo SITE_URL; ?>/assets/js/contest-timer.js';
        script.setAttribute('data-purpose', 'reader-engagement');
        script.defer = true;
        document.head.appendChild(script);
        
        console.log("Contest timer script loaded");
    }, 500);
    
    // Debug button
    document.getElementById('force-show-timer').addEventListener('click', function() {
        var timerContainer = document.querySelector('.contest-timer-container');
        if (timerContainer) {
            timerContainer.style.opacity = '1';
            timerContainer.style.visibility = 'visible';
            timerContainer.style.transform = 'translateY(0)';
            timerContainer.style.display = 'block';
            timerContainer.style.zIndex = '9999';
            timerContainer.classList.add('visible');
            
            document.getElementById('debug-output').textContent = 'Timer forced to show at ' + new Date().toLocaleTimeString();
        } else {
            document.getElementById('debug-output').textContent = 'Timer container not found at ' + new Date().toLocaleTimeString();
        }
    });
    
    // Debug output
    setInterval(function() {
        var timerContainer = document.querySelector('.contest-timer-container');
        if (timerContainer) {
            var styles = window.getComputedStyle(timerContainer);
            var debugInfo = {
                'exists': true,
                'opacity': styles.opacity,
                'visibility': styles.visibility,
                'transform': styles.transform,
                'display': styles.display,
                'zIndex': styles.zIndex,
                'position': styles.position,
                'bottom': styles.bottom,
                'hasVisibleClass': timerContainer.classList.contains('visible'),
                'time': new Date().toLocaleTimeString()
            };
            document.getElementById('debug-output').textContent = JSON.stringify(debugInfo, null, 2);
        }
    }, 1000);
});
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
