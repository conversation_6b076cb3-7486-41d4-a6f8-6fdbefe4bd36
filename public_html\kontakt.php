<?php
// Include configuration
require_once 'config.php'; // Loads constants like SITE_NAME

// --- Define Site Name using the constant ---
$site_name = defined('SITE_NAME') ? SITE_NAME : 'Ime <PERSON>'; // Use constant

require_once 'includes/header.php';

// Set a specific page title
$page_title = "Kontakt";

// --- Simple Contact Form Processing ---
$message_sent = false;
$error_message = '';
$form_data = ['name' => '', 'email' => '', 'subject' => '', 'message' => '']; // Store form data for repopulation

// --- !!! VAŽNO: Postavite Vašu Email Adresu Ovdje !!! ---
// Ovdje unesite email adresu na koju želite primati poruke sa forme
$recipient_email = "<EMAIL>"; // <<<=== ZAMIJENITE OVO!

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['submit_contact'])) {
    // Sanitize and validate inputs
    $form_data['name'] = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
    $form_data['email'] = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL); // Sanitize first
    $form_data['subject'] = filter_input(INPUT_POST, 'subject', FILTER_SANITIZE_STRING);
    $form_data['message'] = filter_input(INPUT_POST, 'message', FILTER_SANITIZE_STRING);

    // Validate email format after sanitizing
    $validated_email = filter_var($form_data['email'], FILTER_VALIDATE_EMAIL);

    if (empty($recipient_email) || $recipient_email === "<EMAIL>") {
         $error_message = "Greška: Email adresa primaoca nije konfigurisana u kodu kontakt stranice.";
    } elseif ($form_data['name'] && $validated_email && $form_data['subject'] && $form_data['message']) {
        // Construct email headers
        $headers = "From: " . $form_data['name'] . " <" . $validated_email . ">\r\n";
        $headers .= "Reply-To: " . $validated_email . "\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion();
        $headers .= "X-Sender-IP: " . $_SERVER['REMOTE_ADDR'] . "\r\n"; // Add sender IP to headers

        // Prepare email body
        $email_subject = "Kontakt Forma [" . htmlspecialchars($site_name) . "]: " . $form_data['subject']; // Use site name in subject
        $email_body = "Imate novu poruku sa kontakt forme sajta " . htmlspecialchars($site_name) . ":\n\n";
        $email_body .= "Ime: " . $form_data['name'] . "\n";
        $email_body .= "Email: " . $validated_email . "\n";
        $email_body .= "Predmet: " . $form_data['subject'] . "\n";
        $email_body .= "Poruka:\n--------------------\n" . $form_data['message'] . "\n--------------------\n";
        $email_body .= "\nIP Adresa pošiljaoca: " . $_SERVER['REMOTE_ADDR'] . "\n";

        // Attempt to send email
        if (@mail($recipient_email, $email_subject, $email_body, $headers)) { // Use @ to suppress default mail() errors/warnings
            $message_sent = true;
            $form_data = ['name' => '', 'email' => '', 'subject' => '', 'message' => '']; // Clear form
        } else {
             // Check for common mail function issues
            if (!function_exists('mail')) {
                $error_message = "Greška: PHP mail() funkcija nije dostupna na ovom serveru.";
            } else {
                 $error_message = "Došlo je do greške prilikom slanja poruke. Server nije mogao poslati email. Molimo pokušajte ponovo kasnije ili nas kontaktirajte direktno putem emaila.";
                 // Log the error for debugging
                 error_log("Mail sending failed. To: $recipient_email, Subject: $email_subject");
            }
        }
    } else {
        $error_message = "Molimo popunite sva obavezna polja ispravno. Provjerite da li je email adresa validna.";
    }
}
// --- End Contact Form Processing ---

?>

<main class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6"><?php echo $page_title; ?></h1>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8"> <?php // Use grid layout ?>

        <div class="prose max-w-none bg-white p-6 rounded-lg shadow">
             <h2 class="text-2xl font-semibold mt-0 mb-4 border-b pb-2">Kontakt Informacije</h2> <?php // mt-0 for first heading ?>
             <p>Imate pitanje o nekom receptu, prijedlog za temu iz oblasti zdravlja ili prirodne medicine? Možda ste zainteresovani za saradnju ili oglašavanje?</p>
             <p>Rado ćemo čuti vaše mišljenje i odgovoriti na vaša pitanja. Koristite formu pored ili nas kontaktirajte direktno.</p>
             <p class="text-sm text-gray-600">Napomena: Ne pružamo medicinske savjete putem emaila ili telefona. Za zdravstvene probleme obratite se svom ljekaru.</p>

             <div class="mt-4 space-y-2"> <?php // Space between info items ?>
                 <p><strong>Email:</strong> <a href="mailto:<?php echo htmlspecialchars($recipient_email); ?>" class="text-indigo-600 hover:text-indigo-800"><?php echo htmlspecialchars($recipient_email); ?></a> <br><span class="text-sm text-red-600">(OBAVEZNO zamijenite email adresu u PHP kodu!)</span></p>
                 <p><strong>Adresa:</strong> [Ako imate fizičku adresu, dodajte je ovdje]</p>
                 <p><strong>Telefon:</strong> [Ako imate kontakt telefon, dodajte ga ovdje]</p>
                 <p><strong>Društvene mreže:</strong> [Linkovi ka vašim profilima, npr. Facebook, Instagram]</p>
             </div>
             <p class="mt-4">Trudimo se odgovoriti na upite vezane za rad sajta, saradnju ili oglašavanje u najkraćem mogućem roku.</p>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-2xl font-semibold mt-0 mb-4 border-b pb-2">Pošaljite Nam Poruku</h2>

            <?php if ($message_sent): ?>
                <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
                    <p class="font-bold">Hvala!</p>
                    <p>Vaša poruka je uspješno poslana.</p>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                    <p class="font-bold">Greška!</p>
                    <p><?php echo htmlspecialchars($error_message); ?></p>
                </div>
            <?php endif; ?>

            <?php if (!$message_sent): // Show form only if message hasn't been sent successfully ?>
            <form action="kontakt.php" method="POST" class="space-y-4">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Ime: <span class="text-red-500">*</span></label>
                    <input type="text" id="name" name="name" required value="<?php echo htmlspecialchars($form_data['name']); ?>"
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php echo ($error_message && !$form_data['name']) ? 'border-red-500' : ''; ?>">
                </div>
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Adresa: <span class="text-red-500">*</span></label>
                    <input type="email" id="email" name="email" required value="<?php echo htmlspecialchars($form_data['email']); ?>"
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php echo ($error_message && !$validated_email) ? 'border-red-500' : ''; ?>">
                </div>
                <div>
                    <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Predmet: <span class="text-red-500">*</span></label>
                    <input type="text" id="subject" name="subject" required value="<?php echo htmlspecialchars($form_data['subject']); ?>"
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php echo ($error_message && !$form_data['subject']) ? 'border-red-500' : ''; ?>">
                </div>
                <div>
                    <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Poruka: <span class="text-red-500">*</span></label>
                    <textarea id="message" name="message" rows="5" required
                              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm <?php echo ($error_message && !$form_data['message']) ? 'border-red-500' : ''; ?>"><?php echo htmlspecialchars($form_data['message']); ?></textarea>
                </div>
                <div>
                    <button type="submit" name="submit_contact"
                            class="inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                        Pošalji Poruku
                    </button>
                </div>
            </form>
             <p class="mt-6 text-xs text-gray-500">Polja označena sa <span class="text-red-500">*</span> su obavezna.</p>
            <?php endif; ?>

            <p class="mt-6 text-sm text-gray-600"><em>Napomena: Slanje emaila putem ove forme zavisi od PHP `mail()` funkcije na serveru. Ako forma ne radi pouzdano, razmislite o korištenju SMTP-a sa bibliotekom kao što je PHPMailer ili kontaktirajte hosting podršku.</em></p>
        </div>

    </div> <?php // End grid ?>
</main>

<?php
// Include footer
require_once 'includes/footer.php';
?>
