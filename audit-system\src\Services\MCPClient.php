<?php

namespace AuditSystem\Services;

use AuditSystem\Interfaces\MCPClientInterface;
use AuditSystem\Exceptions\MCPConnectionException;
use AuditSystem\Config\AuditConfig;

/**
 * MCP (Model Context Protocol) client for context7 server integration
 */
class MCPClient implements MCPClientInterface
{
    private string $serverUrl;
    private int $timeout;
    private bool $connected = false;
    private array $capabilities = [];
    private AuditConfig $config;
    private bool $forceConnectionFailure = false;

    /**
     * Create new MCP client instance
     *
     * @param AuditConfig|null $config Configuration instance
     */
    public function __construct(?AuditConfig $config = null)
    {
        $this->config = $config ?? AuditConfig::getInstance();
        $this->serverUrl = $this->config->get('mcp.server_url', 'context7');
        $this->timeout = $this->config->get('mcp.timeout', 30);
    }

    /**
     * Connect to the MCP server
     *
     * @return bool True if connection successful
     * @throws MCPConnectionException If connection fails
     */
    public function connect(): bool
    {
        if ($this->forceConnectionFailure) {
            $this->connected = false;
            throw MCPConnectionException::serverUnavailable($this->serverUrl);
        }

        try {
            // For context7 MCP server, we simulate connection by checking capabilities
            $this->capabilities = $this->getCapabilities();
            $this->connected = true;
            return true;
        } catch (Exception $e) {
            $this->connected = false;
            throw MCPConnectionException::serverUnavailable($this->serverUrl);
        }
    }

    /**
     * Disconnect from the MCP server
     *
     * @return void
     */
    public function disconnect(): void
    {
        $this->connected = false;
        $this->capabilities = [];
    }

    /**
     * Check if client is connected to server
     *
     * @return bool True if connected
     */
    public function isConnected(): bool
    {
        return $this->connected;
    }

    /**
     * Send a request to the MCP server
     *
     * @param string $method The method to call
     * @param array $params Parameters for the method
     * @return array Response from server
     * @throws MCPConnectionException If request fails
     */
    public function request(string $method, array $params = []): array
    {
        if (!$this->connected) {
            throw new MCPConnectionException('Not connected to MCP server');
        }

        try {
            // Simulate MCP request - in real implementation this would use actual MCP protocol
            $request = [
                'jsonrpc' => '2.0',
                'id' => uniqid(),
                'method' => $method,
                'params' => $params
            ];

            // For context7, we simulate the request based on method
            return $this->simulateRequest($method, $params);
        } catch (Exception $e) {
            throw new MCPConnectionException("MCP request failed: " . $e->getMessage());
        }
    }

    /**
     * Get best practices for a specific technology/area
     *
     * @param string $technology Technology or area (e.g., 'php-security', 'performance')
     * @param string $context Additional context for the request
     * @return array Best practices data
     */
    public function getBestPractices(string $technology, string $context = ''): array
    {
        return $this->request('get_best_practices', [
            'technology' => $technology,
            'context' => $context
        ]);
    }

    /**
     * Validate code against best practices
     *
     * @param string $code Code to validate
     * @param string $language Programming language
     * @param string $context Additional context
     * @return array Validation results
     */
    public function validateCode(string $code, string $language, string $context = ''): array
    {
        return $this->request('validate_code', [
            'code' => $code,
            'language' => $language,
            'context' => $context
        ]);
    }

    /**
     * Get server capabilities
     *
     * @return array Server capabilities
     */
    public function getCapabilities(): array
    {
        if (!empty($this->capabilities)) {
            return $this->capabilities;
        }

        // Simulate context7 capabilities
        return [
            'name' => 'context7',
            'version' => '1.0.0',
            'methods' => [
                'get_best_practices',
                'validate_code',
                'get_security_guidelines',
                'get_performance_recommendations'
            ],
            'technologies' => [
                'php',
                'javascript',
                'css',
                'html',
                'security',
                'performance',
                'accessibility'
            ]
        ];
    }

    /**
     * Force connection failure for testing
     *
     * @param bool $force Whether to force connection failure
     * @return void
     */
    public function forceConnectionFailure(bool $force = true): void
    {
        $this->forceConnectionFailure = $force;
        if ($force) {
            $this->connected = false;
        }
    }

    /**
     * Simulate MCP request for testing/development
     *
     * @param string $method Method name
     * @param array $params Parameters
     * @return array Simulated response
     */
    private function simulateRequest(string $method, array $params): array
    {
        switch ($method) {
            case 'get_best_practices':
                return $this->simulateBestPractices($params['technology'] ?? '', $params['context'] ?? '');
            
            case 'validate_code':
                return $this->simulateCodeValidation($params['code'] ?? '', $params['language'] ?? '');
            
            case 'get_security_guidelines':
                return $this->simulateSecurityGuidelines();
            
            case 'get_performance_recommendations':
                return $this->simulatePerformanceRecommendations();
            
            default:
                throw new MCPConnectionException("Unknown method: {$method}");
        }
    }

    /**
     * Simulate best practices response
     *
     * @param string $technology Technology area
     * @param string $context Context
     * @return array Best practices
     */
    private function simulateBestPractices(string $technology, string $context): array
    {
        $practices = [
            'php-security' => [
                'use_prepared_statements' => 'Always use PDO prepared statements for database queries',
                'validate_input' => 'Validate and sanitize all user input',
                'escape_output' => 'Escape output when displaying user data',
                'csrf_protection' => 'Implement CSRF protection for forms',
                'secure_sessions' => 'Use secure session configuration'
            ],
            'php-performance' => [
                'avoid_n_plus_one' => 'Avoid N+1 query problems with proper joins',
                'use_caching' => 'Implement appropriate caching strategies',
                'optimize_queries' => 'Use database indexes and optimize queries',
                'lazy_loading' => 'Implement lazy loading for large datasets'
            ],
            'javascript-performance' => [
                'minimize_dom_access' => 'Minimize DOM access and manipulation',
                'use_event_delegation' => 'Use event delegation for dynamic content',
                'optimize_loops' => 'Optimize loops and avoid unnecessary iterations',
                'async_loading' => 'Load scripts asynchronously when possible'
            ],
            'css-structure' => [
                'use_semantic_classes' => 'Use semantic class names',
                'avoid_deep_nesting' => 'Avoid deep CSS selector nesting',
                'mobile_first' => 'Use mobile-first responsive design approach',
                'optimize_selectors' => 'Use efficient CSS selectors'
            ]
        ];

        return [
            'technology' => $technology,
            'practices' => $practices[$technology] ?? [],
            'context' => $context,
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Simulate code validation response
     *
     * @param string $code Code to validate
     * @param string $language Programming language
     * @return array Validation results
     */
    private function simulateCodeValidation(string $code, string $language): array
    {
        $issues = [];

        if ($language === 'php') {
            // Check for common PHP security issues
            if (strpos($code, '$_GET') !== false || strpos($code, '$_POST') !== false) {
                if (strpos($code, 'filter_') === false && strpos($code, 'htmlspecialchars') === false) {
                    $issues[] = [
                        'type' => 'security',
                        'severity' => 'high',
                        'message' => 'Unvalidated user input detected',
                        'recommendation' => 'Use filter_input() or htmlspecialchars() to validate/escape user input'
                    ];
                }
            }

            if (strpos($code, 'mysql_query') !== false || strpos($code, 'mysqli_query') !== false) {
                if (strpos($code, 'prepare') === false) {
                    $issues[] = [
                        'type' => 'security',
                        'severity' => 'critical',
                        'message' => 'Potential SQL injection vulnerability',
                        'recommendation' => 'Use PDO prepared statements instead of direct queries'
                    ];
                }
            }
        }

        return [
            'language' => $language,
            'issues' => $issues,
            'score' => count($issues) === 0 ? 100 : max(0, 100 - (count($issues) * 20)),
            'validated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Simulate security guidelines response
     *
     * @return array Security guidelines
     */
    private function simulateSecurityGuidelines(): array
    {
        return [
            'owasp_top_10' => [
                'injection' => 'Prevent injection attacks using parameterized queries',
                'broken_auth' => 'Implement proper authentication and session management',
                'sensitive_data' => 'Protect sensitive data with encryption',
                'xxe' => 'Disable XML external entity processing',
                'broken_access' => 'Implement proper access controls',
                'security_misconfig' => 'Secure configuration management',
                'xss' => 'Prevent cross-site scripting attacks',
                'insecure_deserialization' => 'Validate serialized data',
                'known_vulnerabilities' => 'Keep components up to date',
                'insufficient_logging' => 'Implement comprehensive logging'
            ]
        ];
    }

    /**
     * Simulate performance recommendations response
     *
     * @return array Performance recommendations
     */
    private function simulatePerformanceRecommendations(): array
    {
        return [
            'database' => [
                'use_indexes' => 'Add appropriate database indexes',
                'optimize_queries' => 'Optimize slow queries',
                'connection_pooling' => 'Use connection pooling',
                'avoid_n_plus_one' => 'Eliminate N+1 query problems'
            ],
            'frontend' => [
                'minify_assets' => 'Minify CSS and JavaScript files',
                'optimize_images' => 'Optimize and compress images',
                'use_cdn' => 'Use CDN for static assets',
                'lazy_loading' => 'Implement lazy loading for images'
            ],
            'caching' => [
                'page_caching' => 'Implement page-level caching',
                'object_caching' => 'Use object caching for expensive operations',
                'browser_caching' => 'Set appropriate browser cache headers'
            ]
        ];
    }
}