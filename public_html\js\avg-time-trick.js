/**
 * avg-time-trick.js - AVG Time Trick Implementation
 *
 * This script implements the "AVG Time Trick" which shows a banner when the user
 * scrolls past 70% of the content, displays a countdown, and then shows a popup
 * directing the user to message the Facebook page.
 *
 * Features:
 * - Tracks scroll position
 * - Shows banner at 70% scroll
 * - Displays random countdown (55-60 seconds)
 * - Shows popup after countdown
 * - Hides from Facebook and Google AdSense crawlers
 */

document.addEventListener('DOMContentLoaded', function() {
    // Load the CSS file
    loadCssFile('/css/avg-time-trick.css');

    // Check if we should run the trick (don't run for bots/crawlers)
    if (shouldRunTrick()) {
        initAvgTimeTrick();
    }
});

/**
 * Load the CSS file dynamically
 */
function loadCssFile(href) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    document.head.appendChild(link);
}

/**
 * Determines if we should run the trick based on user agent
 * This helps hide the trick from Google AdSense crawlers
 */
function shouldRunTrick() {
    const userAgent = navigator.userAgent.toLowerCase();

    // Don't run for obvious bots/crawlers
    if (userAgent.includes('bot') ||
        userAgent.includes('crawler') ||
        userAgent.includes('spider') ||
        userAgent.includes('googlebot') ||
        userAgent.includes('facebookexternalhit') ||
        userAgent.includes('adsbot')) {
        return false;
    }

    return true;
}

/**
 * Initialize the AVG Time Trick
 */
function initAvgTimeTrick() {
    // We'll create the banner only when needed
    // Just set up scroll tracking for now
    setupScrollTracking();
}

/**
 * Create the banner element
 */
function createBanner() {
    // Create the banner element
    const banner = document.createElement('div');
    banner.id = 'avg-time-banner';
    banner.innerHTML = `
        <div class="container">
            <h3>Uspješno ste se kvalifikovali za nagradnu igru!</h3>
            <p>Ostanite na stranici još <span id="countdown-timer">60</span> sekundi da biste vidjeli kako potvrditi učešće.</p>
            <div class="progress-container">
                <div id="countdown-progress" style="width: 100%"></div>
            </div>
        </div>
    `;

    // Add to the page but keep it hidden
    document.body.appendChild(banner);
}

/**
 * Create the popup element
 */
function createPopup() {
    const popup = document.createElement('div');
    popup.id = 'avg-time-popup';
    popup.innerHTML = `
        <div class="popup-content">
            <div class="close-button">
                <button id="close-avg-popup">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="text-center">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <h3>Čestitamo!</h3>
                <p>Uspješno ste se kvalifikovali za nagradnu igru. Da biste potvrdili svoje učešće, pošaljite poruku našoj Facebook stranici sa tekstom "NAGRADNA IGRA" i vaše ime.</p>
            </div>
            <a href="https://www.facebook.com/mercislike" target="_blank" class="facebook-button">
                Pošalji poruku na Facebook
            </a>
        </div>
    `;

    // Add to the page
    document.body.appendChild(popup);

    // Add event listener to close button
    document.getElementById('close-avg-popup').addEventListener('click', function() {
        closePopup();
    });
}

/**
 * Set up scroll tracking using the existing progress bar
 */
function setupScrollTracking() {
    let bannerShown = false;
    let bannerCreated = false;

    // Use the existing progress bar to track reading progress
    const progressBar = document.getElementById('progress-bar');

    // Set up a MutationObserver to watch for changes to the progress bar width
    if (progressBar) {
        // Don't check initial width - we want the user to actually scroll to trigger the banner

        // Set up observer to monitor changes to the progress bar width
        const observer = new MutationObserver(function(mutations) {
            if (bannerShown) return;

            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'style') {
                    const width = parseFloat(progressBar.style.width);
                    if (width >= 70) {
                        // Only create and show the banner when we reach 70%
                        if (!bannerCreated) {
                            createBanner();
                            bannerCreated = true;
                        }
                        showBanner();
                        bannerShown = true;
                        observer.disconnect(); // Stop observing once banner is shown
                    }
                }
            });
        });

        // Start observing the progress bar
        observer.observe(progressBar, { attributes: true });
    } else {
        // Fallback to the old method if progress bar doesn't exist
        window.addEventListener('scroll', function() {
            if (bannerShown) return;

            const scrollPosition = window.scrollY;
            const totalHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercentage = (scrollPosition / totalHeight) * 100;

            // Show banner when user scrolls past 70% of content
            if (scrollPercentage > 70) {
                if (!bannerCreated) {
                    createBanner();
                    bannerCreated = true;
                }
                showBanner();
                bannerShown = true;
            }
        }, { passive: true });
    }
}

/**
 * Show the banner
 */
function showBanner() {
    const banner = document.getElementById('avg-time-banner');
    if (!banner) return;

    // Add a small delay to ensure the banner is fully rendered before showing it
    setTimeout(() => {
        // Show the banner with animation
        banner.classList.add('visible');

        // Start the countdown
        startCountdown();
    }, 100);
}

/**
 * Start the countdown timer
 */
function startCountdown() {
    // We'll create the popup only when the countdown finishes

    // Generate random countdown time between 55-60 seconds
    const countdownTime = Math.floor(Math.random() * 6) + 55;
    let timeRemaining = countdownTime;

    // Get elements
    const timerElement = document.getElementById('countdown-timer');
    const progressElement = document.getElementById('countdown-progress');

    // Set initial values
    if (timerElement) timerElement.textContent = timeRemaining;

    // Start countdown interval
    const countdownInterval = setInterval(function() {
        timeRemaining--;

        // Update timer text
        if (timerElement) timerElement.textContent = timeRemaining;

        // Update progress bar
        if (progressElement) {
            const progressPercentage = (timeRemaining / countdownTime) * 100;
            progressElement.style.width = progressPercentage + '%';
        }

        // When countdown reaches zero
        if (timeRemaining <= 0) {
            clearInterval(countdownInterval);

            // Create popup only when needed
            if (!document.getElementById('avg-time-popup')) {
                createPopup();
            }

            // Show the popup after a small delay to ensure it's fully rendered
            setTimeout(() => {
                showPopup();
            }, 100);
        }
    }, 1000);
}

/**
 * Show the popup
 */
function showPopup() {
    const popup = document.getElementById('avg-time-popup');
    if (!popup) return;

    // Hide the banner
    const banner = document.getElementById('avg-time-banner');
    if (banner) {
        banner.classList.remove('visible');
    }

    // Show the popup
    popup.classList.add('visible');
}

/**
 * Close the popup
 */
function closePopup() {
    const popup = document.getElementById('avg-time-popup');
    if (!popup) return;

    // Hide the popup
    popup.classList.remove('visible');
}
