<?php
/**
 * Admin Affiliate Ad Form
 *
 * Allows administrators to create/edit affiliate ads that look like articles
 * but redirect to external sites.
 */

require_once '../config.php';
require_once 'includes/auth_check.php';
require_once '../includes/functions.php';

// Set default values for a new ad
$edit_mode = false;
$ad_id = null;
$ad = [
    'title' => '',
    'description' => '',
    'external_url' => '',
    'category_id' => '',
    'featured_image' => '',
    'display_positions' => ['sidebar_popular'], // MODIFIED: array for multiple positions
    'status' => 'active',
    'open_in_new_tab' => 1,
    'show_sponsored_label' => 1,
    'custom_css' => '',
    'placement_frequency' => 3, // Show every X articles/posts
    'placement_type' => 'fixed', // Default to fixed placement
    'author_id' => null, // Optional author attribution
    'placement_priority' => 5, // 1-10 scale, higher = more likely to show
    'mobile_visibility' => 1, // Show on mobile
    'desktop_visibility' => 1, // Show on desktop
    'tablet_visibility' => 1, // Show on tablet
    'start_date' => null, // Schedule display start
    'end_date' => null, // Schedule display end
    'tracking_code' => '', // Optional UTM or other tracking params
];

$current_featured_image_base = '';

// Check if editing an existing ad
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $edit_mode = true;
    $ad_id = (int)$_GET['id'];
    
    try {
        // Fetch the ad data
        $stmt = $pdo->prepare("
            SELECT * FROM affiliate_ads WHERE id = :id
        ");
        $stmt->bindParam(':id', $ad_id, PDO::PARAM_INT);
        $stmt->execute();
        $ad_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($ad_data) {
            // MODIFIED: Convert display_position to display_positions array 
            if (isset($ad_data['display_position'])) {
                // Check if it's already a JSON array
                $positionsArray = json_decode($ad_data['display_position'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($positionsArray)) {
                    $ad_data['display_positions'] = $positionsArray;
                } else {
                    // Single value - convert to array
                    $ad_data['display_positions'] = [$ad_data['display_position']];
                }
            } else {
                $ad_data['display_positions'] = ['sidebar_popular']; // Default if none set
            }
            
            // Merge with defaults to ensure all keys exist
            $ad = array_merge($ad, $ad_data);
            $current_featured_image_base = $ad['featured_image'];
            
            // Format dates for datetime-local inputs
            if (!empty($ad['start_date'])) {
                $ad['start_date_formatted'] = date('Y-m-d\TH:i', strtotime($ad['start_date']));
            }
            if (!empty($ad['end_date'])) {
                $ad['end_date_formatted'] = date('Y-m-d\TH:i', strtotime($ad['end_date']));
            }
            
            // Set placement type if not set
            if (!isset($ad['placement_type'])) {
                $ad['placement_type'] = 'fixed';
            }
        } else {
            $_SESSION['error_message'] = "Affiliate ad with ID $ad_id not found.";
            header('Location: advertising.php');
            exit;
        }
    } catch (PDOException $e) {
        $_SESSION['error_message'] = "Database error: " . $e->getMessage();
        header('Location: advertising.php');
        exit;
    }
}

// Fetch categories for dropdown (keeping this for compatibility but won't display)
try {
    $categories = $pdo->query("SELECT id, name FROM categories ORDER BY name ASC")->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $categories = [];
    $error_message = "Database error fetching categories: " . $e->getMessage();
}

// Fetch authors for dropdown
try {
    $authors = $pdo->query("SELECT id, name FROM authors WHERE status = 'active' ORDER BY name ASC")->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $authors = [];
    $error_message = "Database error fetching authors: " . $e->getMessage();
}

// Set page title
$admin_page_title = $edit_mode ? "Edit Affiliate Ad" : "New Affiliate Ad";

// Include the admin header
include 'includes/header.php';
?>

<header class="h-16 bg-white border-b border-border flex items-center justify-between px-6 flex-shrink-0 sticky top-0 z-30">
    <div class="flex items-center">
        <h1 class="text-xl font-montserrat font-bold text-dark"><?php echo escape($admin_page_title); ?></h1>
        <?php if ($edit_mode && isset($ad['status'])): ?>
            <div class="ml-4">
                <span class="badge <?php echo ($ad['status'] === 'active') ? 'badge-success' : 'badge-gray'; ?>">
                    <?php echo ucfirst(escape($ad['status'])); ?>
                </span>
            </div>
        <?php endif; ?>
    </div>
    <div class="flex items-center space-x-3">
        <?php if ($edit_mode && !empty($ad['external_url'])): ?>
            <a href="<?php echo escape($ad['external_url']); ?>" target="_blank" class="btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Open External Link
            </a>
        <?php else: ?>
            <button type="button" class="btn-secondary" disabled title="No external link defined yet">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Open External Link
            </button>
        <?php endif; ?>
        
        <button type="submit" form="adForm" class="btn-success">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <?php echo $edit_mode ? 'Update Ad' : 'Save Ad'; ?>
        </button>
    </div>
</header>

<form id="adForm" action="process_ad.php" method="POST" enctype="multipart/form-data" class="p-6 h-full">
    <?php if ($edit_mode): ?>
        <input type="hidden" name="ad_id" value="<?php echo $ad_id; ?>">
        <input type="hidden" name="current_featured_image" value="<?php echo escape($current_featured_image_base); ?>">
    <?php endif; ?>
    <input type="hidden" name="action" value="<?php echo $edit_mode ? 'update' : 'create'; ?>">
    
    <!-- Hidden field to store original title for tracking param generation -->
    <input type="hidden" id="original_title" value="<?php echo escape($ad['title']); ?>">
    
    <?php if (isset($_SESSION['form_errors'])): ?>
        <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm space-y-1 shadow-sm">
            <p class="font-semibold">Please correct the following errors:</p>
            <ul class="list-disc list-inside">
                <?php foreach ($_SESSION['form_errors'] as $error): ?>
                    <li><?php echo escape($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['form_errors']); ?>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm shadow-sm">
            <?php echo escape($_SESSION['error_message']); unset($_SESSION['error_message']); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="mb-4 bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded-lg text-sm shadow-sm">
            <?php echo escape($_SESSION['success_message']); unset($_SESSION['success_message']); ?>
        </div>
    <?php endif; ?>
    
    <div class="flex flex-col lg:flex-row gap-6 h-full">
        <!-- Main Content Column -->
        <div class="lg:w-7/12 flex flex-col h-full">
            <div class="mb-4">
                <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Ad Title *</label>
                <input type="text" id="title" name="title" class="form-input text-xl font-montserrat font-bold py-3" placeholder="Enter ad title" required value="<?php echo escape($ad['title']); ?>" x-track-params>
                <p class="text-xs text-gray-500 mt-1">This will appear as the article title. Make it compelling but honest.</p>
            </div>
            
            <div class="mb-4">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Ad Description</label>
                <textarea id="description" name="description" class="form-textarea" placeholder="Enter a brief description of the ad content..." rows="6"><?php echo escape($ad['description']); ?></textarea>
                <p class="text-xs text-gray-500 mt-1">This will appear as a snippet/excerpt in listings. Keep it concise and engaging.</p>
            </div>
            
            <div class="mb-4">
                <label for="external_url" class="block text-sm font-medium text-gray-700 mb-1">External URL *</label>
                <input type="url" id="external_url" name="external_url" class="form-input" placeholder="https://example.com/landing-page" required value="<?php echo escape($ad['external_url']); ?>">
                <p class="text-xs text-gray-500 mt-1">Where visitors will be redirected when they click the ad.</p>
            </div>
            
            <div class="mb-4">
                <label for="tracking_code" class="block text-sm font-medium text-gray-700 mb-1">Tracking Parameters</label>
                <input type="text" id="tracking_code" name="tracking_code" class="form-input font-mono text-sm" 
                    value="<?php 
                        // Generate default tracking params if empty
                        if (empty($ad['tracking_code'])) {
                            $title_slug = strtolower(preg_replace('/[^\w\s-]/', '', $ad['title']));
                            $title_slug = preg_replace('/\s+/', '-', $title_slug);
                            $title_slug = substr($title_slug, 0, 50);
                            // Use first position as default for tracking
                            $position = !empty($ad['display_positions']) ? $ad['display_positions'][0] : 'sidebar_popular';
                            echo "utm_source=website&utm_medium={$position}&utm_campaign={$title_slug}&utm_content={article_id}&utm_term={ad_id}";
                        } else {
                            echo escape($ad['tracking_code']);
                        }
                    ?>">
                <p class="text-xs text-gray-500 mt-1">Auto-generated based on title and placement. Available tokens: <code>{article_id}</code>, <code>{ad_id}</code>, <code>{category}</code></p>
                <div class="flex mt-2">
                    <button type="button" id="regenerateTracking" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Regenerate Default
                    </button>
                </div>
            </div>
            
            <div class="space-y-2 flex-shrink-0">
                <!-- Custom CSS Section -->
                <div x-data="{ open: false }">
                    <button type="button" @click="open = !open" class="collapsible-header">
                        <span class="text-sm font-semibold text-gray-700">Custom Styling (Advanced)</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform transition-transform" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div x-show="open" x-transition class="collapsible-content" style="display: none;">
                        <textarea id="custom_css" name="custom_css" class="form-textarea h-32 font-mono text-sm" placeholder=".affiliate-ad { border-color: #ff6481; } /* Custom CSS for this ad */"><?php echo escape($ad['custom_css']); ?></textarea>
                        <p class="text-xs text-gray-500 mt-1">Optional custom CSS to style this specific ad. Use with caution.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar/Settings Column -->
        <div class="lg:w-5/12 flex-shrink-0">
            <div class="bg-white border border-border rounded-xl p-4 space-y-4 sticky top-20">
                <!-- Featured Image Section -->
                <div x-data="{ currentImage: '<?php echo $current_featured_image_base ? getFeaturedImageUrl($current_featured_image_base, 'ss')['url'] : ''; ?>', fileChosen: false }">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Featured Image *</label>
                    
                    <div class="mb-2 border border-gray-200 rounded-lg overflow-hidden h-40 flex items-center justify-center bg-gray-50">
                        <img x-show="currentImage" :src="currentImage" alt="Featured Image Preview" class="object-contain h-full w-full">
                        <div x-show="!currentImage" class="text-center text-gray-400 text-sm p-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-10 w-10 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                            <span>No image selected</span>
                        </div>
                    </div>
                    
                    <div>
                        <label for="featured_image_upload" class="block text-xs font-medium text-gray-700 mb-1">Upload Image</label>
                        <input type="file" id="featured_image_upload" name="featured_image_upload" accept="image/jpeg,image/png,image/gif,image/webp"
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90 file:cursor-pointer"
                               @change="fileChosen = true; currentImage = $event.target.files[0] ? URL.createObjectURL($event.target.files[0]) : (document.getElementById('featured_image_url').value || '')">
                        <p class="text-xs text-gray-500 mt-1">Recommended size: 800x600px. Max file size: <?php echo defined('MAX_FILE_SIZE') ? (MAX_FILE_SIZE / 1024 / 1024) : 5; ?>MB.</p>
                    </div>

                    <div class="my-3 text-center text-xs text-gray-400">OR</div>

                    <div>
                        <label for="featured_image_url" class="block text-xs font-medium text-gray-700 mb-1">Image URL</label>
                        <input type="url" id="featured_image_url" name="featured_image_url" class="form-input text-sm" placeholder="https://example.com/image.jpg"
                               value="<?php echo !$edit_mode ? '' : escape($current_featured_image_base ? getFeaturedImageUrl($current_featured_image_base, 'original')['url'] : ''); ?>"
                               @input="if (!fileChosen) { currentImage = $event.target.value }">
                         <p class="text-xs text-gray-500 mt-1">Enter URL if not uploading directly. Upload takes precedence.</p>
                    </div>
                </div>
                
                <!-- Basic Settings -->
                <div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select id="status" name="status" class="form-select py-1.5">
                            <option value="active" <?php echo ($ad['status'] === 'active') ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo ($ad['status'] === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                            <option value="scheduled" <?php echo ($ad['status'] === 'scheduled') ? 'selected' : ''; ?>>Scheduled</option>
                        </select>
                    </div>
                </div>
                
                <!-- Display Options -->
                <div>
                    <h3 class="font-montserrat font-semibold text-gray-700 mb-3 border-b border-gray-200 pb-1">Display Positions</h3>
                    
                    <!-- MODIFIED: Replaced dropdown with checkboxes for multiple positions -->
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600 mb-3">Select one or more positions where this ad should appear:</p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2">
                            <div class="text-sm font-semibold text-gray-700 col-span-2 mt-2">Sidebar Placements:</div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="pos_sidebar_popular" name="display_positions[]" value="sidebar_popular" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    <?php echo in_array('sidebar_popular', $ad['display_positions']) ? 'checked' : ''; ?>>
                                <label for="pos_sidebar_popular" class="ml-2 text-sm text-gray-700">Sidebar - Popular Articles</label>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="pos_sidebar_middle" name="display_positions[]" value="sidebar_middle" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    <?php echo in_array('sidebar_middle', $ad['display_positions']) ? 'checked' : ''; ?>>
                                <label for="pos_sidebar_middle" class="ml-2 text-sm text-gray-700">Sidebar - Middle</label>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="pos_sidebar_bottom" name="display_positions[]" value="sidebar_bottom" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    <?php echo in_array('sidebar_bottom', $ad['display_positions']) ? 'checked' : ''; ?>>
                                <label for="pos_sidebar_bottom" class="ml-2 text-sm text-gray-700">Sidebar - Bottom</label>
                            </div>
                            
                            <div class="text-sm font-semibold text-gray-700 col-span-2 mt-4">Article Placements:</div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="pos_article_beginning" name="display_positions[]" value="article_beginning" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    <?php echo in_array('article_beginning', $ad['display_positions']) ? 'checked' : ''; ?>>
                                <label for="pos_article_beginning" class="ml-2 text-sm text-gray-700">Article Beginning</label>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="pos_in_content" name="display_positions[]" value="in_content" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    <?php echo in_array('in_content', $ad['display_positions']) ? 'checked' : ''; ?>>
                                <label for="pos_in_content" class="ml-2 text-sm text-gray-700">Within Article Content</label>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="pos_after_content" name="display_positions[]" value="after_content" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    <?php echo in_array('after_content', $ad['display_positions']) ? 'checked' : ''; ?>>
                                <label for="pos_after_content" class="ml-2 text-sm text-gray-700">After Content (after tags)</label>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="pos_article_bottom_banner" name="display_positions[]" value="article_bottom_banner" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    <?php echo in_array('article_bottom_banner', $ad['display_positions']) ? 'checked' : ''; ?>>
                                <label for="pos_article_bottom_banner" class="ml-2 text-sm text-gray-700">Article Bottom Banner</label>
                            </div>
                            
                            <div class="text-sm font-semibold text-gray-700 col-span-2 mt-4">Other Placements:</div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="pos_recommended" name="display_positions[]" value="recommended" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    <?php echo in_array('recommended', $ad['display_positions']) ? 'checked' : ''; ?>>
                                <label for="pos_recommended" class="ml-2 text-sm text-gray-700">Recommended Section</label>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="pos_header" name="display_positions[]" value="header" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    <?php echo in_array('header', $ad['display_positions']) ? 'checked' : ''; ?>>
                                <label for="pos_header" class="ml-2 text-sm text-gray-700">Header</label>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="pos_footer" name="display_positions[]" value="footer" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    <?php echo in_array('footer', $ad['display_positions']) ? 'checked' : ''; ?>>
                                <label for="pos_footer" class="ml-2 text-sm text-gray-700">Footer</label>
                            </div>
                        </div>
                        
                        <p class="text-xs text-gray-500 mt-3">Select at least one position where this ad will appear on the website.</p>
                    </div>
                        
                    <div x-data="{ placementType: '<?php echo isset($ad['placement_type']) ? $ad['placement_type'] : 'fixed'; ?>' }">
                        <label for="placement_type" class="block text-sm font-medium text-gray-700 mb-1 mt-4">Placement Type</label>
                        <select id="placement_type" name="placement_type" class="form-select py-1.5" x-model="placementType">
                            <option value="fixed" <?php echo (!isset($ad['placement_type']) || $ad['placement_type'] === 'fixed') ? 'selected' : ''; ?>>Fixed Frequency</option>
                            <option value="ctr_based" <?php echo (isset($ad['placement_type']) && $ad['placement_type'] === 'ctr_based') ? 'selected' : ''; ?>>CTR Based</option>
                        </select>
                        <p class="text-xs text-gray-500 mt-1">CTR Based will prioritize ads with higher click-through rates</p>
                        
                        <div x-show="placementType === 'fixed'" class="mt-3">
                            <label for="placement_frequency" class="block text-sm font-medium text-gray-700 mb-1">Placement Frequency</label>
                            <input type="number" id="placement_frequency" name="placement_frequency" min="1" max="20" class="form-input py-1.5" value="<?php echo escape($ad['placement_frequency']); ?>">
                            <p class="text-xs text-gray-500 mt-1">Show every X articles/posts in listings (e.g., 3 = every 3rd article)</p>
                        </div>
                    </div>
                    
                    <div x-data="{ placementType: '<?php echo isset($ad['placement_type']) ? $ad['placement_type'] : 'fixed'; ?>' }" x-show="placementType === 'fixed'">
                        <label for="placement_priority" class="block text-sm font-medium text-gray-700 mb-1">Placement Priority</label>
                        <input type="range" id="placement_priority" name="placement_priority" min="1" max="10" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer" value="<?php echo escape($ad['placement_priority']); ?>">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>Lower (1)</span>
                            <span>Higher (10)</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Higher priority ads are more likely to be displayed when multiple ads are eligible</p>
                    </div>
                </div>
                
                <!-- Attribution Options -->
                <div>
                    <h3 class="font-montserrat font-semibold text-gray-700 mb-2 border-b border-gray-200 pb-1">Attribution</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <label for="author_id" class="block text-sm font-medium text-gray-700 mb-1">Attributed Author</label>
                            <select id="author_id" name="author_id" class="form-select py-1.5">
                                <option value="">-- No attribution --</option>
                                <?php foreach ($authors as $author): ?>
                                    <option value="<?php echo $author['id']; ?>" <?php echo (isset($ad['author_id']) && $ad['author_id'] == $author['id']) ? 'selected' : ''; ?>>
                                        <?php echo escape($author['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">Optional author attribution (shows their profile image)</p>
                        </div>
                        
                        <div class="flex items-center justify-between pt-2">
                            <span class="text-sm font-medium text-gray-700">Show Sponsored Label</span>
                            <div x-data="{ enabled: <?php echo $ad['show_sponsored_label'] ? 'true' : 'false'; ?> }">
                                <input type="hidden" name="show_sponsored_label" :value="enabled ? 1 : 0">
                                <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                    <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Scheduling -->
                <div x-data="{ schedulingEnabled: <?php echo $ad['status'] === 'scheduled' ? 'true' : 'false'; ?> }">
                    <h3 class="font-montserrat font-semibold text-gray-700 mb-2 border-b border-gray-200 pb-1">Scheduling</h3>
                    
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Enable Scheduling</span>
                            <div>
                                <button 
                                    type="button" 
                                    @click="schedulingEnabled = !schedulingEnabled; if(schedulingEnabled) document.getElementById('status').value = 'scheduled'; else document.getElementById('status').value = 'active';" 
                                    class="toggle-switch" 
                                    :class="schedulingEnabled ? 'bg-primary' : 'bg-gray-200'"
                                >
                                    <span class="toggle-thumb" :class="schedulingEnabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                            </div>
                        </div>
                        
                        <div x-show="schedulingEnabled">
                            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date/Time</label>
                            <input type="datetime-local" id="start_date" name="start_date" class="form-input py-1.5" value="<?php echo escape($ad['start_date_formatted'] ?? ''); ?>">
                        </div>
                        
                        <div x-show="schedulingEnabled">
                            <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date/Time (Optional)</label>
                            <input type="datetime-local" id="end_date" name="end_date" class="form-input py-1.5" value="<?php echo escape($ad['end_date_formatted'] ?? ''); ?>">
                            <p class="text-xs text-gray-500 mt-1">Leave empty for no end date</p>
                        </div>
                    </div>
                </div>
                
                <!-- Device Visibility -->
                <div>
                    <h3 class="font-montserrat font-semibold text-gray-700 mb-2 border-b border-gray-200 pb-1">Device Visibility</h3>
                    
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Show on Desktop</span>
                            <div x-data="{ enabled: <?php echo $ad['desktop_visibility'] ? 'true' : 'false'; ?> }">
                                <input type="hidden" name="desktop_visibility" :value="enabled ? 1 : 0">
                                <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                    <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Show on Mobile</span>
                            <div x-data="{ enabled: <?php echo $ad['mobile_visibility'] ? 'true' : 'false'; ?> }">
                                <input type="hidden" name="mobile_visibility" :value="enabled ? 1 : 0">
                                <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                    <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Show on Tablet</span>
                            <div x-data="{ enabled: <?php echo $ad['tablet_visibility'] ? 'true' : 'false'; ?> }">
                                <input type="hidden" name="tablet_visibility" :value="enabled ? 1 : 0">
                                <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                    <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Link Behavior -->
                <div>
                    <h3 class="font-montserrat font-semibold text-gray-700 mb-2 border-b border-gray-200 pb-1">Link Behavior</h3>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Open in New Tab</span>
                        <div x-data="{ enabled: <?php echo $ad['open_in_new_tab'] ? 'true' : 'false'; ?> }">
                            <input type="hidden" name="open_in_new_tab" :value="enabled ? 1 : 0">
                            <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Add script to update placement type behavior and tracking params -->
<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('placementHandler', () => ({
        placementType: '<?php echo isset($ad['placement_type']) ? $ad['placement_type'] : 'fixed'; ?>',
        init() {
            this.$watch('placementType', value => {
                const frequencyField = document.getElementById('placement_frequency');
                const priorityField = document.getElementById('placement_priority');
                
                if (value === 'ctr_based') {
                    frequencyField.disabled = true;
                    priorityField.disabled = true;
                } else {
                    frequencyField.disabled = false;
                    priorityField.disabled = false;
                }
            });
            
            // Initial state
            if (this.placementType === 'ctr_based') {
                document.getElementById('placement_frequency').disabled = true;
                document.getElementById('placement_priority').disabled = true;
            }
        }
    }));
});

// Enhanced tracking parameter generation
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const regenerateBtn = document.getElementById('regenerateTracking');
    const trackingInput = document.getElementById('tracking_code');
    const originalTitle = document.getElementById('original_title').value;
    
    // Get all position checkboxes
    const positionCheckboxes = document.querySelectorAll('input[name="display_positions[]"]');
    
    if (!titleInput || !trackingInput) return;
    
    // Function to generate a slug from text
    const slugify = (text) => {
        return text.toLowerCase()
            .replace(/[^\w\s-]/g, '')  // Remove special characters
            .replace(/\s+/g, '-')      // Replace spaces with hyphens
            .trim()                    // Trim whitespace
            .substring(0, 50);         // Limit length
    };
    
    // Function to get the first checked position
    const getFirstCheckedPosition = () => {
        for (let checkbox of positionCheckboxes) {
            if (checkbox.checked) {
                return checkbox.value;
            }
        }
        return 'sidebar_popular'; // Default if none checked
    };
    
    // Generate tracking parameters based on form values
    const generateTrackingParams = (title) => {
        const campaign = slugify(title);
        const medium = getFirstCheckedPosition();
        const adId = <?php echo $edit_mode ? $ad_id : 'null'; ?>;
        
        let params = `utm_source=website&utm_medium=${medium}&utm_campaign=${campaign}`;
        
        // Add article_id token for all ads
        params += `&utm_content={article_id}`;
        
        // Add ad_id token if we have one
        if (adId) {
            params += `&utm_term=${adId}`;
        } else {
            params += `&utm_term={ad_id}`;
        }
        
        return params;
    };
    
    // Update tracking parameters when title changes
    titleInput.addEventListener('input', function() {
        const currentTitle = titleInput.value;
        if (currentTitle && currentTitle !== originalTitle) {
            trackingInput.value = generateTrackingParams(currentTitle);
        }
    });
    
    // Update tracking parameters when position changes
    positionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // Only regenerate if at least one position is checked
            let atLeastOneChecked = false;
            for (let cb of positionCheckboxes) {
                if (cb.checked) {
                    atLeastOneChecked = true;
                    break;
                }
            }
            
            if (atLeastOneChecked) {
                trackingInput.value = generateTrackingParams(titleInput.value);
            }
        });
    });
    
    // Force regenerate tracking parameters
    regenerateBtn.addEventListener('click', function() {
        trackingInput.value = generateTrackingParams(titleInput.value);
    });
});
</script>

<?php include 'includes/footer.php'; ?>