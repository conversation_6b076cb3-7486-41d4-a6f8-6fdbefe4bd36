<?php

namespace AuditSystem\Interfaces;

/**
 * Interface for audit system logging
 */
interface LoggerInterface
{
    /**
     * Log an emergency message
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function emergency(string $message, array $context = []): void;

    /**
     * Log an alert message
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function alert(string $message, array $context = []): void;

    /**
     * Log a critical message
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function critical(string $message, array $context = []): void;

    /**
     * Log an error message
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function error(string $message, array $context = []): void;

    /**
     * Log a warning message
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function warning(string $message, array $context = []): void;

    /**
     * Log a notice message
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function notice(string $message, array $context = []): void;

    /**
     * Log an info message
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function info(string $message, array $context = []): void;

    /**
     * Log a debug message
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function debug(string $message, array $context = []): void;

    /**
     * Log a message with arbitrary level
     *
     * @param string $level
     * @param string $message
     * @param array $context
     * @return void
     */
    public function log(string $level, string $message, array $context = []): void;

    /**
     * Clear all log files
     *
     * @return void
     */
    public function clearLogs(): void;

    /**
     * Log audit progress
     *
     * @param string $phase
     * @param int $completed
     * @param int $total
     * @param int $findings
     * @return void
     */
    public function logProgress(string $phase, int $completed, int $total, int $findings): void;

    /**
     * Log performance metrics
     *
     * @param string $operation
     * @param float $duration
     * @param array $metrics
     * @return void
     */
    public function logPerformance(string $operation, float $duration, array $metrics = []): void;

    /**
     * Log file analysis start
     *
     * @param string $filePath
     * @param array $analyzers
     * @return void
     */
    public function logFileAnalysisStart(string $filePath, array $analyzers): void;

    /**
     * Log file analysis completion
     *
     * @param string $filePath
     * @param int $findingsCount
     * @param float $duration
     * @return void
     */
    public function logFileAnalysisComplete(string $filePath, int $findingsCount, float $duration): void;

    /**
     * Log analyzer failure
     *
     * @param string $analyzerClass
     * @param string $filePath
     * @param \Exception $exception
     * @return void
     */
    public function logAnalyzerFailure(string $analyzerClass, string $filePath, \Exception $exception): void;

    /**
     * Log error recovery attempt
     *
     * @param string $exceptionClass
     * @param string $strategy
     * @param string $context
     * @param bool $successful
     * @return void
     */
    public function logErrorRecovery(string $exceptionClass, string $strategy, string $context, bool $successful): void;
}