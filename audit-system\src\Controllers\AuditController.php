<?php

namespace AuditSystem\Controllers;

use AuditSystem\Interfaces\AuditControllerInterface;
use AuditSystem\Interfaces\ProgressTrackerInterface;
use AuditSystem\Interfaces\LoggerInterface;
use AuditSystem\Models\AuditResult;
use AuditSystem\Models\AuditStatus;
use AuditSystem\Config\AuditConfig;
use AuditSystem\Services\FileScanner;
use AuditSystem\Services\AuditLogger;
use AuditSystem\Exceptions\AuditException;
use AuditSystem\Exceptions\GeneralAuditException;
use AuditSystem\Exceptions\AnalysisException;
use AuditSystem\Exceptions\FileAccessException;
use AuditSystem\Exceptions\ConfigurationException;
use AuditSystem\Exceptions\MCPConnectionException;

/**
 * Main audit controller that orchestrates the audit process
 */
class AuditController implements AuditControllerInterface
{
    private AuditConfig $config;
    private ProgressTrackerInterface $progressTracker;
    private FileScanner $fileScanner;
    private LoggerInterface $logger;
    private array $analyzers;
    private bool $isRunning;
    private ?string $currentFile;
    private array $errorRecoveryStrategies;

    public function __construct(
        AuditConfig $config,
        ProgressTrackerInterface $progressTracker,
        FileScanner $fileScanner,
        LoggerInterface $logger = null
    ) {
        $this->config = $config;
        $this->progressTracker = $progressTracker;
        $this->fileScanner = $fileScanner;
        $this->logger = $logger ?? new AuditLogger();
        $this->analyzers = [];
        $this->isRunning = false;
        $this->currentFile = null;
        $this->initializeErrorRecoveryStrategies();
    }

    /**
     * Initialize error recovery strategies
     *
     * @return void
     */
    private function initializeErrorRecoveryStrategies(): void
    {
        $this->errorRecoveryStrategies = [
            FileAccessException::class => 'handleFileAccessError',
            AnalysisException::class => 'handleAnalysisError',
            MCPConnectionException::class => 'handleMCPConnectionError',
            ConfigurationException::class => 'handleConfigurationError'
        ];
    }

    /**
     * Register an analyzer with the controller
     *
     * @param \AuditSystem\Interfaces\AnalyzerInterface $analyzer
     * @return void
     */
    public function registerAnalyzer(\AuditSystem\Interfaces\AnalyzerInterface $analyzer): void
    {
        $this->analyzers[] = $analyzer;
        $this->logger->debug('Registered analyzer: ' . get_class($analyzer));
    }

    /**
     * Initialize and begin audit process
     *
     * @param array $options Configuration options for the audit
     * @return AuditResult The result of the audit process
     * @throws AuditException If audit cannot be started
     */
    public function startAudit(array $options): AuditResult
    {
        if ($this->isRunning) {
            throw GeneralAuditException::auditAlreadyRunning();
        }

        $this->logger->info('Starting new audit', ['options' => $options]);
        
        try {
            // Validate configuration and options
            $this->validateAuditOptions($options);
            
            // Reset progress for fresh start
            $this->progressTracker->resetProgress();
            $this->logger->info('Reset audit progress for fresh start');
            
            // Apply any configuration overrides from options
            foreach ($options as $key => $value) {
                $this->config->set($key, $value);
                $this->logger->debug("Applied configuration override: {$key} = " . json_encode($value));
            }

            // Clear previous logs if requested
            if ($options['clear_logs'] ?? false) {
                $this->logger->clearLogs();
                $this->logger->info('Cleared previous audit logs');
            }

            return $this->executeAudit();
            
        } catch (AuditException $e) {
            $this->logger->error('Failed to start audit: ' . $e->getMessage(), $e->getContext());
            throw $e;
        } catch (\Exception $e) {
            $this->logger->critical('Unexpected error starting audit: ' . $e->getMessage(), [
                'exception_class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw GeneralAuditException::create('Failed to start audit: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Continue audit from last checkpoint
     *
     * @return AuditResult The result of the resumed audit process
     * @throws AuditException If audit cannot be resumed
     */
    public function resumeAudit(): AuditResult
    {
        if ($this->isRunning) {
            throw GeneralAuditException::auditAlreadyRunning();
        }

        $this->logger->info('Attempting to resume audit from checkpoint');
        
        try {
            $progress = $this->progressTracker->loadProgress();
            
            if ($progress === null) {
                throw GeneralAuditException::noExistingProgress();
            }

            $this->logger->info('Found existing progress', [
                'phase' => $progress->currentPhase,
                'completed_files' => count($progress->completedFiles),
                'pending_files' => count($progress->pendingFiles),
                'last_update' => $progress->lastUpdate->format('Y-m-d H:i:s')
            ]);

            // Backup current progress before resuming
            $this->progressTracker->backupProgress();
            $this->logger->info('Created progress backup before resuming');

            return $this->executeAudit();
            
        } catch (AuditException $e) {
            $this->logger->error('Failed to resume audit: ' . $e->getMessage(), $e->getContext());
            throw $e;
        } catch (\Exception $e) {
            $this->logger->critical('Unexpected error resuming audit: ' . $e->getMessage(), [
                'exception_class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw GeneralAuditException::create('Failed to resume audit: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Return current progress and statistics
     *
     * @return AuditStatus Current audit status and progress information
     */
    public function getAuditStatus(): AuditStatus
    {
        try {
            $status = new AuditStatus();
            $progress = $this->progressTracker->loadProgress();
            
            if ($progress !== null) {
                $status->updateFromProgress($progress);
            }
            
            $status->isRunning = $this->isRunning;
            $status->currentFile = $this->currentFile;
            
            return $status;
            
        } catch (\Exception $e) {
            $this->logger->error('Failed to get audit status: ' . $e->getMessage());
            
            // Return minimal status on error
            $status = new AuditStatus();
            $status->isRunning = $this->isRunning;
            $status->currentFile = $this->currentFile;
            return $status;
        }
    }

    /**
     * Execute the audit process with comprehensive error handling and orchestration
     *
     * @return AuditResult
     * @throws AuditException If audit execution fails
     */
    private function executeAudit(): AuditResult
    {
        $startTime = microtime(true);
        $this->isRunning = true;
        $result = new AuditResult();
        
        try {
            $this->logger->info('Starting audit execution');
            
            // Phase 1: Validation and Setup
            $this->progressTracker->updatePhase('validation');
            $this->validateAuditEnvironment();
            $this->logger->info('Audit environment validation completed');
            
            // Phase 2: File Discovery
            $this->progressTracker->updatePhase('file_discovery');
            $this->logger->info('Starting file discovery phase');
            
            $targetDir = $this->config->get('audit.target_directory', '../public_html');
            $this->validateTargetDirectory($targetDir);
            
            $categorizedFiles = $this->fileScanner->scanDirectory($targetDir);
            $this->logger->info('File discovery completed', [
                'priority_files' => count($categorizedFiles['priority_area']),
                'non_priority_files' => count($categorizedFiles['non_priority']),
                'total_files' => count($categorizedFiles['priority_area']) + count($categorizedFiles['non_priority'])
            ]);
            
            // Initialize progress with discovered files
            $allFiles = array_merge(
                array_column($categorizedFiles['priority_area'], 'file'),
                array_column($categorizedFiles['non_priority'], 'file')
            );
            $this->progressTracker->initializeProgress($allFiles);
            
            // Phase 3: Analysis
            $this->progressTracker->updatePhase('analyzing');
            $this->logger->info('Starting analysis phase');
            
            $totalFiles = count($allFiles);
            $processedFiles = 0;
            $totalFindings = 0;
            
            // Process priority area files first
            $this->logger->info('Processing priority area files', ['count' => count($categorizedFiles['priority_area'])]);
            foreach ($categorizedFiles['priority_area'] as $fileInfo) {
                if ($this->progressTracker->isFileCompleted($fileInfo['file'])) {
                    $processedFiles++;
                    continue;
                }
                
                $findings = $this->analyzeFileWithErrorHandling($fileInfo['file']);
                $result->addFileFindings($fileInfo['file'], $findings);
                $this->progressTracker->markFileCompleted($fileInfo['file'], $findings);
                
                $processedFiles++;
                $totalFindings += count($findings);
                
                // Log progress periodically
                if ($processedFiles % 10 === 0 || $processedFiles === $totalFiles) {
                    $this->logger->logProgress('analyzing', $processedFiles, $totalFiles, $totalFindings);
                }
            }
            
            // Process non-priority files
            $this->logger->info('Processing non-priority files', ['count' => count($categorizedFiles['non_priority'])]);
            foreach ($categorizedFiles['non_priority'] as $fileInfo) {
                if ($this->progressTracker->isFileCompleted($fileInfo['file'])) {
                    $processedFiles++;
                    continue;
                }
                
                $findings = $this->analyzeFileWithErrorHandling($fileInfo['file']);
                $result->addFileFindings($fileInfo['file'], $findings);
                $this->progressTracker->markFileCompleted($fileInfo['file'], $findings);
                
                $processedFiles++;
                $totalFindings += count($findings);
                
                // Log progress periodically
                if ($processedFiles % 10 === 0 || $processedFiles === $totalFiles) {
                    $this->logger->logProgress('analyzing', $processedFiles, $totalFiles, $totalFindings);
                }
            }
            
            // Phase 4: Completion
            $this->progressTracker->updatePhase('completed');
            $duration = microtime(true) - $startTime;
            
            $this->logger->info('Audit execution completed successfully', [
                'total_files' => $totalFiles,
                'total_findings' => $totalFindings,
                'duration' => round($duration, 2) . 's'
            ]);
            
            $this->logger->logPerformance('complete_audit', $duration, [
                'files_processed' => $totalFiles,
                'findings_discovered' => $totalFindings,
                'files_per_second' => round($totalFiles / $duration, 2)
            ]);
            
        } catch (AuditException $e) {
            $this->handleAuditFailure($e, $startTime);
            throw $e;
        } catch (\Exception $e) {
            $auditException = GeneralAuditException::create('Unexpected error during audit execution: ' . $e->getMessage(), 0, $e);
            $this->handleAuditFailure($auditException, $startTime);
            throw $auditException;
        } finally {
            $this->isRunning = false;
            $this->currentFile = null;
        }
        
        return $result;
    }

    /**
     * Analyze a single file with error handling and recovery
     *
     * @param string $filePath
     * @return array
     */
    private function analyzeFileWithErrorHandling(string $filePath): array
    {
        $this->currentFile = $filePath;
        $startTime = microtime(true);
        
        try {
            $this->logger->logFileAnalysisStart($filePath, $this->analyzers);
            
            // Validate file access
            if (!file_exists($filePath)) {
                throw FileAccessException::fileNotFound($filePath);
            }
            
            if (!is_readable($filePath)) {
                throw FileAccessException::fileNotReadable($filePath);
            }
            
            // Check file size limits
            $maxFileSize = $this->config->get('audit.max_file_size', 1048576);
            $fileSize = filesize($filePath);
            if ($fileSize > $maxFileSize) {
                throw FileAccessException::fileTooLarge($filePath, $fileSize, $maxFileSize);
            }
            
            $content = file_get_contents($filePath);
            if ($content === false) {
                throw FileAccessException::fileNotReadable($filePath);
            }
            
            $allFindings = $this->analyzeFile($filePath, $content);
            
            $duration = microtime(true) - $startTime;
            $this->logger->logFileAnalysisComplete($filePath, count($allFindings), $duration);
            
            return $allFindings;
            
        } catch (AuditException $e) {
            $this->logger->error("File analysis failed for {$filePath}: " . $e->getMessage(), $e->getContext());
            return $this->recoverFromError($e, $filePath);
        } catch (\Exception $e) {
            $this->logger->error("Unexpected error analyzing {$filePath}: " . $e->getMessage());
            return $this->recoverFromError(new AnalysisException($e->getMessage(), 0, $e), $filePath);
        }
    }

    /**
     * Analyze a single file with all registered analyzers
     *
     * @param string $filePath
     * @param string $content
     * @return array
     */
    private function analyzeFile(string $filePath, string $content): array
    {
        $allFindings = [];
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $timeout = $this->config->get('audit.timeout', 300);

        foreach ($this->analyzers as $analyzer) {
            try {
                $supportedTypes = $analyzer->getSupportedFileTypes();
                
                // Check if analyzer supports this file type
                if (in_array($extension, $supportedTypes) || in_array('*', $supportedTypes)) {
                    // Set timeout for analysis
                    $startTime = time();
                    
                    $findings = $analyzer->analyze($filePath, $content);
                    
                    // Check for timeout
                    if (time() - $startTime > $timeout) {
                        throw AnalysisException::analysisTimeout($filePath, $timeout);
                    }
                    
                    $allFindings = array_merge($allFindings, $findings);
                }
            } catch (\Exception $e) {
                $this->logger->logAnalyzerFailure(get_class($analyzer), $filePath, $e);
                
                // Continue with other analyzers unless it's a critical error
                if (!$this->isCriticalAnalyzerError($e)) {
                    continue;
                }
                
                throw new AnalysisException("Critical analyzer failure: " . $e->getMessage(), 0, $e);
            }
        }

        return $allFindings;
    }

    /**
     * Validate audit options before starting
     *
     * @param array $options
     * @return void
     * @throws ConfigurationException
     */
    private function validateAuditOptions(array $options): void
    {
        // Validate target directory if provided
        if (isset($options['audit.target_directory'])) {
            $targetDir = $options['audit.target_directory'];
            if (!is_string($targetDir) || empty($targetDir)) {
                throw ConfigurationException::invalidConfiguration('audit.target_directory', $targetDir, 'non-empty string');
            }
        }
        
        // Validate timeout if provided
        if (isset($options['audit.timeout'])) {
            $timeout = $options['audit.timeout'];
            if (!is_int($timeout) || $timeout <= 0) {
                throw ConfigurationException::invalidConfiguration('audit.timeout', $timeout, 'positive integer');
            }
        }
        
        // Validate max file size if provided
        if (isset($options['audit.max_file_size'])) {
            $maxSize = $options['audit.max_file_size'];
            if (!is_int($maxSize) || $maxSize <= 0) {
                throw ConfigurationException::invalidConfiguration('audit.max_file_size', $maxSize, 'positive integer');
            }
        }
    }

    /**
     * Validate audit environment before execution
     *
     * @return void
     * @throws AuditException
     */
    private function validateAuditEnvironment(): void
    {
        // Check if analyzers are registered
        if (empty($this->analyzers)) {
            throw GeneralAuditException::noAnalyzersRegistered();
        }
        
        // Validate progress tracker
        if (!$this->progressTracker instanceof ProgressTrackerInterface) {
            throw GeneralAuditException::invalidProgressTracker();
        }
        
        // Check write permissions for progress file
        $progressFile = $this->config->get('audit.progress_file', 'audit-system/data/progress.json');
        $progressDir = dirname($progressFile);
        if (!is_dir($progressDir) && !mkdir($progressDir, 0755, true)) {
            throw GeneralAuditException::cannotCreateDirectory($progressDir);
        }
        
        if (!is_writable($progressDir)) {
            throw GeneralAuditException::directoryNotWritable($progressDir);
        }
    }

    /**
     * Validate target directory
     *
     * @param string $targetDir
     * @return void
     * @throws FileAccessException
     */
    private function validateTargetDirectory(string $targetDir): void
    {
        if (!is_dir($targetDir)) {
            throw FileAccessException::directoryNotAccessible($targetDir);
        }
        
        if (!is_readable($targetDir)) {
            throw FileAccessException::directoryNotAccessible($targetDir);
        }
    }

    /**
     * Handle audit failure with proper cleanup and logging
     *
     * @param AuditException $exception
     * @param float $startTime
     * @return void
     */
    private function handleAuditFailure(AuditException $exception, float $startTime): void
    {
        $duration = microtime(true) - $startTime;
        
        $this->progressTracker->updatePhase('error');
        $this->logger->critical('Audit execution failed', array_merge($exception->getContext(), [
            'duration' => round($duration, 2) . 's',
            'current_file' => $this->currentFile
        ]));
        
        // Backup progress before marking as failed
        $this->progressTracker->backupProgress();
    }

    /**
     * Recover from analysis errors using configured strategies
     *
     * @param AuditException $exception
     * @param string $filePath
     * @return array Empty findings array as fallback
     */
    private function recoverFromError(AuditException $exception, string $filePath): array
    {
        $exceptionClass = get_class($exception);
        
        if (isset($this->errorRecoveryStrategies[$exceptionClass])) {
            $strategy = $this->errorRecoveryStrategies[$exceptionClass];
            return $this->$strategy($exception, $filePath);
        }
        
        // Default recovery: log error and continue with empty findings
        $this->logger->warning("Using default error recovery for {$filePath}");
        return [];
    }

    /**
     * Handle file access errors
     *
     * @param FileAccessException $exception
     * @param string $filePath
     * @return array
     */
    private function handleFileAccessError(FileAccessException $exception, string $filePath): array
    {
        $this->logger->warning("File access error for {$filePath}, skipping file");
        return [];
    }

    /**
     * Handle analysis errors
     *
     * @param AnalysisException $exception
     * @param string $filePath
     * @return array
     */
    private function handleAnalysisError(AnalysisException $exception, string $filePath): array
    {
        $this->logger->warning("Analysis error for {$filePath}, attempting partial analysis");
        
        // Try to run only basic analyzers that are less likely to fail
        $basicFindings = [];
        foreach ($this->analyzers as $analyzer) {
            if ($this->isBasicAnalyzer($analyzer)) {
                try {
                    $content = file_get_contents($filePath);
                    if ($content !== false) {
                        $findings = $analyzer->analyze($filePath, $content);
                        $basicFindings = array_merge($basicFindings, $findings);
                    }
                } catch (\Exception $e) {
                    // Skip this analyzer
                    continue;
                }
            }
        }
        
        return $basicFindings;
    }

    /**
     * Handle MCP connection errors
     *
     * @param MCPConnectionException $exception
     * @param string $filePath
     * @return array
     */
    private function handleMCPConnectionError(MCPConnectionException $exception, string $filePath): array
    {
        $this->logger->warning("MCP connection error, using fallback analysis for {$filePath}");
        
        // Run analysis without MCP-dependent features
        return $this->analyzeFileWithoutMCP($filePath);
    }

    /**
     * Handle configuration errors
     *
     * @param ConfigurationException $exception
     * @param string $filePath
     * @return array
     */
    private function handleConfigurationError(ConfigurationException $exception, string $filePath): array
    {
        $this->logger->error("Configuration error during analysis of {$filePath}");
        return [];
    }

    /**
     * Check if an analyzer error is critical
     *
     * @param \Exception $exception
     * @return bool
     */
    private function isCriticalAnalyzerError(\Exception $exception): bool
    {
        // Consider memory errors and timeouts as critical
        return strpos($exception->getMessage(), 'memory') !== false ||
               strpos($exception->getMessage(), 'timeout') !== false ||
               $exception instanceof \Error;
    }

    /**
     * Check if an analyzer is considered basic/safe
     *
     * @param \AuditSystem\Interfaces\AnalyzerInterface $analyzer
     * @return bool
     */
    private function isBasicAnalyzer(\AuditSystem\Interfaces\AnalyzerInterface $analyzer): bool
    {
        $basicAnalyzers = [
            'FileScanner',
            'BasicSecurityAnalyzer',
            'SimpleCodeAnalyzer'
        ];
        
        $analyzerClass = get_class($analyzer);
        foreach ($basicAnalyzers as $basicAnalyzer) {
            if (strpos($analyzerClass, $basicAnalyzer) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Analyze file without MCP-dependent features
     *
     * @param string $filePath
     * @return array
     */
    private function analyzeFileWithoutMCP(string $filePath): array
    {
        // This would run local analyzers only
        // Implementation depends on specific analyzer architecture
        return [];
    }
}