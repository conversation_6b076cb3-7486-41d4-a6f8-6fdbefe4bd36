# Technology Stack

## Backend
- **PHP 8.3+** - Server-side scripting language
- **MySQL/MariaDB** - Database management system
- **PDO** - Database abstraction layer for secure database operations

## Frontend
- **HTML5** with semantic markup
- **CSS3** with Tailwind CSS framework
- **JavaScript** for interactive features
- **Alpine.js** for reactive components (x-data, x-init directives)
- **SVG icons** for UI elements

## Key Libraries & Dependencies
- **Parsedown** - Markdown parser for content processing
- **DeepSeek API** - AI integration for content generation and optimization
- **Facebook SDK** - Social media integration
- **Google AdSense** - Advertisement management

## Image Processing
- Custom image processing system with multiple sizes:
  - `xs` (320px) - Mobile devices
  - `ss` (400px) - Small screens
  - `ms` (800px) - Medium screens
  - `ls` (1200px) - Large screens
  - `fb` (1200px) - Facebook/OG images
- WebP format support with fallbacks
- Responsive image delivery via `image.php`

## Development Commands
Since this is a traditional PHP application, there are no build commands. Development workflow:

```bash
# Local development server (if using PHP built-in server)
php -S localhost:8000 -t public_html/

# Database operations
# Import database: mysql -u username -p database_name < lakofino_cms.sql
# Backup database: mysqldump -u username -p database_name > backup.sql
```

## Configuration
- Main config in `config.php`
- Database credentials and site settings
- API keys for external services (DeepSeek, Facebook, AdSense)
- Image processing settings and CDN configuration