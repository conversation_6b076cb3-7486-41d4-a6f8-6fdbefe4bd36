<?php

require_once '../vendor/autoload.php';

use AuditSystem\Services\MCPClient;
use AuditSystem\Services\BestPracticesChecker;
use AuditSystem\Config\AuditConfig;
use AuditSystem\Models\Finding;

/**
 * Example demonstrating how to integrate MCP context7 server
 * with the audit system for best practices validation
 */

echo "MCP Integration Example\n";
echo "======================\n\n";

// Initialize configuration and MCP client
$config = AuditConfig::getInstance();
$mcpClient = new MCPClient($config);
$bestPracticesChecker = new BestPracticesChecker($mcpClient, $config);

// Connect to MCP server
echo "1. Connecting to MCP server...\n";
try {
    $mcpClient->connect();
    echo "   ✅ Connected to context7 server\n";
    
    $capabilities = $mcpClient->getCapabilities();
    echo "   📋 Server capabilities: " . implode(', ', $capabilities['methods']) . "\n\n";
} catch (Exception $e) {
    echo "   ❌ Connection failed: " . $e->getMessage() . "\n";
    echo "   🔄 Will use fallback validation\n\n";
}

// Example 1: Validate PHP code for security issues
echo "2. Validating PHP code for security issues...\n";
$phpCode = '<?php
$user_id = $_GET["id"];
$query = "SELECT * FROM users WHERE id = " . $user_id;
$result = mysql_query($query);
?>';

$validation = $bestPracticesChecker->checkCode($phpCode, 'php', 'security audit');
echo "   📊 Validation score: " . $validation['score'] . "/100\n";
echo "   🔍 Issues found: " . count($validation['issues']) . "\n";

foreach ($validation['issues'] as $issue) {
    echo "   ⚠️  " . $issue['severity'] . ": " . $issue['message'] . "\n";
    echo "      💡 " . $issue['recommendation'] . "\n";
}
echo "\n";

// Example 2: Get best practices for PHP security
echo "3. Retrieving PHP security best practices...\n";
$practices = $bestPracticesChecker->getBestPractices('php-security', 'CMS security audit');
echo "   📚 Best practices for " . $practices['technology'] . ":\n";

foreach ($practices['practices'] as $practice => $description) {
    echo "   • " . str_replace('_', ' ', ucfirst($practice)) . ": " . $description . "\n";
}
echo "\n";

// Example 3: Enhance existing findings with best practices
echo "4. Enhancing findings with best practices...\n";
$findings = [
    new Finding(
        'public_html/config.php',
        15,
        Finding::TYPE_SECURITY,
        Finding::SEVERITY_CRITICAL,
        Finding::PRIORITY_AREA,
        'SQL injection vulnerability in user authentication',
        'Use PDO prepared statements',
        'mysql_query("SELECT * FROM users WHERE username = \'" . $_POST["username"] . "\'");'
    ),
    new Finding(
        'public_html/article.php',
        42,
        Finding::TYPE_SECURITY,
        Finding::SEVERITY_HIGH,
        Finding::PRIORITY_AREA,
        'Unescaped user input in HTML output',
        'Use htmlspecialchars() to escape output',
        'echo "<h1>" . $_GET["title"] . "</h1>";'
    )
];

$enhancedFindings = $bestPracticesChecker->validateFindings($findings);
echo "   🔧 Enhanced " . count($enhancedFindings) . " findings with best practice references\n";

foreach ($enhancedFindings as $finding) {
    echo "   📄 " . $finding->getFile() . ":" . $finding->getLine() . "\n";
    echo "      🎯 " . $finding->getDescription() . "\n";
    echo "      💡 " . $finding->getRecommendation() . "\n";
    
    if (!empty($finding->getReferences())) {
        echo "      📖 Best practices:\n";
        foreach ($finding->getReferences() as $reference) {
            echo "         • " . $reference . "\n";
        }
    }
    echo "\n";
}

// Example 4: Test different programming languages
echo "5. Testing validation for different languages...\n";

$testCases = [
    ['JavaScript with eval()', 'eval(userInput);', 'javascript'],
    ['CSS with deep nesting', '.a { .b { .c { .d { color: red; } } } }', 'css'],
    ['Secure PHP code', '<?php echo htmlspecialchars($_GET["safe"]); ?>', 'php']
];

foreach ($testCases as [$description, $code, $language]) {
    echo "   🧪 Testing: $description\n";
    $result = $bestPracticesChecker->checkCode($code, $language);
    echo "      📊 Score: " . $result['score'] . "/100\n";
    echo "      🔍 Issues: " . count($result['issues']) . "\n";
    
    if (!empty($result['issues'])) {
        foreach ($result['issues'] as $issue) {
            echo "         ⚠️  " . $issue['message'] . "\n";
        }
    }
    echo "\n";
}

// Example 5: Cache performance demonstration
echo "6. Demonstrating caching performance...\n";
$testCode = '<?php echo "Performance test"; ?>';

// First call (no cache)
$start = microtime(true);
$result1 = $bestPracticesChecker->checkCode($testCode, 'php');
$time1 = microtime(true) - $start;

// Second call (cached)
$start = microtime(true);
$result2 = $bestPracticesChecker->checkCode($testCode, 'php');
$time2 = microtime(true) - $start;

echo "   ⏱️  First call: " . number_format($time1 * 1000, 2) . "ms\n";
echo "   ⏱️  Second call: " . number_format($time2 * 1000, 2) . "ms\n";
echo "   🚀 Speed improvement: " . number_format(($time1 - $time2) / $time1 * 100, 1) . "%\n\n";

// Cache statistics
$stats = $bestPracticesChecker->getCacheStats();
echo "7. Cache statistics:\n";
echo "   📊 Cache entries: " . $stats['entries'] . "\n";
echo "   💾 Cache size: " . number_format($stats['size'] / 1024, 2) . " KB\n";
echo "   🎯 Hit rate: " . number_format($stats['hit_rate'], 1) . "%\n\n";

// Cleanup
echo "8. Cleanup...\n";
$bestPracticesChecker->clearCache();
$mcpClient->disconnect();
echo "   ✅ Cache cleared and MCP connection closed\n\n";

echo "Example completed successfully! 🎉\n";
echo "\nKey features demonstrated:\n";
echo "• MCP server connectivity and fallback mechanisms\n";
echo "• Code validation against current best practices\n";
echo "• Multi-language support (PHP, JavaScript, CSS)\n";
echo "• Finding enhancement with best practice references\n";
echo "• Caching for improved performance\n";
echo "• Comprehensive error handling\n";