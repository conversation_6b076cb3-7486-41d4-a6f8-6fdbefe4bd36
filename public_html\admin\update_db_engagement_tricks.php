<?php
// <PERSON><PERSON><PERSON> to update the database with the new engagement_trick_type column

// Include configuration
require_once '../config.php';

try {
    // SQL to add the new column
    $sql = "ALTER TABLE `articles` 
            ADD COLUMN IF NOT EXISTS `engagement_trick_type` VARCHAR(50) DEFAULT NULL 
            COMMENT 'Type of engagement trick to use for this article' 
            AFTER `enable_loading_trick`;";
    
    // Execute the SQL
    $pdo->exec($sql);
    
    echo "Database updated successfully. Added engagement_trick_type column to articles table.\n";
} catch (PDOException $e) {
    echo "Error updating database: " . $e->getMessage() . "\n";
}
?>
