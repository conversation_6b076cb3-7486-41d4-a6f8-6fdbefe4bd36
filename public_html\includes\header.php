<?php
/**
 * Header file for the public-facing site.
 * v1.3: Modified Page View Logging to use aggregation buffer table (page_views_buffer).
 * v1.2: Integrated Page View Logging and body data attributes for JS tracking into original structure.
 *
 * This file assumes config.php has been included before it.
 * It expects certain variables to be set by the calling script (e.g., article.php, index.php)
 * for dynamic meta tag generation:
 *
 * - $page_title (string): The title for the <title> tag.
 * - $meta_description (string): The description for the <meta name="description"> tag.
 * - $og_title (string): The title for Open Graph (og:title). Defaults to $page_title.
 * - $og_description (string): The description for Open Graph (og:description). Defaults to $meta_description.
 * - $og_image_url (string): The URL for the Open Graph image (og:image). Defaults to a default image.
 * - $og_image_width (int): The width of the Open Graph image (og:image:width). **MUST be set by calling script.**
 * - $og_image_height (int): The height of the Open Graph image (og:image:height). **MUST be set by calling script.**
 * - $og_image_alt (string): The alt text for the Open Graph image (og:image:alt). Optional specific alt.
 * - $page_category_slug (string): The slug of the current page's category (passed from article.php, etc.). Optional.
 * - $og_url (string): The canonical URL for the Open Graph object (og:url). Defaults to the current request URL.
 * - $og_type (string): The type of the Open Graph object (e.g., 'website', 'article'). Defaults to 'website'.
 * - $final_focus_keyword (string): Escaped focus keyword string (set in article.php).
 * - $final_tags_string (string): Escaped comma-separated tags string (set in article.php).
 * - $article (array): If on an article page, this array is expected to contain article data including 'id'.
 * - $category (array): If on a category page, this array is expected to contain category data including 'id'.
 *
 * Changelog:
 * - 2025-04-22: Modified Page View Logging to use aggregation buffer table (page_views_buffer).
 * - 2025-04-15: Integrated Page View Logging and body data attributes.
 * - 2025-04-06: Added complete menu items with icons to both mobile and desktop menus.
 * - ... (previous changelog entries) ...
 */

// Start session if not already started (needed for potential session ID logging)
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// --- Page View Logging (AGGREGATION VERSION) ---
// Basic check to avoid logging requests for assets or specific scripts
$isAssetRequest = preg_match('/\.(css|js|png|jpg|jpeg|gif|webp|ico|svg|woff|woff2|ttf|eot|map)$/i', $_SERVER['REQUEST_URI']);
$isTrackingScript = in_array(basename($_SERVER['PHP_SELF']), ['track_click.php', 'record_impression.php', 'process_page_views.php']); // Exclude the processing script too

// Check if $pdo is set (it should be if config.php was included)
if (!$isAssetRequest && !$isTrackingScript && isset($pdo) && $pdo instanceof PDO) {
    try {
        // Check if the new buffer table exists before attempting to insert/update
        $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'page_views_buffer'");
        $tableExists = $tableCheckStmt->rowCount() > 0;

        if ($tableExists) {
            // Prepare the INSERT ... ON DUPLICATE KEY UPDATE statement for the buffer table
            $sql = "INSERT INTO page_views_buffer (view_date, view_hour, url_hash, url, view_count)
                    VALUES (:view_date, :view_hour, :url_hash, :url, 1)
                    ON DUPLICATE KEY UPDATE view_count = view_count + 1";

            $url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
            $url_hash = hash('sha256', $url); // Use SHA256 hash for the unique key
            $view_date = date('Y-m-d');
            $view_hour = date('G'); // Hour in 24-format without leading zeros

            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':view_date', $view_date, PDO::PARAM_STR);
            $stmt->bindParam(':view_hour', $view_hour, PDO::PARAM_INT);
            $stmt->bindParam(':url_hash', $url_hash, PDO::PARAM_STR);
            $stmt->bindParam(':url', $url, PDO::PARAM_STR);

            $stmt->execute();
        } else {
             // Log only once per request if buffer table missing
             if (!defined('PAGE_VIEW_BUFFER_TABLE_MISSING_LOGGED')) {
                 error_log("Page view logging skipped: 'page_views_buffer' table does not exist.");
                 define('PAGE_VIEW_BUFFER_TABLE_MISSING_LOGGED', true);
             }
        }

    } catch (PDOException $e) {
        // Log error but don't stop page execution
        $current_url_for_log = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
        error_log("Error logging page view to buffer: " . $e->getMessage() . " | URL: " . $current_url_for_log);
    }
} elseif (!$isAssetRequest && !$isTrackingScript && !isset($pdo)) {
     if (!defined('PDO_MISSING_LOGGED')) {
        error_log("Error logging page view: \$pdo database connection is not available in header.php.");
        define('PDO_MISSING_LOGGED', true);
     }
}
// --- End Page View Logging ---


// --- Define Defaults ---
if (!defined('SITE_URL')) {
    // Determine protocol based on common headers
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
                (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https') ||
                 $_SERVER['SERVER_PORT'] == 443
                 ? "https://" : "http://";
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    define('SITE_URL', rtrim($protocol . $host, '/'));
    error_log("SITE_URL constant was not defined, fallback used in header.php: " . SITE_URL);
}
if (!defined('SITE_NAME')) {
     define('SITE_NAME', 'Lako & Fino');
     error_log("SITE_NAME constant was not defined, fallback used in header.php.");
}

$current_url = SITE_URL . $_SERVER['REQUEST_URI'];

// Ensure helper functions are available (should be included via config.php)
if (!function_exists('getDefaultOgImageData')) {
     error_log("FATAL ERROR: Function getDefaultOgImageData() not found in header.php.");
     $defaultImageData = ['url'=>SITE_URL.'/uploads/images/site/default-og-image.png','width'=>1200,'height'=>630,'alt'=>'Default image','og_alt'=>'Lako & Fino']; // Added default URL assumption
} else {
    $defaultImageData = getDefaultOgImageData();
}
if (!function_exists('escape')) {
     error_log("FATAL ERROR: Function escape() not found in header.php.");
     // Define a basic fallback
     function escape($string) { return htmlspecialchars($string ?? '', ENT_QUOTES, 'UTF-8'); }
}


$default_og_image_url = $defaultImageData['url'];
$default_og_image_width = $defaultImageData['width'];
$default_og_image_height = $defaultImageData['height'];
$default_og_image_alt = $defaultImageData['og_alt'];
$default_description = 'Dobrodošli na ' . SITE_NAME . ' - Ukusni recepti, savjeti za zdravlje i prirodni lijekovi.';

// --- Set Meta Variables with Fallbacks ---
// These variables should be set by the specific page including the header (e.g., article.php, index.php)
$page_title = $page_title ?? SITE_NAME;
$meta_description = $meta_description ?? $default_description;
$og_title = $og_title ?? $page_title;
$og_description = $og_description ?? $meta_description;
$og_image_url = $og_image_url ?? $default_og_image_url;
$og_image_width = $og_image_width ?? $default_og_image_width;
$og_image_height = $og_image_height ?? $default_og_image_height;
$og_url = $og_url ?? $current_url;
$og_type = $og_type ?? 'website';
$og_image_alt = $og_image_alt ?? '';
$page_category_slug = $page_category_slug ?? null;
$final_focus_keyword = $final_focus_keyword ?? '';
$final_tags_string = $final_tags_string ?? '';

// --- Determine page type and ID for JS tracking ---
$current_page_type = 'unknown';
$current_page_id = null;
// Simple detection based on script name or variables set by including page
$script_name = basename($_SERVER['PHP_SELF']);
if ($script_name === 'index.php') {
    $current_page_type = 'homepage';
} elseif ($script_name === 'article.php' && isset($article['id'])) { // Assuming article.php sets $article
    $current_page_type = 'article';
    $current_page_id = $article['id'];
} elseif ($script_name === 'category.php' && isset($category['id'])) { // Assuming category.php sets $category
    $current_page_type = 'category';
    $current_page_id = $category['id'];
} // Add more conditions for other page types (tag.php, search.php etc.)


// Escape values for output
$final_page_title = escape($page_title) . ' - ' . escape(SITE_NAME);
$final_meta_description = escape($meta_description);
$final_og_title = escape($og_title);
$final_og_description = escape($og_description);
$final_og_image_url = escape($og_image_url);
$final_og_image_width = (int)$og_image_width;
$final_og_image_height = (int)$og_image_height;
$final_og_url = escape($og_url);
$final_og_type = escape($og_type);

// --- Determine the OG Image Alt Text ---
$final_og_image_alt = '';
$recipeCategorySlug = 'recepti';
$defaultRecipeOgAlt = 'Ukusni recept objavljen na Lako & Fino.';
if (!empty($og_image_alt)) {
    $final_og_image_alt = escape($og_image_alt);
} elseif ($final_og_image_url === $defaultImageData['url']) {
    $final_og_image_alt = escape($defaultImageData['og_alt']);
} elseif (isset($page_category_slug) && $page_category_slug === $recipeCategorySlug) {
    $final_og_image_alt = escape($defaultRecipeOgAlt);
} else {
    $final_og_image_alt = $final_og_title; // Fallback to OG title
}

?>
<!DOCTYPE html>
<?php // Set language attribute on HTML tag ?>
<html lang="bs">
<head>
<script src="<?php echo SITE_URL; ?>/js/ads.js" async></script> <?php // Ensure ads.js is loaded ?>

<?php // Google AdSense Code
if (defined('ADSENSE_CLIENT_ID') && !empty(ADSENSE_CLIENT_ID)): ?>
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=<?php echo ADSENSE_CLIENT_ID; ?>" crossorigin="anonymous"></script>
<?php endif; ?>

<?php // Facebook Pixel Code
if (defined('FB_PIXEL_ID') && !empty(FB_PIXEL_ID)) {
    echo generateFacebookPixelCode(FB_PIXEL_ID);
}
?>
    <?php // --- Basic Meta --- ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="theme-color" content="#ff6481">

    <?php // --- Title --- ?>
    <title><?php echo $final_page_title; ?></title>

    <?php // --- Standard SEO / Info Meta --- ?>
    <meta name="description" content="<?php echo $final_meta_description; ?>">
    <?php if (!empty($final_focus_keyword)): ?>
    <meta name="keywords" content="<?php echo $final_focus_keyword; ?>">
    <?php endif; ?>
    <meta name="language" content="Bosnian">
    <?php // Add author meta if relevant/available
       // if (!empty($authorName)) echo '<meta name="author" content="' . escape($authorName) . '">';
    ?>

    <?php // --- Open Graph Meta Tags --- ?>
    <meta property="og:title" content="<?php echo $final_og_title; ?>">
    <meta property="og:description" content="<?php echo $final_og_description; ?>">
    <meta property="og:image" content="<?php echo $final_og_image_url; ?>">
    <meta property="og:image:width" content="<?php echo $final_og_image_width; ?>">
    <meta property="og:image:height" content="<?php echo $final_og_image_height; ?>">
    <meta property="og:image:alt" content="<?php echo $final_og_image_alt; ?>">
    <meta property="og:url" content="<?php echo $final_og_url; ?>">
    <meta property="og:type" content="<?php echo $final_og_type; ?>">
    <meta property="og:site_name" content="<?php echo escape(SITE_NAME); ?>">
    <meta property="og:locale" content="bs_BA">
    <?php // Article specific OG tags
        if ($final_og_type === 'article' && !empty($final_tags_string)): ?>
    <meta property="article:tag" content="<?php echo $final_tags_string; ?>">
    <?php endif; ?>
    <?php // Example for published time (ensure $published_time_iso is set in calling script):
        // if ($final_og_type === 'article' && isset($published_time_iso)):
        //    echo '<meta property="article:published_time" content="' . escape($published_time_iso) . '">';
        // endif;
    ?>
    <?php // Example for author (ensure $author_fb_url is set in calling script):
        // if ($final_og_type === 'article' && isset($author_fb_url)):
        //    echo '<meta property="article:author" content="' . escape($author_fb_url) . '">';
        // endif;
    ?>


    <?php // --- Twitter Card Meta Tags --- ?>
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $final_og_title; ?>">
    <meta name="twitter:description" content="<?php echo $final_og_description; ?>">
    <meta name="twitter:image" content="<?php echo $final_og_image_url; ?>">
    <?php // Optional: Add Twitter site/creator tags
    // <meta name="twitter:site" content="@YourTwitterHandle">
    // <meta name="twitter:creator" content="@AuthorTwitterHandle">
    ?>

    <?php // --- Favicons / Icons --- ?>
    <?php
    // Set the favicon paths to the appropriate locations
    $favicon_path = '/uploads/images/site/favicon.ico';
    $apple_touch_icon_path = '/uploads/images/site/apple-touch-icon.png';
    $favicon_32_path = '/uploads/images/site/favicon-32x32.png';
    $favicon_16_path = '/uploads/images/site/favicon-16x16.png';
    $android_chrome_192_path = '/uploads/images/site/android-chrome-192x192.png';
    $android_chrome_512_path = '/uploads/images/site/android-chrome-512x512.png';
    $webmanifest_path = '/uploads/images/site/site.webmanifest';
    ?>
    <link rel="icon" href="<?php echo escape(SITE_URL . $favicon_path); ?>" type="image/x-icon">
    <link rel="shortcut icon" href="<?php echo escape(SITE_URL . $favicon_path); ?>" type="image/x-icon">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo escape(SITE_URL . $apple_touch_icon_path); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo escape(SITE_URL . $favicon_32_path); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo escape(SITE_URL . $favicon_16_path); ?>">
    <link rel="icon" type="image/png" sizes="192x192" href="<?php echo escape(SITE_URL . $android_chrome_192_path); ?>">
    <link rel="icon" type="image/png" sizes="512x512" href="<?php echo escape(SITE_URL . $android_chrome_512_path); ?>">
    <link rel="manifest" href="<?php echo escape(SITE_URL . $webmanifest_path); ?>">

    <?php // --- Resource Hints & Font Loading --- ?>
    <link rel="preconnect" href="https://cdn.tailwindcss.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <?php // --- CSS --- ?>
    <script src="https://cdn.tailwindcss.com"></script> <?php // Tailwind via CDN ?>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/internal-links.css"><?php // Internal Links CSS ?>
    <script>
        // Tailwind configuration
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        background: '#FFF4F5', primary: '#ff6481', secondary: '#ffd6dd',
                        dark: '#333333', 'dark-contrast': '#222222', light: '#ffffff',
                        border: '#feeaec', 'gray-medium': '#6b7280', 'gray-darker': '#4b5563'
                    },
                    fontFamily: {
                        sans: ['Open Sans', 'sans-serif'], montserrat: ['Montserrat', 'sans-serif'],
                    },
                    borderRadius: { 'xl': '1rem', '2xl': '1.5rem' },
                    maxWidth: { '900': '900px' },
                    screens: { 'xs': '475px' }
                }
            }
        };
    </script>
    <style>
        /* Base styles */
        body { background-color: #FFF4F5; font-family: 'Open Sans', sans-serif; color: #333333; overflow-x: hidden; }
        header { transition: all 0.3s ease-in-out; }
        .font-montserrat { font-family: 'Montserrat', sans-serif; }
        .text-primary { color: #ff6481; }
        .bg-primary { background-color: #ff6481; }
        .bg-white { background-color: #ffffff; }
        a { text-decoration: none; } a:hover { text-decoration: none; }
        .progress-bar { position: fixed; top: 0; left: 0; height: 4px; background-color: #ff6481; z-index: 60; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); transition: width 0.1s ease-out; }
        .latest-articles-heading { color: #333333 !important; } .latest-articles-heading::after { background-color: #ff6481; }
        .footer-text { color: #4b5563; } .ad-placeholder-text { color: #4b5563; }
    </style>
    <style type="text/tailwindcss">
        @layer components {
            /* Buttons */
            .btn { @apply bg-primary text-white px-[22px] py-[5px] rounded text-base font-medium relative z-[2] inline-block transition-all duration-300 hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 font-montserrat; }
            /* Cards */
            .card { @apply bg-white rounded-xl transition-all duration-300 border border-border; }
            /* Header */
            .header-transition { @apply transition-all duration-300 ease-in-out; }
            /* Tag Colors */
            .tag-base { @apply flex items-center text-sm px-3 py-1 rounded-full transition-colors duration-200; }
            .tag-technology { @apply tag-base bg-blue-100 text-blue-900 hover:bg-blue-500 hover:text-white; }
            .tag-health { @apply tag-base bg-green-100 text-green-900 hover:bg-green-500 hover:text-white; }
            .tag-lifestyle { @apply tag-base bg-purple-100 text-purple-900 hover:bg-purple-500 hover:text-white; }
            .tag-business { @apply tag-base bg-amber-100 text-amber-900 hover:bg-amber-500 hover:text-white; }
            .tag-travel { @apply tag-base bg-teal-100 text-teal-900 hover:bg-teal-500 hover:text-white; }
            .tag-food { @apply tag-base bg-red-100 text-red-900 hover:bg-red-500 hover:text-white; }
            .tag-fashion { @apply tag-base bg-pink-100 text-pink-900 hover:bg-pink-500 hover:text-white; }
            .tag-default { @apply tag-base bg-gray-100 text-gray-800 hover:bg-gray-500 hover:text-white; }
            /* Search Overlay */
            .search-overlay { @apply fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 transition-all duration-300; }
            /* Headings */
            .heading { @apply font-montserrat font-bold; }
            .article-title { @apply font-montserrat font-bold; }
            /* Reader Pulse Animation */
            .reader-pulse { animation: reader-pulse-transform 1.5s cubic-bezier(0.215, 0.61, 0.355, 1); }
            @keyframes reader-pulse-transform { 0% { transform: scale(1); } 10% { transform: scale(1.15); } 20% { transform: scale(1.05); } 30% { transform: scale(1.1); } 100% { transform: scale(1); } }
            /* Article Specific Styles */
            .article-content h2 { @apply text-2xl font-montserrat font-bold mt-8 mb-4 text-dark-contrast; }
            .article-content h3 { @apply text-xl font-montserrat font-bold mt-6 mb-3 text-dark-contrast; }
            .article-content p { @apply mb-4 leading-relaxed text-gray-darker; }
            .article-content ul { @apply list-disc ml-6 mb-4 space-y-2 text-gray-darker; }
            .article-content ol { @apply list-decimal ml-6 mb-4 space-y-2 text-gray-darker; }
            .article-content blockquote { @apply pl-4 border-l-4 border-primary italic my-6 text-gray-darker py-1; }
            .article-content img { @apply rounded-xl my-6 mx-auto max-w-full h-auto block; }
            .article-content a { @apply text-primary no-underline hover:text-primary/80; }
            .article-content pre { @apply bg-gray-800 text-white p-4 rounded-xl overflow-x-auto mb-6 text-sm; }
            .article-content code:not(pre code) { @apply bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-dark-contrast; }
            .article-content pre code { @apply bg-transparent p-0 rounded-none font-mono; }
            .article-content table { @apply w-full border-collapse mb-6; }
            .article-content table th { @apply bg-gray-100 p-2 text-left border border-gray-300 text-dark-contrast; }
            .article-content table td { @apply p-2 border border-gray-300 text-gray-darker; }
            /* Mobile / Unified Menu Styles */
            .article-card-mobile { @apply flex flex-col p-0 overflow-hidden w-full; }
            .mobile-article-image { @apply w-full h-40 relative overflow-hidden z-[1]; }
            .mobile-article-content { @apply w-full p-4 flex flex-col justify-between; }
            .tag-container { @apply flex flex-wrap gap-2; }
            .mobile-menu-container { @apply fixed top-0 left-0 w-full h-full bg-white z-[60] transform transition-transform duration-300 ease-in-out; }
            .mobile-bottom-nav { @apply md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-border z-40 flex justify-around items-center py-2 px-1; }
            .mobile-bottom-nav-item { @apply flex flex-col items-center justify-center px-2 py-1 no-underline; }
            .mobile-bottom-nav-icon { @apply h-6 w-6 text-gray-medium; }
            .mobile-bottom-nav-text { @apply text-xs text-gray-medium mt-1; }
             /* Responsive Layout Handling */
             @media (max-width: 768px) { .sidebar-content { @apply flex flex-col md:hidden mt-8 space-y-6; } .article-card-desktop { @apply hidden; } .article-card-mobile { @apply block; } }
             @media (min-width: 769px) { .sidebar-content { @apply block; } .article-card-desktop { @apply flex; } .article-card-mobile { @apply hidden; } }
        }
    </style>

    <?php // --- JavaScript --- ?>
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.13.5/cdn.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <?php // Add other header JS if needed ?>
</head>
<?php // Added data attributes to body for JS tracking ?>
<body class="bg-background font-sans text-dark antialiased"
      data-page-type="<?php echo escape($current_page_type); ?>"
      data-page-id="<?php echo escape($current_page_id ?? ''); ?>">

    <?php // Reading progress bar ?>
    <div id="progress-bar" class="progress-bar" style="width: 0%"></div>

    <?php // Search Overlay (Alpine.js) ?>
    <div
        x-data="{ isOpen: false }" x-show="isOpen" @keydown.escape.window="isOpen = false"
        @search-open.window="isOpen = true; $nextTick(() => { document.getElementById('search-input').focus(); })"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0" class="search-overlay" style="display: none;" x-cloak
    >
        <div class="bg-white rounded-xl p-6 max-w-lg w-full mx-4 shadow-xl" @click.away="isOpen = false">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-montserrat font-extrabold text-dark-contrast">Brza pretraga</h3>
                <button @click="isOpen = false" class="text-gray-medium hover:text-primary" aria-label="Zatvori pretragu">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
            </div>
            <?php // Search Form pointing to search.php ?>
            <form action="<?php echo SITE_URL; ?>/search.php" method="GET" class="relative">
                <input id="search-input" type="text" name="query" placeholder="Pretražite članke, teme, autore..."
                       class="w-full bg-gray-100 rounded py-3 px-6 pr-12 focus:outline-none focus:ring-2 focus:ring-primary text-base text-dark-contrast">
                <button type="submit" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-primary" aria-label="Pretraži">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
                </button>
            </form>
            <div class="mt-6">
                <h4 class="font-montserrat font-bold mb-2 text-sm text-gray-medium">POPULARNI UPITI</h4>
                <div class="flex flex-wrap gap-2">
                    <a href="<?php echo SITE_URL; ?>/search.php?query=recepti" class="bg-gray-100 px-3 py-1 rounded text-sm text-gray-darker hover:bg-primary hover:text-white transition-colors">Recepti</a>
                    <a href="<?php echo SITE_URL; ?>/search.php?query=prirodni+lijekovi" class="bg-gray-100 px-3 py-1 rounded text-sm text-gray-darker hover:bg-primary hover:text-white transition-colors">Prirodni lijekovi</a>
                    <a href="<?php echo SITE_URL; ?>/search.php?query=slana+jela" class="bg-gray-100 px-3 py-1 rounded text-sm text-gray-darker hover:bg-primary hover:text-white transition-colors">Slana jela</a>
                    <a href="<?php echo SITE_URL; ?>/search.php?query=slatka+jela" class="bg-gray-100 px-3 py-1 rounded text-sm text-gray-darker hover:bg-primary hover:text-white transition-colors">Slatka jela</a>
                    </div>
            </div>
        </div>
    </div>

    <?php // Mobile Menu Overlay (Alpine.js) ?>
    <div
        x-data="{ isOpen: false }" x-show="isOpen" @keydown.escape.window="isOpen = false"
        @mobile-menu-open.window="isOpen = true" class="mobile-menu-container"
        :class="isOpen ? 'translate-x-0' : '-translate-x-full'" style="display: none;" x-cloak
    >
        <?php // Close button and logo inside overlay ?>
        <div class="p-4 border-b border-border">
            <div class="flex justify-between items-center">
                <h3 class="text-xl font-montserrat font-extrabold text-primary">
                    <a href="<?php echo SITE_URL; ?>">
                        <span class="relative inline-block">
                            Lako <span class="text-dark-contrast italic font-light">&</span> <span class="relative">Fino
                                <span class="absolute -bottom-1 left-0 w-full h-1 bg-primary rounded-full"></span>
                            </span>
                        </span>
                    </a>
                </h3>
                <button @click="isOpen = false" class="text-gray-medium hover:text-primary" aria-label="Zatvori meni">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
            </div>
        </div>
        <?php // Scrollable menu content ?>
        <div class="p-4 overflow-y-auto max-h-screen pb-32">
            <div class="mb-6">
                <h4 class="text-xs font-medium text-gray-medium uppercase tracking-wider mb-2">Kategorije</h4>
                <ul class="space-y-3">
                    <li>
                        <a href="<?php echo SITE_URL; ?>" class="flex items-center text-base text-gray-darker hover:text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" /></svg>
                            Početna
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/category/recepti/" class="flex items-center text-base text-gray-darker hover:text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" /></svg>
                            Recepti
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/category/prirodni-lijekovi/" class="flex items-center text-base text-gray-darker hover:text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" /></svg>
                            Prirodni lijekovi
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/category/vijesti/" class="flex items-center text-base text-gray-darker hover:text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>
                            Vijesti
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/popularno.php" class="flex items-center text-base text-gray-darker hover:text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg>
                            Popularno
                        </a>
                    </li>
                </ul>
            </div>
            <div class="mb-6">
                <h4 class="text-xs font-medium text-gray-medium uppercase tracking-wider mb-2">Popularne oznake</h4>
                <div class="tag-container">
                    <a href="<?php echo SITE_URL; ?>/category/slana-jela/" class="tag-food">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" /></svg>
                        Slana jela
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/prirodni-lijekovi/" class="tag-health">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" /></svg>
                        Prirodni lijekovi
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/slatka-jela/" class="tag-lifestyle">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg>
                        Slatka jela
                    </a>
                </div>
            </div>
            <div class="mb-6">
                <h4 class="text-xs font-medium text-gray-medium uppercase tracking-wider mb-2">Stranice</h4>
                <ul class="space-y-3">
                    <li>
                        <a href="<?php echo SITE_URL; ?>/o-nama" class="flex items-center text-base text-gray-darker hover:text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                            O nama
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/kontakt" class="flex items-center text-base text-gray-darker hover:text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>
                            Kontakt
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/uslovi-koristenja" class="flex items-center text-base text-gray-darker hover:text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>
                            Uslovi korištenja
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/politika-privatnosti" class="flex items-center text-base text-gray-darker hover:text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
                            Politika privatnosti
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <?php // Desktop Header (Alpine.js) ?>
    <header
        x-data="{ scrolled: false, menuOpen: false, headerWidth: window.innerWidth < 768 ? '100%' : '90%', headerRadius: window.innerWidth < 768 ? '0' : '100px', headerTop: window.innerWidth < 768 ? '0px' : '20px' }"
        x-init="
            scrolled = window.scrollY > 50; if (scrolled) { headerWidth = '100%'; headerRadius = '0'; headerTop = '0px'; }
            window.addEventListener('scroll', () => { scrolled = window.scrollY > 50; if (scrolled) { headerWidth = '100%'; headerRadius = '0'; headerTop = '0px'; } else { if (window.innerWidth >= 768) { headerWidth = '90%'; headerRadius = '100px'; headerTop = '20px'; } else { headerWidth = '100%'; headerRadius = '0'; headerTop = '0px'; } } });
            window.addEventListener('resize', () => { if (window.innerWidth < 768) { headerWidth = '100%'; headerRadius = '0'; headerTop = '0px'; } else if (!scrolled) { headerWidth = '90%'; headerRadius = '100px'; headerTop = '20px'; } else { headerWidth = '100%'; headerRadius = '0'; headerTop = '0px'; } });
        "
        :class="scrolled ? 'bg-white shadow-md' : 'bg-white'"
        class="fixed left-0 right-0 z-50 mx-auto header-transition flex items-center justify-between border border-border py-1" <?php // Kept reduced padding ?>
        :style="`width: ${headerWidth}; border-radius: ${headerRadius}; top: ${headerTop}; transition: all 0.3s ease-in-out;`"
    >
        <div class="container mx-auto px-4 flex items-center justify-between max-w-900">
            <?php // Mobile Menu Trigger ?>
            <button @click="$dispatch('mobile-menu-open')" class="md:hidden flex items-center space-x-1 text-primary" aria-label="Otvori meni">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
            </button>

            <?php // Search Button/Input ?>
            <div class="hidden xs:block relative w-32 md:w-64">
                 <button @click="$dispatch('search-open')" class="w-full bg-white border border-secondary hover:border-primary rounded-full py-2 px-4 pr-10 focus:outline-none focus:ring-1 focus:ring-primary text-sm text-left text-gray-medium hover:text-gray-darker transition-colors duration-200">
                    Brza pretraga...
                </button>
                <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
                </span>
            </div>

            <div class="text-center">
    <a href="<?php echo SITE_URL; ?>" class="inline-flex items-center space-x-2">
        <picture>
            <source
                srcset="/uploads/images/site/logo-transparent-64x64.webp 1x,
                        /uploads/images/site/logo-transparent-128x128.webp 2x"
                type="image/webp">
            <source
                srcset="/uploads/images/site/logo-transparent-64x64.png 1x,
                        /uploads/images/site/logo-transparent-128x128.png 2x"
                type="image/png">
            <img
                src="/uploads/images/site/logo-transparent-64x64.png"
                alt="Lako i Fino Logo"
                width="64"
                height="64"
                fetchpriority="high"
                class="will-change-transform">
        </picture>
        <span class="font-montserrat text-xl md:text-2xl font-extrabold text-primary">
            <span class="relative inline-block">
                Lako <span class="text-dark-contrast italic font-light">&</span> <span class="relative">Fino
                    <span class="absolute -bottom-1 left-0 w-full h-1 bg-primary rounded-full"></span>
                </span>
            </span>
        </span>
    </a>
</div>

             <?php // Mobile Search Trigger ?>
             <button @click="$dispatch('search-open')" class="xs:hidden text-primary" aria-label="Otvori pretragu">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
            </button>

            <?php // Desktop Menu Button and Dropdown ?>
            <div class="hidden md:block relative">
                 <button @click="menuOpen = !menuOpen" class="flex items-center space-x-1 bg-primary text-white px-[22px] py-[5px] rounded hover:bg-opacity-90 transition-all inline-block font-montserrat">
                    <span class="hidden md:inline">Meni</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
                </button>
                 <?php // Desktop Dropdown Menu Content ?>
                 <div x-show="menuOpen" @click.away="menuOpen = false"
                     x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95 -translate-y-4"
                     x-transition:enter-end="opacity-100 transform scale-100 translate-y-0" x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform scale-100 translate-y-0" x-transition:leave-end="opacity-0 transform scale-95 -translate-y-4"
                     class="absolute right-0 mt-2 w-64 rounded-xl shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50" style="display: none;" x-cloak>
                    <div class="py-1 max-h-[70vh] overflow-y-auto">
                         <div class="px-3 py-2 text-xs font-medium text-gray-medium uppercase tracking-wider">Kategorije</div>
                         <a href="<?php echo SITE_URL; ?>" class="flex items-center px-4 py-2 text-sm text-gray-darker hover:bg-gray-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" /> </svg>
                            Početna
                         </a>
                         <a href="<?php echo SITE_URL; ?>/category/recepti/" class="flex items-center px-4 py-2 text-sm text-gray-darker hover:bg-gray-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" /> </svg>
                            Recepti
                         </a>
                         <a href="<?php echo SITE_URL; ?>/category/prirodni-lijekovi/" class="flex items-center px-4 py-2 text-sm text-gray-darker hover:bg-gray-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" /> </svg>
                            Prirodni lijekovi
                         </a>
                         <a href="<?php echo SITE_URL; ?>/category/vijesti/" class="flex items-center px-4 py-2 text-sm text-gray-darker hover:bg-gray-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /> </svg>
                            Vijesti
                         </a>
                         <a href="<?php echo SITE_URL; ?>/popularno.php" class="flex items-center px-4 py-2 text-sm text-gray-darker hover:bg-gray-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /> </svg>
                            Popularno
                         </a>

                         <div class="border-t border-gray-100 my-2"></div>
                         <div class="px-3 py-2 text-xs font-medium text-gray-medium uppercase tracking-wider">Popularne oznake</div>
                         <div class="px-4 py-2">
                             <div class="tag-container">
                                <a href="<?php echo SITE_URL; ?>/category/slana-jela/" class="tag-food"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"> <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" /> </svg> Slana jela </a>
                                <a href="<?php echo SITE_URL; ?>/category/prirodni-lijekovi/" class="tag-health"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"> <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" /> </svg> Prirodni lijekovi </a>
                                <a href="<?php echo SITE_URL; ?>/category/slatka-jela/" class="tag-lifestyle"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"> <path stroke-linecap="round" stroke-linejoin="round" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /> </svg> Slatka jela </a>
                             </div>
                         </div>

                         <div class="border-t border-gray-100 my-2"></div>
                         <div class="px-3 py-2 text-xs font-medium text-gray-medium uppercase tracking-wider">Stranice</div>
                         <a href="<?php echo SITE_URL; ?>/o-nama" class="flex items-center px-4 py-2 text-sm text-gray-darker hover:bg-gray-100"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> O nama </a>
                         <a href="<?php echo SITE_URL; ?>/kontakt" class="flex items-center px-4 py-2 text-sm text-gray-darker hover:bg-gray-100"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /> </svg> Kontakt </a>
                         <a href="<?php echo SITE_URL; ?>/uslovi-koristenja" class="flex items-center px-4 py-2 text-sm text-gray-darker hover:bg-gray-100"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /> </svg> Uslovi korištenja </a>
                         <a href="<?php echo SITE_URL; ?>/politika-privatnosti" class="flex items-center px-4 py-2 text-sm text-gray-darker hover:bg-gray-100"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-medium" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /> </svg> Politika privatnosti </a>
                    </div>
                </div>
            </div> <?php // End hidden md:block ?>
        </div>
    </header>

    <?php // Start of main content area ?>
    <main class="container mx-auto pt-24 md:pt-36 pb-24 md:pb-12 px-4 max-w-900">
    <?php // The specific page content starts here ?>
