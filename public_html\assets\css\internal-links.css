/* Internal Links Styling */
.internal-link {
    color: #ff6481; /* Primary color */
    text-decoration: none;
    border-bottom: 1px dotted #ff6481;
    transition: all 0.2s ease-in-out;
}

.internal-link:hover {
    color: #e64c6a; /* Slightly darker on hover */
    border-bottom: 1px solid #e64c6a;
}

/* Add a small icon to indicate internal links */
.internal-link::after {
    content: "↗";
    font-size: 0.8em;
    margin-left: 2px;
    display: inline-block;
    opacity: 0.7;
}

/* Hover effect */
.internal-link:hover::after {
    opacity: 1;
    transform: translateY(-1px);
}
