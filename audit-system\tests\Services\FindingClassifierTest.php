<?php

namespace AuditSystem\Tests\Services;

use PHPUnit\Framework\TestCase;
use AuditSystem\Services\FindingClassifier;
use AuditSystem\Models\Finding;

class FindingClassifierTest extends TestCase
{
    private FindingClassifier $classifier;

    protected function setUp(): void
    {
        $this->classifier = new FindingClassifier();
    }

    public function testClassifySecurityFindingInPriorityArea(): void
    {
        $finding = $this->classifier->classifyFinding(
            'public_html/config.php',
            25,
            Finding::TYPE_SECURITY,
            'sql_injection',
            'Direct SQL query concatenation detected',
            '$query = "SELECT * FROM users WHERE id = " . $_GET["id"];'
        );

        $this->assertEquals(Finding::SEVERITY_CRITICAL, $finding->getSeverity());
        $this->assertEquals(Finding::PRIORITY_AREA, $finding->getPriority());
        $this->assertStringContainsString('URGENT:', $finding->getRecommendation());
        $this->assertStringContainsString('PDO prepared statements', $finding->getRecommendation());
        $this->assertNotEmpty($finding->getReferences());
    }

    public function testClassifyXSSVulnerabilityInUserFacingArea(): void
    {
        $finding = $this->classifier->classifyFinding(
            'public_html/article.php',
            45,
            Finding::TYPE_SECURITY,
            'xss_vulnerability',
            'Unescaped user input in HTML output',
            'echo $_POST["comment"];'
        );

        $this->assertEquals(Finding::SEVERITY_CRITICAL, $finding->getSeverity());
        $this->assertEquals(Finding::PRIORITY_AREA, $finding->getPriority());
        $this->assertStringContainsString('htmlspecialchars', $finding->getRecommendation());
    }

    public function testClassifyPerformanceIssueInAdSystem(): void
    {
        $finding = $this->classifier->classifyFinding(
            'public_html/process_ad_impressions.php',
            30,
            Finding::TYPE_PERFORMANCE,
            'n_plus_one_query',
            'Multiple database queries in loop detected',
            'foreach ($ads as $ad) { $db->query("SELECT * FROM impressions WHERE ad_id = " . $ad->id); }'
        );

        $this->assertEquals(Finding::SEVERITY_HIGH, $finding->getSeverity());
        $this->assertEquals(Finding::PRIORITY_AREA, $finding->getPriority());
        $this->assertStringContainsString('HIGH PRIORITY:', $finding->getRecommendation());
        $this->assertStringContainsString('eager loading', $finding->getRecommendation());
    }

    public function testClassifyQualityIssueWithHighComplexity(): void
    {
        $finding = $this->classifier->classifyFinding(
            'public_html/includes/functions.php',
            100,
            Finding::TYPE_QUALITY,
            'high_complexity',
            'Function has cyclomatic complexity of 25',
            null,
            ['complexity' => 25]
        );

        $this->assertEquals(Finding::SEVERITY_HIGH, $finding->getSeverity());
        $this->assertEquals(Finding::NON_PRIORITY, $finding->getPriority());
        $this->assertStringContainsString('Break down complex functions', $finding->getRecommendation());
    }

    public function testClassifyArchitectureIssueInCoreFile(): void
    {
        $finding = $this->classifier->classifyFinding(
            'public_html/includes/security.php',
            15,
            Finding::TYPE_ARCHITECTURE,
            'mvc_violation',
            'Business logic mixed with presentation layer',
            'echo "<div>User: " . getUserName($id) . "</div>";'
        );

        $this->assertEquals(Finding::SEVERITY_MEDIUM, $finding->getSeverity());
        $this->assertEquals(Finding::PRIORITY_AREA, $finding->getPriority()); // security.php is in priority patterns
        $this->assertStringContainsString('Separate business logic', $finding->getRecommendation());
    }

    public function testPriorityAreaDetection(): void
    {
        $priorityFiles = [
            'public_html/ad_display.php',
            'public_html/css/main.css',
            'public_html/js/app.js',
            'public_html/image.php',
            'public_html/smrsaj-deepseek-api.php',
            'public_html/config.php',
            'public_html/.htaccess',
            'public_html/process_ad_impressions.php'
        ];

        foreach ($priorityFiles as $file) {
            $finding = $this->classifier->classifyFinding(
                $file,
                1,
                Finding::TYPE_SECURITY,
                'csrf_missing',
                'CSRF protection missing',
                null
            );

            $this->assertEquals(Finding::PRIORITY_AREA, $finding->getPriority(), 
                "File {$file} should be classified as PRIORITY_AREA");
        }
    }

    public function testNonPriorityAreaDetection(): void
    {
        $nonPriorityFiles = [
            'public_html/privacy-policy.php',
            'public_html/robots.txt',
            'public_html/sitemap.xml',
            'public_html/cookie-policy.php'
        ];

        foreach ($nonPriorityFiles as $file) {
            $finding = $this->classifier->classifyFinding(
                $file,
                1,
                Finding::TYPE_QUALITY,
                'naming_convention',
                'Inconsistent naming detected',
                null
            );

            $this->assertEquals(Finding::NON_PRIORITY, $finding->getPriority(), 
                "File {$file} should be classified as NON_PRIORITY");
        }
    }

    public function testSeverityEscalationForSecurityInPriorityArea(): void
    {
        // Security issue in priority area should be high severity even if normally medium
        $finding = $this->classifier->classifyFinding(
            'public_html/ad_display.php',
            20,
            Finding::TYPE_SECURITY,
            'missing_input_validation',
            'Input validation missing',
            null
        );

        $this->assertEquals(Finding::SEVERITY_HIGH, $finding->getSeverity());
        $this->assertEquals(Finding::PRIORITY_AREA, $finding->getPriority());
    }

    public function testPerformanceIssueInUserFacingArea(): void
    {
        $finding = $this->classifier->classifyFinding(
            'public_html/index.php',
            50,
            Finding::TYPE_PERFORMANCE,
            'blocking_resource',
            'Blocking JavaScript resource detected',
            '<script src="large-library.js"></script>'
        );

        $this->assertEquals(Finding::SEVERITY_HIGH, $finding->getSeverity());
        $this->assertEquals(Finding::PRIORITY_AREA, $finding->getPriority());
        $this->assertStringContainsString('async/defer', $finding->getRecommendation());
    }

    public function testBatchClassifyFindings(): void
    {
        $findingData = [
            [
                'file' => 'public_html/config.php',
                'line' => 10,
                'type' => Finding::TYPE_SECURITY,
                'subtype' => 'sql_injection',
                'description' => 'SQL injection vulnerability',
                'codeSnippet' => '$query = "SELECT * FROM users WHERE id = " . $_GET["id"];'
            ],
            [
                'file' => 'public_html/article.php',
                'line' => 25,
                'type' => Finding::TYPE_PERFORMANCE,
                'subtype' => 'n_plus_one_query',
                'description' => 'N+1 query problem',
                'context' => ['query_count' => 50]
            ],
            [
                'file' => 'public_html/privacy-policy.php',
                'line' => 5,
                'type' => Finding::TYPE_QUALITY,
                'subtype' => 'naming_convention',
                'description' => 'Inconsistent variable naming'
            ]
        ];

        $findings = $this->classifier->classifyFindings($findingData);

        $this->assertCount(3, $findings);
        $this->assertInstanceOf(Finding::class, $findings[0]);
        $this->assertEquals(Finding::SEVERITY_CRITICAL, $findings[0]->getSeverity());
        $this->assertEquals(Finding::PRIORITY_AREA, $findings[0]->getPriority());
        $this->assertEquals(Finding::SEVERITY_HIGH, $findings[1]->getSeverity());
        $this->assertEquals(Finding::SEVERITY_LOW, $findings[2]->getSeverity());
    }

    public function testGetPriorityStatistics(): void
    {
        $findings = [
            new Finding(
                'public_html/config.php', 10, Finding::TYPE_SECURITY, 
                Finding::SEVERITY_CRITICAL, Finding::PRIORITY_AREA,
                'SQL injection', 'Use prepared statements'
            ),
            new Finding(
                'public_html/ad_display.php', 20, Finding::TYPE_PERFORMANCE, 
                Finding::SEVERITY_HIGH, Finding::PRIORITY_AREA,
                'N+1 query', 'Optimize queries'
            ),
            new Finding(
                'public_html/privacy-policy.php', 5, Finding::TYPE_QUALITY, 
                Finding::SEVERITY_LOW, Finding::NON_PRIORITY,
                'Naming issue', 'Fix naming'
            )
        ];

        $stats = $this->classifier->getPriorityStatistics($findings);

        $this->assertEquals(3, $stats['total_findings']);
        $this->assertEquals(2, $stats['priority_area']);
        $this->assertEquals(1, $stats['non_priority']);
        $this->assertEquals(1, $stats['severity_breakdown'][Finding::SEVERITY_CRITICAL]);
        $this->assertEquals(1, $stats['severity_breakdown'][Finding::SEVERITY_HIGH]);
        $this->assertEquals(1, $stats['severity_breakdown'][Finding::SEVERITY_LOW]);
        $this->assertEquals(1, $stats['type_breakdown'][Finding::TYPE_SECURITY]);
        $this->assertEquals(1, $stats['type_breakdown'][Finding::TYPE_PERFORMANCE]);
        $this->assertEquals(1, $stats['type_breakdown'][Finding::TYPE_QUALITY]);
    }

    public function testRecommendationGeneration(): void
    {
        // Test security recommendation
        $securityFinding = $this->classifier->classifyFinding(
            'test.php', 1, Finding::TYPE_SECURITY, 'sql_injection', 'SQL injection found'
        );
        $this->assertStringContainsString('PDO prepared statements', $securityFinding->getRecommendation());

        // Test performance recommendation
        $performanceFinding = $this->classifier->classifyFinding(
            'test.php', 1, Finding::TYPE_PERFORMANCE, 'n_plus_one_query', 'N+1 query detected'
        );
        $this->assertStringContainsString('eager loading', $performanceFinding->getRecommendation());

        // Test quality recommendation
        $qualityFinding = $this->classifier->classifyFinding(
            'test.php', 1, Finding::TYPE_QUALITY, 'high_complexity', 'Complex function found'
        );
        $this->assertStringContainsString('Break down complex functions', $qualityFinding->getRecommendation());
    }

    public function testReferenceGeneration(): void
    {
        $finding = $this->classifier->classifyFinding(
            'test.php', 1, Finding::TYPE_SECURITY, 'sql_injection', 'SQL injection found'
        );

        $references = $finding->getReferences();
        $this->assertNotEmpty($references);
        $this->assertStringContainsString('owasp.org', $references[0]);
        $this->assertStringContainsString('php.net', $references[1]);
    }

    public function testQualitySeverityBasedOnComplexity(): void
    {
        // Low complexity
        $lowComplexityFinding = $this->classifier->classifyFinding(
            'test.php', 1, Finding::TYPE_QUALITY, 'high_complexity', 
            'Function complexity', null, ['complexity' => 5]
        );
        $this->assertEquals(Finding::SEVERITY_LOW, $lowComplexityFinding->getSeverity());

        // Medium complexity
        $mediumComplexityFinding = $this->classifier->classifyFinding(
            'test.php', 1, Finding::TYPE_QUALITY, 'high_complexity', 
            'Function complexity', null, ['complexity' => 15]
        );
        $this->assertEquals(Finding::SEVERITY_MEDIUM, $mediumComplexityFinding->getSeverity());

        // High complexity
        $highComplexityFinding = $this->classifier->classifyFinding(
            'test.php', 1, Finding::TYPE_QUALITY, 'high_complexity', 
            'Function complexity', null, ['complexity' => 25]
        );
        $this->assertEquals(Finding::SEVERITY_HIGH, $highComplexityFinding->getSeverity());
    }

    public function testCriticalSecurityAlwaysPriority(): void
    {
        // Even in non-priority file, critical security should be high priority
        $finding = $this->classifier->classifyFinding(
            'public_html/robots.txt', // Non-priority file
            1,
            Finding::TYPE_SECURITY,
            'sql_injection', // Critical security type
            'SQL injection found'
        );

        $this->assertEquals(Finding::SEVERITY_CRITICAL, $finding->getSeverity());
        $this->assertEquals(Finding::PRIORITY_AREA, $finding->getPriority());
    }

    public function testUrgencyPrefixInRecommendations(): void
    {
        // Critical finding should have URGENT prefix
        $criticalFinding = $this->classifier->classifyFinding(
            'test.php', 1, Finding::TYPE_SECURITY, 'sql_injection', 'Critical issue'
        );
        $this->assertStringStartsWith('URGENT:', $criticalFinding->getRecommendation());

        // High finding should have HIGH PRIORITY prefix
        $highFinding = $this->classifier->classifyFinding(
            'test.php', 1, Finding::TYPE_PERFORMANCE, 'n_plus_one_query', 'High priority issue'
        );
        $this->assertStringStartsWith('HIGH PRIORITY:', $highFinding->getRecommendation());

        // Medium finding should have MEDIUM PRIORITY prefix
        $mediumFinding = $this->classifier->classifyFinding(
            'public_html/includes/functions.php', 1, Finding::TYPE_ARCHITECTURE, 'mvc_violation', 'Medium issue'
        );
        $this->assertStringStartsWith('MEDIUM PRIORITY:', $mediumFinding->getRecommendation());

        // Low finding should have no prefix
        $lowFinding = $this->classifier->classifyFinding(
            'test.php', 1, Finding::TYPE_QUALITY, 'naming_convention', 'Low priority issue'
        );
        $this->assertStringNotContainsString('PRIORITY:', $lowFinding->getRecommendation());
    }
}