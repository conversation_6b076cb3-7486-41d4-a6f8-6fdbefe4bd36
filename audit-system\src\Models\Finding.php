<?php

namespace AuditSystem\Models;

/**
 * Represents a single audit finding
 */
class Finding
{
    public const TYPE_SECURITY = 'security';
    public const TYPE_PERFORMANCE = 'performance';
    public const TYPE_QUALITY = 'quality';
    public const TYPE_ARCHITECTURE = 'architecture';

    public const SEVERITY_CRITICAL = 'critical';
    public const SEVERITY_HIGH = 'high';
    public const SEVERITY_MEDIUM = 'medium';
    public const SEVERITY_LOW = 'low';

    public const PRIORITY_AREA = 'PRIORITY_AREA';
    public const NON_PRIORITY = 'NON_PRIORITY';

    public string $file;
    public int $line;
    public string $type;
    public string $severity;
    public string $priority;
    public string $description;
    public string $recommendation;
    public ?string $codeSnippet;
    public array $references;

    public function __construct(
        string $file,
        int $line,
        string $type,
        string $severity,
        string $priority,
        string $description,
        string $recommendation,
        ?string $codeSnippet = null,
        array $references = []
    ) {
        $this->file = $file;
        $this->line = $line;
        $this->type = $type;
        $this->severity = $severity;
        $this->priority = $priority;
        $this->description = $description;
        $this->recommendation = $recommendation;
        $this->codeSnippet = $codeSnippet;
        $this->references = $references;
    }

    /**
     * Convert finding to array for serialization
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'file' => $this->file,
            'line' => $this->line,
            'type' => $this->type,
            'severity' => $this->severity,
            'priority' => $this->priority,
            'description' => $this->description,
            'recommendation' => $this->recommendation,
            'codeSnippet' => $this->codeSnippet,
            'references' => $this->references
        ];
    }

    /**
     * Create finding from array data
     *
     * @param array $data
     * @return Finding
     */
    public static function fromArray(array $data): Finding
    {
        return new self(
            $data['file'],
            $data['line'],
            $data['type'],
            $data['severity'],
            $data['priority'],
            $data['description'],
            $data['recommendation'],
            $data['codeSnippet'] ?? null,
            $data['references'] ?? []
        );
    }

    /**
     * Get file path
     *
     * @return string
     */
    public function getFile(): string
    {
        return $this->file;
    }

    /**
     * Get line number
     *
     * @return int
     */
    public function getLine(): int
    {
        return $this->line;
    }

    /**
     * Get finding type
     *
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * Get severity level
     *
     * @return string
     */
    public function getSeverity(): string
    {
        return $this->severity;
    }

    /**
     * Get priority classification
     *
     * @return string
     */
    public function getPriority(): string
    {
        return $this->priority;
    }

    /**
     * Get description
     *
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * Get recommendation
     *
     * @return string
     */
    public function getRecommendation(): string
    {
        return $this->recommendation;
    }

    /**
     * Get code snippet
     *
     * @return string|null
     */
    public function getCodeSnippet(): ?string
    {
        return $this->codeSnippet;
    }

    /**
     * Get references
     *
     * @return array
     */
    public function getReferences(): array
    {
        return $this->references;
    }

    /**
     * Set references
     *
     * @param array $references
     * @return void
     */
    public function setReferences(array $references): void
    {
        $this->references = $references;
    }

    /**
     * Add reference
     *
     * @param string $reference
     * @return void
     */
    public function addReference(string $reference): void
    {
        $this->references[] = $reference;
    }

    /**
     * Check if finding is in priority area
     *
     * @return bool
     */
    public function isPriorityArea(): bool
    {
        return $this->priority === self::PRIORITY_AREA;
    }

    /**
     * Check if finding is critical severity
     *
     * @return bool
     */
    public function isCritical(): bool
    {
        return $this->severity === self::SEVERITY_CRITICAL;
    }

    /**
     * Check if finding is high severity
     *
     * @return bool
     */
    public function isHighSeverity(): bool
    {
        return $this->severity === self::SEVERITY_HIGH;
    }

    /**
     * Check if finding is security-related
     *
     * @return bool
     */
    public function isSecurityFinding(): bool
    {
        return $this->type === self::TYPE_SECURITY;
    }

    /**
     * Get severity weight for sorting (higher number = more severe)
     *
     * @return int
     */
    public function getSeverityWeight(): int
    {
        return match ($this->severity) {
            self::SEVERITY_CRITICAL => 4,
            self::SEVERITY_HIGH => 3,
            self::SEVERITY_MEDIUM => 2,
            self::SEVERITY_LOW => 1,
            default => 0
        };
    }

    /**
     * Get priority weight for sorting (higher number = higher priority)
     *
     * @return int
     */
    public function getPriorityWeight(): int
    {
        return $this->priority === self::PRIORITY_AREA ? 2 : 1;
    }

    /**
     * Get combined score for sorting findings by importance
     *
     * @return int
     */
    public function getImportanceScore(): int
    {
        return ($this->getPriorityWeight() * 10) + $this->getSeverityWeight();
    }

    /**
     * Format finding for display
     *
     * @return string
     */
    public function formatForDisplay(): string
    {
        $priorityIcon = $this->isPriorityArea() ? '🔥' : '📝';
        $severityIcon = match ($this->severity) {
            self::SEVERITY_CRITICAL => '🚨',
            self::SEVERITY_HIGH => '⚠️',
            self::SEVERITY_MEDIUM => '⚡',
            self::SEVERITY_LOW => 'ℹ️',
            default => '❓'
        };

        return sprintf(
            "%s %s [%s] %s:%d - %s",
            $priorityIcon,
            $severityIcon,
            strtoupper($this->type),
            basename($this->file),
            $this->line,
            $this->description
        );
    }

    /**
     * Check if finding matches given criteria
     *
     * @param array $criteria
     * @return bool
     */
    public function matches(array $criteria): bool
    {
        foreach ($criteria as $field => $value) {
            if (!property_exists($this, $field)) {
                continue;
            }

            if (is_array($value)) {
                if (!in_array($this->$field, $value)) {
                    return false;
                }
            } else {
                if ($this->$field !== $value) {
                    return false;
                }
            }
        }

        return true;
    }
}