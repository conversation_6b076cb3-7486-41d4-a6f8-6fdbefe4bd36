<?php

namespace AuditSystem\Exceptions;

use Exception;

/**
 * Base exception class for all audit-related exceptions
 */
abstract class AuditException extends Exception
{
    /**
     * Create a new audit exception
     *
     * @param string $message Error message
     * @param int $code Error code
     * @param Exception|null $previous Previous exception
     */
    public function __construct(string $message = '', int $code = 0, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Get the exception context for logging
     *
     * @return array
     */
    public function getContext(): array
    {
        return [
            'exception_class' => get_class($this),
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => $this->getTraceAsString()
        ];
    }
}