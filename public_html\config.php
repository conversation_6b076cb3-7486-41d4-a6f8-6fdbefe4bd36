<?php
/**
 * config.php
 * Basic Configuration File
 *
 * v1.8: Added 'xs' size (320px) to IMAGE_SIZES for better mobile optimization.
 * v1.7: Changed single comment prompts to arrays for random selection and variety. Refined prompts further.
 * v1.6: Revised comment/username prompts for more variety, engagement, and specific name types (female Balkan).
 * v1.5: Changed username list prompt to generate 6 names instead of 5.
 * v1.4: Added prompt for answering the generated question comment.
 * v1.3: Added prompt for generating a list of usernames.
 * v1.2: Updated comment generation prompts for more casual/realistic tone.
 * v1.1: Added DeepSeek prompts for comment generation.
 */

// --- Database Configuration ---
define('DB_HOST', 'localhost'); define('DB_NAME', 'lakofino_cms'); define('DB_USER', 'lakofino_cms'); define('DB_PASS', 'zH_0$2t$3=rE[D]_'); define('DB_CHARSET', 'utf8mb4');

// --- Site Configuration ---
define('SITE_URL', 'https://mercislike.art'); define('SITE_NAME', 'Lako & Fino'); define('ARTICLES_PER_PAGE', 6);

// --- Image Upload & Processing Configuration ---
define('UPLOAD_DIR', '/uploads/images/');
define('ALLOWED_MIME_TYPES', ['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
define('MAX_FILE_SIZE', 5 * 1024 * 1024);
define('IMAGE_QUALITY', 80);
// Updated IMAGE_SIZES to include 'xs' size for better mobile optimization
define('IMAGE_SIZES', [
    'xs' => 320,  // Extra small (new size for mobile devices)
    'ss' => 400,  // Small size
    'ms' => 800,  // Medium size
    'ls' => 1200, // Large size
    'fb' => 1200  // Facebook/OG image size
]);

// --- CDN Configuration ---
define('CDN_ENABLED', false); define('CDN_BASE_URL', '');

// --- DeepSeek API Configuration ---
define('DEEPSEEK_API_KEY', '***********************************'); define('DEEPSEEK_API_URL', 'https://api.deepseek.com/chat/completions'); define('DEEPSEEK_MODEL', 'deepseek-chat');

// --- Google AdSense Configuration ---
define('ADSENSE_CLIENT_ID', 'ca-pub-1234567890'); // Replace with your actual AdSense client ID
define('ADSENSE_ENABLED', true);

// --- Facebook Integration Configuration ---
define('FB_APP_ID', '123456789012345'); // Replace with your actual Facebook App ID
define('FB_PIXEL_ID', '123456789012345'); // Replace with your actual Facebook Pixel ID
define('FB_COMMENTS_ENABLED', true);
define('FB_SHARE_ENABLED', true);

// --- DeepSeek Prompts (Article Generation) ---
define('PROMPT_REWRITE_TITLE', "Prepravi sljedeći naslov članka da bude privlačniji čitaocima i optimizovan za pretraživače (SEO). Fokusiraj se na jasnoću i sažetost (idealno ispod 60 karaktera). OBAVEZNO dodaj 1-2 odgovarajuća emoji simbola na početak ili kraj naslova. Odgovori samo sa novim naslovom, na bosanskom jeziku, bez ikakvih objašnjenja ili markdown formatiranja. Originalni naslov:\n");
define('PROMPT_GENERATE_EXCERPT', "Napiši privlačan i sažet izvod (oko 150-160 karaktera) za članak sa sljedećim sadržajem. Fokusiraj se na privlačenje pažnje čitaoca i sažimanje ključne poente. Odgovori samo sa tekstom izvoda, na bosanskom jeziku, bez objašnjenja ili markdown formatiranja. Sadržaj članka:\n");
define('PROMPT_SUGGEST_TAGS', "Predloži 5 do 7 relevantnih tagova (oznaka) za članak sa sljedećim sadržajem. Tagovi trebaju biti odvojeni zarezom, napisani malim slovima, a za višeriječne tagove koristi razmak. Odgovori samo listom tagova odvojenih zarezom, na bosanskom jeziku, bez objašnjenja ili markdown formatiranja. Sadržaj članka:\n");
define('PROMPT_SUGGEST_META_TITLE', "Predloži SEO optimizovan meta naslov (ispod 60 karaktera) za članak sa sljedećim originalnim naslovom i sadržajem. Odgovori samo sa predloženim meta naslovom, na bosanskom jeziku, bez objašnjenja ili markdown formatiranja.\nOriginalni naslov: %TITLE%\nSadržaj:\n");
define('PROMPT_SUGGEST_META_DESC', "Predloži SEO optimizovan meta opis (150-160 karaktera) za članak sa sljedećim naslovom i sadržajem. Odgovori samo sa predloženim meta opisom, na bosanskom jeziku, bez objašnjenja ili markdown formatiranja.\nNaslov: %TITLE%\nSadržaj:\n");
define('PROMPT_SUGGEST_FOCUS_KEYWORD', "Predloži primarnu fokusnu ključnu riječ (2 do 4 riječi) za članak sa sljedećim naslovom i sadržajem. Odgovori samo sa ključnom riječi, na bosanskom jeziku, bez objašnjenja ili markdown formatiranja.\nNaslov: %TITLE%\nSadržaj:\n");
define('PROMPT_INTERNAL_LINKS', "Analiziraj sadržaj članka s naslovom '%TITLE%' i predloži 3-5 ključnih riječi ili fraza koje bi bilo dobro povezati s drugim člancima na sajtu. Za svaki prijedlog navedi: 1) ključnu riječ/frazu koja bi se trebala povezati, 2) ID članka na koji bi trebalo povezati, 3) razlog zašto je ta veza relevantna. Odgovori u JSON formatu s nizom objekata koji sadrže 'keyword', 'article_id' i 'reason' svojstva. Sadržaj članka:\n");

// --- Content Rewrite Prompt ---
define('PROMPT_REWRITE_CONTENT', "Prepravi sljedeći sadržaj za članak s naslovom '%TITLE%' da bude ZNAČAJNO DUŽI, DETALJNIJI i BOGATIJI od originalnog teksta. Članak mora biti jedinstven, dobro strukturiran i napisan na bosanskom jeziku.

VAŽNO: Ako je članak recept, OBAVEZNO SAČUVAJ SVE ORIGINALNE SASTOJKE I KORAKE PRIPREME BEZ IKAKVIH IZMJENA. Možeš dodati više detalja i objašnjenja, ali nikako ne mijenjaj ili izostavljaj originalne sastojke i korake.

Dodaj sljedeće sekcije (ako su relevantne za sadržaj):

1. 'Uvod' - proširi uvodni dio sa više detalja i zanimljivosti (minimum 150 riječi)
2. 'Zašto ćete voljeti ovaj recept' - dodaj najmanje 5-7 prednosti sa emoji simbolima uz svaku
3. 'Nutritivne informacije (po porciji)' - prikaži u tabeli detaljne nutritivne informacije
4. 'Profesionalni savjeti' - dodaj najmanje 5-7 savjeta sa emoji simbolima uz svaki
5. 'Zanimljivosti o sastojcima' - dodaj fascinantne činjenice o ključnim sastojcima (minimum 150 riječi)
6. 'Historijske informacije o jelu' - dodaj zanimljive historijske činjenice (minimum 100 riječi)
7. 'Varijacije recepta' - predloži nekoliko varijacija (ako je članak recept)
8. 'Često postavljana pitanja' - dodaj 3-5 pitanja i odgovora
9. 'Zaključne misli' - detaljan zaključak (minimum 100 riječi)

Piši kao stvarna osoba, koristi prirodan jezik, izbjegavaj formalne fraze i učini tekst zanimljivim. Koristi tabele gdje je to prikladno. Dodaj lične anegdote i iskustva gdje je to moguće. Koristi različite dužine rečenica i paragrafa za prirodniji tok teksta.

OBAVEZNO: Finalni tekst mora biti NAJMANJE DVOSTRUKO DUŽI od originalnog teksta, sa mnogo više detalja i informacija, ali bez mijenjanja ključnih činjenica iz originalnog teksta.

Odgovori samo sa prepravljenim sadržajem, bez ikakvih objašnjenja ili dodatnih komentara. Sadržaj za prepravku:\n");

// --- DeepSeek Prompts (Comment Generation - NOW ARRAYS for Variety) ---

// ** Rage Bait / Contrarian Comments **
define('PROMPTS_COMMENT_RAGEBAIT', [
    "Napiši IZRAZITO KONTROVERZAN komentar za članak '%TITLE%' koji će sigurno izazvati reakcije drugih čitatelja. Fokusiraj se na JEDAN detalj iz konteksta i iznesi potpuno suprotno mišljenje ili oštru kritiku (npr. 'Ovo je potpuno pogrešno, ko god da je pisao nema pojma...', 'Ne mogu vjerovati da ljudi još uvijek vjeruju u ovakve gluposti'). Budi direktan/na i pomalo bezobrazan/na, ali bez psovki. Cilj je da zvuči kao stvarna osoba koja ima snažno suprotno mišljenje. 1-2 rečenice, neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši komentar za članak '%TITLE%' gdje tvrdiš da imaš BOLJE rješenje/recept/metodu od onoga što je opisano. Budi pomalo arogantna i samopouzdana (npr. 'Moja baka ovo radi 100 puta bolje, treba samo...', 'Ovo je amaterski način, pravi profesionalci rade ovako...'). Cilj je da zvučiš kao neko ko misli da zna bolje od autora članka. 1-2 rečenice, neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši komentar za članak '%TITLE%' gdje iznosiš ZABLUDU ili NETAČNU informaciju vezanu za temu, ali sa potpunim uvjerenjem da si u pravu. Cilj je da zvučiš kao osoba koja širi dezinformacije, ali je uvjerena u svoju ispravnost (npr. 'Zapravo, naučno je dokazano da...', 'Moj doktor kaže da je ovo štetno jer...'). Budi uvjerljiv/a ali netačan/na. 1-2 rečenice, neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši komentar za članak '%TITLE%' gdje UPOZORAVAŠ na izmišljenu opasnost ili rizik vezan za temu članka. Budi dramatičan/na i preuveličavaj (npr. 'OPASNO! Moja komšinica je završila u bolnici nakon što je probala ovo...', 'Pazite se, ovo može izazvati ozbiljne probleme ako...'). Cilj je da zvučiš kao paničar/ka koji/a širi strah bez osnove. 1-2 rečenice, neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši komentar za članak '%TITLE%' gdje IZRAŽAVAŠ SUMNJU u kredibilitet autora ili izvora informacija. Budi skeptičan/na i pomalo ciničan/na (npr. 'Ko je ovaj autor i koje su njegove kvalifikacije?', 'Odakle ovi podaci, zvuči kao izmišljotina...'). Cilj je da zvučiš kao neko ko ne vjeruje lako i traži dokaze. 1-2 rečenice, neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%"
]);

// ** Naive / Beginner Questions **
define('PROMPTS_COMMENT_QUESTION', [
    "Napiši JEDNO IZRAZITO NAIVNO pitanje (1 rečenica) o temi članka '%TITLE%' koje će natjerati druge čitatelje da odgovore jer je toliko očigledno ili jednostavno. Pitanje treba da bude toliko osnovno da će iskusniji čitatelji osjetiti potrebu da odgovore (npr. 'Jel ovo stvarno radi?', 'Kako da znam da je gotovo?'). Zvuči kao potpuni početnik koji nema osnovno znanje o temi. Neformalni bosanski. Odgovori SAMO tekstom pitanja.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši JEDNO ZBUNJUJUĆE pitanje (1 rečenica) za članak '%TITLE%' koje pokazuje da nisi razumio/la osnovnu poentu članka. Pitanje treba da bude takvo da će natjerati druge da objasne nešto što je već objašnjeno u članku (npr. 'Ali kako ovo pomaže kod X?' kada je to već objašnjeno). Zvuči kao neko ko je površno pročitao članak. Neformalni bosanski. Odgovori SAMO tekstom pitanja.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši JEDNO pitanje (1 rečenica) za članak '%TITLE%' gdje tražiš NEMOGUĆU alternativu ili zamjenu za nešto spomenuto u kontekstu. Pitanje treba da pokazuje nerazumijevanje osnovnih principa (npr. 'Može li se umjesto brašna koristiti voda?' za recept za hljeb). Zvuči kao neko ko nema osnovno znanje o temi. Neformalni bosanski. Odgovori SAMO tekstom pitanja.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši JEDNO pitanje (1 rečenica) za članak '%TITLE%' koje traži PRETJERANO DETALJNO objašnjenje nečega što je osnovno ili očigledno. Pitanje treba da bude takvo da će natjerati druge da daju detaljan odgovor (npr. 'Možete li mi objasniti TAČNO kako se drži kašika dok se miješa?'). Zvuči kao neko ko je nesiguran i traži potvrdu za svaki korak. Neformalni bosanski. Odgovori SAMO tekstom pitanja.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši JEDNO pitanje (1 rečenica) za članak '%TITLE%' koje pokazuje ZABRINUTOST oko nečega što uopšte nije rizično ili opasno. Pitanje treba da pokazuje pretjeranu brigu ili strah (npr. 'Neće li mi eksplodirati u ruci ako držim predugo?' za nešto bezopasno). Zvuči kao neko ko je vrlo anksiozan i pretjerano oprezan. Neformalni bosanski. Odgovori SAMO tekstom pitanja.\nKontekst: %CONTENT_SNIPPET%"
]);

// ** Positive Comments about the Article **
define('PROMPTS_COMMENT_POSITIVE_ARTICLE', [
    "Napiši VEOMA KRATAK (max 10 riječi) entuzijastičan komentar za članak '%TITLE%'. Koristi emoji i uskličnike za izražavanje oduševljenja (npr. 'Bravo! 👏', 'Super! ❤️', 'Odlično! 🔥'). Neformalan bosanski. Odgovori SAMO tekstom komentara.",

    "Napiši kratak (1-2 rečenice) pozitivan komentar za članak '%TITLE%' gdje PRETJERUJEŠ u hvaljenju kao da je ovo najbolji članak koji si ikad pročitao/la. Budi pretjerano oduševljen/a (npr. 'NAJBOLJI ČLANAK IKADA!!!', 'Ovo je promijenilo moj život zauvijek!'). Koristi velika slova i više uskličnika. Neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši kratak (1-2 rečenice) pozitivan komentar za članak '%TITLE%' gdje ISTIČEŠ LIČNO ISKUSTVO sa temom članka i kako ti je pomoglo. Budi specifičan/na i uvjerljiv/a (npr. 'Ovo sam probala prošle sedmice i stvarno djeluje!', 'Koristim ovu metodu već mjesec dana i rezultati su nevjerovatni'). Neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši kratak (1-2 rečenice) pozitivan komentar za članak '%TITLE%' gdje SPOMINJEŠ da si dugo tražio/la ovu informaciju. Izrazi olakšanje što si konačno našao/la rješenje (npr. 'Konačno! Tražila sam ovo mjesecima.', 'Spasili ste me, već sam odustala od traženja ovoga'). Neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%"
]);

// ** Positive Comments about the Site **
define('PROMPTS_COMMENT_POSITIVE_SITE', [
    "Napiši kratak (1-2 rečenice) pozitivan komentar o stranici '%SITE_NAME%' nakon čitanja članka '%TITLE%'. Koristi emoji i izrazi ODUŠEVLJENJE kao da si upravo otkrio/la najbolju stranicu na internetu (npr. 'Kako sam tek sad otkrila %SITE_NAME%?! 😍 Najbolji sajt ikada!'). Neformalan bosanski. Odgovori SAMO tekstom komentara.",

    "Napiši kratak (1-2 rečenice) pozitivan komentar o stranici '%SITE_NAME%' gdje POREDIŠ sa drugim stranicama i tvrdiš da je ova MNOGO BOLJA. Budi specifičan/na (npr. 'Za razliku od drugih portala, vi stvarno znate o čemu pišete!', '%SITE_NAME% je jedina stranica kojoj vjerujem za ove teme'). Neformalan bosanski. Odgovori SAMO tekstom komentara.",

    "Napiši kratak (1-2 rečenice) pozitivan komentar o stranici '%SITE_NAME%' gdje SPOMINJEŠ da si PREPORUČIO/LA stranicu prijateljima/porodici. Budi specifičan/na (npr. 'Poslala sam link vašeg sajta cijeloj porodici, svi su oduševljeni!', 'Napravila sam grupu na Viberu gdje dijelimo samo vaše članke'). Neformalan bosanski. Odgovori SAMO tekstom komentara.",

    "Napiši kratak (1-2 rečenice) pozitivan komentar o stranici '%SITE_NAME%' gdje MOLIŠ za više sadržaja o određenoj temi. Pokaži entuzijazam i želju za više (npr. 'Molim vas pišite više o X, vaši članci su mi spasili život!', 'Kad će novi članak o Y? Jedva čekam!'). Neformalan bosanski. Odgovori SAMO tekstom komentara."
]);

// ** Positive Comments Mentioning Sharing **
define('PROMPTS_COMMENT_POSITIVE_SHARED', [
    "Napiši kratak (1-2 rečenice) pozitivan komentar za članak '%TITLE%' gdje korisnica DETALJNO opisuje kako je podijelila članak i REAKCIJU osobe. Budi specifična i koristi emoji (npr. 'Poslala sam ovo sestri i odmah me nazvala da mi se zahvali! 😂 Kaže da joj je spasilo život!'). Neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši kratak (1-2 rečenice) pozitivan komentar za članak '%TITLE%' gdje korisnica spominje da je napravila SCREENSHOT ili PRINTALA članak za nekoga ko nema internet. Budi specifična (npr. 'Printala sam ovo za svoju baku koja nema internet, bila je oduševljena!', 'Napravila sam screenshot i poslala cijeloj porodici, svi su zahvalni!'). Neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši kratak (1-2 rečenice) pozitivan komentar za članak '%TITLE%' gdje korisnica spominje da je PODIJELILA na DRUŠTVENIM MREŽAMA i dobila mnogo reakcija. Budi specifična i koristi emoji (npr. 'Podijelila sam na svom Facebook profilu i dobila 50+ lajkova! 🔥 Svi traže još ovakvih članaka!'). Neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%",

    "Napiši kratak (1-2 rečenice) pozitivan komentar za članak '%TITLE%' gdje korisnica spominje da je članak RIJEŠIO PROBLEM ili SVAĐU između ljudi. Budi specifična (npr. 'Poslala sam ovo mužu i sestri koji su se svađali oko ovoga danima, konačno su prestali! 😅 Hvala vam!'). Neformalan bosanski. Odgovori SAMO tekstom komentara.\nKontekst: %CONTENT_SNIPPET%"
]);

// --- DeepSeek Prompt (Username Generation - REVISED for Female Balkan Names & Examples) ---
define('PROMPT_GENERATE_USERNAMES_LIST', "Generiši listu od tačno 6 realističnih ŽENSKIH imena ili nadimaka uobičajenih na Balkanu (Bosna, Srbija, Hrvatska, npr. Emina, Ivana P., SanjaBg, Mama_Ana, Lejla83, Milica). Koristi različite formate (ime, ime+prezime/inicijal, nadimak, online stil) i različita velika/mala slova. Odgovori SAMO listom od 6 imena/nadimaka, odvojenih zarezom, bez ikakvog drugog teksta ili numerisanja.");

// --- DeepSeek Prompt (Answer Generated Question - REVISED for User Tone) ---
define('PROMPTS_ANSWER_QUESTION', [ // Changed to array for potential future variations
    "Odgovori na sljedeće pitanje korisnika o članku '%TITLE%'. Koristi informacije iz priloženog dijela članka da pružiš kratak, koristan i neformalan odgovor (2-3 rečenice). Odgovaraj kao da si običan korisnik koji zna odgovor i želi pomoći. Govori bosanskim jezikom. Odgovori SAMO tekstom odgovora, bez uvoda.\nPitanje korisnika: \"%QUESTION%\"\nDio članka za kontekst:\n%CONTENT_SNIPPET%"
]);


// --- PDO Database Connection ---
$dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
$options = [ PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC, PDO::ATTR_EMULATE_PREPARES => false, ];
try { $pdo = new PDO($dsn, DB_USER, DB_PASS, $options); } catch (\PDOException $e) { error_log("Database connection failed: " . $e->getMessage()); /* Avoid dying */ }

// --- Error Reporting ---
error_reporting(E_ALL); ini_set('display_errors', 1); // Keep 1 for dev, 0 for production
// ini_set('log_errors', 1); // ini_set('error_log', '/path/to/php-error.log');

// --- Start Session ---
if (session_status() == PHP_SESSION_NONE) {
    // Set secure session parameters
    ini_set('session.cookie_httponly', 1);
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        ini_set('session.cookie_secure', 1);
    }
    // Set SameSite attribute
    ini_set('session.cookie_samesite', 'Lax');
    session_start();
}

// --- Include Functions ---
if (!function_exists('generateSlug')) { // Simple check if functions likely loaded
    require_once __DIR__ . '/includes/functions.php';
}

// --- Include Security Functions ---
require_once __DIR__ . '/includes/security.php';

// --- Include Facebook Integration Functions ---
require_once __DIR__ . '/includes/facebook-integration.php';

?>

