<?php
/**
 * Content Display - Rendering Functions
 *
 * Generates HTML markup for different content types (including promotional).
 * v1.1: Modified renderAffiliateAdUnit to pass more context to generateTrackingUrl.
 * v1.2: Updated renderAdUnit to include ad ID and type in data attributes for JS tracking.
 * v1.3: Fixed deprecated mb_convert_encoding warning and cleaned up container structure.
 * v1.4: Fixed sidebar_bottom image display, standardized ads to match in-content style.
 * v1.5: Made After Content exactly match Article Beginning design.
 * v2.0: Complete rewrite with dedicated function for article layout to guarantee image sizing.
 * v2.1: Fixed image display for Sidebar Bottom placement.
 * v2.2: Fixed in-content ad injection to properly display ads within article content.
 * v2.3: Made in-content ads styling match article_beginning and after_content styles.
 * v2.4: Renamed CSS classes, IDs, and data attributes to be less ad-blocker sensitive. Changed 'ad'/'adsense'/'affiliate' to more generic terms like 'promo'/'content-block'.
 * v2.5: Improved image sizing for ads - using medium size ('ms') by default for better display on desktop, keeping small size ('ss') only for sidebar placements.
 */

// Prevent direct access
if (!defined('ABSPATH') && !defined('SITE_URL')) {
    if (file_exists('../config.php')) {
        require_once '../config.php';
    } else {
        exit('Direct script access denied.');
    }
}

// Include dependencies
require_once __DIR__ . '/ad_manager.php'; // Keep original name if functions inside rely on it, or rename if possible
require_once __DIR__ . '/ad_tracking.php'; // Needed for generateTrackingUrl - Keep original name if functions rely on it
require_once __DIR__ . '/functions.php'; // For escape(), getFeaturedImageUrl()

/**
 * Internal helper function to render the standard article card layout
 * used by both article_beginning and after_content to ensure they are 100% identical.
 * Uses generic class names.
 */
function renderStandardArticleCard($trackingUrl, $targetAttribute, $imageUrl, $imageAlt, $imageWidth, $imageHeight, $showSponsoredLabel, $title, $description) {
    // Using 'content-card' instead of 'card' for potentially more semantic meaning if needed, but keeping 'card' for Tailwind consistency
    $html = "<div class=\"card p-0 overflow-hidden mb-6 block no-underline text-current hover:shadow-md transition-shadow duration-300\">\n";
    $html .= "  <a href=\"" . escape($trackingUrl) . "\" " . $targetAttribute . " class=\"flex flex-col sm:flex-row no-underline\">\n";
    // Image Section with explicit styling to force correct dimensions
    $html .= "    <div class=\"flex-shrink-0 w-full sm:w-40 h-40 sm:h-auto bg-gray-100 border-b sm:border-b-0 sm:border-r border-border flex items-center justify-center overflow-hidden\" style=\"min-width: 160px;\">\n";
    if ($imageUrl) {
        $html .= "      <img src=\"" . escape($imageUrl) . "\" alt=\"" . escape($imageAlt) . "\" class=\"w-full h-full object-cover\" width=\"{$imageWidth}\" height=\"{$imageHeight}\" loading=\"lazy\" style=\"width: 100%; height: 100%; object-fit: cover;\">\n";
    } else {
         $html .= '      <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>';
    }
    $html .= "    </div>\n";
    // Content Section
    $html .= "    <div class=\"flex-grow p-4 flex flex-col justify-between\">\n";
    $html .= "      <div>\n";
    if ($showSponsoredLabel) {
        // Kept "Sponzorirano" text, assuming it's desired for transparency. Class is generic.
        $html .= "        <span class=\"inline-block bg-gray-100 text-gray-500 text-xs font-medium px-2 py-0.5 rounded-full mb-2\">Sponzorirano</span>\n";
    }
    $html .= "        <h4 class=\"font-montserrat font-bold text-lg mb-2 line-clamp-2 text-dark-contrast hover:text-primary transition-colors\">" . $title . "</h4>\n";
    if ($description) {
        $html .= "        <p class=\"text-sm text-gray-600 line-clamp-3\">" . $description . "</p>\n";
    }
    $html .= "      </div>\n";
    $html .= "      <div class=\"mt-3 text-right\">\n";
    // Kept button text, assuming it's desired. Class is generic.
    $html .= "        <span class=\"inline-block text-xs font-semibold text-primary border border-primary rounded-full px-3 py-1 hover:bg-primary hover:text-white transition-colors\">Saznaj Više</span>\n";
    $html .= "      </div>\n";
    $html .= "    </div>\n"; // End Content Section
    $html .= "  </a>\n"; // End Anchor Tag
    $html .= "</div>\n"; // End Container Div

    return $html;
}

/**
 * Selects and renders the most appropriate promotional content for a given placement.
 * Uses generic naming.
 *
 * @param PDO $pdo PDO database connection object.
 * @param string $placement Placement identifier (e.g., 'sidebar_popular', 'in_content').
 * @param array $context Contextual information for targeting.
 * @param int $limit Maximum number of items to render for this placement (default 1).
 * @param int $positionIndex Optional index for items placed multiple times (e.g., in-content items).
 * @return string HTML markup for the selected item(s), or empty string.
 */
function displayAdsForPlacement(PDO $pdo, string $placement, array $context = [], int $limit = 1, int $positionIndex = 1): string {
    // Assuming getActiveAdsForPlacement fetches the correct *data* (affiliate or adsense)
    // The function name itself might be flagged, but changing it requires checking dependencies.
    // Let's keep it for now, focusing on the output HTML.
    $activeItems = getActiveAdsForPlacement($pdo, $placement, $context, $limit);

    if (empty($activeItems)) {
        return "";
    }

    // Use only the requested number of items
    $itemsToRender = array_slice($activeItems, 0, $limit);

    $outputHtml = '';
    foreach ($itemsToRender as $itemData) {
        // Add placement and position context for specific styling and tracking
        $itemData['current_placement'] = $placement;
        // Define a unique click position identifier
        $itemData['click_position'] = $placement . '_' . $positionIndex;
        // Pass the original page context as well
        $itemData['page_context'] = $context;

        // Use the renamed rendering function
        $outputHtml .= renderPromoUnit($itemData); // Render each selected item
        $positionIndex++; // Increment position for next potential item in the same call
    }

    return $outputHtml;
}

/**
 * Renders a specific promotional unit based on its data.
 * Internal function called by displayAdsForPlacement.
 * Adds data attributes for JS tracking using generic names.
 *
 * @param array $itemData Associative array containing item details. Must include 'type', 'id'.
 * @return string HTML markup for the item, or empty string on error.
 */
function renderPromoUnit(array $itemData): string {
    // Renamed function and variables
    if (empty($itemData['type']) || empty($itemData['id'])) { // Ensure ID is present
        error_log("Promo Item data missing 'type' or 'id' key for rendering.");
        return '';
    }

    $itemType = $itemData['type']; // e.g., 'adsense', 'affiliate'
    $itemId = (int)$itemData['id'];
    $currentPlacement = $itemData['current_placement'] ?? ($itemData['placement'] ?? $itemData['display_position'] ?? 'unknown');

    // Renamed CSS classes
    $wrapperClasses = "content-block block-type-{$itemType} block-location-" . escape($currentPlacement);

    // Generate the item content based on type
    $output = '';
    switch ($itemType) {
        case 'adsense':
            // If AdSense is used, its structure is hard to hide completely.
            // This function renders the container; AdSense JS does the rest.
            $output = renderExternalScriptUnit($itemData); // Renamed function
            break;
        case 'affiliate':
            // This is the type styled like articles.
            $output = renderAffiliatePromoUnit($itemData); // Renamed function, pass full itemData with context
            break;
        default:
            error_log("Attempted to render unknown promo item type: " . $itemType);
            return "";
    }

    if (empty($output)) {
        return "";
    }

    // Add wrapper div with generic classes and data attributes for JS tracking
    // Renamed ID and data attributes
    $finalOutput = "<div id=\"promo-item-{$itemType}-{$itemId}\" class=\"{$wrapperClasses}\" data-item-id=\"{$itemId}\" data-item-type=\"{$itemType}\" data-item-location=\"".escape($currentPlacement)."\">";
    $finalOutput .= $output; // Output directly
    $finalOutput .= "</div>";

    return $finalOutput;
}

/**
 * Renders an AdSense unit container.
 * AdSense code itself is hard to mask from blockers.
 *
 * @param array $adSenseData AdSense unit data. Requires 'ad_code'. Optional: 'custom_css', 'fixed_width', 'fixed_height'.
 * @return string HTML markup for the AdSense unit container.
 */
function renderExternalScriptUnit(array $adSenseData): string {
    // Renamed function
    if (empty($adSenseData['ad_code'])) {
        return '';
    }

    $adCode = $adSenseData['ad_code']; // The actual AdSense code snippet
    $customCss = trim($adSenseData['custom_css'] ?? '');
    $width = $adSenseData['fixed_width'] ?? null;
    $height = $adSenseData['fixed_height'] ?? null;
    $currentPlacement = $adSenseData['current_placement'] ?? $adSenseData['placement'] ?? 'unknown';

    // Basic style for the container
    $containerStyle = "max-width: 100%; overflow: hidden; margin: 0 auto; text-align: center; display: block;"; // Default block display

    // Adjustments based on placement
    if (str_contains($currentPlacement, 'sidebar')) {
         $containerStyle .= "width: 100%;"; // Let sidebar control width
    } elseif ($currentPlacement === 'article_bottom_banner') {
         $containerStyle .= "width: 100%;";
    } else {
         $containerStyle .= "display: inline-block;"; // Center inline-block for other placements
    }

    if ($width && $width > 0) $containerStyle .= "width: {$width}px; ";
    if ($height && $height > 0) $containerStyle .= "min-height: {$height}px; ";

    $html = '';
    if (!empty($customCss)) {
        $sanitizedCss = preg_replace('/<script[^>]*?>.*?<\/script>/is', '', $customCss);
        $sanitizedCss = strip_tags($sanitizedCss);
        $html .= "<style>" . $sanitizedCss . "</style>\n";
    }

    // Renamed class
    $html .= "<div class=\"external-script-container\" style=\"" . escape($containerStyle) . "\">\n";
    // IMPORTANT: The $adCode itself (Google AdSense script) is the primary thing blockers look for.
    // Renaming the container helps slightly but won't hide the AdSense script itself.
    $html .= $adCode;
    $html .= "\n</div>\n";

    return $html;
}

/**
 * Renders an Affiliate promotional unit (styled like an article card).
 * Passes context information to generateTrackingUrl.
 * Uses generic naming.
 *
 * @param array $itemData Affiliate promo data including context. Requires: 'id', 'title', 'external_url'.
 * Context should include: 'current_placement', 'click_position', 'page_context'.
 * @return string HTML markup for the affiliate promo unit.
 */
function renderAffiliatePromoUnit(array $itemData): string {
    // Renamed function and variables
    if (empty($itemData['title']) || empty($itemData['external_url']) || empty($itemData['id'])) {
        return '';
    }

    $itemId = (int)$itemData['id'];
    $title = escape($itemData['title']);
    $description = !empty($itemData['description']) ? escape($itemData['description']) : '';
    $targetUrl = $itemData['external_url'];
    // Kept rel="sponsored" as it's semantically correct. Blockers are less likely to target this alone than classes/IDs.
    $targetAttribute = !empty($itemData['open_in_new_tab']) ? 'target="_blank" rel="noopener noreferrer sponsored"' : 'rel="sponsored"';
    $showSponsoredLabel = !empty($itemData['show_sponsored_label']);
    $customCss = trim($itemData['custom_css'] ?? '');
    $currentPlacement = $itemData['current_placement'] ?? 'unknown';
    $clickPosition = $itemData['click_position'] ?? 'unknown'; // Position identifier
    $pageContext = $itemData['page_context'] ?? []; // Original page context

    // Prepare context for tracking URL generation
    $trackingContext = array_merge($pageContext, [
        'current_placement' => $currentPlacement,
        'click_position' => $clickPosition,
        'ad_id' => $itemId // Keep 'ad_id' if generateTrackingUrl expects it, otherwise change to 'item_id'
        // TODO: Verify if generateTrackingUrl specifically needs 'ad_id' or if 'item_id' would work. Assuming 'ad_id' for now.
    ]);

    // Generate the tracking URL with detailed context
    // The URL structure itself might be targeted by blockers if it contains obvious tracking params.
    $trackingUrl = generateTrackingUrl($itemId, 'affiliate', $targetUrl, $trackingContext);

    // Get image data - use appropriate size based on placement
    // UPDATED: Use 'ms' (medium size 800px) by default for better desktop display quality
    // Only use 'ss' (small size 400px) for sidebar placements which are naturally narrow
    $imageSize = 'ms'; // Default size is now medium (800px) for better desktop display

    if (in_array($currentPlacement, ['sidebar_popular', 'sidebar_middle', 'sidebar_bottom'])) {
        $imageSize = 'ss'; // Use small size (400px) only for sidebar placements
    }

    // Use dynamic image processing for ads
    $featuredImage = $itemData['featured_image'] ?? null;
    if ($featuredImage) {
        // Use a single size for all placements to avoid PageSpeed "properly size images" warning
        // Most ad images display at around 360px width on both mobile and desktop
        $width = 360; // Single width for all placements
        $height = 203; // Default height (16:9 aspect ratio)

        // Generate dynamic image URL
        $originalPath = 'ads/' . $featuredImage;
        $imageUrl = '/image.php?src=' . urlencode($originalPath) .
                   '&w=' . $width .
                   '&h=' . $height .
                   '&q=70&fit=cover&f=webp'; // WebP format with aggressive compression

        $imageWidth = $width;
        $imageHeight = $height;
        $imageAlt = $title;
    } else {
        $imageUrl = null;
        $imageWidth = 300;
        $imageHeight = 200;
        $imageAlt = $title;
    }

    // Custom CSS Styling
    $style = '';
    if (!empty($customCss)) {
        $sanitizedCss = preg_replace('/<script[^>]*?>.*?<\/script>/is', '', $customCss);
        $sanitizedCss = strip_tags($sanitizedCss);
        // Scope the CSS to this specific item instance using the renamed ID
        $style = "<style>#promo-item-affiliate-{$itemId} { " . $sanitizedCss . " }</style>";
    }

    $html = $style;

    // Use shared function for article_beginning, after_content, and in_content to ensure identical layout
    if ($currentPlacement === 'article_beginning' || $currentPlacement === 'after_content' || $currentPlacement === 'in_content') {
        // This function now uses generic classes internally
        $html .= renderStandardArticleCard($trackingUrl, $targetAttribute, $imageUrl, $imageAlt, $imageWidth, $imageHeight, $showSponsoredLabel, $title, $description);
        return $html; // Return early as the wrapper div is added by renderPromoUnit
    }

    // SIDEBAR_BOTTOM in article.php - Fixed image display
    else if ($currentPlacement === 'sidebar_bottom') {
        // Using generic classes, keeping Tailwind for layout
        $html .= "<div class=\"p-0 overflow-hidden block no-underline text-current hover:shadow-sm transition-shadow\">\n";
        $html .= "  <a href=\"" . escape($trackingUrl) . "\" " . $targetAttribute . " class=\"flex flex-col no-underline\">\n";
        if ($imageUrl) {
            // Fixed sidebar_bottom image with proper dimensions and styling
            $html .= "    <div class=\"w-full h-40 bg-gray-100 border border-gray-200 rounded-t-lg overflow-hidden\" style=\"height: 160px;\">\n";
            $html .= "      <img src=\"" . escape($imageUrl) . "\" alt=\"" . escape($imageAlt) . "\" class=\"w-full h-full object-cover\" width=\"{$imageWidth}\" height=\"{$imageHeight}\" loading=\"lazy\" style=\"width: 100%; height: 100%; object-fit: cover;\">\n";
            $html .= "    </div>\n";
        }
        $html .= "    <div class=\"p-3 bg-white border border-t-0 border-gray-200 rounded-b-lg\">\n";
        if ($showSponsoredLabel) {
            $html .= "      <span class=\"inline-block bg-gray-100 text-gray-500 text-xs font-medium px-2 py-0.5 rounded-full mb-1\">Sponzorirano</span>\n";
        }
        $html .= "      <h4 class=\"font-montserrat font-bold text-base line-clamp-2 text-dark-contrast hover:text-primary transition-colors mb-1\">" . $title . "</h4>\n";
        if ($description) {
            $html .= "      <p class=\"text-xs text-gray-600 line-clamp-2 mb-2\">" . $description . "</p>\n";
        }
        $html .= "      <div class=\"text-right\">\n";
        $html .= "        <span class=\"inline-block text-xs font-semibold text-primary border border-primary rounded-full px-2 py-0.5 hover:bg-primary hover:text-white transition-colors\">Saznaj Više</span>\n";
        $html .= "      </div>\n";
        $html .= "    </div>\n"; // End Content Section
        $html .= "  </a>\n"; // End Anchor Tag
        $html .= "</div>\n"; // End Container Div
    }
    // SIDEBAR_MIDDLE on homepage and article.php
    else if ($currentPlacement === 'sidebar_middle') {
        // Using generic classes
        $html .= "<div class=\"p-0 overflow-hidden block no-underline text-current hover:shadow-sm transition-shadow w-full h-full\">\n";
        $html .= "  <a href=\"" . escape($trackingUrl) . "\" " . $targetAttribute . " class=\"flex flex-col no-underline h-full\">\n";
        if ($imageUrl) {
            $html .= "    <div class=\"w-full h-32 bg-gray-100 border border-gray-200 rounded-t-lg overflow-hidden\">\n";
            $html .= "      <img src=\"" . escape($imageUrl) . "\" alt=\"" . escape($imageAlt) . "\" class=\"w-full h-full object-cover\" loading=\"lazy\">\n";
            $html .= "    </div>\n";
        }
        $html .= "    <div class=\"p-3 bg-white border border-t-0 border-gray-200 rounded-b-lg flex-grow flex flex-col justify-between\">\n";
        $html .= "      <div>\n";
        if ($showSponsoredLabel) {
            $html .= "        <span class=\"inline-block bg-gray-100 text-gray-500 text-xs font-medium px-2 py-0.5 rounded-full mb-1\">Sponzorirano</span>\n";
        }
        $html .= "        <h4 class=\"font-montserrat font-bold text-base line-clamp-2 text-dark-contrast hover:text-primary transition-colors mb-1\">" . $title . "</h4>\n";
        if ($description) {
            $html .= "        <p class=\"text-xs text-gray-600 line-clamp-2 mb-2\">" . $description . "</p>\n";
        }
        $html .= "      </div>\n";
        $html .= "      <div class=\"text-right mt-auto\">\n";
        $html .= "        <span class=\"inline-block text-xs font-semibold text-primary border border-primary rounded-full px-2 py-0.5 hover:bg-primary hover:text-white transition-colors\">Saznaj Više</span>\n";
        $html .= "      </div>\n";
        $html .= "    </div>\n"; // End Content Section
        $html .= "  </a>\n"; // End Anchor Tag
        $html .= "</div>\n"; // End Container Div
    }
    // ARTICLE_BOTTOM_BANNER and other placements (Default fallback)
    else {
        // Determine margin class based on placement
        $marginClass = "my-4"; // Default margin
        if ($currentPlacement === 'article_bottom_banner') {
            $marginClass = "my-0"; // No margin for bottom banner
        }
        // Using generic classes
        $html .= "<div class=\"card p-0 overflow-hidden {$marginClass} block no-underline text-current hover:shadow-md transition-shadow duration-300\">\n";
        $html .= "  <a href=\"" . escape($trackingUrl) . "\" " . $targetAttribute . " class=\"flex flex-col sm:flex-row no-underline\">\n";
        // Image Section
        $html .= "    <div class=\"flex-shrink-0 w-full sm:w-40 h-40 sm:h-auto bg-gray-100 border-b sm:border-b-0 sm:border-r border-border flex items-center justify-center overflow-hidden\">\n";
        if ($imageUrl) {
            $html .= "      <img src=\"" . escape($imageUrl) . "\" alt=\"" . escape($imageAlt) . "\" class=\"w-full h-full object-cover\" loading=\"lazy\">\n";
        } else {
             $html .= '      <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>';
        }
        $html .= "    </div>\n";
        // Content Section
        $html .= "    <div class=\"flex-grow p-4 flex flex-col justify-between\">\n";
        $html .= "      <div>\n";
        if ($showSponsoredLabel) {
            $html .= "        <span class=\"inline-block bg-gray-100 text-gray-500 text-xs font-medium px-2 py-0.5 rounded-full mb-2\">Sponzorirano</span>\n";
        }
        $html .= "        <h4 class=\"font-montserrat font-bold text-lg mb-2 line-clamp-2 text-dark-contrast hover:text-primary transition-colors\">" . $title . "</h4>\n";
        if ($description) {
            $html .= "        <p class=\"text-sm text-gray-600 line-clamp-3\">" . $description . "</p>\n";
        }
        $html .= "      </div>\n";
        $html .= "      <div class=\"mt-3 text-right\">\n";
        $html .= "        <span class=\"inline-block text-xs font-semibold text-primary border border-primary rounded-full px-3 py-1 hover:bg-primary hover:text-white transition-colors\">Saznaj Više</span>\n";
        $html .= "      </div>\n";
        $html .= "    </div>\n"; // End Content Section
        $html .= "  </a>\n"; // End Anchor Tag
        $html .= "</div>\n"; // End Container Div
    }

    // The wrapper div with generic classes/attributes is added in renderPromoUnit
    return $html;
}


/**
 * Injects promotional items into article content.
 * Passes context and calculates click_position.
 * Uses generic naming.
 *
 * @param string $content Original article content (HTML).
 * @param PDO $pdo Database connection.
 * @param array $context Contextual information (must include page_type, article_id etc.).
 * @return string Content with items potentially injected.
 */
function injectAdsIntoContent(string $content, PDO $pdo, array $context = []): string {
    // Renamed function, but keeps using getActiveAdsForPlacement internally.
    // Consider renaming getActiveAdsForPlacement if it causes issues and update calls.
    $itemsToInject = getActiveAdsForPlacement($pdo, 'in_content', $context);
    if (empty($itemsToInject)) {
        return $content;
    }

    // --- Configuration ---
    // Assumes the fetched data structure still uses these keys
    $skipParagraphs = (int)($itemsToInject[0]['skip_paragraphs'] ?? 2);
    $insertFrequency = (int)($itemsToInject[0]['frequency'] ?? 3);
    $maxItemsPerPage = (int)($itemsToInject[0]['max_ads_per_page'] ?? 3); // Keep key name if DB uses it

    // --- Logic ---
    $dom = new DOMDocument();
    libxml_use_internal_errors(true); // Suppress HTML5 parsing errors

    // Fix for deprecated mb_convert_encoding
    // Use htmlspecialchars to handle entities properly before loading into DOM
    $safeContent = htmlspecialchars_decode(htmlspecialchars($content, ENT_SUBSTITUTE | ENT_QUOTES, 'UTF-8'));
    // Ensure UTF-8 meta tag for correct parsing
    $dom->loadHTML('<?xml encoding="UTF-8"><meta charset="UTF-8">' . $safeContent, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
    $dom->encoding = 'UTF-8'; // Explicitly set encoding

    libxml_clear_errors();
    $xpath = new DOMXPath($dom);
    $paragraphs = $xpath->query('//p'); // Consider targeting other block elements too?

    // Debug information
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log("In-content items found: " . count($itemsToInject));
        error_log("First item ID: " . ($itemsToInject[0]['id'] ?? 'none'));
        error_log("Skip paragraphs: {$skipParagraphs}, Frequency: {$insertFrequency}, Max: {$maxItemsPerPage}");
        error_log("Total paragraphs: " . ($paragraphs ? $paragraphs->length : 0));
    }

    $itemIndex = 0;
    $itemsInserted = 0;
    $eligibleInsertPoints = 0;
    $itemPlaceholders = []; // Store references to placeholders and their HTML

    if ($paragraphs && $paragraphs->length > $skipParagraphs) {
        foreach ($paragraphs as $index => $paragraphNode) {
            $currentParagraphIndex = $index + 1; // 1-based index

            // Check if this paragraph is an eligible insertion point
            if ($currentParagraphIndex > $skipParagraphs) {
                $eligibleInsertPoints++;

                // Check if it's time to insert based on frequency
                if (($eligibleInsertPoints % $insertFrequency === 1 || $insertFrequency === 1) &&
                    $itemsInserted < $maxItemsPerPage &&
                    isset($itemsToInject[$itemIndex]))
                {
                    // Prepare context for this specific item instance
                    $itemContext = $itemsToInject[$itemIndex];
                    $itemContext['current_placement'] = 'in_content';
                    // Define click position based on paragraph index or eligible point index
                    $itemContext['click_position'] = 'in_content_after_p' . $currentParagraphIndex;
                    $itemContext['page_context'] = $context; // Pass original page context

                    // Use the renamed rendering function
                    $itemHtml = renderPromoUnit($itemContext); // Render the item with context

                    if (!empty($itemHtml)) {
                        // Create a placeholder div with unique ID using renamed prefix
                        $uniqueId = 'injected-item-placeholder-' . $itemsInserted . '-' . time();
                        $placeholderContainer = $dom->createElement('div');
                        $placeholderContainer->setAttribute('id', $uniqueId);
                        // Use renamed class
                        $placeholderContainer->setAttribute('class', 'in-article-item-container');

                        if ($paragraphNode->parentNode) {
                            if ($paragraphNode->nextSibling) {
                                $paragraphNode->parentNode->insertBefore($placeholderContainer, $paragraphNode->nextSibling);
                            } else {
                                $paragraphNode->parentNode->appendChild($placeholderContainer);
                            }

                            // Store the placeholder ID and item HTML for later replacement
                            $itemPlaceholders[] = [
                                'id' => $uniqueId,
                                'html' => $itemHtml
                            ];

                            $itemsInserted++;
                            $itemIndex = ($itemIndex + 1) % count($itemsToInject); // Cycle through items

                            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                                error_log("Item inserted after paragraph {$currentParagraphIndex}, total inserted: {$itemsInserted}");
                            }
                        }
                    }
                }
            }
        }
    }

    // Save the modified HTML, preserving encoding
    $bodyNode = $xpath->query('//body')->item(0);
    $newContent = '';

    if ($bodyNode && $bodyNode->hasChildNodes()) {
        foreach ($bodyNode->childNodes as $node) {
            // Save HTML making sure it's UTF-8
            $newContent .= $dom->saveHTML($node);
        }
    } else {
        // Fallback if loadHTML created implicit body/html tags
        $newContent = $dom->saveHTML();
        // Attempt to strip outer html/body/xml/meta tags if they were added
         $newContent = preg_replace('~^<\?xml encoding="UTF-8"\?>\s*~i', '', $newContent);
         $newContent = preg_replace('~<meta charset="UTF-8">\s*~i', '', $newContent);
         $newContent = preg_replace('~<(?:!DOCTYPE|/?(?:html|body))[^>]*>\s*~i', '', $newContent);
         $newContent = trim($newContent);
    }

    // Now inject the actual item HTML into the placeholders
    foreach ($itemPlaceholders as $placeholderInfo) {
        // Use the renamed class for the placeholder div
        $placeholder = '<div id="' . $placeholderInfo['id'] . '" class="in-article-item-container"></div>';
        // The actual item HTML ($placeholderInfo['html']) already includes the outer wrapper with renamed classes/IDs
        $replacement = $placeholderInfo['html'];
        // Replace the empty placeholder div with the fully rendered item HTML
        // Note: The replacement includes the outer div, so we replace the placeholder *including* its div tag.
        // A safer way might be to inject the *inner* content of $replacement into the placeholder node using DOM,
        // but string replacement is simpler if the placeholder structure is reliable.
        // Let's refine the replacement to ensure the container isn't duplicated.
        $placeholderTag = '<div id="' . $placeholderInfo['id'] . '" class="in-article-item-container">';
        // We need to ensure the $replacement HTML doesn't *also* start with the same container div if renderPromoUnit adds it.
        // renderPromoUnit *does* add the outer div. So we replace the placeholder div entirely.
        $newContent = str_replace($placeholder, $replacement, $newContent);

    }


    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log("Final items inserted: {$itemsInserted}");
    }

    return $newContent ?: $content; // Return modified or original content
}
?>

