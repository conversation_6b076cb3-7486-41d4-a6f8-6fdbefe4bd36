<?php

namespace AuditSystem\Tests\Models;

use PHPUnit\Framework\TestCase;
use AuditSystem\Models\Finding;

class FindingTest extends TestCase
{
    public function testFindingCreation()
    {
        $finding = new Finding(
            'test.php',
            10,
            Finding::TYPE_SECURITY,
            Finding::SEVERITY_HIGH,
            Finding::PRIORITY_AREA,
            'SQL injection vulnerability',
            'Use prepared statements',
            '$query = "SELECT * FROM users WHERE id = " . $_GET["id"];',
            ['https://owasp.org/sql-injection']
        );

        $this->assertEquals('test.php', $finding->file);
        $this->assertEquals(10, $finding->line);
        $this->assertEquals(Finding::TYPE_SECURITY, $finding->type);
        $this->assertEquals(Finding::SEVERITY_HIGH, $finding->severity);
        $this->assertEquals(Finding::PRIORITY_AREA, $finding->priority);
        $this->assertEquals('SQL injection vulnerability', $finding->description);
        $this->assertEquals('Use prepared statements', $finding->recommendation);
        $this->assertStringContainsString('SELECT * FROM users', $finding->codeSnippet);
        $this->assertContains('https://owasp.org/sql-injection', $finding->references);
    }

    public function testFindingArrayConversion()
    {
        $finding = new Finding(
            'test.php',
            10,
            Finding::TYPE_SECURITY,
            Finding::SEVERITY_HIGH,
            Finding::PRIORITY_AREA,
            'Test finding',
            'Test recommendation'
        );

        $array = $finding->toArray();
        $this->assertIsArray($array);
        $this->assertEquals('test.php', $array['file']);
        $this->assertEquals(10, $array['line']);

        $reconstructed = Finding::fromArray($array);
        $this->assertEquals($finding->file, $reconstructed->file);
        $this->assertEquals($finding->line, $reconstructed->line);
        $this->assertEquals($finding->type, $reconstructed->type);
    }
}