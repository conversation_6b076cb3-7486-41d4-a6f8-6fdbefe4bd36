<?php
require_once '../config.php'; // Adjust path as needed
require_once 'includes/auth_check.php'; // Check if admin is logged in
require_once '../includes/functions.php'; // Include helper functions

// Check if it's a POST request and article_ids is set
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['article_ids']) || !is_array($_POST['article_ids'])) {
    $_SESSION['error_message'] = 'Invalid bulk delete request.';
    header('Location: articles.php');
    exit;
}

$article_ids = $_POST['article_ids'];

// Validate that we have at least one ID and they're all numeric
if (empty($article_ids)) {
    $_SESSION['error_message'] = 'No articles selected for deletion.';
    header('Location: articles.php');
    exit;
}

// Filter to ensure all IDs are numeric
$article_ids = array_filter($article_ids, 'is_numeric');
if (empty($article_ids)) {
    $_SESSION['error_message'] = 'Invalid article IDs provided.';
    header('Location: articles.php');
    exit;
}

// --- Delete the articles ---
try {
    // Begin a transaction to ensure all operations succeed or fail together
    $pdo->beginTransaction();
    
    // Create placeholders for the IN clause
    $placeholders = implode(',', array_fill(0, count($article_ids), '?'));
    
    // Optional: Delete related records from article_tags junction table first (if you have foreign key constraints)
    $stmt_tags = $pdo->prepare("DELETE FROM article_tags WHERE article_id IN ($placeholders)");
    foreach ($article_ids as $index => $id) {
        $stmt_tags->bindValue($index + 1, $id, PDO::PARAM_INT);
    }
    $stmt_tags->execute();
    
    // Delete the articles themselves
    $stmt = $pdo->prepare("DELETE FROM articles WHERE id IN ($placeholders)");
    foreach ($article_ids as $index => $id) {
        $stmt->bindValue($index + 1, $id, PDO::PARAM_INT);
    }
    $stmt->execute();
    
    // Commit the transaction if everything succeeded
    $pdo->commit();
    
    // Check how many rows were deleted and provide appropriate feedback
    $deleted_count = $stmt->rowCount();
    if ($deleted_count > 0) {
        $_SESSION['success_message'] = $deleted_count . ' article' . ($deleted_count == 1 ? '' : 's') . ' successfully deleted.';
    } else {
        $_SESSION['error_message'] = 'No articles were found or they were already deleted.';
    }

} catch (PDOException $e) {
    // Roll back the transaction if an error occurred
    $pdo->rollBack();
    
    // Handle potential foreign key constraint errors or other DB issues
    $_SESSION['error_message'] = "Error deleting articles: " . $e->getMessage();
    // Log error in a real application
    error_log("Bulk delete error: " . $e->getMessage());
}

// Redirect back to the articles list
header('Location: articles.php');
exit;
?>