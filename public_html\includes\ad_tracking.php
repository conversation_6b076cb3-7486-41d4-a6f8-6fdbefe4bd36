<?php
/**
 * Ad Tracking - Impression/Click Tracking Functions
 *
 * Handles database updates for ad views/clicks and manages click redirections.
 * v1.4: Modified processAdClickAndRedirect to append tracking params to the final URL.
 * v1.3: Added checks for table existence and more robust error logging/handling during inserts.
 * v1.2: Implemented detailed click/impression logging into new tables.
 */

// Prevent direct access
if (!defined('ABSPATH') && !defined('SITE_URL')) {
    // Adjust path relative to this file to find config.php one level up
    if (file_exists(dirname(__DIR__) . '/config.php')) {
        require_once dirname(__DIR__) . '/config.php';
    } else {
        exit('Direct script access denied. Config not found.');
    }
}

/**
 * Records an ad impression in the database.
 * Updates the summary count and logs details to ad_impressions table.
 *
 * @param PDO $pdo PDO database connection object.
 * @param int $adId ID of the ad being viewed.
 * @param string $adType Type of ad ('adsense', 'affiliate').
 * @param array $context Context data (e.g., placement, page_id, page_type).
 * @return bool True on successful update attempt, false on failure or invalid input.
 */
function recordAdImpression(PDO $pdo, int $adId, string $adType, array $context = []): bool {
    if ($adId <= 0 || empty($adType)) {
        error_log("[Ad Tracking] Invalid input for recordAdImpression: ID={$adId}, Type={$adType}");
        return false;
    }

    $tableName = '';
    if ($adType === 'adsense') {
        $tableName = 'adsense_units';
    } elseif ($adType === 'affiliate') {
        $tableName = 'affiliate_ads';
    } else {
        error_log("[Ad Tracking] Invalid adType '{$adType}' for recordAdImpression.");
        return false;
    }

    $pdo->beginTransaction();
    try {
        // 1. Update the summary view counter in the main ad table
        if (!empty($tableName)) {
            $sql = "UPDATE {$tableName} SET views = views + 1 WHERE id = :id";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':id', $adId, PDO::PARAM_INT);
            $stmt->execute();
        }

        // 2. Log detailed impression data to ad_impressions table
        $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'ad_impressions'");
        if ($tableCheckStmt->rowCount() > 0) {
            $sqlDetail = "INSERT INTO ad_impressions (
                            ad_id, ad_type, placement, page_id, page_type,
                            user_agent, device_type, referrer, ip_hash,
                            created_at, extra_data
                        ) VALUES (
                            :ad_id, :ad_type, :placement, :page_id, :page_type,
                            :user_agent, :device_type, :referrer, :ip_hash,
                            NOW(), :extra_data
                        )";

            $placement = $context['placement'] ?? $context['current_placement'] ?? 'unknown';
            $pageId = $context['page_id'] ?? $context['source_page_id'] ?? $context['article_id'] ?? null;
            $pageType = $context['page_type'] ?? $context['source_page_type'] ?? 'unknown';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $deviceType = detectDeviceType();
            $referrer = $_SERVER['HTTP_REFERER'] ?? '';
            $ipHash = !empty($_SERVER['REMOTE_ADDR']) ? md5($_SERVER['REMOTE_ADDR']) : '';
            $sanitizedContext = array_map(function($value) {
                return is_string($value) ? htmlspecialchars($value, ENT_QUOTES, 'UTF-8') : $value;
            }, $context);
            $extraData = json_encode($sanitizedContext);

            $stmtDetail = $pdo->prepare($sqlDetail);
            $stmtDetail->bindValue(':ad_id', $adId, PDO::PARAM_INT);
            $stmtDetail->bindValue(':ad_type', $adType, PDO::PARAM_STR);
            $stmtDetail->bindValue(':placement', $placement, PDO::PARAM_STR);
            $stmtDetail->bindValue(':page_id', $pageId, $pageId === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
            $stmtDetail->bindValue(':page_type', $pageType, PDO::PARAM_STR);
            $stmtDetail->bindValue(':user_agent', $userAgent, PDO::PARAM_STR);
            $stmtDetail->bindValue(':device_type', $deviceType, PDO::PARAM_STR);
            $stmtDetail->bindValue(':referrer', $referrer, PDO::PARAM_STR);
            $stmtDetail->bindValue(':ip_hash', $ipHash, PDO::PARAM_STR);
            $stmtDetail->bindValue(':extra_data', $extraData, PDO::PARAM_STR);

            if (!$stmtDetail->execute()) {
                 $errorInfo = $stmtDetail->errorInfo();
                 error_log("[Ad Tracking] Failed to insert into ad_impressions. Error: " . ($errorInfo[2] ?? 'Unknown PDO Error'));
            }
        } else {
             error_log("[Ad Tracking] 'ad_impressions' table not found. Skipping detailed impression log.");
        }

        $pdo->commit();
        return true;

    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("[Ad Tracking] Error recording impression for {$adType} ad ID {$adId}: " . $e->getMessage());
        return false;
    }
}


/**
 * Records an ad click in the database.
 * Updates the summary count and logs details to detailed_ad_clicks table.
 *
 * @param PDO $pdo PDO database connection object.
 * @param int $adId ID of the ad being clicked.
 * @param string $adType Type of ad ('adsense', 'affiliate').
 * @param array $context Context data (e.g., placement, source_page_type, source_page_id, click_position, target_url, utm params from $_GET).
 * @return bool True on successful update attempt, false on failure or invalid input.
 */
function recordAdClick(PDO $pdo, int $adId, string $adType, array $context = []): bool {
    if ($adId <= 0 || empty($adType)) {
        error_log("[Ad Tracking] Invalid input for recordAdClick: ID={$adId}, Type={$adType}");
        return false;
    }

    $tableName = '';
    if ($adType === 'affiliate') {
        $tableName = 'affiliate_ads';
    } elseif ($adType !== 'adsense') { // Only log non-adsense types here
        error_log("[Ad Tracking] Invalid adType '{$adType}' for recordAdClick.");
        return false;
    }

    $pdo->beginTransaction();
    try {
        // 1. Update summary click counter if applicable (Affiliate Ads)
        if (!empty($tableName)) {
            $sql = "UPDATE {$tableName} SET clicks = clicks + 1 WHERE id = :id";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':id', $adId, PDO::PARAM_INT);
            $stmt->execute();
        }

        // 2. Log detailed click data to detailed_ad_clicks table
        $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'detailed_ad_clicks'");
        if ($tableCheckStmt->rowCount() > 0) {
            $sqlDetail = "INSERT INTO detailed_ad_clicks (
                            ad_id, ad_type, placement, source_page_type, source_page_id, click_position,
                            target_url, user_agent, device_type, referrer, ip_hash,
                            utm_source, utm_medium, utm_campaign, utm_term, utm_content,
                            raw_get_params, created_at
                        ) VALUES (
                            :ad_id, :ad_type, :placement, :source_page_type, :source_page_id, :click_position,
                            :target_url, :user_agent, :device_type, :referrer, :ip_hash,
                            :utm_source, :utm_medium, :utm_campaign, :utm_term, :utm_content,
                            :raw_get_params, NOW()
                        )";

            // Extract data from context (passed from track_click.php which includes $_GET)
            $placement = $context['placement'] ?? 'unknown';
            $sourcePageType = $context['source_page_type'] ?? 'unknown';
            $sourcePageId = $context['source_page_id'] ?? null;
            $clickPosition = $context['click_position'] ?? 'unknown';
            $targetUrl = $context['target_url'] ?? ''; // Decoded target URL
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $deviceType = detectDeviceType();
            $referrer = $_SERVER['HTTP_REFERER'] ?? '';
            $ipHash = !empty($_SERVER['REMOTE_ADDR']) ? md5($_SERVER['REMOTE_ADDR']) : '';

            // Extract UTM parameters directly from the context (which should contain $_GET)
            $utmSource = $context['utm_source'] ?? null;
            $utmMedium = $context['utm_medium'] ?? null;
            $utmCampaign = $context['utm_campaign'] ?? null;
            $utmTerm = $context['utm_term'] ?? null;
            $utmContent = $context['utm_content'] ?? null;

            // Log the raw GET params from context if available, otherwise re-encode $_GET
            $rawGetParams = isset($context['raw_get_params']) ? $context['raw_get_params'] : json_encode($_GET);

            $stmtDetail = $pdo->prepare($sqlDetail);
            // Bind values carefully, handling nulls
            $stmtDetail->bindValue(':ad_id', $adId, PDO::PARAM_INT);
            $stmtDetail->bindValue(':ad_type', $adType, PDO::PARAM_STR);
            $stmtDetail->bindValue(':placement', $placement, PDO::PARAM_STR);
            $stmtDetail->bindValue(':source_page_type', $sourcePageType, PDO::PARAM_STR);
            $stmtDetail->bindValue(':source_page_id', $sourcePageId, $sourcePageId === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
            $stmtDetail->bindValue(':click_position', $clickPosition, PDO::PARAM_STR);
            $stmtDetail->bindValue(':target_url', $targetUrl, PDO::PARAM_STR);
            $stmtDetail->bindValue(':user_agent', $userAgent, PDO::PARAM_STR);
            $stmtDetail->bindValue(':device_type', $deviceType, PDO::PARAM_STR);
            $stmtDetail->bindValue(':referrer', $referrer, PDO::PARAM_STR);
            $stmtDetail->bindValue(':ip_hash', $ipHash, PDO::PARAM_STR);
            $stmtDetail->bindValue(':utm_source', $utmSource, $utmSource === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
            $stmtDetail->bindValue(':utm_medium', $utmMedium, $utmMedium === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
            $stmtDetail->bindValue(':utm_campaign', $utmCampaign, $utmCampaign === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
            $stmtDetail->bindValue(':utm_term', $utmTerm, $utmTerm === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
            $stmtDetail->bindValue(':utm_content', $utmContent, $utmContent === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
            $stmtDetail->bindValue(':raw_get_params', $rawGetParams, PDO::PARAM_STR);

             if (!$stmtDetail->execute()) {
                 $errorInfo = $stmtDetail->errorInfo();
                 error_log("[Ad Tracking] Failed to insert into detailed_ad_clicks. Error: " . ($errorInfo[2] ?? 'Unknown PDO Error') . " | Data: " . json_encode($context));
            } else {
                 // Optional success log for debugging
                 // error_log("[Ad Tracking] Successfully logged click for Ad ID: {$adId}, UTM Source: {$utmSource}");
            }
        } else {
             error_log("[Ad Tracking] 'detailed_ad_clicks' table not found. Skipping detailed click log.");
        }

        $pdo->commit();
        return true;

    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("[Ad Tracking] Error recording click for {$adType} ad ID {$adId}: " . $e->getMessage());
        return false;
    }
}

/**
 * Appends tracking parameters from the context to a base URL.
 * Preserves existing parameters in the base URL.
 *
 * @param string $baseUrl The original target URL.
 * @param array $context The context array containing tracking parameters (e.g., from $_GET of tracking link).
 * @param array $paramsToAppend List of parameter keys from context to append.
 * @return string The final URL with parameters appended.
 */
function appendTrackingParamsToUrl(string $baseUrl, array $context, array $paramsToAppend): string {
    if (empty($paramsToAppend)) {
        return $baseUrl;
    }

    $urlParts = parse_url($baseUrl);
    if ($urlParts === false) {
        // Invalid base URL, return it as is
        return $baseUrl;
    }

    // Parse existing query string
    $existingParams = [];
    if (isset($urlParts['query'])) {
        parse_str($urlParts['query'], $existingParams);
    }

    // Prepare parameters to add from context
    $newParams = [];
    foreach ($paramsToAppend as $key) {
        if (isset($context[$key]) && !empty($context[$key])) {
            // Add only if not already present in the original URL's query
            if (!isset($existingParams[$key])) {
                $newParams[$key] = $context[$key];
            }
        }
    }

    // If no new parameters to add, return original URL
    if (empty($newParams)) {
        return $baseUrl;
    }

    // Merge existing and new parameters
    $allParams = array_merge($existingParams, $newParams);

    // Build the new query string
    $newQueryString = http_build_query($allParams);

    // Rebuild the final URL
    $finalUrl = '';
    if (isset($urlParts['scheme'])) { $finalUrl .= $urlParts['scheme'] . '://'; }
    if (isset($urlParts['host'])) { $finalUrl .= $urlParts['host']; }
    if (isset($urlParts['port'])) { $finalUrl .= ':' . $urlParts['port']; }
    if (isset($urlParts['path'])) { $finalUrl .= $urlParts['path']; }
    if (!empty($newQueryString)) { $finalUrl .= '?' . $newQueryString; }
    if (isset($urlParts['fragment'])) { $finalUrl .= '#' . $urlParts['fragment']; }

    return $finalUrl;
}


/**
 * Handles the full click tracking process: records click and redirects.
 * *** MODIFIED ***: Now appends tracking parameters to the final URL.
 *
 * @param PDO $pdo PDO database connection object.
 * @param int $adId ID of the ad being clicked.
 * @param string $adType Type of ad.
 * @param string $targetUrl The final destination URL (decoded).
 * @param array $context Additional context data extracted from the request (should include $_GET params).
 * @return void Performs redirection or outputs an error and exits.
 */
function processAdClickAndRedirect(PDO $pdo, int $adId, string $adType, string $targetUrl, array $context = []): void {
    if (empty($targetUrl) || !filter_var($targetUrl, FILTER_VALIDATE_URL)) {
        error_log("[Ad Tracking] Invalid target URL in processAdClickAndRedirect. Ad ID: {$adId}, URL: {$targetUrl}");
        header("Location: " . (defined('SITE_URL') ? SITE_URL : '/'), true, 302);
        exit;
    }

    // 1. Record the Click (Pass the full context)
    recordAdClick($pdo, $adId, $adType, $context);

    // 2. *** NEW: Append tracking parameters to the target URL ***
    // Define which parameters from the tracking link context should be appended
    $paramsToForward = [
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
        'placement', 'ad_id', 'click_position' // Add other relevant params if needed
        // Avoid forwarding 'url' or 'type' or 'ts' from the tracking link itself
    ];
    $finalRedirectUrl = appendTrackingParamsToUrl($targetUrl, $context, $paramsToForward);
    // *** END NEW ***

    // 3. Perform the Redirection to the MODIFIED URL
    header("Location: " . $finalRedirectUrl, true, 302);
    exit;
}

/**
 * Generates a tracking URL that points to your click tracking script.
 * Includes detailed tracking parameters and UTM parameters from ad config.
 *
 * @param int $adId ID of the ad.
 * @param string $adType Type of ad ('affiliate', potentially others).
 * @param string $targetUrl The final destination URL.
 * @param array $context Context for the tracking URL (e.g., ['current_placement' => 'sidebar', 'page_type' => 'article', 'source_page_id' => 123, 'click_position' => 1, 'ad_tracking_code' => 'utm_campaign=...', 'category_slug' => '...']).
 * @return string The tracking URL with all necessary parameters.
 */
function generateTrackingUrl(int $adId, string $adType, string $targetUrl, array $context = []): string {
    if (!defined('SITE_URL')) {
        error_log("SITE_URL not defined in generateTrackingUrl");
        return '#error-site-url-undefined';
    }

    $encodedTargetUrl = rawurlencode($targetUrl);
    $trackingScriptPath = '/track_click.php';
    $trackingUrlBase = rtrim(SITE_URL, '/') . $trackingScriptPath;

    // Essential parameters
    $params = [
        'ad_id' => $adId,
        'type' => $adType,
        'url' => $encodedTargetUrl, // Pass encoded original target URL
        'ts' => time()
    ];

    // Add context parameters if available
    $placement = $context['current_placement'] ?? $context['placement'] ?? null;
    if ($placement) { $params['placement'] = $placement; }

    $pageType = $context['page_type'] ?? null;
    if ($pageType) { $params['source_page_type'] = $pageType; }

    // Use source_page_id if explicitly passed, otherwise fallback to article_id or page_id
    $sourcePageId = $context['source_page_id'] ?? $context['article_id'] ?? $context['page_id'] ?? null;
    // Ensure sourcePageId is not empty before adding
    if ($sourcePageId !== null && $sourcePageId !== '') { $params['source_page_id'] = $sourcePageId; }

    if (isset($context['click_position'])) { $params['click_position'] = $context['click_position']; }

    // Include UTM parameters defined in the ad's tracking_code field
    // Assumes $context might contain 'ad_tracking_code' passed down from ad rendering logic
    $adTrackingCode = $context['ad_tracking_code'] ?? ''; // Get tracking code if available in context
    if (!empty($adTrackingCode)) {
        parse_str($adTrackingCode, $utmParams); // Parse the tracking code string

        // Define tokens to replace in UTM values
        $tokens = [
            '{ad_id}' => $adId,
            '{article_id}' => $sourcePageId ?? '', // Use the determined source_page_id
            '{category}' => $context['category_slug'] ?? '', // Use category slug if available in context
            '{placement}' => $placement ?? '',
            '{page_type}' => $pageType ?? '',
            '{timestamp}' => $params['ts'],
            '{random}' => mt_rand(1000, 9999),
        ];

        foreach ($utmParams as $key => $value) {
            // Only add parameters that start with 'utm_'
            if (strpos($key, 'utm_') === 0 && is_string($value)) {
                 // Replace tokens in the value
                 $params[$key] = str_replace(array_keys($tokens), array_values($tokens), $value);
            }
        }
    }

    $queryString = http_build_query($params);
    $trackingUrl = $trackingUrlBase . "?" . $queryString;

    return $trackingUrl;
}


/**
 * Helper function to detect the device type from the user agent
 *
 * @return string Device type: 'mobile', 'tablet', 'desktop', 'unknown'
 */
if (!function_exists('detectDeviceType')) { // Avoid redeclaring if already in functions.php
    function detectDeviceType(): string {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (empty($userAgent)) return 'unknown';
        // More specific checks for tablets
        if (preg_match('/(tablet|ipad|playbook)|(android(?!.*(mobi|opera mini)))/i', $userAgent)) return 'tablet';
        // Checks for mobile devices
        if (preg_match('/(up.browser|up.link|mmp|symbian|smartphone|midp|wap|phone|android|iemobile|iphone|ipod|opera mini)/i', $userAgent)) return 'mobile';
        // Assume desktop if not tablet or mobile
        return 'desktop';
    }
}

?>

