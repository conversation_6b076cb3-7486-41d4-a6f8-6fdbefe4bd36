<?php

/**
 * Simple PSR-4 autoloader for AuditSystem
 */
spl_autoload_register(function ($class) {
    // Only handle AuditSystem namespace
    if (strpos($class, 'AuditSystem\\') !== 0) {
        return;
    }
    
    // Convert namespace to file path
    $classPath = str_replace('AuditSystem\\', '', $class);
    $classPath = str_replace('\\', DIRECTORY_SEPARATOR, $classPath);
    $file = __DIR__ . '/../src/' . $classPath . '.php';
    
    if (file_exists($file)) {
        require_once $file;
    }
});