# Enable Rewrite Engine
RewriteEngine On

# Set Base Directory (Optional for root, but good practice)
# If files are in the root, RewriteBase is usually '/'
RewriteBase /

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]


# Rules for the tricks
RewriteRule ^load/([^/]+)/?$ loading.php?slug=$1 [L,QSA]
RewriteRule ^category/([^/]+)/?$ category.php?slug=$1 [L,QSA]

# Rules for policy and other important pages
RewriteRule ^privacy-policy/?$ privacy-policy.php [L,QSA]
RewriteRule ^cookie-policy/?$ cookie-policy.php [L,QSA]
RewriteRule ^politika-privatnosti/?$ politika-privatnosti.php [L,QSA]
RewriteRule ^politika-kolacica/?$ politika-kolacica.php [L,QSA]
RewriteRule ^uslovi-koristenja/?$ uslovi_koristenja.php [L,QSA]
RewriteRule ^kontakt/?$ kontakt.php [L,QSA]
RewriteRule ^o-nama/?$ o_nama.php [L,QSA]

# Rule for regular articles (you likely have this already)
RewriteRule ^([^/.]+)/?$ article.php?slug=$1 [L,QSA]
# Rule 1: Redirect requests for 'index.php' to the root '/'
RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/index\.php [NC]
RewriteRule ^index\.php$ / [L,R=301]

# Rule 2: Handle the loading trick URL (/objava/article-slug/)
# Rewrite requests like /objava/some-article-slug/ to loading.php?slug=some-article-slug
# Pattern is relative to RewriteBase (which is now /)
RewriteRule ^objava/([a-zA-Z0-9-]+)/?$ loading.php?slug=$1 [L,QSA]

# Rule 3: Handle clean article URLs (/article-slug/)
# Make sure it's not an actual file or directory first (relative to server root)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
# Rewrite requests like /some-article-slug/ to article.php?slug=some-article-slug
# Pattern is relative to RewriteBase (which is now /)
RewriteRule ^([a-zA-Z0-9-]+)/?$ article.php?slug=$1 [L,QSA]

# Rule 4: Handle homepage access (root /) - directs to index.php
# Make sure it's not an actual file or directory (relative to server root)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
# Rewrite empty path (relative to RewriteBase) to index.php
RewriteRule ^$ index.php [L,QSA]

# --- Security: Prevent Directory Listing ---
Options -Indexes

# --- Security: Protect .htaccess file ---
<Files .htaccess>
    Require all denied
</Files>

# --- Security: Protect config.php ---
<Files config.php>
    Require all denied
</Files>

# --- Security: Protect includes directory ---
# Forbid direct access to the includes directory
RewriteRule ^includes/ - [F,L]
# Alternatively, place a separate .htaccess inside /includes/ with:
# Require all denied

# --- SITEMAP RULES ---

# Automatically generate sitemap.xml when requested
RewriteRule ^sitemap\.xml$ sitemap.php [L]

# --- IMAGE OPTIMIZATION RULES ---

# Ensure image.php is directly accessible
RewriteRule ^image\.php$ - [L]

# Set PHP memory limit for image processing
<IfModule mod_php7.c>
    php_value memory_limit 256M
    php_value max_execution_time 60
    php_value upload_max_filesize 20M
    php_value post_max_size 20M
</IfModule>

<IfModule mod_php8.c>
    php_value memory_limit 256M
    php_value max_execution_time 60
    php_value upload_max_filesize 20M
    php_value post_max_size 20M
</IfModule>

# --- BROWSER CACHING RULES ---

<IfModule mod_expires.c>
    ExpiresActive On

    # Default expiration: 1 hour
    ExpiresDefault "access plus 1 hour"

    # Images
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"

    # CSS, JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"

    # Fonts
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# --- GZIP COMPRESSION ---

<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml

    # Remove browser bugs (only needed for really old browsers)
    BrowserMatch ^Mozilla/4 gzip-only-text/html
    BrowserMatch ^Mozilla/4\.0[678] no-gzip
    BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
    Header append Vary User-Agent
</IfModule>

# Enable browser caching for images
<IfModule mod_expires.c>
  ExpiresActive On

  # Cache images for 1 year (helps returning visitors)
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/webp "access plus 1 year"
  ExpiresByType image/avif "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType image/x-icon "access plus 1 year"
</IfModule>

# Add correct content-type for WebP and AVIF images
<IfModule mod_mime.c>
  AddType image/webp .webp
  AddType image/avif .avif
</IfModule>

# GZIP compression for SVG images
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Set Cache-Control headers for images
<IfModule mod_headers.c>
  # One year for image files
  <FilesMatch "\.(jpg|jpeg|png|gif|webp|avif)$">
    Header set Cache-Control "max-age=31536000, public"
  </FilesMatch>

  # Cache control for processed images
  <FilesMatch "^image\.php">
    Header set Cache-Control "max-age=31536000, public"
  </FilesMatch>
</IfModule>

