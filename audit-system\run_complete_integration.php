<?php

/**
 * Complete System Integration Demonstration
 * 
 * This script demonstrates the fully integrated audit system working with all
 * components wired together, performing a comprehensive audit of the CMS codebase.
 */

require_once __DIR__ . '/vendor/autoload.php';

use AuditSystem\Integration\SystemIntegrator;
use AuditSystem\Config\AuditConfig;
use AuditSystem\Optimization\PerformanceOptimizer;

echo "=== LAKO & FINO CMS COMPLETE AUDIT SYSTEM ===\n";
echo "Demonstrating fully integrated system with all components\n\n";

try {
    // Step 1: Initialize Configuration
    echo "📋 Step 1: Loading Configuration\n";
    echo str_repeat("-", 40) . "\n";
    
    $config = AuditConfig::getInstance();
    $configFile = __DIR__ . '/config/audit.json';
    
    if (file_exists($configFile)) {
        $config->loadFromFile($configFile);
        echo "✅ Configuration loaded from: {$configFile}\n";
    } else {
        echo "⚠️  Configuration file not found, using defaults\n";
    }
    
    // Override for demonstration
    $cmsPath = dirname(__DIR__) . '/public_html';
    $config->set('audit.target_directory', $cmsPath);
    $config->set('audit.report_directory', __DIR__ . '/reports');
    $config->set('audit.progress_file', __DIR__ . '/data/integration_demo_progress.json');
    
    echo "Target Directory: {$cmsPath}\n";
    echo "Report Directory: " . $config->get('audit.report_directory') . "\n\n";
    
    // Step 2: Initialize System Integrator
    echo "🔧 Step 2: Initializing System Integration\n";
    echo str_repeat("-", 40) . "\n";
    
    $integrator = new SystemIntegrator($config);
    $integrator->initialize();
    
    $status = $integrator->getSystemStatus();
    echo "✅ System initialized successfully\n";
    echo "   - Analyzers loaded: " . $status['analyzers']['count'] . "\n";
    echo "   - Services initialized: " . $status['services']['count'] . "\n";
    echo "   - Memory usage: " . formatBytes($status['memory_usage']['current']) . "\n\n";
    
    // Step 3: Perform Health Check
    echo "🏥 Step 3: System Health Check\n";
    echo str_repeat("-", 40) . "\n";
    
    $healthCheck = $integrator->performHealthCheck();
    echo "Overall Status: " . strtoupper($healthCheck['overall_status']) . "\n";
    
    foreach ($healthCheck['checks'] as $checkName => $checkResult) {
        $statusIcon = $checkResult['status'] === 'pass' ? '✅' : 
                     ($checkResult['status'] === 'warning' ? '⚠️' : '❌');
        echo "   {$statusIcon} " . ucwords(str_replace('_', ' ', $checkName)) . ": " . $checkResult['message'] . "\n";
    }
    
    if ($healthCheck['overall_status'] === 'unhealthy') {
        echo "\n❌ System health check failed. Please address the issues above.\n";
        exit(1);
    }
    
    echo "\n";
    
    // Step 4: Initialize Performance Optimizer
    echo "⚡ Step 4: Performance Optimization\n";
    echo str_repeat("-", 40) . "\n";
    
    $optimizer = new PerformanceOptimizer($config);
    $optimizer->optimizeForLargeCodebase();
    
    echo "✅ System optimized for large codebase scanning\n";
    echo "   - Memory limit: " . ini_get('memory_limit') . "\n";
    echo "   - Timeout: " . $config->get('audit.timeout') . " seconds\n";
    echo "   - Max file size: " . formatBytes($config->get('audit.max_file_size')) . "\n\n";
    
    // Step 5: Execute Comprehensive Audit
    echo "🔍 Step 5: Executing Comprehensive Audit\n";
    echo str_repeat("-", 40) . "\n";
    
    if (!is_dir($cmsPath)) {
        echo "❌ CMS directory not found: {$cmsPath}\n";
        echo "Please ensure the CMS files are available for testing.\n";
        exit(1);
    }
    
    $controller = $integrator->getAuditController();
    
    echo "Starting audit execution...\n";
    $startTime = microtime(true);
    
    // Configure audit options
    $auditOptions = [
        'audit.target_directory' => $cmsPath,
        'audit.timeout' => 300,
        'audit.max_file_size' => 2 * 1024 * 1024, // 2MB for demo
        'clear_logs' => true
    ];
    
    // Execute audit with progress monitoring
    $result = $controller->startAudit($auditOptions);
    
    $endTime = microtime(true);
    $duration = $endTime - $startTime;
    
    echo "✅ Audit completed in " . round($duration, 2) . " seconds\n";
    echo "   - Files analyzed: " . count($result->fileStatus) . "\n";
    echo "   - Total findings: " . $result->statistics->totalFindings . "\n";
    echo "   - Critical issues: " . $result->statistics->criticalFindings . "\n";
    echo "   - Priority area issues: " . $result->statistics->priorityAreaFindings . "\n\n";
    
    // Step 6: Generate Comprehensive Reports
    echo "📄 Step 6: Generating Reports\n";
    echo str_repeat("-", 40) . "\n";
    
    $reportGenerator = $integrator->getService('report_generator');
    $reportDir = $config->get('audit.report_directory');
    
    // Ensure report directory exists
    if (!is_dir($reportDir)) {
        mkdir($reportDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d_H-i-s');
    $reports = [
        'markdown' => "{$reportDir}/integration_demo_{$timestamp}.md",
        'json' => "{$reportDir}/integration_demo_{$timestamp}.json",
        'html' => "{$reportDir}/integration_demo_{$timestamp}.html"
    ];
    
    $generatedReports = [];
    foreach ($reports as $format => $path) {
        if ($reportGenerator->exportReport($result, $format, $path)) {
            $generatedReports[] = $format;
            echo "✅ {$format} report: " . basename($path) . "\n";
        } else {
            echo "❌ Failed to generate {$format} report\n";
        }
    }
    
    echo "\n";
    
    // Step 7: Performance Analysis
    echo "📊 Step 7: Performance Analysis\n";
    echo str_repeat("-", 40) . "\n";
    
    $performanceReport = $optimizer->getPerformanceReport();
    
    echo "Performance Metrics:\n";
    echo "   - Processing speed: " . $performanceReport['summary']['files_per_second'] . " files/second\n";
    echo "   - Memory usage: " . formatBytes($performanceReport['summary']['current_memory_usage']) . "\n";
    echo "   - Peak memory: " . formatBytes($performanceReport['summary']['peak_memory_usage']) . "\n";
    
    if (isset($performanceReport['file_processing'])) {
        echo "   - Average processing time: " . $performanceReport['file_processing']['average_processing_time'] . " seconds/file\n";
        echo "   - Average findings per file: " . $performanceReport['file_processing']['average_findings_per_file'] . "\n";
    }
    
    // Generate optimization recommendations
    $recommendations = $optimizer->generateOptimizationRecommendations();
    if (!empty($recommendations)) {
        echo "\nOptimization Recommendations:\n";
        foreach ($recommendations as $rec) {
            $priorityIcon = $rec['priority'] === 'high' ? '🔴' : 
                           ($rec['priority'] === 'medium' ? '🟡' : '🟢');
            echo "   {$priorityIcon} {$rec['message']}\n";
        }
    } else {
        echo "\n✅ No optimization recommendations - system performing well\n";
    }
    
    echo "\n";
    
    // Step 8: Summary and Next Steps
    echo "📋 Step 8: Integration Summary\n";
    echo str_repeat("-", 40) . "\n";
    
    echo "INTEGRATION DEMONSTRATION COMPLETED SUCCESSFULLY!\n\n";
    
    echo "System Components Validated:\n";
    echo "✅ System Integrator - All components wired together\n";
    echo "✅ Audit Controller - Orchestrated complete audit workflow\n";
    echo "✅ Multiple Analyzers - Security, Performance, PHP, Frontend, Configuration\n";
    echo "✅ Progress Tracking - Maintained audit state throughout process\n";
    echo "✅ Report Generation - Created reports in multiple formats\n";
    echo "✅ Performance Optimization - Optimized for large codebase scanning\n";
    echo "✅ Error Handling - Graceful handling of issues and recovery\n";
    echo "✅ Best Practices Integration - Validated against current standards\n\n";
    
    echo "Audit Results Summary:\n";
    echo "📁 Files Analyzed: " . count($result->fileStatus) . "\n";
    echo "🔍 Total Findings: " . $result->statistics->totalFindings . "\n";
    echo "🚨 Critical Issues: " . $result->statistics->criticalFindings . "\n";
    echo "⚠️  High Priority: " . $result->statistics->highFindings . "\n";
    echo "📊 Medium Priority: " . $result->statistics->mediumFindings . "\n";
    echo "ℹ️  Low Priority: " . $result->statistics->lowFindings . "\n";
    echo "🎯 Priority Area Issues: " . $result->statistics->priorityAreaFindings . "\n\n";
    
    echo "Generated Reports:\n";
    foreach ($generatedReports as $format) {
        echo "📄 {$format} report generated\n";
    }
    echo "\n";
    
    echo "Next Steps:\n";
    echo "1. Review generated reports in the reports/ directory\n";
    echo "2. Address critical and high-priority findings first\n";
    echo "3. Use 'php bin/audit.php' for regular audits\n";
    echo "4. Run 'php bin/audit.php validate' to check system health\n";
    echo "5. Monitor performance with 'php bin/audit.php status'\n\n";
    
    if ($result->statistics->criticalFindings > 0) {
        echo "⚠️  IMPORTANT: {$result->statistics->criticalFindings} critical issues found!\n";
        echo "Please review the generated reports and address these issues immediately.\n\n";
    }
    
    echo "🎉 Complete system integration demonstration successful!\n";
    echo "The audit system is fully operational and ready for production use.\n";
    
} catch (Exception $e) {
    echo "\n❌ Integration demonstration failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
} finally {
    // Cleanup
    if (isset($integrator)) {
        $integrator->cleanup();
    }
}

/**
 * Format bytes to human readable format
 */
function formatBytes(int $bytes): string
{
    $units = ['B', 'KB', 'MB', 'GB'];
    $unitIndex = 0;
    
    while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
        $bytes /= 1024;
        $unitIndex++;
    }
    
    return round($bytes, 2) . ' ' . $units[$unitIndex];
}