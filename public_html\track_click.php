<?php
/**
 * Ad Click Tracking Script
 *
 * Records ad clicks (including detailed parameters) and redirects to the target URL.
 * Extracts detailed parameters from the query string.
 * v1.1: Extracts all relevant GET parameters and passes them as context.
 */

// Include required files
require_once 'config.php'; // Needs $pdo
require_once 'includes/ad_tracking.php'; // Needs processAdClickAndRedirect

// --- Parameter Extraction ---
$adId = isset($_GET['ad_id']) ? (int)$_GET['ad_id'] : 0;
$adType = isset($_GET['type']) ? trim($_GET['type']) : '';
$encodedTargetUrl = isset($_GET['url']) ? $_GET['url'] : ''; // Keep encoded initially

// --- Basic Validation ---
if ($adId <= 0 || empty($adType) || empty($encodedTargetUrl)) {
    // Redirect immediately if essential parameters are missing
    error_log("[Track Click] Missing essential parameters. Ad ID: {$adId}, Type: {$adType}, Encoded URL: {$encodedTargetUrl}");
    header("Location: " . (defined('SITE_URL') ? SITE_URL : '/'), true, 302);
    exit;
}

// --- Decode and Validate Target URL ---
$targetUrl = rawurldecode($encodedTargetUrl);
if (!filter_var($targetUrl, FILTER_VALIDATE_URL)) {
     error_log("[Track Click] Invalid decoded target URL. Ad ID: {$adId}, Decoded URL: {$targetUrl}");
     header("Location: " . (defined('SITE_URL') ? SITE_URL : '/'), true, 302);
     exit;
}


// --- Build Context Array from ALL GET Parameters ---
// This ensures all parameters passed in the tracking URL (including UTMs, placement, etc.)
// are available for the recordAdClick function.
$context = $_GET;
$context['target_url'] = $targetUrl; // Add the decoded target URL specifically for logging

// Ensure numeric values are integers if needed by the logging function
if (isset($context['source_page_id'])) {
    $context['source_page_id'] = filter_var($context['source_page_id'], FILTER_VALIDATE_INT, FILTER_NULL_ON_FAILURE);
}
if (isset($context['ad_id'])) { // Ensure ad_id from context is also int
    $context['ad_id'] = (int)$context['ad_id'];
}


// --- Process the Click (Record and Redirect) ---
// The processAdClickAndRedirect function now handles calling recordAdClick
// which in turn inserts into the detailed_ad_clicks table using the $context.
// $pdo should be available from config.php
if (!isset($pdo)) {
     error_log("[Track Click] PDO connection not available.");
     // Redirect to target URL even if logging fails
     header("Location: " . $targetUrl, true, 302);
     exit;
}
processAdClickAndRedirect($pdo, $adId, $adType, $targetUrl, $context);

// Note: processAdClickAndRedirect() will exit after redirecting.

?>
