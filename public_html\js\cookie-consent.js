/**
 * <PERSON><PERSON>sent <PERSON>
 *
 * This script handles the cookie consent banner and functionality.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if consent has already been given
    if (!localStorage.getItem('cookieConsent')) {
        // Create the cookie consent banner
        const banner = document.createElement('div');
        banner.id = 'cookie-consent-banner';
        banner.className = 'fixed bottom-0 left-0 right-0 bg-gray-800 text-white p-4 z-50 shadow-lg';
        banner.innerHTML = `
            <div class="container mx-auto flex flex-col md:flex-row items-center justify-between">
                <div class="mb-4 md:mb-0 text-center md:text-left">
                    <p class="text-sm md:text-base">
                        <PERSON><PERSON>imo kola<PERSON>e kako bismo vam pružili najbolje iskustvo na našoj web stranici.
                        <a href="${window.location.origin}/politika-kolacica" class="underline hover:text-primary">Saznajte više</a>
                    </p>
                    <p class="text-xs mt-1">
                        Ko<PERSON>štenjem sajta prihvatate naše
                        <a href="${window.location.origin}/uslovi-koristenja" class="underline hover:text-primary">Uslove korištenja</a> i
                        <a href="${window.location.origin}/politika-privatnosti" class="underline hover:text-primary">Politiku privatnosti</a>
                    </p>
                </div>
                <div class="flex space-x-3">
                    <button id="cookie-consent-accept" class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg text-sm transition-colors">
                        Prihvatam
                    </button>
                    <button id="cookie-consent-reject" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                        Samo neophodni
                    </button>
                </div>
            </div>
        `;

        // Add the banner to the page
        document.body.appendChild(banner);

        // Add event listeners to the buttons
        document.getElementById('cookie-consent-accept').addEventListener('click', function() {
            acceptCookies(true);
        });

        document.getElementById('cookie-consent-reject').addEventListener('click', function() {
            acceptCookies(false);
        });
    } else {
        // If consent has been given, initialize tracking scripts
        const consentValue = localStorage.getItem('cookieConsent');
        if (consentValue === 'true') {
            initializeTrackingScripts();
        }
    }
});

/**
 * Accept or reject cookies
 * @param {boolean} acceptAll - Whether to accept all cookies or only necessary ones
 */
function acceptCookies(acceptAll) {
    // Store the consent in localStorage
    localStorage.setItem('cookieConsent', acceptAll);

    // Remove the banner
    const banner = document.getElementById('cookie-consent-banner');
    if (banner) {
        banner.remove();
    }

    // Initialize tracking scripts if all cookies are accepted
    if (acceptAll) {
        initializeTrackingScripts();
    }
}

/**
 * Initialize tracking scripts
 */
function initializeTrackingScripts() {
    // Initialize Facebook Pixel if it exists
    if (typeof fbq !== 'undefined') {
        fbq('consent', 'grant');
    }

    // Initialize Google Analytics if it exists
    if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
            'analytics_storage': 'granted',
            'ad_storage': 'granted'
        });
    }
}
