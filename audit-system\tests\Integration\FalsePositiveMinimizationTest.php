<?php

namespace AuditSystem\Tests\Integration;

use PHPUnit\Framework\TestCase;
use AuditSystem\Analyzers\SecurityAnalyzer;
use AuditSystem\Analyzers\PerformanceAnalyzer;
use AuditSystem\Analyzers\PHPAnalyzer;
use AuditSystem\Models\Finding;

/**
 * False Positive Minimization Test Suite
 * 
 * Tests to ensure the audit system minimizes false positives by:
 * - Not flagging well-written, secure code
 * - Correctly identifying legitimate patterns
 * - Providing accurate severity assessments
 */
class FalsePositiveMinimizationTest extends TestCase
{
    private SecurityAnalyzer $securityAnalyzer;
    private PerformanceAnalyzer $performanceAnalyzer;
    private PHPAnalyzer $phpAnalyzer;

    protected function setUp(): void
    {
        parent::setUp();
        $this->securityAnalyzer = new SecurityAnalyzer();
        $this->performanceAnalyzer = new PerformanceAnalyzer();
        $this->phpAnalyzer = new PHPAnalyzer();
    }

    /**
     * Test that clean, secure code produces minimal findings
     */
    public function testCleanSecureCodeMinimalFindings(): void
    {
        $cleanSecureCode = '<?php
declare(strict_types=1);

class UserService
{
    private PDO $pdo;
    
    public function __construct(PDO $pdo)
    {
        $this->pdo = $pdo;
    }
    
    public function getUserById(int $id): ?array
    {
        $stmt = $this->pdo->prepare("SELECT id, username, email FROM users WHERE id = :id");
        $stmt->bindParam(":id", $id, PDO::PARAM_INT);
        $stmt->execute();
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ?: null;
    }
    
    public function createUser(string $username, string $email, string $password): bool
    {
        // Validate input
        if (empty($username) || empty($email) || empty($password)) {
            throw new InvalidArgumentException("All fields are required");
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException("Invalid email format");
        }
        
        // Hash password securely
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $stmt = $this->pdo->prepare("INSERT INTO users (username, email, password) VALUES (:username, :email, :password)");
        $stmt->bindParam(":username", $username, PDO::PARAM_STR);
        $stmt->bindParam(":email", $email, PDO::PARAM_STR);
        $stmt->bindParam(":password", $hashedPassword, PDO::PARAM_STR);
        
        return $stmt->execute();
    }
    
    public function sanitizeOutput(string $text): string
    {
        return htmlspecialchars($text, ENT_QUOTES, "UTF-8");
    }
}';

        $tempFile = $this->createTempFile($cleanSecureCode);
        
        try {
            $securityFindings = $this->securityAnalyzer->analyzeFile($tempFile);
            $performanceFindings = $this->performanceAnalyzer->analyzeFile($tempFile);
            $qualityFindings = $this->phpAnalyzer->analyzeFile($tempFile);
            
            $totalFindings = count($securityFindings) + count($performanceFindings) + count($qualityFindings);
            
            // Clean code should have very few findings
            $this->assertLessThan(3, $totalFindings, 
                'Clean, secure code should have minimal findings');
            
            // No critical security findings should be present
            $criticalSecurityFindings = array_filter($securityFindings, function($finding) {
                return $finding->getSeverity() === 'critical';
            });
            
            $this->assertEmpty($criticalSecurityFindings, 
                'Clean code should not have critical security findings');
                
        } finally {
            unlink($tempFile);
        }
    }

    /**
     * Test that proper PDO usage is not flagged as SQL injection
     */
    public function testProperPDOUsageNotFlagged(): void
    {
        $properPDOCode = '<?php
class DatabaseService
{
    private PDO $pdo;
    
    public function getArticlesByCategory(int $categoryId, int $limit = 10): array
    {
        $stmt = $this->pdo->prepare("
            SELECT a.id, a.title, a.content, a.created_at 
            FROM articles a 
            WHERE a.category_id = :category_id 
            AND a.status = :status 
            ORDER BY a.created_at DESC 
            LIMIT :limit
        ");
        
        $stmt->bindParam(":category_id", $categoryId, PDO::PARAM_INT);
        $stmt->bindValue(":status", "published", PDO::PARAM_STR);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function updateArticleViews(int $articleId): bool
    {
        $stmt = $this->pdo->prepare("UPDATE articles SET views = views + 1 WHERE id = :id");
        $stmt->bindParam(":id", $articleId, PDO::PARAM_INT);
        return $stmt->execute();
    }
}';

        $tempFile = $this->createTempFile($properPDOCode);
        
        try {
            $findings = $this->securityAnalyzer->analyzeFile($tempFile);
            
            // Should not flag proper PDO usage as SQL injection
            $sqlInjectionFindings = array_filter($findings, function($finding) {
                return strpos($finding->getDescription(), 'SQL injection') !== false;
            });
            
            $this->assertEmpty($sqlInjectionFindings, 
                'Proper PDO usage should not be flagged as SQL injection');
                
        } finally {
            unlink($tempFile);
        }
    }

    /**
     * Test that proper input validation is not flagged
     */
    public function testProperInputValidationNotFlagged(): void
    {
        $validationCode = '<?php
class InputValidator
{
    public function validateComment(array $input): array
    {
        $errors = [];
        
        // Validate name
        if (empty($input["name"])) {
            $errors[] = "Name is required";
        } elseif (strlen($input["name"]) > 100) {
            $errors[] = "Name is too long";
        }
        
        // Validate email
        if (!empty($input["email"]) && !filter_var($input["email"], FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Invalid email format";
        }
        
        // Validate comment
        if (empty($input["comment"])) {
            $errors[] = "Comment is required";
        } elseif (strlen($input["comment"]) > 1000) {
            $errors[] = "Comment is too long";
        }
        
        return $errors;
    }
    
    public function sanitizeInput(string $input): string
    {
        $input = trim($input);
        $input = stripslashes($input);
        return htmlspecialchars($input, ENT_QUOTES, "UTF-8");
    }
}';

        $tempFile = $this->createTempFile($validationCode);
        
        try {
            $findings = $this->securityAnalyzer->analyzeFile($tempFile);
            
            // Should not flag proper input validation
            $validationFindings = array_filter($findings, function($finding) {
                return $finding->getSeverity() === 'critical' &&
                       (strpos($finding->getDescription(), 'validation') !== false ||
                        strpos($finding->getDescription(), 'sanitiz') !== false);
            });
            
            $this->assertEmpty($validationFindings, 
                'Proper input validation should not be flagged as critical issue');
                
        } finally {
            unlink($tempFile);
        }
    }

    /**
     * Test that optimized code is not flagged for performance issues
     */
    public function testOptimizedCodeNotFlagged(): void
    {
        $optimizedCode = '<?php
class OptimizedService
{
    private PDO $pdo;
    private array $cache = [];
    
    public function getCachedCategories(): array
    {
        if (isset($this->cache["categories"])) {
            return $this->cache["categories"];
        }
        
        $stmt = $this->pdo->prepare("SELECT id, name, slug FROM categories WHERE active = 1 ORDER BY name");
        $stmt->execute();
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $this->cache["categories"] = $categories;
        return $categories;
    }
    
    public function getArticlesWithCategories(array $articleIds): array
    {
        if (empty($articleIds)) {
            return [];
        }
        
        $placeholders = str_repeat("?,", count($articleIds) - 1) . "?";
        $stmt = $this->pdo->prepare("
            SELECT a.id, a.title, c.name as category_name
            FROM articles a
            LEFT JOIN categories c ON a.category_id = c.id
            WHERE a.id IN ($placeholders)
        ");
        $stmt->execute($articleIds);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}';

        $tempFile = $this->createTempFile($optimizedCode);
        
        try {
            $findings = $this->performanceAnalyzer->analyzeFile($tempFile);
            
            // Should not flag optimized code for major performance issues
            $majorPerformanceFindings = array_filter($findings, function($finding) {
                return $finding->getSeverity() === 'high' &&
                       (strpos($finding->getDescription(), 'N+1') !== false ||
                        strpos($finding->getDescription(), 'cache') !== false);
            });
            
            $this->assertEmpty($majorPerformanceFindings, 
                'Optimized code should not be flagged for major performance issues');
                
        } finally {
            unlink($tempFile);
        }
    }

    /**
     * Test that well-structured code is not flagged for quality issues
     */
    public function testWellStructuredCodeNotFlagged(): void
    {
        $wellStructuredCode = '<?php
declare(strict_types=1);

namespace App\Services;

use App\Models\Article;
use App\Repositories\ArticleRepository;

/**
 * Article service for managing article operations
 */
class ArticleService
{
    private ArticleRepository $articleRepository;
    
    public function __construct(ArticleRepository $articleRepository)
    {
        $this->articleRepository = $articleRepository;
    }
    
    /**
     * Get published articles with pagination
     */
    public function getPublishedArticles(int $page = 1, int $perPage = 10): array
    {
        $offset = ($page - 1) * $perPage;
        return $this->articleRepository->findPublished($offset, $perPage);
    }
    
    /**
     * Create a new article
     */
    public function createArticle(array $data): Article
    {
        $this->validateArticleData($data);
        return $this->articleRepository->create($data);
    }
    
    /**
     * Validate article data
     */
    private function validateArticleData(array $data): void
    {
        if (empty($data["title"])) {
            throw new \InvalidArgumentException("Title is required");
        }
        
        if (empty($data["content"])) {
            throw new \InvalidArgumentException("Content is required");
        }
    }
}';

        $tempFile = $this->createTempFile($wellStructuredCode);
        
        try {
            $findings = $this->phpAnalyzer->analyzeFile($tempFile);
            
            // Should not flag well-structured code for major quality issues
            $majorQualityFindings = array_filter($findings, function($finding) {
                return $finding->getSeverity() === 'high' &&
                       (strpos($finding->getDescription(), 'complex') !== false ||
                        strpos($finding->getDescription(), 'naming') !== false ||
                        strpos($finding->getDescription(), 'structure') !== false);
            });
            
            $this->assertLessThan(2, count($majorQualityFindings), 
                'Well-structured code should have minimal major quality issues');
                
        } finally {
            unlink($tempFile);
        }
    }

    /**
     * Test that configuration constants are not always flagged
     */
    public function testConfigurationConstantsHandledCorrectly(): void
    {
        $configCode = '<?php
// Environment-specific configuration
define("DB_HOST", getenv("DB_HOST") ?: "localhost");
define("DB_NAME", getenv("DB_NAME") ?: "default_db");
define("DB_USER", getenv("DB_USER") ?: "default_user");
define("DB_PASS", getenv("DB_PASS") ?: "");

// Application settings
define("SITE_URL", "https://example.com");
define("SITE_NAME", "Example Site");
define("DEBUG_MODE", false);
define("LOG_LEVEL", "error");

// Security settings
define("SESSION_TIMEOUT", 3600);
define("CSRF_TOKEN_EXPIRY", 1800);';

        $tempFile = $this->createTempFile($configCode);
        
        try {
            $findings = $this->securityAnalyzer->analyzeFile($tempFile);
            
            // Should distinguish between environment variables and hardcoded values
            $hardcodedFindings = array_filter($findings, function($finding) {
                return strpos($finding->getDescription(), 'hardcoded') !== false;
            });
            
            // Should not flag environment variable usage as hardcoded
            $envVarFindings = array_filter($hardcodedFindings, function($finding) {
                return strpos($finding->getCodeSnippet(), 'getenv') !== false;
            });
            
            $this->assertEmpty($envVarFindings, 
                'Environment variable usage should not be flagged as hardcoded');
                
        } finally {
            unlink($tempFile);
        }
    }

    /**
     * Test that legitimate file operations are not over-flagged
     */
    public function testLegitimateFileOperationsNotOverFlagged(): void
    {
        $fileOperationsCode = '<?php
class FileManager
{
    private string $uploadDir;
    
    public function __construct(string $uploadDir)
    {
        $this->uploadDir = rtrim($uploadDir, "/");
    }
    
    public function saveUploadedFile(array $file, string $filename): bool
    {
        // Validate file
        if ($file["error"] !== UPLOAD_ERR_OK) {
            return false;
        }
        
        // Sanitize filename
        $filename = preg_replace("/[^a-zA-Z0-9._-]/", "", $filename);
        $targetPath = $this->uploadDir . "/" . $filename;
        
        // Check if directory exists
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
        
        return move_uploaded_file($file["tmp_name"], $targetPath);
    }
    
    public function getFileContents(string $filename): ?string
    {
        $filepath = $this->uploadDir . "/" . basename($filename);
        
        if (!file_exists($filepath) || !is_readable($filepath)) {
            return null;
        }
        
        return file_get_contents($filepath);
    }
}';

        $tempFile = $this->createTempFile($fileOperationsCode);
        
        try {
            $findings = $this->securityAnalyzer->analyzeFile($tempFile);
            
            // Should not flag secure file operations as critical vulnerabilities
            $criticalFileFindings = array_filter($findings, function($finding) {
                return $finding->getSeverity() === 'critical' &&
                       (strpos($finding->getDescription(), 'file') !== false ||
                        strpos($finding->getDescription(), 'path') !== false);
            });
            
            $this->assertEmpty($criticalFileFindings, 
                'Secure file operations should not be flagged as critical vulnerabilities');
                
        } finally {
            unlink($tempFile);
        }
    }

    /**
     * Test comprehensive false positive analysis
     */
    public function testComprehensiveFalsePositiveAnalysis(): void
    {
        $testCases = [
            'secure_database' => '<?php
class SecureDB {
    private PDO $pdo;
    public function getUser(int $id): ?array {
        $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = :id");
        $stmt->bindParam(":id", $id, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }
}',
            'proper_validation' => '<?php
function validateEmail(string $email): bool {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}
function sanitizeOutput(string $text): string {
    return htmlspecialchars($text, ENT_QUOTES, "UTF-8");
}',
            'optimized_queries' => '<?php
class OptimizedQueries {
    private PDO $pdo;
    public function getBulkData(array $ids): array {
        if (empty($ids)) return [];
        $placeholders = str_repeat("?,", count($ids) - 1) . "?";
        $stmt = $this->pdo->prepare("SELECT * FROM table WHERE id IN ($placeholders)");
        $stmt->execute($ids);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}'
        ];
        
        $results = [];
        
        foreach ($testCases as $name => $code) {
            $tempFile = $this->createTempFile($code);
            
            try {
                $securityFindings = $this->securityAnalyzer->analyzeFile($tempFile);
                $performanceFindings = $this->performanceAnalyzer->analyzeFile($tempFile);
                $qualityFindings = $this->phpAnalyzer->analyzeFile($tempFile);
                
                $criticalFindings = array_filter(
                    array_merge($securityFindings, $performanceFindings, $qualityFindings),
                    function($finding) {
                        return $finding->getSeverity() === 'critical';
                    }
                );
                
                $results[$name] = [
                    'total_findings' => count($securityFindings) + count($performanceFindings) + count($qualityFindings),
                    'critical_findings' => count($criticalFindings),
                    'security_findings' => count($securityFindings),
                    'performance_findings' => count($performanceFindings),
                    'quality_findings' => count($qualityFindings)
                ];
                
            } finally {
                unlink($tempFile);
            }
        }
        
        // Calculate overall false positive rate
        $totalCriticalFindings = array_sum(array_column($results, 'critical_findings'));
        $totalFindings = array_sum(array_column($results, 'total_findings'));
        
        // Good code should have very few critical findings
        $this->assertLessThan(3, $totalCriticalFindings, 
            'Good code samples should have minimal critical findings');
        
        // Overall findings should be reasonable
        $this->assertLessThan(15, $totalFindings, 
            'Good code samples should have reasonable total findings');
        
        echo "\nFalse Positive Analysis Results:\n";
        foreach ($results as $testCase => $data) {
            echo "$testCase: {$data['total_findings']} total ({$data['critical_findings']} critical)\n";
        }
        echo "Total Critical Findings: $totalCriticalFindings\n";
        echo "Total Findings: $totalFindings\n";
    }

    private function createTempFile(string $content): string
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'audit_test_');
        file_put_contents($tempFile, $content);
        return $tempFile;
    }
}