<?php

namespace AuditSystem\Tests\Integration;

use PHPUnit\Framework\TestCase;
use AuditSystem\Controllers\AuditController;
use AuditSystem\Analyzers\SecurityAnalyzer;
use AuditSystem\Analyzers\PerformanceAnalyzer;
use AuditSystem\Analyzers\PHPAnalyzer;
use AuditSystem\Models\Finding;

/**
 * Regression Test Suite
 * 
 * Ensures that known issues continue to be detected across system updates
 * and that previously fixed issues don't reappear.
 */
class RegressionTestSuite extends TestCase
{
    private SecurityAnalyzer $securityAnalyzer;
    private PerformanceAnalyzer $performanceAnalyzer;
    private PHPAnalyzer $phpAnalyzer;
    private string $cmsPath;
    private array $knownIssues;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->securityAnalyzer = new SecurityAnalyzer();
        $this->performanceAnalyzer = new PerformanceAnalyzer();
        $this->phpAnalyzer = new PHPAnalyzer();
        $this->cmsPath = dirname(__DIR__, 3) . '/public_html';
        
        // Define known issues that should always be detected
        $this->knownIssues = [
            'config_hardcoded_credentials' => [
                'file' => 'config.php',
                'type' => 'security',
                'severity' => 'critical',
                'description_contains' => ['hardcoded', 'credential', 'password'],
                'code_contains' => 'DB_PASS'
            ],
            'config_exposed_api_keys' => [
                'file' => 'config.php',
                'type' => 'security',
                'severity' => 'high',
                'description_contains' => ['API key', 'secret', 'exposed'],
                'code_contains' => 'DEEPSEEK_API_KEY'
            ],
            'config_debug_enabled' => [
                'file' => 'config.php',
                'type' => 'security',
                'severity' => 'medium',
                'description_contains' => ['debug', 'error_reporting', 'display_errors'],
                'code_contains' => 'error_reporting(E_ALL)'
            ],
            'comment_input_validation' => [
                'file' => 'process_comment.php',
                'type' => 'security',
                'severity' => 'medium',
                'description_contains' => ['input validation', 'sanitization'],
                'code_contains' => '$_POST'
            ]
        ];
    }

    /**
     * Test that all known critical security issues are still detected
     */
    public function testKnownSecurityIssuesDetected(): void
    {
        $detectedIssues = [];
        
        foreach ($this->knownIssues as $issueId => $issue) {
            if ($issue['type'] !== 'security') continue;
            
            $filePath = $this->cmsPath . '/' . $issue['file'];
            
            if (!file_exists($filePath)) {
                $this->fail("Required file not found: {$issue['file']}");
            }
            
            $findings = $this->securityAnalyzer->analyzeFile($filePath);
            
            $matchingFindings = $this->findMatchingFindings($findings, $issue);
            
            if (!empty($matchingFindings)) {
                $detectedIssues[$issueId] = true;
            } else {
                $detectedIssues[$issueId] = false;
                
                // Log details for debugging
                echo "\nFailed to detect known issue: $issueId\n";
                echo "File: {$issue['file']}\n";
                echo "Expected severity: {$issue['severity']}\n";
                echo "Expected description contains: " . implode(', ', $issue['description_contains']) . "\n";
                echo "Total findings in file: " . count($findings) . "\n";
            }
        }
        
        $detectedCount = array_sum($detectedIssues);
        $totalSecurityIssues = count(array_filter($this->knownIssues, function($issue) {
            return $issue['type'] === 'security';
        }));
        
        $this->assertEquals($totalSecurityIssues, $detectedCount, 
            'All known security issues should be detected. Detected: ' . $detectedCount . '/' . $totalSecurityIssues);
    }

    /**
     * Test that specific vulnerability patterns are consistently detected
     */
    public function testVulnerabilityPatternsConsistentlyDetected(): void
    {
        // Test hardcoded credentials pattern
        $hardcodedCredentialCode = '<?php
define("DB_PASSWORD", "secret123");
define("API_KEY", "sk-1234567890abcdef");
$password = "hardcoded_password";';

        $tempFile = $this->createTempFile($hardcodedCredentialCode);
        
        try {
            $findings = $this->securityAnalyzer->analyzeFile($tempFile);
            
            $credentialFindings = array_filter($findings, function($finding) {
                return $finding->getType() === 'security' &&
                       (strpos($finding->getDescription(), 'hardcoded') !== false ||
                        strpos($finding->getDescription(), 'credential') !== false);
            });
            
            $this->assertNotEmpty($credentialFindings, 
                'Hardcoded credential pattern should always be detected');
                
        } finally {
            unlink($tempFile);
        }
        
        // Test SQL injection pattern
        $sqlInjectionCode = '<?php
$query = "SELECT * FROM users WHERE id = " . $_GET["id"];
$result = mysql_query($query);';

        $tempFile = $this->createTempFile($sqlInjectionCode);
        
        try {
            $findings = $this->securityAnalyzer->analyzeFile($tempFile);
            
            $sqlFindings = array_filter($findings, function($finding) {
                return $finding->getType() === 'security' &&
                       strpos($finding->getDescription(), 'SQL injection') !== false;
            });
            
            $this->assertNotEmpty($sqlFindings, 
                'SQL injection pattern should always be detected');
                
        } finally {
            unlink($tempFile);
        }
    }

    /**
     * Test that performance bottleneck patterns are consistently detected
     */
    public function testPerformanceBottleneckPatternsDetected(): void
    {
        // Test N+1 query pattern
        $n1QueryCode = '<?php
$articles = $pdo->query("SELECT * FROM articles")->fetchAll();
foreach ($articles as $article) {
    $comments = $pdo->query("SELECT * FROM comments WHERE article_id = " . $article["id"])->fetchAll();
    $article["comments"] = $comments;
}';

        $tempFile = $this->createTempFile($n1QueryCode);
        
        try {
            $findings = $this->performanceAnalyzer->analyzeFile($tempFile);
            
            $n1Findings = array_filter($findings, function($finding) {
                return $finding->getType() === 'performance' &&
                       (strpos($finding->getDescription(), 'N+1') !== false ||
                        strpos($finding->getDescription(), 'query in loop') !== false);
            });
            
            $this->assertNotEmpty($n1Findings, 
                'N+1 query pattern should be detected');
                
        } finally {
            unlink($tempFile);
        }
        
        // Test inefficient file operations
        $fileOperationCode = '<?php
for ($i = 0; $i < 100; $i++) {
    $content = file_get_contents("large_file.txt");
    $processed = process_content($content);
    file_put_contents("output_$i.txt", $processed);
}';

        $tempFile = $this->createTempFile($fileOperationCode);
        
        try {
            $findings = $this->performanceAnalyzer->analyzeFile($tempFile);
            
            $fileFindings = array_filter($findings, function($finding) {
                return $finding->getType() === 'performance' &&
                       (strpos($finding->getDescription(), 'file') !== false ||
                        strpos($finding->getDescription(), 'I/O') !== false);
            });
            
            $this->assertNotEmpty($fileFindings, 
                'Inefficient file operation pattern should be detected');
                
        } finally {
            unlink($tempFile);
        }
    }

    /**
     * Test that code quality issues are consistently detected
     */
    public function testCodeQualityIssuesDetected(): void
    {
        // Test complex function
        $complexFunctionCode = '<?php
function complexFunction($a, $b, $c, $d, $e) {
    if ($a > 0) {
        if ($b > 0) {
            if ($c > 0) {
                if ($d > 0) {
                    if ($e > 0) {
                        for ($i = 0; $i < 100; $i++) {
                            for ($j = 0; $j < 100; $j++) {
                                if ($i % 2 == 0) {
                                    if ($j % 2 == 0) {
                                        // Complex nested logic
                                        return $a + $b + $c + $d + $e + $i + $j;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    return 0;
}';

        $tempFile = $this->createTempFile($complexFunctionCode);
        
        try {
            $findings = $this->phpAnalyzer->analyzeFile($tempFile);
            
            $complexityFindings = array_filter($findings, function($finding) {
                return $finding->getType() === 'quality' &&
                       (strpos($finding->getDescription(), 'complex') !== false ||
                        strpos($finding->getDescription(), 'nested') !== false);
            });
            
            $this->assertNotEmpty($complexityFindings, 
                'Complex function pattern should be detected');
                
        } finally {
            unlink($tempFile);
        }
        
        // Test naming convention issues
        $namingIssueCode = '<?php
class badClassName {
    public $Bad_Variable_Name;
    
    public function BAD_FUNCTION_NAME() {
        $another_bad_var = "test";
        return $another_bad_var;
    }
}';

        $tempFile = $this->createTempFile($namingIssueCode);
        
        try {
            $findings = $this->phpAnalyzer->analyzeFile($tempFile);
            
            $namingFindings = array_filter($findings, function($finding) {
                return $finding->getType() === 'quality' &&
                       strpos($finding->getDescription(), 'naming') !== false;
            });
            
            $this->assertNotEmpty($namingFindings, 
                'Naming convention issues should be detected');
                
        } finally {
            unlink($tempFile);
        }
    }

    /**
     * Test that the audit system maintains consistent results over time
     */
    public function testConsistentResultsOverTime(): void
    {
        $testFiles = [
            'config.php',
            'process_comment.php'
        ];
        
        $results = [];
        
        foreach ($testFiles as $file) {
            $filePath = $this->cmsPath . '/' . $file;
            
            if (!file_exists($filePath)) {
                continue;
            }
            
            // Run analysis multiple times to ensure consistency
            $runs = [];
            for ($i = 0; $i < 3; $i++) {
                $securityFindings = $this->securityAnalyzer->analyzeFile($filePath);
                $performanceFindings = $this->performanceAnalyzer->analyzeFile($filePath);
                $qualityFindings = $this->phpAnalyzer->analyzeFile($filePath);
                
                $runs[$i] = [
                    'security' => count($securityFindings),
                    'performance' => count($performanceFindings),
                    'quality' => count($qualityFindings),
                    'total' => count($securityFindings) + count($performanceFindings) + count($qualityFindings)
                ];
            }
            
            // Check consistency across runs
            $securityCounts = array_column($runs, 'security');
            $performanceCounts = array_column($runs, 'performance');
            $qualityCounts = array_column($runs, 'quality');
            
            $this->assertEquals(min($securityCounts), max($securityCounts), 
                "Security findings should be consistent for $file");
            $this->assertEquals(min($performanceCounts), max($performanceCounts), 
                "Performance findings should be consistent for $file");
            $this->assertEquals(min($qualityCounts), max($qualityCounts), 
                "Quality findings should be consistent for $file");
            
            $results[$file] = $runs[0]; // Store first run results
        }
        
        echo "\nConsistency Test Results:\n";
        foreach ($results as $file => $data) {
            echo "$file: {$data['total']} total findings (S:{$data['security']}, P:{$data['performance']}, Q:{$data['quality']})\n";
        }
    }

    /**
     * Test that priority classification remains stable
     */
    public function testPriorityClassificationStability(): void
    {
        $priorityFiles = [
            'config.php' => 'PRIORITY_AREA',
            'process_comment.php' => 'PRIORITY_AREA',
            'admin/index.php' => 'PRIORITY_AREA'
        ];
        
        foreach ($priorityFiles as $file => $expectedPriority) {
            $filePath = $this->cmsPath . '/' . $file;
            
            if (!file_exists($filePath)) {
                continue;
            }
            
            $findings = $this->securityAnalyzer->analyzeFile($filePath);
            
            $priorityFindings = array_filter($findings, function($finding) use ($expectedPriority) {
                return $finding->getPriority() === $expectedPriority;
            });
            
            $this->assertNotEmpty($priorityFindings, 
                "File $file should have $expectedPriority findings");
        }
    }

    /**
     * Test that severity levels remain appropriate
     */
    public function testSeverityLevelsAppropriate(): void
    {
        $configPath = $this->cmsPath . '/config.php';
        
        if (!file_exists($configPath)) {
            $this->markTestSkipped('config.php not found');
        }
        
        $findings = $this->securityAnalyzer->analyzeFile($configPath);
        
        // Should have at least one critical finding (hardcoded credentials)
        $criticalFindings = array_filter($findings, function($finding) {
            return $finding->getSeverity() === 'critical';
        });
        
        $this->assertNotEmpty($criticalFindings, 
            'config.php should have critical security findings');
        
        // Critical findings should be related to credentials or keys
        $credentialCriticalFindings = array_filter($criticalFindings, function($finding) {
            return strpos($finding->getDescription(), 'hardcoded') !== false ||
                   strpos($finding->getDescription(), 'credential') !== false ||
                   strpos($finding->getDescription(), 'password') !== false;
        });
        
        $this->assertNotEmpty($credentialCriticalFindings, 
            'Critical findings should include credential-related issues');
    }

    /**
     * Test baseline metrics to detect regressions
     */
    public function testBaselineMetrics(): void
    {
        $criticalFiles = [
            'config.php',
            'index.php',
            'article.php',
            'process_comment.php'
        ];
        
        $totalFindings = 0;
        $criticalFindings = 0;
        $highFindings = 0;
        $priorityFindings = 0;
        
        foreach ($criticalFiles as $file) {
            $filePath = $this->cmsPath . '/' . $file;
            
            if (!file_exists($filePath)) {
                continue;
            }
            
            $securityFindings = $this->securityAnalyzer->analyzeFile($filePath);
            $performanceFindings = $this->performanceAnalyzer->analyzeFile($filePath);
            $qualityFindings = $this->phpAnalyzer->analyzeFile($filePath);
            
            $allFindings = array_merge($securityFindings, $performanceFindings, $qualityFindings);
            
            foreach ($allFindings as $finding) {
                $totalFindings++;
                
                if ($finding->getSeverity() === 'critical') {
                    $criticalFindings++;
                }
                
                if ($finding->getSeverity() === 'high') {
                    $highFindings++;
                }
                
                if ($finding->getPriority() === 'PRIORITY_AREA') {
                    $priorityFindings++;
                }
            }
        }
        
        // Baseline expectations (adjust based on actual CMS state)
        $this->assertGreaterThan(10, $totalFindings, 
            'Should find significant issues in CMS (baseline: >10)');
        
        $this->assertGreaterThan(2, $criticalFindings, 
            'Should find critical issues (baseline: >2)');
        
        $this->assertGreaterThan(0, $priorityFindings, 
            'Should identify priority area findings (baseline: >0)');
        
        echo "\nBaseline Metrics:\n";
        echo "Total Findings: $totalFindings\n";
        echo "Critical Findings: $criticalFindings\n";
        echo "High Findings: $highFindings\n";
        echo "Priority Findings: $priorityFindings\n";
        
        // Store baseline for future comparison
        $baseline = [
            'total' => $totalFindings,
            'critical' => $criticalFindings,
            'high' => $highFindings,
            'priority' => $priorityFindings,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Could save this to a file for historical comparison
        // file_put_contents(__DIR__ . '/baseline_metrics.json', json_encode($baseline, JSON_PRETTY_PRINT));
    }

    private function findMatchingFindings(array $findings, array $expectedIssue): array
    {
        return array_filter($findings, function($finding) use ($expectedIssue) {
            // Check type
            if ($finding->getType() !== $expectedIssue['type']) {
                return false;
            }
            
            // Check severity
            if ($finding->getSeverity() !== $expectedIssue['severity']) {
                return false;
            }
            
            // Check description contains expected terms
            $description = strtolower($finding->getDescription());
            foreach ($expectedIssue['description_contains'] as $term) {
                if (strpos($description, strtolower($term)) !== false) {
                    return true;
                }
            }
            
            // Check code snippet contains expected code
            if (isset($expectedIssue['code_contains'])) {
                $codeSnippet = $finding->getCodeSnippet();
                if (strpos($codeSnippet, $expectedIssue['code_contains']) !== false) {
                    return true;
                }
            }
            
            return false;
        });
    }

    private function createTempFile(string $content): string
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'regression_test_');
        file_put_contents($tempFile, $content);
        return $tempFile;
    }
}