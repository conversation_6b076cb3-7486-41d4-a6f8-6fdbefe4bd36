<?php

require_once 'vendor/autoload.php';

use AuditSystem\Services\FindingClassifier;
use AuditSystem\Models\Finding;

echo "Testing FindingClassifier...\n\n";

$classifier = new FindingClassifier();

// Test 1: Security finding in priority area
echo "Test 1: Security finding in priority area\n";
$finding1 = $classifier->classifyFinding(
    'public_html/config.php',
    25,
    Finding::TYPE_SECURITY,
    'sql_injection',
    'Direct SQL query concatenation detected',
    '$query = "SELECT * FROM users WHERE id = " . $_GET["id"];'
);

echo "File: " . $finding1->getFile() . "\n";
echo "Severity: " . $finding1->getSeverity() . "\n";
echo "Priority: " . $finding1->getPriority() . "\n";
echo "Recommendation: " . substr($finding1->getRecommendation(), 0, 50) . "...\n";
echo "References count: " . count($finding1->getReferences()) . "\n\n";

// Test 2: Performance issue in ad system
echo "Test 2: Performance issue in ad system\n";
$finding2 = $classifier->classifyFinding(
    'public_html/process_ad_impressions.php',
    30,
    Finding::TYPE_PERFORMANCE,
    'n_plus_one_query',
    'Multiple database queries in loop detected',
    'foreach ($ads as $ad) { $db->query("SELECT * FROM impressions WHERE ad_id = " . $ad->id); }'
);

echo "File: " . $finding2->getFile() . "\n";
echo "Severity: " . $finding2->getSeverity() . "\n";
echo "Priority: " . $finding2->getPriority() . "\n";
echo "Recommendation: " . substr($finding2->getRecommendation(), 0, 50) . "...\n\n";

// Test 3: Quality issue with high complexity
echo "Test 3: Quality issue with high complexity\n";
$finding3 = $classifier->classifyFinding(
    'public_html/includes/functions.php',
    100,
    Finding::TYPE_QUALITY,
    'high_complexity',
    'Function has cyclomatic complexity of 25',
    null,
    ['complexity' => 25]
);

echo "File: " . $finding3->getFile() . "\n";
echo "Severity: " . $finding3->getSeverity() . "\n";
echo "Priority: " . $finding3->getPriority() . "\n";
echo "Recommendation: " . substr($finding3->getRecommendation(), 0, 50) . "...\n\n";

// Test 4: Batch classification
echo "Test 4: Batch classification\n";
$findingData = [
    [
        'file' => 'public_html/config.php',
        'line' => 10,
        'type' => Finding::TYPE_SECURITY,
        'subtype' => 'sql_injection',
        'description' => 'SQL injection vulnerability',
        'codeSnippet' => '$query = "SELECT * FROM users WHERE id = " . $_GET["id"];'
    ],
    [
        'file' => 'public_html/article.php',
        'line' => 25,
        'type' => Finding::TYPE_PERFORMANCE,
        'subtype' => 'n_plus_one_query',
        'description' => 'N+1 query problem',
        'context' => ['query_count' => 50]
    ],
    [
        'file' => 'public_html/privacy-policy.php',
        'line' => 5,
        'type' => Finding::TYPE_QUALITY,
        'subtype' => 'naming_convention',
        'description' => 'Inconsistent variable naming'
    ]
];

$findings = $classifier->classifyFindings($findingData);
echo "Batch classified " . count($findings) . " findings\n";

// Test 5: Priority statistics
echo "\nTest 5: Priority statistics\n";
$stats = $classifier->getPriorityStatistics($findings);
echo "Total findings: " . $stats['total_findings'] . "\n";
echo "Priority area: " . $stats['priority_area'] . "\n";
echo "Non-priority: " . $stats['non_priority'] . "\n";
echo "Critical severity: " . $stats['severity_breakdown'][Finding::SEVERITY_CRITICAL] . "\n";
echo "High severity: " . $stats['severity_breakdown'][Finding::SEVERITY_HIGH] . "\n";
echo "Security findings: " . $stats['type_breakdown'][Finding::TYPE_SECURITY] . "\n";

echo "\nAll tests completed successfully!\n";