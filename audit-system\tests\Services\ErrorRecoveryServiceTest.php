<?php

use PHPUnit\Framework\TestCase;
use AuditSystem\Services\ErrorRecoveryService;
use AuditSystem\Services\AuditLogger;
use AuditSystem\Exceptions\FileAccessException;
use AuditSystem\Exceptions\AnalysisException;
use AuditSystem\Exceptions\MCPConnectionException;
use AuditSystem\Exceptions\ConfigurationException;
use AuditSystem\Exceptions\ProgressException;
use AuditSystem\Exceptions\ReportException;
use AuditSystem\Exceptions\SecurityException;

class ErrorRecoveryServiceTest extends TestCase
{
    private ErrorRecoveryService $recoveryService;
    private AuditLogger $logger;
    private string $tempDir;

    protected function setUp(): void
    {
        $this->tempDir = sys_get_temp_dir() . '/audit_test_' . uniqid();
        mkdir($this->tempDir, 0755, true);
        
        $this->logger = new AuditLogger($this->tempDir . '/logs');
        $this->recoveryService = new ErrorRecoveryService($this->logger, 3);
    }

    protected function tearDown(): void
    {
        $this->removeDirectory($this->tempDir);
    }

    public function testFileAccessExceptionRecovery()
    {
        $exception = FileAccessException::fileNotFound('/nonexistent/file.php');
        $context = 'file_analysis';
        $additionalData = ['file_path' => '/nonexistent/file.php'];

        $result = $this->recoveryService->attemptRecovery($exception, $context, $additionalData);

        $this->assertNotNull($result);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('action', $result);
    }

    public function testAnalysisExceptionRecovery()
    {
        $exception = AnalysisException::analyzerFailed('TestAnalyzer', '/test/file.php', 'parsing failed');
        $context = 'php_analysis';
        $additionalData = ['analyzer' => 'TestAnalyzer', 'file_path' => '/test/file.php'];

        $result = $this->recoveryService->attemptRecovery($exception, $context, $additionalData);

        $this->assertNotNull($result);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('action', $result);
    }

    public function testMCPConnectionExceptionRecovery()
    {
        $exception = MCPConnectionException::serverUnavailable('http://localhost:3000');
        $context = 'best_practices_check';

        $result = $this->recoveryService->attemptRecovery($exception, $context);

        $this->assertNotNull($result);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('action', $result);
    }

    public function testConfigurationExceptionRecovery()
    {
        $exception = ConfigurationException::missingConfiguration('audit.target_directory');
        $context = 'audit_initialization';

        $result = $this->recoveryService->attemptRecovery($exception, $context);

        $this->assertNotNull($result);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('action', $result);
    }

    public function testProgressExceptionRecovery()
    {
        $exception = ProgressException::progressFileCorrupted('/path/to/progress.json');
        $context = 'progress_loading';
        $additionalData = ['backup_path' => '/path/to/backup.json'];

        $result = $this->recoveryService->attemptRecovery($exception, $context, $additionalData);

        $this->assertNotNull($result);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('action', $result);
    }

    public function testReportExceptionRecovery()
    {
        $exception = ReportException::generationFailed('HTML', 'template not found');
        $context = 'report_generation';
        $additionalData = ['format' => 'html'];

        $result = $this->recoveryService->attemptRecovery($exception, $context, $additionalData);

        $this->assertNotNull($result);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('action', $result);
    }

    public function testSecurityExceptionRecovery()
    {
        $exception = SecurityException::pathTraversal('../../../etc/passwd');
        $context = 'file_scanning';

        $result = $this->recoveryService->attemptRecovery($exception, $context);

        $this->assertNotNull($result);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('action', $result);
    }

    public function testMaxRetryAttemptsExceeded()
    {
        $exception = FileAccessException::fileNotFound('/nonexistent/file.php');
        $context = 'file_analysis';

        // Attempt recovery multiple times to exceed limit
        for ($i = 0; $i < 4; $i++) {
            $result = $this->recoveryService->attemptRecovery($exception, $context);
        }

        // The 4th attempt should return null (exceeded max attempts)
        $this->assertNull($result);
    }

    public function testGracefulDegradation()
    {
        $service = 'mcp_server';
        $reason = 'Connection timeout';

        $fallbacks = $this->recoveryService->enableGracefulDegradation($service, $reason);

        $this->assertIsArray($fallbacks);
        $this->assertNotEmpty($fallbacks);
        $this->assertContains('use_local_best_practices', $fallbacks);
    }

    public function testRecoveryStatistics()
    {
        // Generate some recovery attempts
        $exception1 = FileAccessException::fileNotFound('/file1.php');
        $exception2 = AnalysisException::analyzerFailed('TestAnalyzer', '/file2.php', 'error');
        
        $this->recoveryService->attemptRecovery($exception1, 'context1');
        $this->recoveryService->attemptRecovery($exception1, 'context1');
        $this->recoveryService->attemptRecovery($exception2, 'context2');

        $stats = $this->recoveryService->getRecoveryStatistics();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_attempts', $stats);
        $this->assertArrayHasKey('unique_errors', $stats);
        $this->assertArrayHasKey('by_exception', $stats);
        $this->assertEquals(3, $stats['total_attempts']);
        $this->assertEquals(2, $stats['unique_errors']);
    }

    public function testResetRecoveryCounters()
    {
        // Generate some recovery attempts
        $exception = FileAccessException::fileNotFound('/file.php');
        $this->recoveryService->attemptRecovery($exception, 'context');

        $statsBefore = $this->recoveryService->getRecoveryStatistics();
        $this->assertGreaterThan(0, $statsBefore['total_attempts']);

        $this->recoveryService->resetRecoveryCounters();

        $statsAfter = $this->recoveryService->getRecoveryStatistics();
        $this->assertEquals(0, $statsAfter['total_attempts']);
    }

    public function testRetryWithDelayStrategy()
    {
        $exception = FileAccessException::fileNotReadable('/test/file.php');
        $context = 'file_reading';
        $additionalData = ['file_path' => '/test/file.php'];

        $startTime = microtime(true);
        $result = $this->recoveryService->attemptRecovery($exception, $context, $additionalData);
        $endTime = microtime(true);

        $this->assertNotNull($result);
        $this->assertArrayHasKey('action', $result);
        $this->assertEquals('retry', $result['action']);
        
        // Should have some delay (at least 1 second for first retry)
        $this->assertGreaterThanOrEqual(1, $endTime - $startTime);
    }

    public function testSkipFileStrategy()
    {
        $exception = FileAccessException::fileTooLarge('/large/file.php', 2000000, 1000000);
        $context = 'file_analysis';
        $additionalData = ['file_path' => '/large/file.php'];

        $result = $this->recoveryService->attemptRecovery($exception, $context, $additionalData);

        $this->assertNotNull($result);
        $this->assertEquals('skip', $result['action']);
        $this->assertEquals('/large/file.php', $result['file']);
    }

    public function testAlternativePathStrategy()
    {
        // Create a test file that can be found as alternative
        $testFile = $this->tempDir . '/test.php';
        file_put_contents($testFile, '<?php echo "test"; ?>');
        
        $exception = FileAccessException::fileNotFound($this->tempDir . '/TEST.PHP');
        $context = 'file_analysis';
        $additionalData = ['file_path' => $this->tempDir . '/TEST.PHP'];

        $result = $this->recoveryService->attemptRecovery($exception, $context, $additionalData);

        // This test might not find alternatives in this simple case, but should not crash
        $this->assertIsArray($result);
    }

    public function testMCPFallbackStrategy()
    {
        $exception = MCPConnectionException::timeout(30);
        $context = 'best_practices_validation';

        $result = $this->recoveryService->attemptRecovery($exception, $context);

        $this->assertNotNull($result);
        $this->assertArrayHasKey('action', $result);
        $this->assertContains($result['action'], ['retry_connection', 'use_cache', 'local_analysis']);
    }

    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        rmdir($dir);
    }
}