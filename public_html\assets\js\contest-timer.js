/**
 * Article Enhancement Module
 *
 * This script enhances the article reading experience with interactive elements.
 */
console.log("Contest timer script initialized");

document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM loaded for contest timer");

    // Only run on article pages
    if (document.body.getAttribute('data-page-type') !== 'article') {
        console.log("Not an article page, contest timer aborted");
        return;
    }

    // Check if contest timer is enabled for this article
    const contestTimerEnabled = document.body.getAttribute('data-contest-timer');
    console.log("Contest timer enabled value:", contestTimerEnabled);

    // Check for string '1' value (the only valid value)
    if (contestTimerEnabled !== '1') {
        console.log("Contest timer not enabled for this article");
        return;
    }

    console.log("Contest timer is enabled for this article!");

    // Check if this is a real user (not a bot)
    if (!isRealUser()) {
        console.log("Bot detected, contest timer aborted");
        return;
    }

    console.log("Contest timer proceeding with initialization");

    // Function to detect bots/crawlers
    function isRealUser() {
        // Check if it's a bot based on user agent
        const botPatterns = [
            'googlebot', 'bingbot', 'yandex', 'baiduspider', 'facebookexternalhit',
            'twitterbot', 'rogerbot', 'linkedinbot', 'embedly', 'quora link preview',
            'showyoubot', 'outbrain', 'pinterest', 'slackbot', 'vkShare', 'W3C_Validator',
            'crawler', 'spider', 'adsbot', 'Mediapartners-Google'
        ];

        const userAgent = navigator.userAgent.toLowerCase();
        if (botPatterns.some(pattern => userAgent.includes(pattern))) {
            return false;
        }

        // Check for browser features that bots typically don't have
        if (!('ontouchstart' in window) &&
            !navigator.maxTouchPoints &&
            typeof navigator.languages === 'undefined') {
            return false;
        }

        // Check if cookies are enabled (most bots don't process cookies)
        if (!navigator.cookieEnabled) {
            return false;
        }

        return true;
    }

    // Function to check if the timer is visible
    function isTimerVisible() {
        if (!timerContainer) return false;

        // Check if the timer is visible
        const rect = timerContainer.getBoundingClientRect();
        const isVisible = rect.top >= 0 && rect.bottom <= window.innerHeight;

        console.log("Timer visibility check:", isVisible, "Rect:", rect);
        return isVisible;
    }

    // Create timer elements in a way that's harder for crawlers to detect
    function createTimerElements() {
        // Create container with a generic class name
        const timerContainer = document.createElement('div');
        timerContainer.className = 'reader-engagement-module contest-timer-container';

        // Use data attributes instead of obvious class names where possible
        timerContainer.setAttribute('data-module-type', 'engagement');

        // Create content with DOM methods instead of innerHTML
        // This is harder for crawlers to parse and analyze
        const headerDiv = document.createElement('div');
        headerDiv.className = 'contest-timer-header';

        const headerText = document.createElement('span');
        headerText.textContent = 'Nagradna igra - Ostanite 2 minute na članku i učestvujte!';

        const minimizeBtn = document.createElement('button');
        minimizeBtn.className = 'contest-timer-minimize';
        minimizeBtn.setAttribute('aria-label', 'Minimiziraj timer');

        const minimizeSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        minimizeSvg.setAttribute('width', '16');
        minimizeSvg.setAttribute('height', '16');
        minimizeSvg.setAttribute('fill', 'currentColor');
        minimizeSvg.setAttribute('viewBox', '0 0 16 16');

        const svgPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        svgPath.setAttribute('d', 'M14 8a.5.5 0 0 1-.5.5H1.5a.5.5 0 0 1 0-1H13.5a.5.5 0 0 1 .5.5z');

        minimizeSvg.appendChild(svgPath);
        minimizeBtn.appendChild(minimizeSvg);
        headerDiv.appendChild(headerText);
        headerDiv.appendChild(minimizeBtn);

        // Create body
        const bodyDiv = document.createElement('div');
        bodyDiv.className = 'contest-timer-body';

        const messageDiv = document.createElement('div');
        messageDiv.className = 'contest-timer-message';
        messageDiv.innerHTML = 'Ostanite na članku još <strong>2 minute</strong> da biste učestvovali u nagradnoj igri!';

        const timerDisplay = document.createElement('div');
        timerDisplay.className = 'contest-timer-display';
        timerDisplay.textContent = '02:00';

        const infoButton = document.createElement('button');
        infoButton.className = 'contest-timer-button';
        infoButton.id = 'contest-info-button';
        infoButton.textContent = 'Više informacija';

        const progressContainer = document.createElement('div');
        progressContainer.className = 'contest-timer-progress-container';

        const progressDiv = document.createElement('div');
        progressDiv.className = 'contest-timer-progress';

        const progressBar = document.createElement('div');
        progressBar.className = 'contest-timer-progress-bar';
        progressBar.style.width = '0%';

        progressDiv.appendChild(progressBar);
        progressContainer.appendChild(progressDiv);

        bodyDiv.appendChild(messageDiv);
        bodyDiv.appendChild(timerDisplay);
        bodyDiv.appendChild(infoButton);
        bodyDiv.appendChild(progressContainer);

        // Create icon div
        const iconDiv = document.createElement('div');
        iconDiv.className = 'contest-timer-icon';
        iconDiv.textContent = 'Kliknite za prikaz nagradne igre';

        // Assemble the container
        timerContainer.appendChild(headerDiv);
        timerContainer.appendChild(bodyDiv);
        timerContainer.appendChild(iconDiv);

        // Add to document
        document.body.appendChild(timerContainer);

        // Return the container for later use
        return timerContainer;
    }

    // Create timer container (this will be populated by addDelayedElement)
    let timerContainer;

    // Call the function to create elements with minimal delay
    setTimeout(function() {
        timerContainer = createTimerElements();
        console.log("Timer container created:", timerContainer);

        // Add event listeners immediately
        addEventListeners();

        // Force show timer after a short delay
        setTimeout(function() {
            showTimer();
        }, 500);
    }, 500);

    // Create popup
    const popupOverlay = document.createElement('div');
    popupOverlay.className = 'contest-popup-overlay';
    popupOverlay.innerHTML = `
        <div class="contest-popup">
            <div class="contest-popup-header">
                Čestitamo!
                <button class="contest-popup-close" aria-label="Zatvori popup">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
                    </svg>
                </button>
            </div>
            <div class="contest-popup-body">
                <div class="contest-popup-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                        <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                    </svg>
                </div>
                <h3 class="contest-popup-title">Čestitamo! Kvalifikovali ste se!</h3>
                <p class="contest-popup-message">
                    Ostali ste na članku 2 minute i sada možete učestvovati u nagradnoj igri! Pošaljite poruku na našu Facebook stranicu sa tekstom "Učestvujem u nagradnoj igri" da biste potvrdili svoje učešće.
                </p>
                <a href="https://www.facebook.com/mercislike.art" target="_blank" class="contest-popup-button">
                    Pošalji poruku
                </a>
            </div>
        </div>
    `;
    document.body.appendChild(popupOverlay);

    // Variables
    let timerInterval;
    let secondsLeft = 0;
    let totalSeconds = 0;
    let isTimerRunning = false;
    let hasCompletedReading = false;

    // Fixed 2-minute timer for contest
    function calculateReadingTime() {
        // Fixed 2 minutes (120 seconds) for the contest
        return 120;
    }

    // Format seconds to MM:SS
    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    // Update timer display
    function updateTimerDisplay() {
        const timerDisplay = document.querySelector('.contest-timer-display');
        const progressBar = document.querySelector('.contest-timer-progress-bar');

        if (timerDisplay && progressBar) {
            timerDisplay.textContent = formatTime(secondsLeft);

            // Update progress bar
            const progressPercentage = ((totalSeconds - secondsLeft) / totalSeconds) * 100;
            progressBar.style.width = `${progressPercentage}%`;

            // Update message when close to completion
            const timerMessage = document.querySelector('.contest-timer-message');
            if (timerMessage) {
                if (secondsLeft <= 30) {
                    timerMessage.innerHTML = `<strong>Još samo ${secondsLeft} sekundi</strong> do učešća u nagradnoj igri!`;
                }
            }
        }
    }

    // Start the timer
    function startTimer() {
        if (isTimerRunning) return;

        totalSeconds = calculateReadingTime();
        secondsLeft = totalSeconds;
        updateTimerDisplay();

        isTimerRunning = true;

        timerInterval = setInterval(() => {
            secondsLeft--;
            updateTimerDisplay();

            if (secondsLeft <= 0) {
                clearInterval(timerInterval);
                isTimerRunning = false;
                hasCompletedReading = true;
                showCompletionPopup();
            }
        }, 1000);
    }

    // Show completion popup
    function showCompletionPopup() {
        // Create confetti effect
        createConfetti();

        // Show popup
        popupOverlay.classList.add('active');
    }

    // Create confetti effect
    function createConfetti() {
        const colors = ['#ff6481', '#ffd6dd', '#ff4d6e', '#ffb3c0', '#ff99aa'];
        const confettiCount = 100;

        for (let i = 0; i < confettiCount; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.left = `${Math.random() * 100}%`;
            confetti.style.top = `${Math.random() * 100}%`;
            confetti.style.transform = `rotate(${Math.random() * 360}deg)`;

            popupOverlay.appendChild(confetti);

            // Animate confetti
            setTimeout(() => {
                confetti.style.opacity = '1';
                confetti.style.transform = `translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px) rotate(${Math.random() * 360}deg)`;
                confetti.style.transition = `all ${Math.random() * 3 + 1}s ease-out`;

                setTimeout(() => {
                    confetti.style.opacity = '0';
                    setTimeout(() => {
                        confetti.remove();
                    }, 1000);
                }, 2000);
            }, Math.random() * 500);
        }
    }

    // Function to add event listeners
    function addEventListeners() {
        console.log('Adding event listeners');

        // Find elements within the container
        const minimizeButton = timerContainer.querySelector('.contest-timer-minimize');
        const iconElement = timerContainer.querySelector('.contest-timer-icon');
        const infoButton = document.getElementById('contest-info-button');
        const popupCloseButton = document.querySelector('.contest-popup-close');

        // Add event listeners
        if (minimizeButton) {
            minimizeButton.addEventListener('click', function() {
                timerContainer.classList.toggle('minimized');
            });
        }

        if (iconElement) {
            iconElement.addEventListener('click', function() {
                timerContainer.classList.remove('minimized');
            });
        }

        if (popupCloseButton) {
            popupCloseButton.addEventListener('click', function() {
                popupOverlay.classList.remove('active');
            });
        }

        if (infoButton) {
            infoButton.addEventListener('click', function() {
                showCompletionPopup();
            });
        }
    }

    // Show timer only after scrolling 60-70% of the article
    let hasScrolledEnough = false;
    let timerShown = false;

    function checkScrollPosition() {
        if (timerShown) return;

        // Try multiple selectors to find the article content
        const articleContent = document.querySelector('.article-content') ||
                              document.querySelector('.post-content') ||
                              document.querySelector('article') ||
                              document.querySelector('.entry-content');

        if (!articleContent) {
            console.log('Contest timer: Article content not found');
            return;
        }

        // Get scroll position
        const windowHeight = window.innerHeight;
        const documentHeight = Math.max(
            document.body.scrollHeight,
            document.documentElement.scrollHeight,
            document.body.offsetHeight,
            document.documentElement.offsetHeight,
            document.body.clientHeight,
            document.documentElement.clientHeight
        );
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Calculate scroll percentage of entire page
        const scrollPercentage = (scrollTop + windowHeight) / documentHeight;

        // Log for debugging
        console.log('Scroll percentage: ' + (scrollPercentage * 100).toFixed(2) + '%');

        // Show timer when user has scrolled through 65% of the page
        if (scrollPercentage >= 0.65 && !hasScrolledEnough) {
            console.log('Scroll threshold reached, showing timer');
            hasScrolledEnough = true;
            showTimer();
        }
    }

    function showTimer() {
        if (timerShown || !timerContainer) {
            console.log('Timer already shown or container not found');
            return;
        }

        console.log('Showing contest timer');

        // Make sure the timer is visible with inline styles as well
        timerContainer.style.opacity = '1';
        timerContainer.style.visibility = 'visible';
        timerContainer.style.transform = 'translateY(0)';
        timerContainer.style.display = 'block';
        timerContainer.style.zIndex = '9999';
        timerContainer.style.position = 'fixed';
        timerContainer.style.bottom = '50px';

        // Add the visible class
        timerContainer.classList.add('visible');

        timerShown = true;
        startTimer();

        // Force a reflow to ensure styles are applied
        void timerContainer.offsetWidth;

        console.log('Timer should now be visible');
    }

    // Add scroll event listener
    console.log("Adding scroll event listener for contest timer");
    window.addEventListener('scroll', checkScrollPosition, { passive: true });

    // Check initial position (in case the page is loaded already scrolled down)
    console.log("Setting up initial scroll position checks");
    setTimeout(function() {
        console.log("Running first scroll check (500ms)");
        checkScrollPosition();
    }, 500);

    // Check again after a bit longer to ensure we catch it
    setTimeout(function() {
        console.log("Running second scroll check (1500ms)");
        checkScrollPosition();
    }, 1500);

    setTimeout(function() {
        console.log("Running third scroll check (3000ms)");
        checkScrollPosition();
    }, 3000);

    // Force the timer to show immediately regardless of scroll position
    setTimeout(function() {
        console.log("Forcing timer to show immediately");
        if (!timerShown && timerContainer) {
            console.log("Forcing timer display now");
            showTimer();
        }
    }, 1000);

    // Visibility change handling (pause timer when tab is not active)
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            if (isTimerRunning) {
                clearInterval(timerInterval);
                isTimerRunning = false;
            }
        } else {
            if (!isTimerRunning && !hasCompletedReading && timerShown) {
                startTimer();
            }
            // Re-check scroll position when tab becomes visible again
            checkScrollPosition();
        }
    });
});
