<?php
// --- Configuration & Setup ---
// Ensure no whitespace or output before this line

// Include database configuration ($pdo or $conn expected)
require_once 'config.php';

// --- IMPORTANT: Replace with your actual DeepSeek API Key ---
if (!defined('DEEPSEEK_API_KEY')) {
    define('DEEPSEEK_API_KEY', '***********************************');
}
if (!defined('DEEPSEEK_API_URL')) {
    define('DEEPSEEK_API_URL', 'https://api.deepseek.com/v1/chat/completions');
}

// --- Set header to return JSON ---
header('Content-Type: application/json');

// --- Input Validation & Processing ---

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit;
}

// Get POST data and perform validation
$gender = $_POST['gender'] ?? null;
$age = filter_input(INPUT_POST, 'age', FILTER_VALIDATE_INT, ["options" => ["min_range" => 16, "max_range" => 100]]);
$height = filter_input(INPUT_POST, 'height', FILTER_VALIDATE_INT, ["options" => ["min_range" => 100, "max_range" => 250]]);
$current_weight = filter_input(INPUT_POST, 'weight', FILTER_VALIDATE_FLOAT, ["flags" => FILTER_FLAG_ALLOW_FRACTION, "options" => ["min_range" => 30, "max_range" => 300]]);
$goal_weight = filter_input(INPUT_POST, 'goal_weight', FILTER_VALIDATE_FLOAT, ["flags" => FILTER_FLAG_ALLOW_FRACTION, "options" => ["min_range" => 30, "max_range" => 300]]);
$activity_level = $_POST['activity_level'] ?? null;
$preferences = $_POST['preferences'] ?? '';

// Validation checks
$errors = [];
if (empty($gender) || !in_array($gender, ['muski', 'zenski'])) {
    $errors[] = 'Nevažeći pol.';
}
if ($age === false) {
    $errors[] = 'Nevažeće godine (moraju biti između 16 i 100).';
}
if ($height === false) {
    $errors[] = 'Nevažeća visina (mora biti između 100 i 250 cm).';
}
if ($current_weight === false) {
    $errors[] = 'Nevažeća trenutna težina (mora biti između 30 i 300 kg).';
}
if ($goal_weight === false) {
    $errors[] = 'Nevažeća ciljna težina (mora biti između 30 i 300 kg).';
}
// Check goal weight only if both weights are valid numbers
if ($goal_weight !== false && $current_weight !== false && $goal_weight >= $current_weight) {
    $errors[] = 'Ciljna težina mora biti manja od trenutne težine.';
}
if (empty($activity_level) || !in_array($activity_level, ['sedentary', 'lightly_active', 'moderately_active', 'very_active', 'extra_active'])) {
    $errors[] = 'Nevažeći nivo aktivnosti.';
}
// Check if API key is still the placeholder
if (DEEPSEEK_API_KEY === 'YOUR_DEEPSEEK_API_KEY') {
     $errors[] = 'API ključ nije konfigurisan u smrsaj-deepseek-api.php.';
}

// If errors exist, return them as JSON
if (!empty($errors)) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => implode(' ', $errors)]);
    exit;
}

// --- Caching Logic ---

// Create a unique key for caching based on input parameters
$cache_params = [
    'gender' => $gender,
    'age' => $age,
    'height' => $height,
    'current_weight' => $current_weight,
    'goal_weight' => $goal_weight,
    'activity_level' => $activity_level,
    'preferences' => $preferences,
];
ksort($cache_params); // Ensure consistent order for hashing
$request_hash = hash('sha256', http_build_query($cache_params));

// Check cache (assuming $pdo is your PDO connection object from config.php)
try {
    // Check if $pdo is set and is a PDO object
    if (!isset($pdo) || !($pdo instanceof PDO)) {
         throw new Exception("Database connection (\$pdo) not available.");
    }

    $stmt = $pdo->prepare("SELECT response_text FROM smrsaj_cache WHERE request_hash = :hash LIMIT 1");
    $stmt->bindParam(':hash', $request_hash, PDO::PARAM_STR);
    $stmt->execute();
    $cached_response = $stmt->fetchColumn();

    if ($cached_response !== false) {
        // Cache hit! Return cached response
        
        // Convert markdown to HTML
        require_once 'includes/Parsedown.php';
        $Parsedown = new Parsedown();
        $html_response = $Parsedown->text($cached_response);
        
        echo json_encode(['success' => true, 'advice' => $html_response, 'source' => 'cache']);
        exit;
    }
} catch (Exception $e) {
    error_log("Cache check failed: " . $e->getMessage());
}

// *** CRITICAL FIX: Release session lock before making long API call ***
if (session_status() === PHP_SESSION_ACTIVE) {
    session_write_close();
}

// --- DeepSeek API Interaction ---

// Construct the prompt in Bosnian
$prompt = "Kreiraj detaljan, zdrav i realno brz plan mršavljenja za osobu sa sljedećim karakteristikama:\n";
$prompt .= "- Pol: " . ($gender === 'muski' ? 'Muški' : 'Ženski') . "\n";
$prompt .= "- Godine: $age\n";
$prompt .= "- Visina: $height cm\n";
$prompt .= "- Trenutna težina: $current_weight kg\n";
$prompt .= "- Ciljna težina: $goal_weight kg\n";
$prompt .= "- Nivo aktivnosti: ";
switch ($activity_level) {
    case 'sedentary': $prompt .= "Sjedilački (malo ili nimalo vježbanja)"; break;
    case 'lightly_active': $prompt .= "Lagano aktivan (lagano vježbanje/sport 1-3 dana/sedmično)"; break;
    case 'moderately_active': $prompt .= "Umjereno aktivan (umjereno vježbanje/sport 3-5 dana/sedmično)"; break;
    case 'very_active': $prompt .= "Veoma aktivan (intenzivno vježbanje/sport 6-7 dana/sedmično)"; break;
    case 'extra_active': $prompt .= "Ekstra aktivan (vrlo intenzivno vježbanje/sport & fizički posao)"; break;
}

// Add preferences if present
if (!empty($preferences)) {
    $prompt .= "\n- Preferencije u ishrani: $preferences";
}

$prompt .= "\n\nPlan treba da uključi:\n";
$prompt .= "1.  Preporuke za ishranu (vrste hrane, veličine porcija, raspored obroka).\n";
$prompt .= "2.  Preporuke za fizičku aktivnost (vrste vježbi, učestalost, trajanje).\n";
$prompt .= "3.  Savjete za održavanje motivacije i praćenje napretka.\n";
$prompt .= "4.  Procjenu realnog vremenskog okvira za postizanje cilja.\n";
$prompt .= "Naglasak treba biti na zdravim navikama i održivosti, a ne na ekstremnim metodama. Odgovor treba biti na bosanskom jeziku, formatiran čitljivo (koristi paragrafe, liste, markdown za bolje formatiranje).";

// Prepare data for DeepSeek API
$data = [
    'model' => 'deepseek-chat', // Use the appropriate model identifier
    'messages' => [
        ['role' => 'system', 'content' => 'Ti si AI asistent specijalizovan za kreiranje personalizovanih planova mršavljenja na bosanskom jeziku. Formatiraj odgovor koristeći markdown za bolju čitljivost.'],
        ['role' => 'user', 'content' => $prompt]
    ],
    'max_tokens' => 1500, // Adjust token limit as needed
    'temperature' => 0.7, // Adjust for creativity vs deterministic output
];

// Initialize cURL session
$ch = curl_init(DEEPSEEK_API_URL);

// Set cURL options
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return response as string
curl_setopt($ch, CURLOPT_POST, true); // Set method to POST
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data)); // Set POST data as JSON
curl_setopt($ch, CURLOPT_HTTPHEADER, [ // Set necessary headers
    'Content-Type: application/json',
    'Authorization: Bearer ' . DEEPSEEK_API_KEY
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 90); // Increase timeout slightly (e.g., 90 seconds)
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // Connection timeout
curl_setopt($ch, CURLOPT_NOSIGNAL, 1); // Prevent SIGALRM from interrupting the request

// Execute cURL request
$response_raw = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE); // Get HTTP status code
$curl_error = curl_error($ch); // Get cURL error message, if any
curl_close($ch); // Close cURL session

// --- Process API Response & Cache ---

// Check for cURL errors
if ($curl_error) {
    error_log("DeepSeek API cURL Error: " . $curl_error);
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'Greška pri komunikaciji sa AI servisom (cURL).']);
    exit;
}

// Check for HTTP errors (4xx or 5xx)
if ($http_code >= 400) {
     error_log("DeepSeek API HTTP Error: Code $http_code, Response: $response_raw");
     $error_details = json_decode($response_raw, true);
     // Try to extract a meaningful error message from the API response
     $api_error_message = $error_details['error']['message'] ?? 'Nepoznata greška sa AI servisa.';
     http_response_code($http_code); // Forward the status code if possible
     echo json_encode(['success' => false, 'message' => "Greška od AI servisa (HTTP $http_code): " . $api_error_message]);
    exit;
}

// Decode the JSON response from DeepSeek
$response_data = json_decode($response_raw, true);

// Check for JSON decoding errors
if (json_last_error() !== JSON_ERROR_NONE) {
    error_log("DeepSeek API JSON Decode Error: " . json_last_error_msg() . ", Raw Response: " . $response_raw);
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'Greška pri obradi odgovora od AI servisa (JSON).']);
    exit;
}

// Extract the actual plan text
$plan_text = $response_data['choices'][0]['message']['content'] ?? null;

// Check if the plan text was successfully extracted
if (empty($plan_text)) {
     error_log("DeepSeek API Empty Content or Unexpected Structure: " . $response_raw);
     http_response_code(500); // Internal Server Error
     echo json_encode(['success' => false, 'message' => 'AI servis nije vratio plan u očekivanom formatu.']);
    exit;
}

// --- Store in Cache ---
try {
    // Check again if $pdo is valid before using it
    if (!isset($pdo) || !($pdo instanceof PDO)) {
         throw new Exception("Database connection (\$pdo) not available for caching.");
    }
    
    // Set PDO to throw exceptions on error
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Prepare and execute in one step
    $pdo->prepare("INSERT INTO smrsaj_cache (request_hash, response_text) VALUES (:hash, :response) ON DUPLICATE KEY UPDATE response_text = :response")
        ->execute([
            ':hash' => $request_hash,
            ':response' => $plan_text
        ]);
} catch (Exception $e) {
    // Just log the error but continue - caching is not critical for functionality
    error_log("Cache store failed: " . $e->getMessage());
}

// Convert markdown to HTML for display
require_once 'includes/Parsedown.php';
$Parsedown = new Parsedown();
$html_response = $Parsedown->text($plan_text);

// --- Return Success Response ---
echo json_encode(['success' => true, 'advice' => $html_response, 'source' => 'api']);

exit;
?>
