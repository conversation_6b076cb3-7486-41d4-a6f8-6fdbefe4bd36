<?php
// This file assumes config.php has been included before it.
// Ensure SITE_URL is defined, provide a fallback if necessary for safety.
if (!defined('SITE_URL')) {
    // Attempt to define SITE_URL based on server variables if not already defined
    // This is a basic fallback and might need adjustment based on your server setup
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    define('SITE_URL', rtrim($protocol . $host, '/'));
    error_log("SITE_URL constant was not defined, fallback used in footer.php: " . SITE_URL);
}
?>
    </main> <?php // Closing the main tag opened in header.php ?>

    <?php // Facebook SDK
    if (defined('FB_APP_ID') && !empty(FB_APP_ID)) {
        echo generateFacebookSDKCode(FB_APP_ID);
    }
    ?>

    <?php // --- Mobile Bottom Navigation --- ?>
    <nav class="mobile-bottom-nav" x-data> <?php // Added x-data for Alpine.js context if needed, though dispatch works globally ?>
        <?php // --- Home Link --- ?>
        <a href="<?php echo SITE_URL; ?>" class="mobile-bottom-nav-item" aria-label="Početna stranica">
            <svg xmlns="http://www.w3.org/2000/svg" class="mobile-bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <span class="mobile-bottom-nav-text">Početna</span>
        </a>

        <?php // --- Search Button --- ?>
        <?php // This button uses Alpine.js to dispatch an event that opens the search overlay (defined in header.php) ?>
        <button @click="$dispatch('search-open')" class="mobile-bottom-nav-item" aria-label="Otvori pretragu">
            <svg xmlns="http://www.w3.org/2000/svg" class="mobile-bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span class="mobile-bottom-nav-text">Pretraga</span>
        </button>

        <?php // --- Recipes Link (Updated) --- ?>
        <?php // Links to the 'recepti' category page ?>
        <a href="<?php echo SITE_URL; ?>/category/recepti/" class="mobile-bottom-nav-item" aria-label="Recepti">
            <svg xmlns="http://www.w3.org/2000/svg" class="mobile-bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            <span class="mobile-bottom-nav-text">Recepti</span>
        </a>

        <?php // --- Popular Link (Replaces Saved) --- ?>
        <?php // Links to the popular page ?>
        <a href="<?php echo SITE_URL; ?>/popularno.php" class="mobile-bottom-nav-item" aria-label="Popularno">
            <?php // Using a 'fire' icon for Popular ?>
            <svg xmlns="http://www.w3.org/2000/svg" class="mobile-bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.657 7.343A8 8 0 0117.657 18.657z" />
              <path stroke-linecap="round" stroke-linejoin="round" d="M9.522 17.47a4.5 4.5 0 015.956-5.956" />
            </svg>
            <span class="mobile-bottom-nav-text">Popularno</span>
        </a>
    </nav>

    <?php // --- Footer Section --- ?>
    <footer class="bg-white mt-12 py-8 md:py-12 border-t border-border">
        <div class="container mx-auto px-4 max-w-900">
            <div class="grid grid-cols-2 md:grid-cols-3 gap-6 md:gap-8">
                <?php // --- Footer Logo and Description --- ?>
                <div class="col-span-2 md:col-span-1">
                    <h3 class="mb-4">
                         <a href="<?php echo SITE_URL; ?>">
                            <span class="font-montserrat text-xl font-extrabold text-primary">
                                <span class="relative inline-block">
                                    Lako <span class="text-dark-contrast italic font-light">&</span> <span class="relative">Fino
                                        <span class="absolute -bottom-1 left-0 w-full h-0.5 bg-primary rounded-full"></span>
                                    </span>
                                </span>
                            </span>
                        </a>
                    </h3>
                    <p class="footer-text text-sm">Ukusni recepti, savjeti za zdravlje i prirodni lijekovi. Istražite naše omiljene recepte za domaća jela, torte i druge slastice.</p>
                </div>
                <?php // --- Footer Categories Links --- ?>
                <div>
                    <h4 class="font-montserrat font-bold mb-3 md:mb-4 relative after:absolute after:w-8 md:after:w-12 after:h-0.5 after:bg-primary after:bottom-0 after:left-0 pb-2 text-sm md:text-base text-dark-contrast">Kategorije</h4>
                    <ul class="space-y-2 text-xs md:text-sm">
                        <?php // Dynamically generate these from DB if possible ?>
                        <li><a href="<?php echo SITE_URL; ?>/category/slana-jela/" class="footer-text hover:text-primary">Slana jela</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/category/slatka-jela/" class="footer-text hover:text-primary">Slatka jela</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/category/torte/" class="footer-text hover:text-primary">Torte</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/category/prirodni-lijekovi/" class="footer-text hover:text-primary">Prirodni lijekovi</a></li>
                    </ul>
                </div>
                 <?php // --- Footer Pages Links --- ?>
                <div>
                    <h4 class="font-montserrat font-bold mb-3 md:mb-4 relative after:absolute after:w-8 md:after:w-12 after:h-0.5 after:bg-primary after:bottom-0 after:left-0 pb-2 text-sm md:text-base text-dark-contrast">Stranice</h4>
                    <ul class="space-y-2 text-xs md:text-sm">
                        <li><a href="<?php echo SITE_URL; ?>/o-nama" class="footer-text hover:text-primary">O nama</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/kontakt" class="footer-text hover:text-primary">Kontakt</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/politika-privatnosti" class="footer-text hover:text-primary">Politika privatnosti</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/uslovi-koristenja" class="footer-text hover:text-primary">Uslovi korištenja</a></li>
                    </ul>
                </div>
            </div>
            <?php // --- Copyright --- ?>
            <div class="border-t border-border mt-6 md:mt-8 pt-4 md:pt-8 text-center text-xs md:text-sm footer-text">
                <p>&copy; <?php echo date('Y'); ?> <?php echo defined('SITE_NAME') ? escape(SITE_NAME) : 'Lako & Fino'; ?>. Sva prava zadržana.</p>
            </div>
        </div>
    </footer>

    <?php // --- JavaScript Includes and Inline Scripts --- ?>

    <?php // --- Reading Progress Bar Logic --- ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const progressBar = document.getElementById('progress-bar');
            const articleContent = document.querySelector('.article-content'); // Target the main content area

            if (progressBar && articleContent) {
                const updateProgress = () => {
                    try {
                        const contentRect = articleContent.getBoundingClientRect();
                        const windowHeight = window.innerHeight;
                        // Calculate the total scrollable height of the content itself
                        const totalScrollableHeight = articleContent.scrollHeight; // Use scrollHeight for full content height
                        // Calculate the distance over which the progress bar should reach 100%
                        // This is the content height minus the viewport height (or 0 if content is shorter)
                        const scrollableDistance = Math.max(0, totalScrollableHeight - windowHeight);
                        // Current scroll position relative to the document top
                        const currentScroll = window.scrollY || document.documentElement.scrollTop;
                        // Offset of the article content top from the document top
                        const contentTopOffset = articleContent.offsetTop;

                        let progress = 0;
                        // Only calculate progress if we have scrolled past the top of the content
                        // and there is a scrollable distance
                        if (currentScroll > contentTopOffset && scrollableDistance > 0) {
                             // Calculate progress based on how much of the *scrollable* part of the content is passed
                             progress = ((currentScroll - contentTopOffset) / scrollableDistance) * 100;
                             // Clamp progress between 0 and 100
                             progress = Math.min(100, Math.max(0, progress));
                        } else if (currentScroll <= contentTopOffset) {
                            // If scroll is above the content, progress is 0
                            progress = 0;
                        } else {
                            // If content is shorter than viewport or scrollableDistance is 0,
                            // progress is 100 once scrolled past the top of the content
                            progress = (currentScroll > contentTopOffset) ? 100 : 0;
                        }

                        progressBar.style.width = progress + '%';
                    } catch (e) {
                        console.error("Error calculating reading progress:", e);
                        // Optionally hide the progress bar if an error occurs
                        if(progressBar) progressBar.style.display = 'none';
                    }
                };
                // Add listeners for scroll and resize events
                window.addEventListener('scroll', updateProgress, { passive: true }); // Use passive listener for better scroll performance
                window.addEventListener('resize', updateProgress);
                // Run initially to set the progress bar state on load
                updateProgress();
            } else {
                // If progress bar or article content not found, hide the bar
                 const progressBarElement = document.getElementById('progress-bar');
                 if(progressBarElement) progressBarElement.style.display = 'none';
            }
        });
    </script>

    <?php // --- Load More Articles Logic --- ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            const articlesContainer = document.getElementById('articlesContainer'); // Container where articles are listed
            const loadingIndicator = document.getElementById('loadingIndicator');
            const loadMoreContainer = document.getElementById('loadMoreContainer'); // Container holding the button/indicator

            // Ensure all necessary elements exist before adding listener
            if (loadMoreBtn && articlesContainer && loadingIndicator && loadMoreContainer) {
                loadMoreBtn.addEventListener('click', function() {
                    // Get the next page number from the button's data attribute
                    let nextPage = parseInt(this.getAttribute('data-next-page'), 10);
                    if (isNaN(nextPage)) nextPage = 2; // Default to page 2 if attribute is missing/invalid

                    // Show loading indicator and disable the button to prevent multiple clicks
                    loadingIndicator.classList.remove('hidden');
                    loadMoreBtn.disabled = true;
                    loadMoreBtn.classList.add('opacity-50', 'cursor-not-allowed');

                    // Fetch the next page of articles from the server
                    fetch(`<?php echo SITE_URL; ?>/load_articles.php?page=${nextPage}`) // Use SITE_URL for absolute path
                        .then(response => {
                            // Check if the network response was successful
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            // Parse the JSON response
                            return response.json();
                        })
                        .then(data => {
                            // Check for specific error messages from the server-side script
                            if (data.error) {
                                throw new Error(data.error);
                            }

                            // Check if HTML content was received
                            if (data.html && data.html.trim().length > 0) {
                                // Insert the new article HTML *before* the 'Load More' button container
                                // This keeps the button and any messages below the newly loaded articles
                                loadMoreContainer.insertAdjacentHTML('beforebegin', data.html);
                                // Increment the page number for the next request
                                this.setAttribute('data-next-page', nextPage + 1);

                                // --- Re-initialize Alpine.js for newly added content ---
                                // If the loaded content uses Alpine.js directives (like x-data for reader count),
                                // you might need to tell Alpine to initialize them.
                                // This depends on how Alpine is set up, but often simply accessing
                                // a property on the window.Alpine object can trigger initialization
                                // if components weren't automatically discovered.
                                // A more robust way might involve specific Alpine functions if available.
                                if (window.Alpine) {
                                    // Find newly added elements that are Alpine components (have x-data)
                                    // First try using previousElementSibling
                                    let newComponents = [];

                                    if (loadMoreContainer.previousElementSibling) {
                                        // Use previousElementSibling to get the element node
                                        newComponents = loadMoreContainer.previousElementSibling.querySelectorAll('[x-data]');
                                    } else {
                                        // Fallback: Look for all recently added articles with the 'loaded-item' class
                                        // This is more reliable as it doesn't depend on DOM structure
                                        newComponents = document.querySelectorAll('.loaded-item [x-data]');
                                    }

                                    // Process the found components
                                    if (newComponents.length > 0) {
                                        newComponents.forEach(el => {
                                            // For Alpine v3, components should initialize automatically
                                            // If using Alpine v2, you might need to manually initialize
                                            // if (typeof window.Alpine.initializeComponent === 'function') {
                                            //     window.Alpine.initializeComponent(el);
                                            // }
                                        });
                                    }
                                }
                                // --- End Alpine Re-initialization ---

                            } else {
                                // If no HTML is returned, assume no more articles are available
                                data.moreAvailable = false;
                            }

                            // If the server indicates no more articles are available, update the UI
                            if (!data.moreAvailable) {
                                // Replace the button/indicator container with a "No more articles" message
                                loadMoreContainer.innerHTML = '<p class="text-gray-500 italic mt-4">Nema više članaka.</p>';
                            }
                        })
                        .catch(error => {
                            // Log the error and display a user-friendly message
                            console.error('Error loading more articles:', error);
                            loadMoreContainer.innerHTML = '<p class="text-red-600 italic mt-4">Greška pri učitavanju. Pokušajte ponovo kasnije.</p>';
                        })
                        .finally(() => {
                            // This block runs whether the fetch succeeded or failed

                            // Hide the loading indicator
                            loadingIndicator.classList.add('hidden');

                            // Re-enable the button *if* it still exists (it might have been replaced by a message)
                            const currentBtn = document.getElementById('loadMoreBtn');
                            if (currentBtn) {
                                 currentBtn.disabled = false;
                                 currentBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                            }
                        });
                });
            }
        });
    </script>

    <?php // Ensure Alpine.js is loaded (can be here or in <head> with defer) ?>
    <?php // <script defer src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.13.5/cdn.min.js"></script> ?>
    <?php // Note: Alpine.js script tag is included in the header artifact ?>


<?php // FIX START: Check if $article and the key exist before outputting the block ?>
<?php if (isset($article) && is_array($article) && !empty($article['enable_fb_share'])): ?>
<?php // FIX END ?>
<div id="scrollSharePrompt" class="fixed bottom-6 right-6 z-50 transform translate-y-28 opacity-0 transition-all duration-500 shadow-lg rounded-lg bg-white border-2 border-purple-200 w-80 invisible">
    <div class="relative">
        <button id="closeSharePrompt" class="absolute top-2 right-2 text-gray-400 hover:text-gray-500 z-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>

        <div id="viewShareOptions" class="cursor-pointer">
            <div class="bg-gradient-to-r from-purple-500 to-fuchsia-500 p-4 rounded-t-lg">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-white mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                    </svg>
                    <h4 class="font-bold text-lg text-white">Kakva si zapravo prijateljica?</h4>
                </div>
            </div>

            <div class="p-4">
                <p class="text-sm text-gray-600 mb-3">
                    Prijatelji koji dijele vrijedne informacije imaju 3.2x jače veze. <strong>Otkrij kakva si ti prijateljica!</strong>
                </p>

                <div class="flex items-center justify-center bg-purple-50 p-2 rounded-lg">
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                        <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <div class="w-2 h-2 bg-purple-600 rounded-full"></div>
                    </div>
                    <span class="ml-2 text-sm font-medium text-purple-700">Klikni da otkriješ</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 text-purple-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                </div>
            </div>

            <div class="px-4 py-2 bg-gray-50 rounded-b-lg border-t border-purple-100 flex items-center justify-center">
                <span class="text-xs text-purple-700 flex items-center">
                    <span class="w-2 h-2 bg-purple-500 rounded-full mr-1 animate-pulse"></span>
                    87% čitatelja dijeli ovaj članak s prijateljicama
                </span>
            </div>
        </div>
    </div>
</div>

<script>
// Function to handle the scroll event
function handleScroll() {
    const shareSection = document.querySelector('.mt-8 .p-5.bg-gradient-to-r.from-fuchsia-50.to-violet-50'); // Target the sharing section
    if (!shareSection) return; // If share section doesn't exist, exit

    const scrollPrompt = document.getElementById('scrollSharePrompt');
    if (!scrollPrompt) return; // If prompt element doesn't exist, exit

    // Get article ID to use as a key for session storage
    // FIX START: Check if $article exists before accessing its ID
    const articleId = <?php echo (isset($article) && isset($article['id'])) ? json_encode($article['id']) : 'null'; ?>;
    // FIX END
    if (!articleId) return; // Don't run prompt logic if no article ID
    const promptClickedKey = 'sharePromptClicked_' + articleId;

    // Check if user has already clicked the prompt for this article
    if (sessionStorage.getItem(promptClickedKey) === 'true') {
        return; // Don't show the prompt again
    }

    // Get the article content element
    const articleContent = document.querySelector('.article-content');
    if (!articleContent) return;

    // Calculate the position where we want to show the prompt (50% through the article content)
    const articleRect = articleContent.getBoundingClientRect();
    const articleHeight = articleRect.height;
    const articleTop = articleRect.top + window.scrollY;
    const triggerPosition = articleTop + (articleHeight * 0.5); // Changed to 50%

    // If we've scrolled past the trigger point and the prompt is not already shown
    if (window.scrollY > triggerPosition && scrollPrompt.classList.contains('invisible')) {
        // Make it visible
        scrollPrompt.classList.remove('invisible');
        // Animate it in
        setTimeout(() => {
            scrollPrompt.classList.remove('translate-y-28', 'opacity-0');
        }, 100);

        // Add a subtle pulse animation to draw attention
        const pulseElement = scrollPrompt.querySelector('.bg-gradient-to-r');
        if (pulseElement) {
            pulseElement.classList.add('pulse-attention');
            setTimeout(() => {
                pulseElement.classList.remove('pulse-attention');
            }, 2000);
        }

        // Track this impression (optional)
        if (typeof trackSharePromptImpression === 'function') {
            trackSharePromptImpression();
        }
    }
}

// Function to scroll to the sharing section
function scrollToShareSection() {
    const shareSection = document.querySelector('.mt-8 .p-5.bg-gradient-to-r.from-fuchsia-50.to-violet-50');
    if (!shareSection) return;

    // Get article ID to mark as clicked in session storage
    // FIX START: Check if $article exists before accessing its ID
    const articleId = <?php echo (isset($article) && isset($article['id'])) ? json_encode($article['id']) : 'null'; ?>;
    // FIX END
    if (!articleId) return; // Don't run logic if no article ID

    const promptClickedKey = 'sharePromptClicked_' + articleId;

    // Set flag in session storage to not show the prompt again for this article
    sessionStorage.setItem(promptClickedKey, 'true');

    // Hide the prompt
    const scrollPrompt = document.getElementById('scrollSharePrompt');
    if (scrollPrompt) {
        scrollPrompt.classList.add('translate-y-28', 'opacity-0');
        setTimeout(() => {
            scrollPrompt.classList.add('invisible');
        }, 500);
    }

    // Scroll to the share section with smooth behavior
    shareSection.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Add a highlight effect
    shareSection.classList.add('highlight-pulse');
    setTimeout(() => {
        shareSection.classList.remove('highlight-pulse');
    }, 1500);

    // Track this click (optional)
    if (typeof trackSharePromptClick === 'function') {
        trackSharePromptClick();
    }
}

// Close the prompt without scrolling
function closePrompt() {
    const scrollPrompt = document.getElementById('scrollSharePrompt');
    if (!scrollPrompt) return;

    // Get article ID to mark as clicked in session storage
    // FIX START: Check if $article exists before accessing its ID
    const articleId = <?php echo (isset($article) && isset($article['id'])) ? json_encode($article['id']) : 'null'; ?>;
    // FIX END
    if (!articleId) return; // Don't run logic if no article ID

    const promptClickedKey = 'sharePromptClicked_' + articleId;

    // Set flag in session storage to not show the prompt again for this article
    sessionStorage.setItem(promptClickedKey, 'true');

    scrollPrompt.classList.add('translate-y-28', 'opacity-0');
    setTimeout(() => {
        scrollPrompt.classList.add('invisible');
    }, 500);

    // Track this dismissal (optional)
    if (typeof trackSharePromptDismiss === 'function') {
        trackSharePromptDismiss();
    }
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add scroll event listener only if the prompt exists
    const scrollPrompt = document.getElementById('scrollSharePrompt');
    if(scrollPrompt) {
         window.addEventListener('scroll', handleScroll, { passive: true });
    }

    // Add click event to the view share options button
    const viewShareOptionsBtn = document.getElementById('viewShareOptions');
    if (viewShareOptionsBtn) {
        viewShareOptionsBtn.addEventListener('click', scrollToShareSection);
    }

    // Add click event to the close button
    const closeBtn = document.getElementById('closeSharePrompt');
    if (closeBtn) {
        closeBtn.addEventListener('click', closePrompt);
    }

    // Add animation styles only if the prompt exists
    if(scrollPrompt) {
        const style = document.createElement('style');
        style.textContent = `
            .highlight-pulse {
                animation: highlightPulse 1.5s ease-in-out;
            }

            @keyframes highlightPulse {
                0% { box-shadow: 0 0 0 0 rgba(147, 51, 234, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(147, 51, 234, 0); }
                100% { box-shadow: 0 0 0 0 rgba(147, 51, 234, 0); }
            }

            .pulse-attention {
                animation: pulseAttention 1s ease-in-out 3;
            }

            @keyframes pulseAttention {
                0% { transform: scale(1); }
                50% { transform: scale(1.03); }
                100% { transform: scale(1); }
            }

            #scrollSharePrompt:hover .bg-purple-50 {
                background-color: #EDE9FE;
                transition: background-color 0.3s ease;
            }

            #scrollSharePrompt:hover .bg-gradient-to-r.from-purple-500.to-fuchsia-500 {
                background-image: linear-gradient(to right, #8B5CF6, #D946EF);
                transition: background-image 0.3s ease;
            }
        `;
        document.head.appendChild(style);
    }
});
</script>
<?php endif; // End the check for $article and enable_fb_share ?>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a page with comments functionality
    // by looking for key elements
    const commentsList = document.getElementById('comments-list');
    const commentForm = document.getElementById('comment-form');

    // Only initialize comments functionality if the required elements exist
    if (commentsList || commentForm) {
        const loadMoreButton = document.getElementById('load-more-comments');
        const loadingIndicator = document.getElementById('loading-comments-indicator');
        const successMessage = document.getElementById('comment-form-success');
        const errorMessage = document.getElementById('comment-form-error');
        const submitButton = document.getElementById('submit-comment-button');
        const commentCountSpan = document.getElementById('comment-count');
        const noCommentsMessage = document.getElementById('no-comments-message');

    // --- Render Comment HTML Function ---
    function renderCommentHTML(comment) {
        // Basic avatar generation
        let initials = '?';
        if (comment.user_name) {
            const nameParts = comment.user_name.trim().split(' ');
            initials = nameParts[0] ? nameParts[0].substring(0, 1).toUpperCase() : '';
            if (nameParts.length > 1) {
                initials += nameParts[nameParts.length - 1].substring(0, 1).toUpperCase();
            }
            if (!initials) initials = '?';
        }
        const colorIndex = Math.abs(hashCode(comment.user_name || '')) % 5;
        const avatarColors = ['from-blue-500 to-purple-500', 'from-pink-500 to-red-500', 'from-green-400 to-blue-500', 'from-yellow-400 to-orange-500', 'from-indigo-500 to-violet-500'];
        const avatarClass = avatarColors[colorIndex];

        // Sanitize comment text before inserting as HTML (basic escaping)
        const escapedCommentText = comment.comment_text.replace(/</g, "&lt;").replace(/>/g, "&gt;");
        const escapedUserName = comment.user_name.replace(/</g, "&lt;").replace(/>/g, "&gt;");

        let repliesHTML = '';
        if (comment.replies && comment.replies.length > 0) {
            repliesHTML = '<div class="mt-4 pl-6 border-l-2 border-gray-100 space-y-4">';
            comment.replies.forEach(reply => {
                repliesHTML += renderCommentHTML(reply); // Recursive call for replies
            });
            repliesHTML += '</div>';
        }

        return `
            <div class="comment-item p-4 bg-white rounded-lg border border-gray-100 shadow-sm transition-all hover:border-primary/20" id="comment-${comment.id}">
              <div class="flex items-start gap-3">
                <div class="w-10 h-10 rounded-full bg-gradient-to-r ${avatarClass} flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                  ${initials}
                </div>
                <div class="flex-grow">
                  <div class="flex items-center justify-between">
                    <h4 class="font-montserrat font-semibold text-gray-800">${escapedUserName}</h4>
                    <span class="text-xs text-gray-500">${comment.time_ago || timeAgoJS(comment.created_at)}</span>
                  </div>
                  <p class="mt-2 text-gray-700">${escapedCommentText.replace(/\n/g, '<br>')}</p>
                  <div class="flex items-center mt-3 space-x-4">
                    <button data-comment-id="${comment.id}" class="like-button text-xs text-gray-500 hover:text-primary flex items-center transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 like-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" /></svg>
                      Sviđa mi se (<span class="like-count">${comment.likes || 0}</span>)
                    </button>
                    <button data-comment-id="${comment.id}" data-user-name="${escapedUserName}" class="reply-button text-xs text-gray-500 hover:text-primary flex items-center transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" /></svg>
                      Odgovori
                    </button>
                  </div>
                  ${repliesHTML}
                </div>
              </div>
            </div>
        `;
    }

    // --- Hash function for avatar colors ---
    function hashCode(str) {
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }
      return hash;
    }

    // --- Format Comment Date JS Function (Client-side fallback) ---
    function timeAgoJS(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString.replace(' ', 'T')); // Parse the date

        // Format the date as "d. M Y." (e.g., "12. maj 2023.")
        const day = date.getDate();

        // Get month name in Bosnian
        const monthNames = ['jan', 'feb', 'mar', 'apr', 'maj', 'jun', 'jul', 'avg', 'sep', 'okt', 'nov', 'dec'];
        const month = monthNames[date.getMonth()];

        const year = date.getFullYear();

        return `${day}. ${month} ${year}.`;
    }


    // --- Submit Comment ---
    if (commentForm) {
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2 inline-block"></span> Slanje...';
            successMessage.classList.add('hidden');
            errorMessage.classList.add('hidden');
            errorMessage.textContent = '';

            const formData = new FormData(commentForm);

            fetch('<?php echo SITE_URL; ?>/process_comment.php', { // Assumes process_comment.php in root
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.comment) {
                    successMessage.classList.remove('hidden');
                    commentForm.reset(); // Clear the form
                    document.querySelector('input[name="parent_id"]').value = ''; // Clear parent ID

                    // Add the new comment to the top of the list or as a reply
                    const newCommentHTML = renderCommentHTML(data.comment);
                    if (data.comment.parent_id) {
                        // Append reply
                        const parentCommentElement = document.getElementById('comment-' + data.comment.parent_id);
                        if (parentCommentElement) {
                             let repliesContainer = parentCommentElement.querySelector('.pl-6.space-y-4');
                             if (!repliesContainer) {
                                // Create replies container if it doesn't exist
                                repliesContainer = document.createElement('div');
                                repliesContainer.className = 'mt-4 pl-6 border-l-2 border-gray-100 space-y-4';
                                // Find the right place to insert (e.g., after the action buttons)
                                const buttonDiv = parentCommentElement.querySelector('.flex.items-center.mt-3.space-x-4');
                                if(buttonDiv && buttonDiv.parentNode) {
                                    buttonDiv.parentNode.appendChild(repliesContainer);
                                } else {
                                     // Fallback append if structure slightly different
                                     parentCommentElement.querySelector('.flex-grow').appendChild(repliesContainer);
                                }
                             }
                             repliesContainer.insertAdjacentHTML('beforeend', newCommentHTML);
                         } else {
                             commentsList.insertAdjacentHTML('afterbegin', newCommentHTML); // Fallback: add as top-level
                         }
                    } else {
                        // Prepend top-level comment
                        commentsList.insertAdjacentHTML('afterbegin', newCommentHTML);
                         if (noCommentsMessage) noCommentsMessage.style.display = 'none'; // Hide "no comments" message
                    }

                    // Update total comment count
                    if (commentCountSpan && !data.comment.parent_id) { // Only increment for top-level comments
                        commentCountSpan.textContent = parseInt(commentCountSpan.textContent) + 1;
                    }
                } else {
                    errorMessage.textContent = data.error || 'Došlo je do greške.';
                    errorMessage.classList.remove('hidden');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                errorMessage.textContent = 'Došlo je do mrežne greške. Pokušajte ponovo.';
                errorMessage.classList.remove('hidden');
            })
            .finally(() => {
                submitButton.disabled = false;
                 submitButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/></svg> Pošalji komentar';
            });
        });
    }

    // --- Load More Comments ---
    if (loadMoreButton) {
        loadMoreButton.addEventListener('click', function() {
            const articleId = this.dataset.articleId;
            let offset = parseInt(this.dataset.offset);
            const remainingCountSpan = document.getElementById('remaining-count');
            const commentsPerPage = <?php echo isset($commentsPerPage) ? $commentsPerPage : 5; ?>; // Get limit from PHP or default to 5

            loadMoreButton.classList.add('hidden');
            loadingIndicator.classList.remove('hidden');

            fetch(`<?php echo SITE_URL; ?>/load_comments.php?article_id=${articleId}&offset=${offset}&limit=${commentsPerPage}`) // Assumes load_comments.php
            .then(response => response.json())
            .then(data => {
                if (data.success && data.comments) {
                    data.comments.forEach(comment => {
                        commentsList.insertAdjacentHTML('beforeend', renderCommentHTML(comment));
                    });

                    offset += data.comments.length;
                    this.dataset.offset = offset;

                    const remaining = data.total - offset;
                    if (remaining > 0) {
                        loadMoreButton.classList.remove('hidden');
                        if (remainingCountSpan) remainingCountSpan.textContent = remaining;
                    } else {
                        document.getElementById('load-more-container').remove(); // Remove container if no more comments
                    }
                } else {
                    console.error("Error loading comments:", data.error);
                    loadMoreButton.classList.remove('hidden'); // Show button again on error
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                loadMoreButton.classList.remove('hidden'); // Show button again on error
            })
            .finally(() => {
                loadingIndicator.classList.add('hidden');
            });
        });
    }

    // --- Handle Reply and Like Buttons (Delegation) ---
    if (commentsList) {
        commentsList.addEventListener('click', function(e) {
            const target = e.target;

            // Handle Reply Button Click
            if (target.closest('.reply-button')) {
                const button = target.closest('.reply-button');
                const commentId = button.dataset.commentId;
                const userName = button.dataset.userName;
                const commentTextInput = document.getElementById('comment-text');
                const parentIdInput = document.querySelector('input[name="parent_id"]');

                if (commentTextInput && parentIdInput) {
                    parentIdInput.value = commentId; // Set the parent ID
                    commentTextInput.value = `@${userName} `; // Pre-fill textarea
                    commentTextInput.focus();
                    // Optional: Scroll form into view
                    commentForm.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }

            // Handle Like Button Click (Placeholder)
            if (target.closest('.like-button')) {
                const button = target.closest('.like-button');
                const commentId = button.dataset.commentId;
                const countSpan = button.querySelector('.like-count');
                const icon = button.querySelector('.like-icon');

                // TODO: Implement AJAX call to server to update like count
                // For now, just simulate the UI change

                // Simulate incrementing the count
                const currentCount = parseInt(countSpan.textContent);
                countSpan.textContent = currentCount + 1;

                // Simulate changing button state (e.g., color)
                button.classList.toggle('text-primary'); // Toggle primary color
                button.classList.toggle('text-gray-500');
                icon.setAttribute('fill', button.classList.contains('text-primary') ? 'currentColor' : 'none');

                console.log(`Liked comment ID: ${commentId}`);
                // Prevent multiple clicks or implement actual like logic here
                button.disabled = true; // Simple prevention
                 setTimeout(() => button.disabled = false, 1000); // Re-enable after 1s
            }
        });
    }
    } // Close the if (commentsList || commentForm) block

});
</script>

<script>
// Handle comment likes
document.addEventListener('DOMContentLoaded', function() {
    // Only run if there are like buttons on the page
    if (document.querySelectorAll('.like-button').length > 0) {
        document.querySelectorAll('.like-button').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();

                // Get the comment ID from the data attribute
                const commentId = this.getAttribute('data-comment-id');
                const likeCountElement = this.querySelector('.like-count');
                const clickedButton = this;

                // Disable button temporarily to prevent multiple clicks
                clickedButton.disabled = true;

                // Send AJAX request to process the like
                const formData = new FormData();
                formData.append('comment_id', commentId);

                fetch('/process_like.php', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update the like count on success
                        likeCountElement.textContent = data.likes;

                        // Visual feedback - highlight the button temporarily
                        clickedButton.classList.add('text-primary');
                        clickedButton.classList.remove('text-gray-500');

                        // Optional: Add a small animation to the like count
                        likeCountElement.classList.add('animate-pulse');
                        setTimeout(() => {
                            likeCountElement.classList.remove('animate-pulse');
                        }, 1000);
                    } else {
                        console.error('Error liking comment:', data.error);
                    }

                    // Re-enable the button after processing
                    setTimeout(() => {
                        clickedButton.disabled = false;
                    }, 500);
                })
                .catch(error => {
                    console.error('Error processing like:', error);
                    clickedButton.disabled = false;
                });
            });
        });
    }
});
</script>

<script src="<?php echo SITE_URL; ?>/js/cookie-consent.js"></script>

</body>
</html>

