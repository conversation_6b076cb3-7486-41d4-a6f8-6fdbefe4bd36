<?php
/**
 * Process Admin Settings Form Submission
 *
 * Handles saving settings submitted from settings.php.
 * Saves settings to a separate PHP file (settings_data.php) for simplicity.
 */

require_once '../config.php'; // Adjust path as needed
require_once 'includes/auth_check.php'; // Check if admin is logged in
require_once '../includes/functions.php'; // General functions

// Define the path to the settings data file
$settings_file = __DIR__ . '/settings_data.php';

// Check if the form was submitted via POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error_message'] = 'Invalid request method.';
    header('Location: settings.php');
    exit;
}

// --- Retrieve and Sanitize Submitted Settings ---
$submitted_settings = $_POST['settings'] ?? [];
$sanitized_settings = [];

// Loop through expected settings and sanitize/validate
// This is crucial for security and data integrity.
// Add validation for each setting type (boolean, string, number, array, etc.)

// Example Sanitization (expand for all settings):
$sanitized_settings['website_name'] = isset($submitted_settings['website_name']) ? trim(strip_tags($submitted_settings['website_name'])) : 'Lako & Fino';
$sanitized_settings['site_url'] = isset($submitted_settings['site_url']) ? filter_var(trim($submitted_settings['site_url']), FILTER_SANITIZE_URL) : '';
$sanitized_settings['default_language'] = isset($submitted_settings['default_language']) ? trim(strip_tags($submitted_settings['default_language'])) : 'bs';

// Boolean settings (toggles/checkboxes) - check for '1' or '0'
$boolean_settings = [
    'show_article_meta', 'default_sidebar', 'default_similar_posts', 'default_fb_share',
    'default_article_recommendations', 'random_author_assignment', 'show_author_bio',
    'compress_images', 'convert_modern_formats', 'generate_responsive_sizes', 'strip_metadata',
    'increase_contrast', 'cdn_enabled', 'cdn_images', 'cdn_css', 'cdn_js', 'cdn_fonts',
    'enable_loading_trick', 'live_readers_count', 'knowledge_progress_bar', 'time_sensitive_offers',
    'auto_internal_linking', 'lazy_load_ads', 'mobile_ad_visibility', 'header_ad_enabled',
    'sidebar_ad_enabled', 'incontent_ad_enabled', 'between_posts_ad_enabled', 'footer_ad_enabled',
    'popup_ad_enabled', 'minify_html', 'minify_css', 'minify_js', 'concat_css', 'concat_js',
    'gzip_compression', 'browser_caching', 'server_side_caching', 'database_caching',
    'enable_article_cloaking', 'track_cloak_clicks',
    'popup2025_enabled', 'popup2025_use_ip_blocking', 'popup2025_use_bot_detection',
    'popup2025_use_referrer_check', 'popup2025_track_clicks', 'popup2025_check_intersection',
    'popup2025_debug_mode',
    'deepseek_enabled' // New DeepSeek toggle
];
foreach ($boolean_settings as $key) {
    $sanitized_settings[$key] = isset($submitted_settings[$key]) && $submitted_settings[$key] == '1';
}

// Numeric settings
$numeric_settings = [
    'min_readers_count', 'loading_time', 'min_keyword_length', 'max_links_per_article',
    'ad_refresh_rate', 'html_cache_duration', 'css_js_cache_duration', 'image_cache_duration'
];
foreach ($numeric_settings as $key) {
    $sanitized_settings[$key] = isset($submitted_settings[$key]) ? (float)$submitted_settings[$key] : 0; // Use float or int as appropriate
}

// String settings (basic trim/strip_tags) - Add more as needed
$string_settings = [
    'default_article_layout', 'default_category_id', 'default_author_id', 'adsense_client_id',
    'cdn_url', 'cloak_url_prefix', 'popup2025_device_filter', 'popup2025_allowed_countries',
    'popup2025_blocked_countries',
    'deepseek_api_key', // New DeepSeek settings
    'deepseek_prompt_title',
    'deepseek_prompt_excerpt',
    'deepseek_prompt_tags',
    'deepseek_prompt_meta_desc',
    'deepseek_prompt_focus_keyword',
    'deepseek_prompt_internal_links'
];
foreach ($string_settings as $key) {
    // Special handling for API key - don't strip tags, just trim
    if ($key === 'deepseek_api_key') {
        $sanitized_settings[$key] = isset($submitted_settings[$key]) ? trim($submitted_settings[$key]) : '';
    } else {
        $sanitized_settings[$key] = isset($submitted_settings[$key]) ? trim(strip_tags($submitted_settings[$key])) : '';
    }
}

// Array settings (like keywords)
$keywords_raw = isset($submitted_settings['default_keywords']) ? trim($submitted_settings['default_keywords']) : '';
$sanitized_settings['default_keywords'] = !empty($keywords_raw) ? array_map('trim', array_filter(explode(',', $keywords_raw))) : [];

// --- Handle Favicon Upload ---
// Note: This is a basic example. Robust file handling needs more checks (permissions, potential overwrites, etc.)
$favicon_upload = $_FILES['favicon_upload'] ?? null;
if (isset($favicon_upload['error']) && $favicon_upload['error'] === UPLOAD_ERR_OK) {
    $allowed_favicon_types = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/png'];
    $max_favicon_size = 1 * 1024 * 1024; // 1MB limit for favicon

    if ($favicon_upload['size'] > $max_favicon_size) {
        $_SESSION['error_message'] = 'Favicon file is too large (Max 1MB).';
    } elseif (!in_array($favicon_upload['type'], $allowed_favicon_types)) {
        $_SESSION['error_message'] = 'Invalid favicon file type. Allowed: .ico, .png';
    } else {
        $docRoot = rtrim($_SERVER['DOCUMENT_ROOT'], '/');
        $faviconDir = $docRoot . '/uploads/images/site/'; // Define favicon directory relative to doc root
        if (!is_dir($faviconDir)) {
            mkdir($faviconDir, 0755, true);
        }
        $extension = pathinfo($favicon_upload['name'], PATHINFO_EXTENSION);
        $faviconFilename = 'favicon.' . strtolower($extension); // e.g., favicon.png or favicon.ico
        $faviconPath = $faviconDir . $faviconFilename;

        if (move_uploaded_file($favicon_upload['tmp_name'], $faviconPath)) {
            // Save the relative path or URL to the settings
            $sanitized_settings['favicon_path'] = '/uploads/images/site/' . $faviconFilename; // Store relative path
        } else {
            $_SESSION['error_message'] = 'Failed to move uploaded favicon.';
        }
    }
} elseif (isset($submitted_settings['favicon_path'])) {
    // Keep existing path if no new file uploaded
    $sanitized_settings['favicon_path'] = trim(strip_tags($submitted_settings['favicon_path']));
} else {
    $sanitized_settings['favicon_path'] = null;
}


// --- Save Sanitized Settings ---
try {
    // Format the settings array as PHP code
    $settings_code = '<?php' . "\n\n";
    $settings_code .= '// Settings generated on ' . date('Y-m-d H:i:s') . "\n";
    $settings_code .= 'return ' . var_export($sanitized_settings, true) . ";\n";

    // Write the code to the settings file
    // Use file locking for safety if concurrent access is possible
    if (file_put_contents($settings_file, $settings_code, LOCK_EX) === false) {
        throw new Exception('Failed to write settings to file. Check file permissions for ' . $settings_file);
    }

    $_SESSION['success_message'] = 'Settings saved successfully.';

} catch (Exception $e) {
    $_SESSION['error_message'] = 'Error saving settings: ' . $e->getMessage();
    // Log the detailed error
    error_log("Error saving settings: " . $e->getMessage());
}

// Redirect back to the settings page
header('Location: settings.php');
exit;
?>
