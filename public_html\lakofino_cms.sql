-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Apr 22, 2025 at 05:13 PM
-- Server version: 11.4.5-MariaDB-ubu2404
-- PHP Version: 8.3.19

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `lakofino_cms`
--

-- --------------------------------------------------------

--
-- Table structure for table `adsense_units`
--

CREATE TABLE `adsense_units` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `ad_code` text NOT NULL,
  `placement` varchar(50) DEFAULT 'in_content',
  `status` varchar(20) DEFAULT 'active',
  `device_visibility` varchar(20) DEFAULT 'all',
  `position_index` int(11) DEFAULT 1,
  `custom_css` text DEFAULT NULL,
  `fixed_width` int(11) DEFAULT 300,
  `fixed_height` int(11) DEFAULT 250,
  `placement_selector` varchar(255) DEFAULT NULL,
  `frequency` int(11) DEFAULT 1,
  `skip_paragraphs` int(11) DEFAULT 2,
  `max_ads_per_page` int(11) DEFAULT 3,
  `show_on_homepage` tinyint(1) DEFAULT 1,
  `show_on_categories` tinyint(1) DEFAULT 1,
  `show_on_articles` tinyint(1) DEFAULT 1,
  `show_on_tags` tinyint(1) DEFAULT 1,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `custom_targeting` text DEFAULT NULL,
  `lazy_load` tinyint(1) DEFAULT 1,
  `views` int(11) DEFAULT 0,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `adsense_units`
--

INSERT INTO `adsense_units` (`id`, `name`, `ad_code`, `placement`, `status`, `device_visibility`, `position_index`, `custom_css`, `fixed_width`, `fixed_height`, `placement_selector`, `frequency`, `skip_paragraphs`, `max_ads_per_page`, `show_on_homepage`, `show_on_categories`, `show_on_articles`, `show_on_tags`, `start_date`, `end_date`, `custom_targeting`, `lazy_load`, `views`, `created_at`, `updated_at`) VALUES
(1, 'Sidebar Top 300x250', '<script async src=\"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-1234567890\"></script>\r\n    <ins class=\"adsbygoogle\"\r\n         style=\"display:block\"\r\n         data-ad-client=\"ca-pub-1234567890\"\r\n         data-ad-slot=\"1234567890\"\r\n         data-ad-format=\"auto\"\r\n         data-full-width-responsive=\"true\"></ins>\r\n    <script>\r\n         (adsbygoogle = window.adsbygoogle || []).push({});\r\n    </script>', '[\"sidebar_bottom\"]', 'active', 'all', 1, '', 300, 250, '', 1, 2, 2, 1, 1, 1, 1, NULL, NULL, '', 1, 0, '2025-04-14 19:53:34', '2025-04-15 20:25:32');

-- --------------------------------------------------------

--
-- Table structure for table `ad_impressions`
--

CREATE TABLE `ad_impressions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `ad_id` int(10) UNSIGNED NOT NULL COMMENT 'FK to affiliate_ads.id or adsense_units.id',
  `ad_type` varchar(20) NOT NULL COMMENT 'e.g., affiliate, adsense',
  `placement` varchar(100) DEFAULT NULL COMMENT 'Placement identifier',
  `page_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID of the page where impression occurred',
  `page_type` varchar(50) DEFAULT NULL COMMENT 'Type of page (e.g., article, homepage)',
  `user_agent` text DEFAULT NULL,
  `device_type` varchar(20) DEFAULT NULL,
  `referrer` text DEFAULT NULL,
  `ip_hash` varchar(32) DEFAULT NULL,
  `extra_data` text DEFAULT NULL COMMENT 'JSON encoded additional context if needed',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Detailed tracking for ad impressions/views';

--
-- Dumping data for table `ad_impressions`
--

INSERT INTO `ad_impressions` (`id`, `ad_id`, `ad_type`, `placement`, `page_id`, `page_type`, `user_agent`, `device_type`, `referrer`, `ip_hash`, `extra_data`, `created_at`) VALUES
(1, 2, 'affiliate', 'article_bottom_banner', 32, 'article', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'desktop', 'https://balkanskamuzika.com/ekolosko-ciscenje-doma-koristite-ljuske-od-jaja-1/', 'db26633391ffbba1d2892c2d1f5ba2cd', '{\"placement\":\"article_bottom_banner\",\"page_type\":\"article\",\"page_id\":32}', '2025-04-15 21:25:58'),

-- --------------------------------------------------------

--
-- Table structure for table `ad_statistics`
--

CREATE TABLE `ad_statistics` (
  `id` int(11) NOT NULL,
  `ad_id` int(11) NOT NULL,
  `ad_type` varchar(20) NOT NULL,
  `view_date` date NOT NULL,
  `views` int(11) DEFAULT 0,
  `clicks` int(11) DEFAULT 0,
  `article_id` int(11) DEFAULT NULL,
  `device_type` varchar(20) DEFAULT NULL,
  `country_code` varchar(2) DEFAULT NULL,
  `referrer` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `affiliate_ads`
--

CREATE TABLE `affiliate_ads` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `external_url` varchar(500) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `featured_image` varchar(255) DEFAULT NULL,
  `display_position` varchar(50) DEFAULT 'article_list',
  `status` varchar(20) DEFAULT 'active',
  `open_in_new_tab` tinyint(1) DEFAULT 1,
  `show_sponsored_label` tinyint(1) DEFAULT 1,
  `custom_css` text DEFAULT NULL,
  `placement_frequency` int(11) DEFAULT 3,
  `placement_type` varchar(20) DEFAULT 'fixed',
  `author_id` int(11) DEFAULT NULL,
  `placement_priority` int(11) DEFAULT 5,
  `mobile_visibility` tinyint(1) DEFAULT 1,
  `desktop_visibility` tinyint(1) DEFAULT 1,
  `tablet_visibility` tinyint(1) DEFAULT 1,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `tracking_code` varchar(255) DEFAULT NULL,
  `views` int(11) DEFAULT 0,
  `clicks` int(11) DEFAULT 0,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `affiliate_ads`
--

INSERT INTO `affiliate_ads` (`id`, `title`, `description`, `external_url`, `category_id`, `featured_image`, `display_position`, `status`, `open_in_new_tab`, `show_sponsored_label`, `custom_css`, `placement_frequency`, `placement_type`, `author_id`, `placement_priority`, `mobile_visibility`, `desktop_visibility`, `tablet_visibility`, `start_date`, `end_date`, `tracking_code`, `views`, `clicks`, `created_at`, `updated_at`) VALUES
(8, 'Article Beginning', 'Article BeginningArticle BeginningArticle BeginningArticle BeginningArticle BeginningArticle BeginningArticle BeginningArticle BeginningArticle BeginningArticle BeginningArticle BeginningArticle BeginningArticle BeginningArticle BeginningArticle Beginning', 'https://balkanskamuzika.com/', NULL, 'a3ae8ea3df2bacaa', '[\"article_beginning\"]', 'active', 1, 1, '', 3, 'ctr_based', NULL, 5, 1, 1, 1, NULL, NULL, 'utm_source=website&utm_medium=article_beginning&utm_campaign=article-beginning&utm_content={article_id}&utm_term={ad_id}', 156, 30, '2025-04-16 16:23:24', '2025-04-21 20:50:31'),
(9, 'Within Article Content', 'Within Article ContentWithin Article ContentWithin Article ContentWithin Article ContentWithin Article ContentWithin Article ContentWithin Article ContentWithin Article Content', 'https://balkanskamuzika.com/', NULL, 'f418e906325e70bb', '[\"in_content\"]', 'active', 1, 1, '', 3, 'ctr_based', NULL, 5, 1, 1, 1, NULL, NULL, 'utm_source=website&utm_medium=in_content&utm_campaign=within-article-content&utm_content={article_id}&utm_term={ad_id}', 340, 45, '2025-04-16 16:24:08', '2025-04-21 20:49:51'),
(11, 'Article Bottom Banner', 'Article Bottom BannerArticle Bottom BannerArticle Bottom BannerArticle Bottom BannerArticle Bottom BannerArticle Bottom BannerArticle Bottom Banner', 'https://balkanskamuzika.com/', NULL, 'a742ddb65560c605', '[\"article_bottom_banner\"]', 'active', 1, 1, '', 3, 'fixed', NULL, 5, 1, 1, 1, NULL, NULL, 'utm_source=website&utm_medium=article_bottom_banner&utm_campaign=article-bottom-banner&utm_content={article_id}&utm_term=11', 88, 28, '2025-04-16 16:28:32', '2025-04-21 20:49:56'),
(12, 'After Content (after tags)', 'After Content (after tags)After Content (after tags)After Content (after tags)After Content (after tags)', 'https://balkanskamuzika.com/', NULL, '420584a3c8c0b6ae', '[\"recommended\"]', 'active', 1, 1, '', 3, 'fixed', NULL, 5, 1, 1, 1, NULL, NULL, 'utm_source=website&utm_medium=recommended&utm_campaign=after-content-after-tags&utm_content={article_id}&utm_term=12', 82, 28, '2025-04-16 18:57:10', '2025-04-21 20:50:29'),
(13, 'Sidebar - Popular Articles', 'Sidebar - Popular ArticlesSidebar - Popular Articles', 'https://mercislike.art/', NULL, '6c24d8af49ad48ee', '[\"sidebar_popular\"]', 'active', 1, 1, '', 3, 'fixed', NULL, 5, 1, 1, 1, NULL, NULL, 'utm_source=website&utm_medium=sidebar_popular&utm_campaign=sidebar---popular-articles&utm_content={article_id}&utm_term={ad_id}', 0, 32, '2025-04-18 15:11:30', '2025-04-21 20:50:44'),
(14, 'Sidebar - Bottom', 'Sidebar - BottomSidebar - Bottom', 'https://mercislike.art/', NULL, 'e2a267c23401ab83', '[\"sidebar_bottom\"]', 'active', 1, 1, '', 3, 'fixed', NULL, 5, 1, 1, 1, NULL, NULL, 'utm_source=website&utm_medium=sidebar_bottom&utm_campaign=sidebar---bottom&utm_content={article_id}&utm_term={ad_id}', 1, 24, '2025-04-18 15:11:52', '2025-04-21 20:50:18');

-- --------------------------------------------------------

--
-- Table structure for table `articles`
--

CREATE TABLE `articles` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `excerpt` text DEFAULT NULL,
  `featured_image` varchar(255) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `author_id` int(11) UNSIGNED DEFAULT NULL,
  `status` enum('published','draft','scheduled','archived') NOT NULL DEFAULT 'draft',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `published_at` timestamp NULL DEFAULT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `focus_keyword` varchar(255) DEFAULT NULL,
  `views` int(11) NOT NULL DEFAULT 0,
  `reading_time` int(11) DEFAULT 5,
  `enable_sidebar` tinyint(1) NOT NULL DEFAULT 1,
  `show_similar_posts` tinyint(1) NOT NULL DEFAULT 1,
  `enable_fb_share` tinyint(1) NOT NULL DEFAULT 1,
  `include_in_recommendations` tinyint(1) NOT NULL DEFAULT 1,
  `custom_recommendations_code` text DEFAULT NULL,
  `enable_loading_trick` tinyint(1) NOT NULL DEFAULT 1,
  `cloak_article_link` tinyint(1) NOT NULL DEFAULT 0,
  `enable_adsense` tinyint(1) NOT NULL DEFAULT 1,
  `enable_affiliate_ads` tinyint(1) NOT NULL DEFAULT 1,
  `enable_custom_ads` tinyint(1) NOT NULL DEFAULT 0,
  `custom_css` text DEFAULT NULL,
  `youtube_url` varchar(255) DEFAULT NULL,
  `enable_auto_linking` tinyint(1) DEFAULT 1,
  `auto_link_keywords` text DEFAULT NULL,
  `use_loading_trick` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `articles`
--

INSERT INTO `articles` (`id`, `title`, `slug`, `content`, `excerpt`, `featured_image`, `category_id`, `author_id`, `status`, `created_at`, `updated_at`, `published_at`, `meta_title`, `meta_description`, `focus_keyword`, `views`, `reading_time`, `enable_sidebar`, `show_similar_posts`, `enable_fb_share`, `include_in_recommendations`, `custom_recommendations_code`, `enable_loading_trick`, `cloak_article_link`, `enable_adsense`, `enable_affiliate_ads`, `enable_custom_ads`, `custom_css`, `youtube_url`, `enable_auto_linking`, `auto_link_keywords`, `use_loading_trick`) VALUES
(11, 'Samo 3 sastojka i 10 minuta: savršena poslastica. Bez pečenja!', 'samo-3-sastojka-i-10-minuta-savrsena-poslastica-bez-pecenja', 'Samo 3 sastojka i 10 minuta: savršena poslastica. Bez pečenja! 😋\r\n\r\n✅ Sastojci:\r\n\r\nKeks – 300 g\r\nPuter od kikirikija – 300 g\r\nTamna čokolada – 200 g\r\n✅ Priprema:\r\n\r\nUsitnite keks u blenderu dok ne dobijete mrvice. Prebacite ih u duboku posudu.\r\nDodajte puter od kikirikija mrvicama keksa i rukom sjedinite sastojke.\r\nMasa treba biti blago vlažna, mekana i elastična.\r\nOd smjese oblikujte štapiće ili bilo koji drugi oblik po želji.\r\nPripremljene oblike posložite na pleh ili u posudu, ostavljajući malo prostora između kako bi zadržali oblik.\r\nNa pari otopite čokoladu. Obično koristim tamnu, ali možete uzeti bilo koju drugu vrstu. Na primjer, možete prekriti kekse tamnom, a ukrasiti bijelom čokoladom.\r\nSvaki keks umočite u otopljenu čokoladu. Stavite ih na rešetku kako bi se višak čokolade iscijedio. Kada se glazura stvrdne, ukrasite po želji.\r\nPrijatno! ❤️', '', '2d8b1b2e077cdae2', 1, NULL, 'published', '2025-04-02 23:58:54', '2025-04-22 11:47:11', '2025-04-02 23:58:54', '', '', '', 145, 1, 1, 1, 1, 1, '', 1, 0, 1, 1, 0, '', '', 1, NULL, 0),
(34, 'Najbolje metode za veći prinos krastavaca i zaštitu od bolesti', 'najbolje-metode-za-veci-prinos-krastavaca-i-zastitu-od-bolesti', '**Kako uspješno uzgajati krastavce: Prirodni savjeti za obilnu žetvu**  \r\n\r\nUzgoj krastavaca može biti jednostavan i izuzetno plodan ako se koriste pravilne metode njege i prirodni tretmani. Bez upotrebe hemikalija, možete postići zdravije biljke i znatno veći prinos. Evo kako to ostvariti:  \r\n\r\n### **Priprema tla za uzgoj krastavaca**  \r\nKrastavci najbolje uspijevaju na sunčanim i zaštićenim mjestima. Ključ uspjeha je u pravilnoj pripremi gredica:  \r\n- Napravite žlijeb širine 40 cm u sredini gredice.  \r\n- Ispunite ga kompostom i kvalitetnom zemljom.  \r\n- Dobri susjedi: grašak, pasulj, zelena salata i celer.  \r\n- Loši susjedi: paradajz, rotkvica i kupus.  \r\n\r\n### **Kada i kako sijati krastavce?**  \r\n- **Vrijeme sjetve:** Kraj aprila (za plastenike) ili nakon što temperatura prijeđe 15°C (za otvoreno tlo).  \r\n- **Odabir sorti:** Birajte sorte otporne na plijesan.  \r\n- **Priprema sjemena:** Posadite nekoliko sjemenki u male saksije s drenažnim rupama.  \r\n- **Njega mladih biljaka:** Redovno zalijevajte i držite ih na svjetlu.  \r\n\r\n### **Pravilna njega krastavaca**  \r\n- **Zalijevanje:**  \r\n  - Mladice zalijevajte svaki drugi dan.  \r\n  - Odrasle biljke zalijevajte svaka tri dana.  \r\n- **Orezivanje:**  \r\n  - Glavni izdanak ne treba dirati.  \r\n  - Bočne izdanke skratite nakon trećeg lista radi boljeg plodonosnosti.  \r\n\r\n### **Kada beriti krastavce?**  \r\n- Berba počinje oko dvije sedmice nakon cvatnje.  \r\n- Plodovi trebaju biti svijetlozeleni, glatki i čvrsti.  \r\n- Berite ih dok su mali kako biste potakli novi rast.  \r\n- Žute mrlje ukazuju na prezrelo voće.  \r\n\r\n### **Prirodni tretmani za zdravlje biljaka**  \r\n\r\n1. **Protiv plijesni i za jačanje biljaka**  \r\n   - Pomiješajte: 20 g sapuna, 1 l mlijeka, 30 kapi joda i 10 l vode.  \r\n   - Prskajte svake dvije sedmice.  \r\n\r\n2. **Za požutjele vrhove**  \r\n   - U kantu vode namočite 300 g starog hljeba, dodajte ½ kašičice joda i ostavite da fermentira.  \r\n   - Prskajte svake dvije sedmice.  \r\n\r\n3. **Prirodni stimulator rasta**  \r\n   - Pomiješajte: 2 l surutke, 150 g šećera i 10 l vode.  \r\n   - Koristite za zalijevanje.  \r\n\r\n4. **Tretman od ljuske luka**  \r\n   - Prokuhajte 500 g ljuske luka u 5 l vode.  \r\n   - Ostavite preko noći, razrijedite (1:4) i koristite za prskanje.  \r\n\r\n5. **Protiv venuća plodova**  \r\n   - Ako plodovi vene, zalijte ih rastvorom surutke i vode (1:5).  \r\n\r\n### **Zaključak**  \r\nUz pravilnu njegu, redovno zalijevanje i prirodne tretmane, možete postići izuzetan prinos krastavaca bez kemije. Isprobajte ove metode i uživajte u bogatom urodu! 🌱🥒', 'Želite trostruko veći prinos krastavaca bez hemije? Otkrijte tajne uspješnog uzgoja: pripremu idealnog tla, pravilnu sjetvu, efikasno zalijevanje i prirodne tretmane protiv bolesti. Uživajte u obilnoj žetvi!', '5009267bced33523', 1, NULL, 'published', '2025-04-17 15:07:23', '2025-04-22 11:46:17', '2025-04-17 15:07:22', 'Savjeti za veći prinos krastavaca i zaštitu od bolesti', 'Saznajte najbolje metode za veći prinos krastavaca i njihovu zaštitu od bolesti. Prirodni savjeti za uzgoj, zalijevanje, berbu i tretmane bez kemije za obilnu žetvu.', 'uzgoj krastavaca', 95, 5, 1, 1, 1, 1, '', 1, 0, 1, 1, 0, '', '', 1, '', 0),
(55, 'Najbolji recept za univerzalno tijesto: kiflice, pizza, pogača!', 'najbolji-recept-za-univerzalno-tijesto-kiflice-pizza-pogaca', '**Recept za univerzalno tijesto koje možete koristiti za sve!**  \r\n\r\nVeć godinu dana tražim ovaj savršen recept za **LUDO TIJESTO** – magično testo koje može stajati danima u frižideru, a pogodno je za razne namirnice: kiflice, pizzu, pogaču i još mnogo toga!  \r\n\r\nOvo tijesto je stvarno čudo – pripremite ga jednom, pohranite u hladnjak i koristite po potrebi. Možete od njega praviti **slane i slatke specijalitete**: pizze, peciva, kiflice, a čak i domaće pite. Ako želite slanu verziju, dodajte sol, a za slatku – šećer. Jednostavno i genijalno!  \r\n\r\nZa detaljne korake pripreme, pogledajte video uputstvo. Isprobajte i uvjerite se u savršenstvo ovog tijesta! 🍕🥐', 'Otkrili smo magično tijesto koje može stajati danima u frižideru! Idealno za pizzu, kiflice, pogaču – slatko ili slano. Jedan recept, beskonačno mogućnosti! Pogledajte video za detalje.', '70a7693a403d39aa', 1, NULL, 'published', '2025-04-18 19:15:55', '2025-04-22 11:47:07', '2025-04-18 19:15:55', 'Ludo tijesto - recept za pizzu, kiflice i pogaču', 'Otkrijte recept za LUDO TIJESTO koje može stajati danima! Savršeno za pizzu, kiflice i pogaču. Pripremite jednom, koristite više puta - slano ili slatko. Magija u kuhinji!', 'ludo tijesto recept', 62, 1, 1, 1, 1, 1, '', 1, 0, 1, 1, 0, '', '', 1, '', 0),
(57, 'Pileći bataci s povrćem – Brz i ukusan ručak iz pećnice', 'pileci-bataci-s-povrcem-brz-i-ukusan-rucak-iz-pecnice', '**Pileći bataci s povrćem iz pećnice – brz i ukusan obrok za cijelu obitelj**  \r\n\r\nAko želite pripremiti jednostavan, a hranjiv ručak bez puno muke, ovo jelo je savršen izbor. Pileći bataci s povrćem iz pećnice spremaju se brzo, a rezultat je sočno meso i mekano povrće koje će zadovoljiti sve ukuse. Idealno je za zauzete dane kada nemate vremena za komplicirano kuhanje.  \r\n\r\n**Prednosti ovog recepta:**  \r\n- **Prilagodljivost** – osim osnovnih sastojaka (krumpira, mrkve i graška), možete dodati i drugo povrće poput paprika, tikvica, luka ili brokule.  \r\n- **Jednostavnost** – sve se priprema u jednoj posudi, što znači manje pranja i nereda.  \r\n- **Ukus i tekstura** – bataci postaju sočni iznutra, a povrće mekano i aromatično.  \r\n\r\n**Savjet:** Jelo je ukusno i sutradan, pa ga možete spremiti u posudu za ponijeti na posao.  \r\n\r\n---  \r\n\r\n### **Recept za pileće batake s povrćem iz pećnice**  \r\n*Izvor: Foodie__rina (Instagram)*  \r\n\r\n#### **Sastojci (za 4 osobe):**  \r\n- 4 pileća batka  \r\n- 3 srednja krumpira  \r\n- 2 mrkve  \r\n- 200 g graška (svježeg ili smrznutog)  \r\n- 1 kašika Vegete  \r\n- 1 kašičica slatke mljevene paprike  \r\n- prstohvat crnog bibera  \r\n- 2 kašike umaka od rajčice  \r\n- 100 ml vode  \r\n- 1 kašika ulja  \r\n\r\n#### **Priprema:**  \r\n1. **Priprema povrća:**  \r\n   - Ogulite krumpir i mrkve, te ih narežite na kockice.  \r\n   - U veliku zdjelu stavite krumpir, mrkvu i grašak, pa dodajte Vegetu, papriku, biber, umak od rajčice, vodu i ulje. Dobro promiješajte.  \r\n\r\n2. **Priprema bataka:**  \r\n   - Batake posolite i po želji začinite dodatno.  \r\n\r\n3. **Slaganje:**  \r\n   - U vatrostalnu posudu ili tepsiju rasporedite povrće, a povrh njih ravnomjerno postavite batake.  \r\n\r\n4. **Pečenje:**  \r\n   - Zagrijte pećnicu na 200°C.  \r\n   - Pečite 45–50 minuta, a na pola vremena okrenite batake kako bi se ravnomjerno zapekli.  \r\n   - Provjerite da li su bataci pečeni, a povrće mekano. Ako je potrebno, produžite pečenje još 5–10 minuta.  \r\n\r\n5. **Serviranje:**  \r\n   - Poslužite toplo uz svježi salat ili domaći kruh.  \r\n\r\n**Uživajte u jednostavnom i ukusnom obroku!** 🍗🥕', 'Brzi i ukusni ručak za cijelu obitelj! Pileći bataci s povrćem iz pećnice - jednostavno, hranjivo i bez nereda u kuhinji. Sočno meso, hrskava korica i raznobojno povrće za savršen obrok za samo 50 minuta. Idealno za zauzete ljude! #Recept #Ručak', '665a47ddeae80eaa', 1, 19, 'published', '2025-04-19 12:56:41', '2025-04-22 17:06:46', '2025-04-19 12:56:00', 'Pileći bataci s povrćem iz pećnice – brzi i ukusni ručak', 'Pileći bataci s povrćem iz pećnice – brz i ukusan ručak za cijelu obitelj. Sočno meso i mekano povrće spremni za 50 minuta. Savršen obrok za zauzete dane!', 'pileći bataci pećnica', 116, 2, 1, 1, 1, 1, '', 1, 0, 1, 1, 0, '', '', 1, 'kruh,brzi ručak,ručak,sastojci,obrok,recept,recepta', 0),
(64, 'BELO MESO U SOSU OD PEČURAKA: Svi su oduševljeni ovim ukusom!', 'belo-meso-u-sosu-od-pecuraka-svi-su-odusevljeni-ovim-ukusom', 'Belo meso u sosu od pečuraka je jelo koje ćete pripremiti za manje od pola sata, a ukus će vas oduševiti. Sočno, kremasto i aromatično – savršeno za brzi ručak ili večeru.\r\n\r\n<strong>Sastojci:</strong>\r\n<ul>\r\n 	<li>300 g pilećeg filea</li>\r\n 	<li>250 g šampinjona</li>\r\n 	<li>30 g putera</li>\r\n 	<li>150 g pavlake za kuvanje</li>\r\n 	<li>50 ml vode</li>\r\n 	<li>1 čen belog luka</li>\r\n 	<li>Peršun</li>\r\n</ul>\r\nPriprema: Meso isecite na tanke šnicle, propržite na ulju i puteru, a zatim ga izvadite iz tiganja. U isti tiganj dodajte šampinjone, pavlaku i vodu, pa kuvajte dok se sos ne zgusne. Vratite meso i dodajte beli luk i peršun.\r\n\r\nDetaljan recept pogledajte u videu.\r\n\r\n&nbsp;\r\n\r\n<iframe title=\"Hühnerbrust mit Pilzen in einer cremigen Sauce # 105\" src=\"https://www.youtube.com/embed/2E721j2cvv8\" width=\"800\" height=\"450\" frameborder=\"0\" allowfullscreen=\"allowfullscreen\" data-mce-fragment=\"1\"></iframe>\r\n\r\n&nbsp;\r\n\r\nBONUS <strong>VIP</strong> OBJAVA ZA VAS\r\n\r\n<strong>Najbolji recept za kolač Slatki greh: Kremastiji i ukusniji ne može biti</strong>\r\n\r\n<img class=\"alignnone  wp-image-5313\" src=\"https://lakofinorecept.info/wp-content/uploads/2024/09/Screenshot_302-300x165.jpg\" alt=\"\" width=\"549\" height=\"302\" />\r\n\r\nSlatki greh je kremast kolač koji se sprema brzo i lako. Idealan za one koji vole bogate, kremaste poslastice, a ne žele dugo da provode vreme u kuhinji.\r\n\r\n<strong>Sastojci:</strong>\r\n<ul>\r\n 	<li>300 g turskog keksa</li>\r\n 	<li>200 g čokolade</li>\r\n 	<li>6 kašika ulja</li>\r\n 	<li>8 kašika mleka</li>\r\n 	<li>2 kesice pudinga od vanile (koji se ne kuvaju)</li>\r\n 	<li>500 ml mleka</li>\r\n 	<li>200 g kisele pavlake</li>\r\n 	<li>200 ml slatke pavlake</li>\r\n</ul>\r\nPriprema i detalji su dostupni u videu.\r\n\r\n<iframe title=\"Kolač SLATKI GREH - za vrele letnje dane\" src=\"https://www.youtube.com/embed/SRpKG9YSZX8\" width=\"800\" height=\"450\" frameborder=\"0\" allowfullscreen=\"allowfullscreen\" data-mce-fragment=\"1\"></iframe>', '', '33c6325166c3efdc', 6, 35, 'published', '2024-09-29 12:08:28', '2025-04-22 11:46:11', '2024-09-29 12:08:28', 'BELO MESO U SOSU OD PEČURAKA: Svi su oduševljeni ovim ukusom!', '', NULL, 42, 1, 1, 1, 1, 1, NULL, 1, 0, 1, 1, 0, NULL, NULL, 1, NULL, 0),
(65, 'FAŠIRANE ŠNICLE OD MLJEVENOG MESA OVAKO IH JA PRAVIM I MENI SU NAJBOLJE', 'fasirane-snicle-od-mljevenog-mesa-ovako-ih-ja-pravim-i-meni-su-najbolje-3', '<article id=\"post-9775\" class=\"posts-entry fbox post-9775 post type-post status-publish format-standard has-post-thumbnail hentry category-hrana-i-pice\"><header class=\"entry-header\">\r\n<div class=\"entry-meta\">\r\n<div class=\"blog-data-wrapper\">\r\n<div class=\"post-data-positioning\">\r\n<div class=\"post-data-text\">\r\n\r\n<em>Ako niste sigurni što danas pripremiti za ručak, imamo divnu preporuku koja će se svidjeti svima, od najmlađih do onih starijih. Razgovaramo o punjenim šniclama i imamo recept koji nudi neke korisne savjete kako bi vaše šnicle postale omiljeno jelo.</em>\r\n<div class=\"td-g-rec td-g-rec-id-content_inline td_uid_1_66f5bed6905a6_rand td_block_template_1 \"></div>\r\n<div class=\"code-block code-block-2\">\r\n<div data-type=\"_mgwidget\" data-widget-id=\"1670778\">\r\n<div class=\"td-post-content\">\r\n\r\n<strong>OSNOVNE KOMPONENTE POTREBNE ZA KUHANE ODRESKE UKLJUČUJU SLJEDEĆE:</strong>\r\n\r\n500 grama mljevene junetine, 2 srednja krompira, 2 cvekle, 1 glavica luka, 1 jaje, malo soli i malo brašna.\r\n\r\n<strong>KOJA JE METODA ZA SAVRŠENSTVO PEČENJA ODRESA?</strong>\r\n\r\nU mljeveno meso narendajte krompir i repu, zatim dodajte jaje i malo soli, zatim sitno nasjeckani luk i sve sastojke dobro sjedinite.\r\n\r\nOd smjese oblikujte pljeskavice željene veličine, pospite ih brašnom, lagano protisnite i pržite na vrućem ulju. Kad se stvori korica, smanjite vatru, okrenite pljeskavice i pržite desetak minuta na najnižoj razini.\r\n\r\n<img class=\"alignnone size-full wp-image-3629 td-animation-stack-type0-2\" src=\"http://federalne.com/wp-content/uploads/2024/08/2-21.png\" sizes=\"(max-width: 632px) 100vw, 632px\" srcset=\"https://federalne.com/wp-content/uploads/2024/08/2-21.png 632w, https://federalne.com/wp-content/uploads/2024/08/2-21-300x199.png 300w\" alt=\"\" width=\"632\" height=\"420\" />\r\n\r\n<strong>BONUS:</strong>\r\n\r\n<strong>VLAŽAN I NJEŽAN KOLAČ S VIŠNJAMA I ŠIVOM: Ovo jelo je jednostavno za napraviti i savršeno za iznenadne posjetitelje jer se može pripremiti u tren oka.</strong>\r\n\r\nOvaj mekani i sočni kolač od višanja jednostavan je i brz izbor deserta za goste iznenađenja, što ga čini idealnim za ugošćavanje. Naša kuhinja obično sadrži sve bitne sastojke potrebne za izradu ovog divnog i energizirajućeg kolača, što ga čini jednostavnim i ugodnim receptom koji će svi u obitelji cijeniti.\r\n\r\nStvaranje ovog kolača od višanja je jednostavno i jednostavno, potrebno je samo nekoliko osnovnih sastojaka koji su obično dostupni u vašoj kuhinji. Uz višnje, griz i jogurt glavni su elementi koji jamče vlažnu i nježnu teksturu. Dok ovaj recept blista sa svježim trešnjama, u njemu možete uživati ​​tijekom cijele godine koristeći višnje iz konzerve.\r\n\r\n<strong>POTREBNE OSNOVNE KOMPONENTE:</strong>\r\n\r\nZa ovaj slatki recept pripremite sljedeće sastojke: 5 jaja, 120 g šećera, 200 ml jogurta, 100 ml ulja, 200 g brašna, 150 g griza, 1/2 vrećice praška za pecivo i 500 g dobro oprane i očišćene trešnje. Po želji, vrh možete posuti šećerom u prahu.\r\n\r\n<img class=\"alignnone size-full wp-image-3630 td-animation-stack-type0-2\" src=\"http://federalne.com/wp-content/uploads/2024/08/4-5.jpg\" sizes=\"(max-width: 760px) 100vw, 760px\" srcset=\"https://federalne.com/wp-content/uploads/2024/08/4-5.jpg 760w, https://federalne.com/wp-content/uploads/2024/08/4-5-300x200.jpg 300w, https://federalne.com/wp-content/uploads/2024/08/4-5-696x463.jpg 696w, https://federalne.com/wp-content/uploads/2024/08/4-5-631x420.jpg 631w\" alt=\"\" width=\"760\" height=\"506\" />\r\n\r\n<strong>KAKAV JE NAČIN PRAVLJENJA KOLAČA S VIŠNJAMA?</strong>\r\n\r\nBjelanjke umutite dok ne postanu čvrsti snijeg. Žumanjke pjenasto umutite, a zatim u smjesu umiješajte šećer. Brzo pomiješajte ulje i jogurt, a zatim ih dodajte jedno po jedno. Pomiješajte brašno, prašak za pecivo i griz sa ostalim sastojcima.\r\n\r\nZa optimalne rezultate bitno je pažljivo umiješati čvrsti snijeg od bjelanjaka u smjesu, lopaticom ih pomiješati s ostalim sastojcima. Tepsiju dimenzija 21×36 cm namastite i pobrašnite za upotrebu. Dobivenu smjesu ravnomjerno rasporedite po plehu.\r\n\r\nZa ukrašavanje torte promišljeno rasporedite oprane i očišćene višnje. Ako se odlučite za višnje u teglicama, preporučljivo ih je prethodno protisnuti u cjedilu. Da bi kolač bio savršeno pečen, stavite ga u pećnicu zagrijanu na 180 stupnjeva i ostavite da se peče 40 minuta. Ostaviti da se ohladi. Ako želite, kolač prije rezanja možete posuti šećerom u prahu.\r\n<div class=\"td-a-rec td-a-rec-id-content_bottom  td_uid_3_66f5bed690a48_rand td_block_template_1\">\r\n<div data-type=\"_mgwidget\" data-widget-id=\"1669668\"></div>\r\n</div>\r\n</div>\r\n<footer>\r\n<div class=\"td-post-source-tags\"></div>\r\n<div class=\"td-post-sharing-bottom\"></div>\r\n</footer></div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</header></article>', '', 'b4fcf8938e0ed192', 6, 35, 'published', '2024-09-26 20:07:06', '2025-04-21 20:49:14', '2024-09-26 20:07:06', 'FAŠIRANE ŠNICLE OD MLJEVENOG MESA OVAKO IH JA PRAVIM I MENI SU NAJBOLJE', '', NULL, 11, 3, 1, 1, 1, 1, NULL, 1, 0, 1, 1, 0, NULL, NULL, 1, NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `article_tags`
--

CREATE TABLE `article_tags` (
  `article_id` int(11) NOT NULL,
  `tag_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `article_tags`
--

INSERT INTO `article_tags` (`article_id`, `tag_id`) VALUES
(57, 14),
(34, 37),
(34, 38),
(34, 39),
(34, 40),
(34, 41),
(34, 43),
(55, 44),
(55, 45),
(55, 46),
(55, 47),
(55, 48),
(55, 49),
(55, 50),
(57, 51),
(57, 52),
(57, 53),
(57, 54),
(57, 55),
(57, 56);

-- --------------------------------------------------------

--
-- Table structure for table `authors`
--

CREATE TABLE `authors` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `avatar_url` varchar(255) DEFAULT NULL,
  `role` varchar(100) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `authors`
--

INSERT INTO `authors` (`id`, `name`, `email`, `password`, `bio`, `avatar_url`, `role`, `status`, `created_at`) VALUES
(2, 'Amina Hodžić', '<EMAIL>', NULL, 'Stručnjak za digitalni marketing sa posebnim fokusom na kulinarsko sadržaje. Amina posjeduje magisterij iz komunikacija i bogato iskustvo u kreiranju viralnih kampanja za prehrambene brendove. Njena strast za storytellingom pomaže autorima da prenesu svoje jedinstvene kulinarske priče širokoj publici na autentičan i angažirajući način.', 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=50&auto=format&fit=crop&q=60', 'Content Creator', 'active', '2025-04-01 23:37:34'),

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `slug`, `description`, `created_at`) VALUES
(1, 'Recepti', 'recepti', 'Ukusni recepti za svaki dan.', '2025-04-01 23:37:34'),


-- --------------------------------------------------------

--
-- Table structure for table `comments`
--

CREATE TABLE `comments` (
  `id` int(11) NOT NULL,
  `article_id` int(11) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `user_name` varchar(100) NOT NULL,
  `comment_text` text NOT NULL,
  `likes` int(11) DEFAULT 0,
  `is_approved` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `comments`
--

INSERT INTO `comments` (`id`, `article_id`, `parent_id`, `user_name`, `comment_text`, `likes`, `is_approved`, `created_at`) VALUES
(1, 34, NULL, 'Ljubitelj hrane', 'TEST TEST TEST TEST TEST TEST TEST TEST TEST TEST TEST TEST', 1, 1, '2025-04-17 18:34:33'),

-- --------------------------------------------------------

--
-- Table structure for table `detailed_ad_clicks`
--

CREATE TABLE `detailed_ad_clicks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `ad_id` int(10) UNSIGNED NOT NULL COMMENT 'FK to affiliate_ads.id or adsense_units.id',
  `ad_type` varchar(20) NOT NULL COMMENT 'e.g., affiliate, adsense',
  `placement` varchar(100) DEFAULT NULL COMMENT 'Placement identifier (e.g., sidebar_popular, in_content_after_p3)',
  `source_page_type` varchar(50) DEFAULT NULL COMMENT 'Type of page where click occurred (e.g., article, homepage, category)',
  `source_page_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID of the source page (e.g., article_id, category_id)',
  `click_position` varchar(100) DEFAULT NULL COMMENT 'Position identifier (e.g., 1, bottom, recommended_2)',
  `target_url` text DEFAULT NULL COMMENT 'The final destination URL the user was sent to',
  `user_agent` text DEFAULT NULL,
  `device_type` varchar(20) DEFAULT NULL COMMENT 'e.g., desktop, mobile, tablet',
  `referrer` text DEFAULT NULL,
  `ip_hash` varchar(32) DEFAULT NULL COMMENT 'MD5 hash of user IP for anonymized tracking',
  `utm_source` varchar(255) DEFAULT NULL,
  `utm_medium` varchar(255) DEFAULT NULL,
  `utm_campaign` varchar(255) DEFAULT NULL,
  `utm_term` varchar(255) DEFAULT NULL,
  `utm_content` varchar(255) DEFAULT NULL,
  `raw_get_params` text DEFAULT NULL COMMENT 'JSON encoded full GET parameters from tracking link',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Detailed tracking for ad clicks';

--
-- Dumping data for table `detailed_ad_clicks`
--

INSERT INTO `detailed_ad_clicks` (`id`, `ad_id`, `ad_type`, `placement`, `source_page_type`, `source_page_id`, `click_position`, `target_url`, `user_agent`, `device_type`, `referrer`, `ip_hash`, `utm_source`, `utm_medium`, `utm_campaign`, `utm_term`, `utm_content`, `raw_get_params`, `created_at`) VALUES

-- --------------------------------------------------------

--
-- Table structure for table `media`
--

CREATE TABLE `media` (
  `id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_type` varchar(50) NOT NULL,
  `file_size` int(11) NOT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `uploaded_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `uploaded_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `page_views`
--

CREATE TABLE `page_views` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `url` varchar(2048) NOT NULL COMMENT 'The URL viewed',
  `referrer` varchar(2048) DEFAULT NULL COMMENT 'The referring URL',
  `user_agent` text DEFAULT NULL,
  `ip_hash` varchar(32) DEFAULT NULL COMMENT 'MD5 hash of user IP',
  `session_id` varchar(64) DEFAULT NULL COMMENT 'Unique session identifier',
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `country_code` char(2) DEFAULT NULL COMMENT 'ISO 3166-1 alpha-2 country code',
  `page_load_time_ms` int(10) UNSIGNED DEFAULT NULL COMMENT 'Optional: Page load time in milliseconds'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Logs individual page views for website analytics';

--
-- Dumping data for table `page_views`
--

INSERT INTO `page_views` (`id`, `url`, `referrer`, `user_agent`, `ip_hash`, `session_id`, `timestamp`, `country_code`, `page_load_time_ms`) VALUES

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`setting_key`, `setting_value`) VALUES
('ad_refresh_rate', '0'),
('adsense_client_id', ''),
('cdn_css', '1'),
('cdn_fonts', '1'),
('cdn_images', '1'),
('cdn_js', '1'),
('cdn_url', ''),
('cloak_url_prefix', 'go'),
('compress_images', '1'),
('convert_image_format', '1'),
('default_article_recs', '1'),
('default_author', '1'),
('default_category', '1'),
('default_fb_share', '1'),
('default_language', 'bs'),
('default_sidebar', '1'),
('default_similar_posts', '1'),
('enable_adsense_ads', '1'),
('enable_affiliate_ads', '1'),
('enable_article_cloaking', '0'),
('enable_cdn', '0'),
('enable_custom_ads', '0'),
('enable_internal_linking', '1'),
('enable_knowledge_check', '1'),
('enable_loading_trick', '1'),
('enable_time_sensitive_offers', '1'),
('generate_responsive_sizes', '1'),
('increase_contrast', '1'),
('lazy_load_ads', '1'),
('live_readers_count', '1'),
('loading_time', '3'),
('max_links_per_article', '5'),
('min_keyword_length', '4'),
('min_readers_count', '50'),
('mobile_ad_visibility', '1'),
('show_article_meta', '1'),
('site_url', 'http://yourdomain.com'),
('strip_metadata', '1'),
('track_cloak_clicks', '1'),
('website_name', 'Lako & Fino');

-- --------------------------------------------------------

--
-- Table structure for table `smrsaj_cache`
--

CREATE TABLE `smrsaj_cache` (
  `request_hash` varchar(64) NOT NULL,
  `response_text` text NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tags`
--

CREATE TABLE `tags` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `tags`
--

INSERT INTO `tags` (`id`, `name`, `slug`) VALUES
(5, 'Slana jela', 'slana-jela'),
(6, 'Slatka jela', 'slatka-jela'),
(7, 'Prirodni lijekovi', 'prirodni-lijekovi-tag'),
(8, 'brza-poslastica', 'brza-poslastica'),
(9, 'bez-pecenja', 'bez-pecenja'),
(10, 'puter-od-kikirikija', 'puter-od-kikirikija'),
(11, 'cokolada', 'cokolada'),
(12, 'keks', 'keks'),
(13, 'domaci-desert', 'domaci-desert'),
(14, 'jednostavni-recepti', 'jednostavni-recepti'),
(24, 'poslastice-bez-pecenja', 'poslastice-bez-pecenja'),
(25, 'brzi-recepti', 'brzi-recepti'),
(26, 'domaci-deserti', 'domaci-deserti'),
(27, 'keks-torta', 'keks-torta'),
(28, 'cokoladni-deserti', 'cokoladni-deserti'),
(29, 'poslastice', 'poslastice'),
(30, 'ljuske od jaja', 'ljuske-od-jaja'),
(31, 'prirodno čišćenje', 'prirodno-ciscenje'),
(32, 'ekološki proizvodi', 'ekoloski-proizvodi'),
(33, 'đubrivo za biljke', 'dubrivo-za-biljke'),
(34, 'soda bikarbona', 'soda-bikarbona'),
(35, 'održivi dom', 'odrzivi-dom'),
(36, 'recikliranje u kući', 'recikliranje-u-kuci'),
(37, 'uzgoj krastavaca', 'uzgoj-krastavaca'),
(38, 'prirodni tretmani', 'prirodni-tretmani'),
(39, 'njega biljaka', 'njega-biljaka'),
(40, 'zalijevanje', 'zalijevanje'),
(41, 'priprema tla', 'priprema-tla'),
(43, 'prirodna đubriva', 'prirodna-dubriva'),
(44, 'ludo tijesto', 'ludo-tijesto'),
(45, 'recept', 'recept'),
(46, 'tijesto za sve', 'tijesto-za-sve'),
(47, 'kiflice', 'kiflice'),
(48, 'pizza', 'pizza'),
(49, 'pogača', 'pogaca'),
(50, 'slano i slatko', 'slano-i-slatko'),
(51, 'pileći bataci', 'pileci-bataci'),
(52, 'pečenje u pećnici', 'pecenje-u-pecnici'),
(53, 'brzi ručak', 'brzi-rucak'),
(54, 'zdravi recepti', 'zdravi-recepti'),
(55, 'povrće', 'povrce'),
(56, 'domaća jela', 'domaca-jela'),
(57, 'x329329239', 'x329329239'),
(58, 'čokoladica', 'cokoladica');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `adsense_units`
--
ALTER TABLE `adsense_units`
  ADD PRIMARY KEY (`id`),
  ADD KEY `placement` (`placement`),
  ADD KEY `status` (`status`),
  ADD KEY `device_visibility` (`device_visibility`);

--
-- Indexes for table `ad_impressions`
--
ALTER TABLE `ad_impressions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_ad_id_type` (`ad_id`,`ad_type`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_placement` (`placement`);

--
-- Indexes for table `ad_statistics`
--
ALTER TABLE `ad_statistics`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `ad_date_device` (`ad_id`,`ad_type`,`view_date`,`device_type`),
  ADD KEY `view_date` (`view_date`),
  ADD KEY `article_id` (`article_id`);

--
-- Indexes for table `affiliate_ads`
--
ALTER TABLE `affiliate_ads`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `status` (`status`),
  ADD KEY `display_position` (`display_position`),
  ADD KEY `author_id` (`author_id`);

--
-- Indexes for table `articles`
--
ALTER TABLE `articles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `author_id` (`author_id`),
  ADD KEY `idx_slug` (`slug`),
  ADD KEY `idx_status_published` (`status`,`published_at`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_author_id` (`author_id`),
  ADD KEY `idx_views` (`views`),
  ADD KEY `idx_published_created` (`published_at`,`created_at`);

--
-- Indexes for table `article_tags`
--
ALTER TABLE `article_tags`
  ADD PRIMARY KEY (`article_id`,`tag_id`),
  ADD KEY `tag_id` (`tag_id`),
  ADD KEY `idx_article_tag` (`article_id`,`tag_id`),
  ADD KEY `idx_tag_id` (`tag_id`);

--
-- Indexes for table `authors`
--
ALTER TABLE `authors`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `idx_slug` (`slug`);

--
-- Indexes for table `comments`
--
ALTER TABLE `comments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `article_id` (`article_id`),
  ADD KEY `parent_id` (`parent_id`);

--
-- Indexes for table `detailed_ad_clicks`
--
ALTER TABLE `detailed_ad_clicks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_ad_id_type` (`ad_id`,`ad_type`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_placement` (`placement`),
  ADD KEY `idx_utm_source` (`utm_source`),
  ADD KEY `idx_utm_campaign` (`utm_campaign`);

--
-- Indexes for table `media`
--
ALTER TABLE `media`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `page_views`
--
ALTER TABLE `page_views`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_timestamp` (`timestamp`),
  ADD KEY `idx_url` (`url`(255)),
  ADD KEY `idx_session_id` (`session_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`setting_key`);

--
-- Indexes for table `smrsaj_cache`
--
ALTER TABLE `smrsaj_cache`
  ADD PRIMARY KEY (`request_hash`);

--
-- Indexes for table `tags`
--
ALTER TABLE `tags`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `idx_slug` (`slug`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `adsense_units`
--
ALTER TABLE `adsense_units`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `ad_impressions`
--
ALTER TABLE `ad_impressions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1110;

--
-- AUTO_INCREMENT for table `ad_statistics`
--
ALTER TABLE `ad_statistics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `affiliate_ads`
--
ALTER TABLE `affiliate_ads`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `articles`
--
ALTER TABLE `articles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=66;

--
-- AUTO_INCREMENT for table `authors`
--
ALTER TABLE `authors`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=36;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `comments`
--
ALTER TABLE `comments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=91;

--
-- AUTO_INCREMENT for table `detailed_ad_clicks`
--
ALTER TABLE `detailed_ad_clicks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=251;

--
-- AUTO_INCREMENT for table `media`
--
ALTER TABLE `media`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `page_views`
--
ALTER TABLE `page_views`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1560;

--
-- AUTO_INCREMENT for table `tags`
--
ALTER TABLE `tags`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=59;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `articles`
--
ALTER TABLE `articles`
  ADD CONSTRAINT `articles_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `articles_ibfk_2` FOREIGN KEY (`author_id`) REFERENCES `authors` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `article_tags`
--
ALTER TABLE `article_tags`
  ADD CONSTRAINT `article_tags_ibfk_1` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `article_tags_ibfk_2` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `comments`
--
ALTER TABLE `comments`
  ADD CONSTRAINT `comments_ibfk_1` FOREIGN KEY (`article_id`) REFERENCES `articles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `comments_ibfk_2` FOREIGN KEY (`parent_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
