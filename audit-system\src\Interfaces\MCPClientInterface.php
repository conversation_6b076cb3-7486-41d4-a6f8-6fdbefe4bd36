<?php

namespace AuditSystem\Interfaces;

/**
 * Interface for MCP (Model Context Protocol) client implementations
 */
interface MCPClientInterface
{
    /**
     * Connect to the MCP server
     *
     * @return bool True if connection successful
     * @throws MCPConnectionException If connection fails
     */
    public function connect(): bool;

    /**
     * Disconnect from the MCP server
     *
     * @return void
     */
    public function disconnect(): void;

    /**
     * Check if client is connected to server
     *
     * @return bool True if connected
     */
    public function isConnected(): bool;

    /**
     * Send a request to the MCP server
     *
     * @param string $method The method to call
     * @param array $params Parameters for the method
     * @return array Response from server
     * @throws MCPConnectionException If request fails
     */
    public function request(string $method, array $params = []): array;

    /**
     * Get best practices for a specific technology/area
     *
     * @param string $technology Technology or area (e.g., 'php-security', 'performance')
     * @param string $context Additional context for the request
     * @return array Best practices data
     */
    public function getBestPractices(string $technology, string $context = ''): array;

    /**
     * Validate code against best practices
     *
     * @param string $code Code to validate
     * @param string $language Programming language
     * @param string $context Additional context
     * @return array Validation results
     */
    public function validateCode(string $code, string $language, string $context = ''): array;

    /**
     * Get server capabilities
     *
     * @return array Server capabilities
     */
    public function getCapabilities(): array;
}