<?php

namespace AuditSystem\Models;

/**
 * Tracks audit statistics and metrics
 */
class AuditStatistics
{
    public int $totalFindings;
    public int $criticalFindings;
    public int $highFindings;
    public int $mediumFindings;
    public int $lowFindings;
    public int $priorityAreaFindings;
    public int $nonPriorityFindings;
    public int $securityFindings;
    public int $performanceFindings;
    public int $qualityFindings;
    public int $architectureFindings;
    public int $filesWithIssues;
    public int $optimalFiles;

    public function __construct()
    {
        $this->totalFindings = 0;
        $this->criticalFindings = 0;
        $this->highFindings = 0;
        $this->mediumFindings = 0;
        $this->lowFindings = 0;
        $this->priorityAreaFindings = 0;
        $this->nonPriorityFindings = 0;
        $this->securityFindings = 0;
        $this->performanceFindings = 0;
        $this->qualityFindings = 0;
        $this->architectureFindings = 0;
        $this->filesWithIssues = 0;
        $this->optimalFiles = 0;
    }

    /**
     * Update statistics from an array of findings
     *
     * @param Finding[] $findings
     * @return void
     */
    public function updateFromFindings(array $findings): void
    {
        if (!empty($findings)) {
            $this->filesWithIssues++;
        } else {
            $this->optimalFiles++;
        }

        foreach ($findings as $finding) {
            $this->totalFindings++;

            // Count by severity
            switch ($finding->severity) {
                case Finding::SEVERITY_CRITICAL:
                    $this->criticalFindings++;
                    break;
                case Finding::SEVERITY_HIGH:
                    $this->highFindings++;
                    break;
                case Finding::SEVERITY_MEDIUM:
                    $this->mediumFindings++;
                    break;
                case Finding::SEVERITY_LOW:
                    $this->lowFindings++;
                    break;
            }

            // Count by priority
            if ($finding->priority === Finding::PRIORITY_AREA) {
                $this->priorityAreaFindings++;
            } else {
                $this->nonPriorityFindings++;
            }

            // Count by type
            switch ($finding->type) {
                case Finding::TYPE_SECURITY:
                    $this->securityFindings++;
                    break;
                case Finding::TYPE_PERFORMANCE:
                    $this->performanceFindings++;
                    break;
                case Finding::TYPE_QUALITY:
                    $this->qualityFindings++;
                    break;
                case Finding::TYPE_ARCHITECTURE:
                    $this->architectureFindings++;
                    break;
            }
        }
    }

    /**
     * Convert to array for serialization
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'totalFindings' => $this->totalFindings,
            'criticalFindings' => $this->criticalFindings,
            'highFindings' => $this->highFindings,
            'mediumFindings' => $this->mediumFindings,
            'lowFindings' => $this->lowFindings,
            'priorityAreaFindings' => $this->priorityAreaFindings,
            'nonPriorityFindings' => $this->nonPriorityFindings,
            'securityFindings' => $this->securityFindings,
            'performanceFindings' => $this->performanceFindings,
            'qualityFindings' => $this->qualityFindings,
            'architectureFindings' => $this->architectureFindings,
            'filesWithIssues' => $this->filesWithIssues,
            'optimalFiles' => $this->optimalFiles
        ];
    }
}