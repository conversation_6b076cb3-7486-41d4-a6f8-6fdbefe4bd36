<?php

namespace AuditSystem\Tests\Integration;

use PHPUnit\Framework\TestCase;

/**
 * Integration tests for the CLI audit tool
 */
class CLIIntegrationTest extends TestCase
{
    private string $cliScript;
    private string $testConfigFile;
    private string $testTargetDir;
    private string $testProgressFile;
    private string $testReportDir;

    protected function setUp(): void
    {
        $this->cliScript = __DIR__ . '/../../bin/audit.php';
        $this->testConfigFile = __DIR__ . '/../fixtures/test-audit-config.json';
        $this->testTargetDir = __DIR__ . '/../fixtures/test-cms';
        $this->testProgressFile = __DIR__ . '/../fixtures/test-progress.json';
        $this->testReportDir = __DIR__ . '/../fixtures/test-reports';
        
        // Create test directories
        $this->createTestDirectories();
        $this->createTestConfiguration();
        $this->createTestCMSFiles();
    }

    protected function tearDown(): void
    {
        // Clean up test files
        $this->cleanupTestFiles();
    }

    /**
     * Test basic CLI help command
     */
    public function testHelpCommand(): void
    {
        $output = $this->runCLI(['help']);
        
        $this->assertStringContainsString('Lako & Fino CMS Audit Tool', $output);
        $this->assertStringContainsString('USAGE:', $output);
        $this->assertStringContainsString('COMMANDS:', $output);
        $this->assertStringContainsString('OPTIONS:', $output);
        $this->assertStringContainsString('EXAMPLES:', $output);
    }

    /**
     * Test version command
     */
    public function testVersionCommand(): void
    {
        $output = $this->runCLI(['version']);
        
        $this->assertStringContainsString('Lako & Fino CMS Audit Tool v', $output);
        $this->assertStringContainsString('PHP Version:', $output);
        $this->assertStringContainsString('Platform:', $output);
    }

    /**
     * Test configuration validation
     */
    public function testValidateCommand(): void
    {
        $output = $this->runCLI([
            'validate',
            '--config=' . $this->testConfigFile
        ]);
        
        $this->assertStringContainsString('Validating Audit Configuration', $output);
        $this->assertStringContainsString('Configuration validation passed!', $output);
    }

    /**
     * Test configuration display
     */
    public function testConfigCommand(): void
    {
        $output = $this->runCLI([
            'config',
            '--config=' . $this->testConfigFile
        ]);
        
        $this->assertStringContainsString('Current Audit Configuration', $output);
        $this->assertStringContainsString('Target Directory', $output);
        $this->assertStringContainsString('Progress File', $output);
        $this->assertStringContainsString('Report Directory', $output);
    }

    /**
     * Test audit execution with custom configuration
     */
    public function testAuditExecution(): void
    {
        $output = $this->runCLI([
            'audit',
            '--config=' . $this->testConfigFile,
            '--target=' . $this->testTargetDir,
            '--quiet'
        ]);
        
        // Should complete without errors
        $this->assertStringNotContainsString('ERROR:', $output);
        
        // Check that reports were generated
        $this->assertTrue(is_dir($this->testReportDir));
        
        $reportFiles = glob($this->testReportDir . '/audit_report_*.json');
        $this->assertGreaterThan(0, count($reportFiles), 'JSON report should be generated');
        
        $reportFiles = glob($this->testReportDir . '/audit_report_*.md');
        $this->assertGreaterThan(0, count($reportFiles), 'Markdown report should be generated');
        
        $reportFiles = glob($this->testReportDir . '/audit_report_*.html');
        $this->assertGreaterThan(0, count($reportFiles), 'HTML report should be generated');
    }

    /**
     * Test audit resume functionality
     */
    public function testAuditResume(): void
    {
        // First, start an audit and let it create progress
        $this->runCLI([
            'audit',
            '--config=' . $this->testConfigFile,
            '--target=' . $this->testTargetDir,
            '--quiet'
        ]);
        
        // Now test resume (should work even if previous audit completed)
        $output = $this->runCLI([
            'resume',
            '--config=' . $this->testConfigFile,
            '--quiet'
        ]);
        
        // Should handle resume gracefully
        $this->assertStringNotContainsString('ERROR:', $output);
    }

    /**
     * Test status command
     */
    public function testStatusCommand(): void
    {
        // Create some progress data first
        $this->createTestProgressData();
        
        $output = $this->runCLI([
            'status',
            '--config=' . $this->testConfigFile
        ]);
        
        $this->assertStringContainsString('Audit Status', $output);
        $this->assertStringContainsString('Phase:', $output);
        $this->assertStringContainsString('Progress:', $output);
        $this->assertStringContainsString('Files:', $output);
    }

    /**
     * Test verbose output
     */
    public function testVerboseOutput(): void
    {
        $output = $this->runCLI([
            'audit',
            '--config=' . $this->testConfigFile,
            '--target=' . $this->testTargetDir,
            '--verbose'
        ]);
        
        $this->assertStringContainsString('Audit Configuration:', $output);
        $this->assertStringContainsString('Found', $output);
        $this->assertStringContainsString('files to analyze', $output);
    }

    /**
     * Test argument parsing with various option formats
     */
    public function testArgumentParsing(): void
    {
        // Test long options with equals
        $output = $this->runCLI([
            'config',
            '--target=' . $this->testTargetDir,
            '--timeout=60'
        ]);
        
        $this->assertStringContainsString($this->testTargetDir, $output);
        
        // Test short flags
        $output = $this->runCLI(['-h']);
        $this->assertStringContainsString('USAGE:', $output);
    }

    /**
     * Test error handling for invalid commands
     */
    public function testInvalidCommand(): void
    {
        $output = $this->runCLI(['invalid-command']);
        
        $this->assertStringContainsString('Unknown command: invalid-command', $output);
        $this->assertStringContainsString('Use \'php audit.php help\'', $output);
    }

    /**
     * Test error handling for invalid target directory
     */
    public function testInvalidTargetDirectory(): void
    {
        $output = $this->runCLI([
            'audit',
            '--target=/nonexistent/directory',
            '--quiet'
        ]);
        
        $this->assertStringContainsString('ERROR:', $output);
        $this->assertStringContainsString('does not exist', $output);
    }

    /**
     * Test conflicting flags handling
     */
    public function testConflictingFlags(): void
    {
        $output = $this->runCLI([
            'audit',
            '--verbose',
            '--quiet'
        ]);
        
        $this->assertStringContainsString('Cannot use --verbose and --quiet together', $output);
    }

    /**
     * Test custom configuration file loading
     */
    public function testCustomConfigFile(): void
    {
        $customConfig = [
            'audit' => [
                'target_directory' => $this->testTargetDir,
                'progress_file' => $this->testProgressFile,
                'report_directory' => $this->testReportDir,
                'timeout' => 120
            ]
        ];
        
        $customConfigFile = $this->testReportDir . '/custom-config.json';
        file_put_contents($customConfigFile, json_encode($customConfig, JSON_PRETTY_PRINT));
        
        $output = $this->runCLI([
            'config',
            '--config=' . $customConfigFile
        ]);
        
        $this->assertStringContainsString('120 seconds', $output);
    }

    /**
     * Run CLI command and return output
     */
    private function runCLI(array $args): string
    {
        $command = 'php ' . escapeshellarg($this->cliScript);
        
        foreach ($args as $arg) {
            $command .= ' ' . escapeshellarg($arg);
        }
        
        $output = shell_exec($command . ' 2>&1');
        
        return $output ?? '';
    }

    /**
     * Create test directories
     */
    private function createTestDirectories(): void
    {
        $dirs = [
            dirname($this->testConfigFile),
            $this->testTargetDir,
            dirname($this->testProgressFile),
            $this->testReportDir
        ];
        
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    /**
     * Create test configuration file
     */
    private function createTestConfiguration(): void
    {
        $config = [
            'audit' => [
                'target_directory' => $this->testTargetDir,
                'progress_file' => $this->testProgressFile,
                'report_directory' => $this->testReportDir,
                'timeout' => 60,
                'max_file_size' => 1048576
            ],
            'priority_areas' => [
                'patterns' => [
                    'test_*',
                    'config.php',
                    'security*'
                ]
            ],
            'analyzers' => [
                'security' => [
                    'enabled' => true
                ]
            ]
        ];
        
        file_put_contents($this->testConfigFile, json_encode($config, JSON_PRETTY_PRINT));
    }

    /**
     * Create test CMS files for analysis
     */
    private function createTestCMSFiles(): void
    {
        // Create test PHP file with potential security issues
        $phpContent = '<?php
// Test PHP file with security issues
$user_input = $_GET["input"];
$query = "SELECT * FROM users WHERE name = \'" . $user_input . "\'";
echo $user_input;
?>';
        
        file_put_contents($this->testTargetDir . '/test_security.php', $phpContent);
        
        // Create test config file
        $configContent = '<?php
define("DB_HOST", "localhost");
define("DB_USER", "root");
define("DB_PASS", "password123");
?>';
        
        file_put_contents($this->testTargetDir . '/config.php', $configContent);
        
        // Create test CSS file
        $cssContent = '/* Test CSS file */
.test-class {
    color: red;
    font-size: 12px;
}';
        
        file_put_contents($this->testTargetDir . '/style.css', $cssContent);
        
        // Create test JavaScript file
        $jsContent = '// Test JavaScript file
function testFunction() {
    var userInput = document.getElementById("input").value;
    document.getElementById("output").innerHTML = userInput;
}';
        
        file_put_contents($this->testTargetDir . '/script.js', $jsContent);
    }

    /**
     * Create test progress data
     */
    private function createTestProgressData(): void
    {
        $progressData = [
            'phase' => 'analyzing',
            'completed_files' => [
                $this->testTargetDir . '/config.php',
                $this->testTargetDir . '/style.css'
            ],
            'pending_files' => [
                $this->testTargetDir . '/test_security.php',
                $this->testTargetDir . '/script.js'
            ],
            'findings' => [],
            'last_update' => date('Y-m-d H:i:s'),
            'statistics' => [
                'total_findings' => 3,
                'critical_findings' => 1,
                'priority_area_findings' => 2
            ]
        ];
        
        file_put_contents($this->testProgressFile, json_encode($progressData, JSON_PRETTY_PRINT));
    }

    /**
     * Clean up test files
     */
    private function cleanupTestFiles(): void
    {
        $this->removeDirectory(dirname($this->testConfigFile));
    }

    /**
     * Recursively remove directory
     */
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
}