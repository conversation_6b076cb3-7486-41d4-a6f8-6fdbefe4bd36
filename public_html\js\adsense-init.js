/**
 * AdSense Initialization Script
 * 
 * This script initializes Google AdSense and provides utility functions
 * for working with AdSense ads.
 */

// Initialize AdSense when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AdSense auto ads if enabled
    if (typeof adsbygoogle !== 'undefined') {
        try {
            // Push the adsbygoogle command
            (adsbygoogle = window.adsbygoogle || []).push({});
            console.log('AdSense initialization successful');
        } catch (e) {
            console.error('AdSense initialization failed:', e);
        }
    } else {
        console.warn('AdSense not available');
    }
});

/**
 * Refreshes AdSense ads after a specified interval
 * @param {number} refreshInterval - Refresh interval in milliseconds
 */
function refreshAdsenseAds(refreshInterval) {
    if (!refreshInterval || refreshInterval <= 0) return;

    // Find all ad units that support refreshing
    document.querySelectorAll('.ad-unit.ad-refreshable').forEach(adUnit => {
        // Set up a timeout to refresh the ad
        setTimeout(() => {
            // Implementation for AdSense ads
            if (adUnit.classList.contains('ad-type-adsense') && window.adsbygoogle) {
                // Remove old ad
                while (adUnit.firstChild) {
                    adUnit.removeChild(adUnit.firstChild);
                }

                // Create new ad slot
                const adSlot = document.createElement('ins');
                adSlot.className = 'adsbygoogle';
                adSlot.setAttribute('data-ad-client', adUnit.dataset.adClient || '');
                adSlot.setAttribute('data-ad-slot', adUnit.dataset.adSlot || '');
                adSlot.setAttribute('data-ad-format', adUnit.dataset.adFormat || 'auto');
                adUnit.appendChild(adSlot);

                // Push to adsbygoogle for rendering
                try {
                    (adsbygoogle = window.adsbygoogle || []).push({});
                    console.log(`Refreshed AdSense unit: ${adUnit.id}`);
                } catch (e) {
                    console.error(`Error refreshing AdSense unit ${adUnit.id}:`, e);
                }
            }
        }, refreshInterval);
    });
}

/**
 * Checks if an ad blocker is active
 * @param {Function} callback - Callback function to run with result (true if ad blocker detected)
 */
function detectAdBlocker(callback) {
    // Create a bait element
    const bait = document.createElement('div');
    bait.className = 'ad-unit ad-bait';
    bait.style.position = 'absolute';
    bait.style.left = '-999px';
    bait.style.top = '-999px';
    bait.style.width = '1px';
    bait.style.height = '1px';
    document.body.appendChild(bait);

    // Check if the bait element is hidden by an ad blocker
    setTimeout(() => {
        const isBlocked = bait.offsetHeight === 0 || 
                         bait.offsetWidth === 0 || 
                         bait.clientHeight === 0 || 
                         bait.clientWidth === 0 ||
                         window.getComputedStyle(bait).display === 'none' ||
                         window.getComputedStyle(bait).visibility === 'hidden';
        
        // Remove the bait element
        document.body.removeChild(bait);
        
        // Call the callback with the result
        if (typeof callback === 'function') {
            callback(isBlocked);
        }
    }, 100);
}
