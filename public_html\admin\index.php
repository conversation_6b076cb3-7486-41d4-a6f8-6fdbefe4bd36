<?php
require_once '../config.php'; // Adjust path as needed
require_once 'includes/auth_check.php'; // Check if admin is logged in
require_once '../includes/functions.php'; // General functions

// --- Page Setup ---
$admin_page_title = 'Dashboard'; // Set the page title for the header
$error_message = null;

// --- Fetch Ad Stats ---
$adStats = [
    'total_affiliate_ads' => 0,
    'active_affiliate_ads' => 0,
    'total_adsense_units' => 0,
    'active_adsense_units' => 0,
    'total_affiliate_clicks' => 0,
    'total_affiliate_views' => 0,
];
try {
    $adStats['total_affiliate_ads'] = (int)$pdo->query("SELECT COUNT(*) FROM affiliate_ads")->fetchColumn();
    $adStats['active_affiliate_ads'] = (int)$pdo->query("SELECT COUNT(*) FROM affiliate_ads WHERE status = 'active'")->fetchColumn();
    $adStats['total_adsense_units'] = (int)$pdo->query("SELECT COUNT(*) FROM adsense_units")->fetchColumn();
    $adStats['active_adsense_units'] = (int)$pdo->query("SELECT COUNT(*) FROM adsense_units WHERE status = 'active'")->fetchColumn();

    $affiliateTotals = $pdo->query("SELECT SUM(views) as total_views, SUM(clicks) as total_clicks FROM affiliate_ads WHERE status = 'active'")->fetch(PDO::FETCH_ASSOC);
    $adStats['total_affiliate_views'] = (int)($affiliateTotals['total_views'] ?? 0);
    $adStats['total_affiliate_clicks'] = (int)($affiliateTotals['total_clicks'] ?? 0);

} catch (PDOException $e) {
    $error_message = "Database error fetching ad stats: " . $e->getMessage();
    error_log("Admin Dashboard Ad Stats DB Error: " . $e->getMessage());
}

// --- Fetch Website Stats (Using Placeholders) ---
// Define placeholder functions locally or include from functions.php
if (!function_exists('getWebsitePerformanceStats')) {
    function getWebsitePerformanceStats(PDO $pdo, string $period = '30d'): array {
        return [ 'total_page_views' => 124897, 'avg_time_on_site_seconds' => 227, 'bounce_rate_percent' => 32.6, 'internal_link_ctr_percent' => 8.7 ];
    }
}
if (!function_exists('getLoadingTrickStats')) {
    function getLoadingTrickStats(PDO $pdo, string $period = '30d'): array {
         return [ 'loading_trick_views' => 43576, 'loading_trick_conversions' => 34251 ];
    }
}
$websitePerformance = getWebsitePerformanceStats($pdo); // Using placeholder function
$loadingTrickStats = getLoadingTrickStats($pdo); // Using placeholder function

// --- Fetch Recent Articles ---
$recent_articles = [];
try {
    $stmt_recent = $pdo->query("SELECT id, title, status, created_at, updated_at FROM articles ORDER BY updated_at DESC LIMIT 5");
    $recent_articles = $stmt_recent->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    if(!$error_message) $error_message = "Database error fetching recent articles: " . $e->getMessage();
}

// Include the admin header
include 'includes/header.php';
?>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.13.5/cdn.min.js"></script>

<header class="h-16 bg-white border-b border-border flex items-center justify-between px-6 flex-shrink-0 sticky top-0 z-30">
    <h1 class="text-xl font-montserrat font-bold text-dark"><?php echo escape($admin_page_title); ?></h1>
    <div class="text-sm text-gray-600">
        Logged in as: <?php echo isset($_SESSION['admin_email']) ? escape($_SESSION['admin_email']) : 'Admin'; ?>
    </div>
</header>

<div class="p-6 space-y-6" x-data="{ activeTab: 'website' }">

    <?php // --- Display Messages --- ?>
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded-lg text-sm">
            <?php echo escape($_SESSION['success_message']); unset($_SESSION['success_message']); ?>
        </div>
    <?php endif; ?>
    <?php if (isset($error_message)): ?>
        <div class="bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm">
            <?php echo escape($error_message); ?>
        </div>
    <?php endif; ?>

    <?php // --- Tab Navigation --- ?>
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-6" aria-label="Tabs">
            <button
                @click="activeTab = 'website'"
                :class="{ 'border-primary text-primary': activeTab === 'website', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'website' }"
                class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm transition-colors duration-200 focus:outline-none"
                aria-current="page">
                Website Overview
            </button>
            <button
                @click="activeTab = 'ads'"
                :class="{ 'border-primary text-primary': activeTab === 'ads', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'ads' }"
                class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm transition-colors duration-200 focus:outline-none">
                Ads Overview
            </button>
        </nav>
    </div>

    <?php // --- Website Analytics Tab Content --- ?>
    <div x-show="activeTab === 'website'" x-transition>
        <h2 class="text-lg font-semibold text-gray-700 mb-4">Website Performance (Placeholder Data)</h2>
        <p class="text-xs text-gray-500 mb-4 italic">Note: This data is illustrative. Integrate with Google Analytics or implement server-side tracking for real metrics.</p>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="metric-card">
                <h3 class="metric-label mb-1">Page Views (30d)</h3>
                <p class="metric-value"><?php echo number_format($websitePerformance['total_page_views']); ?></p>
            </div>
            <div class="metric-card">
                <h3 class="metric-label mb-1">Avg. Time on Site</h3>
                <p class="metric-value"><?php echo floor($websitePerformance['avg_time_on_site_seconds'] / 60); ?>m <?php echo $websitePerformance['avg_time_on_site_seconds'] % 60; ?>s</p>
            </div>
            <div class="metric-card">
                <h3 class="metric-label mb-1">Bounce Rate</h3>
                <p class="metric-value"><?php echo $websitePerformance['bounce_rate_percent']; ?>%</p>
            </div>
            <div class="metric-card">
                 <h3 class="metric-label mb-1">Loading Trick Views</h3>
                 <p class="metric-value"><?php echo number_format($loadingTrickStats['loading_trick_views']); ?></p>
            </div>
        </div>
        <div class="mt-4 text-right">
             <a href="analytics.php" class="text-sm text-primary hover:underline font-medium">View Full Website Analytics &rarr;</a>
        </div>
    </div>

    <?php // --- Ads Analytics Tab Content --- ?>
    <div x-show="activeTab === 'ads'" x-transition style="display: none;">
         <h2 class="text-lg font-semibold text-gray-700 mb-4">Ads Performance</h2>
         <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="metric-card">
                <h3 class="metric-label mb-1">Affiliate Views</h3>
                <p class="metric-value"><?php echo number_format($adStats['total_affiliate_views']); ?></p>
            </div>
            <div class="metric-card">
                <h3 class="metric-label mb-1">Affiliate Clicks</h3>
                <p class="metric-value"><?php echo number_format($adStats['total_affiliate_clicks']); ?></p>
            </div>
             <div class="metric-card">
                <h3 class="metric-label mb-1">AdSense Views (DB)</h3>
                <p class="metric-value"><?php echo number_format($adStats['total_adsense_views']); ?></p>
            </div>
            <div class="metric-card">
                <h3 class="metric-label mb-1">Affiliate CTR</h3>
                <p class="metric-value">
                    <?php echo ($adStats['total_affiliate_views'] > 0) ? round(($adStats['total_affiliate_clicks'] / $adStats['total_affiliate_views']) * 100, 2) : 0; ?>%
                </p>
            </div>
        </div>
         <div class="mt-4 text-right">
             <a href="ad_analytics.php" class="text-sm text-primary hover:underline font-medium">View Full Ads Analytics &rarr;</a>
        </div>
    </div>

    <?php // --- Quick Actions (Common) --- ?>
    <div class="card">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-100 pb-2">Quick Actions</h3>
        <div class="flex flex-wrap gap-4">
            <a href="article_form.php" class="btn">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                New Article
            </a>
            <a href="articles.php" class="btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                View All Articles
            </a>
             <a href="ad_form.php" class="btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>
                New Affiliate Ad
            </a>
             <a href="adsense_form.php" class="btn-secondary">
               <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>
                New AdSense Unit
            </a>
        </div>
    </div>

    <?php // --- Recent Articles (Common) --- ?>
    <div class="card">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-100 pb-2">Recently Updated Articles</h3>
        <?php if (empty($recent_articles)): ?>
            <p class="text-gray-600">No recently updated articles found.</p>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full table">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Status</th>
                            <th>Last Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-border">
                        <?php foreach ($recent_articles as $article): ?>
                            <tr>
                                <td class="font-medium text-gray-900 max-w-xs truncate">
                                    <a href="article_form.php?id=<?php echo $article['id']; ?>" title="<?php echo escape($article['title']); ?>" class="hover:text-primary">
                                        <?php echo escape($article['title']); ?>
                                    </a>
                                </td>
                                <td>
                                    <span class="badge
                                        <?php echo ($article['status'] === 'published') ? 'badge-success' : ''; ?>
                                        <?php echo ($article['status'] === 'draft') ? 'badge-warning' : ''; ?>
                                        <?php echo ($article['status'] === 'scheduled') ? 'badge-info' : ''; ?>
                                        <?php echo ($article['status'] === 'archived') ? 'badge-gray' : ''; ?>
                                    ">
                                        <?php echo ucfirst(escape($article['status'])); ?>
                                    </span>
                                </td>
                                <td><?php echo formatDate($article['updated_at'], 'M j, Y H:i'); ?></td>
                                <td>
                                    <a href="article_form.php?id=<?php echo $article['id']; ?>" class="text-primary hover:text-primary/80 text-sm font-medium">Edit</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

</div>

<?php
// Include the admin footer
include 'includes/footer.php';
?>

