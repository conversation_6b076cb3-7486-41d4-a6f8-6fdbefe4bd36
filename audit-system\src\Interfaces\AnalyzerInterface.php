<?php

namespace AuditSystem\Interfaces;

use AuditSystem\Models\Finding;

/**
 * Interface for all code analyzers in the audit system
 */
interface AnalyzerInterface
{
    /**
     * Analyze a file and return findings
     *
     * @param string $filePath Path to the file to analyze
     * @param string $content File content to analyze
     * @return Finding[] Array of findings discovered in the file
     */
    public function analyze(string $filePath, string $content): array;

    /**
     * Get the types of files this analyzer can handle
     *
     * @return string[] Array of file extensions or patterns this analyzer supports
     */
    public function getSupportedFileTypes(): array;

    /**
     * Get the analyzer name for identification
     *
     * @return string Name of the analyzer
     */
    public function getName(): string;
}