<?php

namespace AuditSystem\Services;

use AuditSystem\Interfaces\LoggerInterface;
use DateTime;

/**
 * File-based logger for audit system operations
 */
class AuditLogger implements LoggerInterface
{
    private string $logFile;
    private string $errorLogFile;
    private string $performanceLogFile;
    private array $logLevels;

    public function __construct(string $logDirectory = 'audit-system/logs')
    {
        // Ensure log directory exists
        if (!is_dir($logDirectory)) {
            mkdir($logDirectory, 0755, true);
        }

        $this->logFile = $logDirectory . '/audit.log';
        $this->errorLogFile = $logDirectory . '/error.log';
        $this->performanceLogFile = $logDirectory . '/performance.log';
        
        $this->logLevels = [
            'emergency' => 0,
            'alert' => 1,
            'critical' => 2,
            'error' => 3,
            'warning' => 4,
            'notice' => 5,
            'info' => 6,
            'debug' => 7
        ];
    }

    /**
     * Log an emergency message
     */
    public function emergency(string $message, array $context = []): void
    {
        $this->log('emergency', $message, $context);
    }

    /**
     * Log an alert message
     */
    public function alert(string $message, array $context = []): void
    {
        $this->log('alert', $message, $context);
    }

    /**
     * Log a critical message
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log('critical', $message, $context);
    }

    /**
     * Log an error message
     */
    public function error(string $message, array $context = []): void
    {
        $this->log('error', $message, $context);
    }

    /**
     * Log a warning message
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log('warning', $message, $context);
    }

    /**
     * Log a notice message
     */
    public function notice(string $message, array $context = []): void
    {
        $this->log('notice', $message, $context);
    }

    /**
     * Log an info message
     */
    public function info(string $message, array $context = []): void
    {
        $this->log('info', $message, $context);
    }

    /**
     * Log a debug message
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log('debug', $message, $context);
    }

    /**
     * Log a message with arbitrary level
     */
    public function log(string $level, string $message, array $context = []): void
    {
        $timestamp = (new DateTime())->format('Y-m-d H:i:s');
        $contextStr = empty($context) ? '' : ' ' . json_encode($context);
        $logEntry = "[{$timestamp}] {$level}: {$message}{$contextStr}" . PHP_EOL;

        // Write to main log file
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);

        // Write errors to separate error log
        if (in_array($level, ['emergency', 'alert', 'critical', 'error'])) {
            file_put_contents($this->errorLogFile, $logEntry, FILE_APPEND | LOCK_EX);
        }
    }

    /**
     * Log performance metrics
     *
     * @param string $operation Operation name
     * @param float $duration Duration in seconds
     * @param array $metrics Additional metrics
     * @return void
     */
    public function logPerformance(string $operation, float $duration, array $metrics = []): void
    {
        $timestamp = (new DateTime())->format('Y-m-d H:i:s');
        $metricsStr = empty($metrics) ? '' : ' ' . json_encode($metrics);
        $logEntry = "[{$timestamp}] PERFORMANCE: {$operation} took {$duration}s{$metricsStr}" . PHP_EOL;
        
        file_put_contents($this->performanceLogFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Log audit progress
     *
     * @param string $phase Current audit phase
     * @param int $processedFiles Number of processed files
     * @param int $totalFiles Total number of files
     * @param int $findings Number of findings so far
     * @return void
     */
    public function logProgress(string $phase, int $processedFiles, int $totalFiles, int $findings): void
    {
        $percentage = $totalFiles > 0 ? round(($processedFiles / $totalFiles) * 100, 2) : 0;
        $this->info("Audit progress: {$phase} - {$processedFiles}/{$totalFiles} files ({$percentage}%) - {$findings} findings");
    }

    /**
     * Log file analysis start
     *
     * @param string $filePath Path of file being analyzed
     * @param array $analyzers List of analyzers being used
     * @return void
     */
    public function logFileAnalysisStart(string $filePath, array $analyzers): void
    {
        $analyzerNames = array_map(fn($analyzer) => get_class($analyzer), $analyzers);
        $this->debug("Starting analysis of {$filePath} with analyzers: " . implode(', ', $analyzerNames));
    }

    /**
     * Log file analysis completion
     *
     * @param string $filePath Path of analyzed file
     * @param int $findingsCount Number of findings discovered
     * @param float $duration Analysis duration in seconds
     * @return void
     */
    public function logFileAnalysisComplete(string $filePath, int $findingsCount, float $duration): void
    {
        $this->debug("Completed analysis of {$filePath}: {$findingsCount} findings in {$duration}s");
    }

    /**
     * Log analyzer failure
     *
     * @param string $analyzerClass Analyzer class name
     * @param string $filePath File being analyzed
     * @param \Exception $exception Exception that occurred
     * @return void
     */
    public function logAnalyzerFailure(string $analyzerClass, string $filePath, \Exception $exception): void
    {
        $this->error("Analyzer {$analyzerClass} failed for {$filePath}: {$exception->getMessage()}", [
            'exception_class' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ]);
    }

    /**
     * Clear log files
     *
     * @return void
     */
    public function clearLogs(): void
    {
        if (file_exists($this->logFile)) {
            file_put_contents($this->logFile, '');
        }
        if (file_exists($this->errorLogFile)) {
            file_put_contents($this->errorLogFile, '');
        }
        if (file_exists($this->performanceLogFile)) {
            file_put_contents($this->performanceLogFile, '');
        }
    }

    /**
     * Get recent log entries
     *
     * @param int $lines Number of lines to retrieve
     * @param string $level Log level filter (optional)
     * @return array
     */
    public function getRecentLogs(int $lines = 100, string $level = null): array
    {
        if (!file_exists($this->logFile)) {
            return [];
        }

        $content = file_get_contents($this->logFile);
        $logLines = array_filter(explode(PHP_EOL, $content));
        $logLines = array_slice($logLines, -$lines);

        if ($level !== null) {
            $logLines = array_filter($logLines, function($line) use ($level) {
                return strpos($line, " {$level}:") !== false;
            });
        }

        return array_values($logLines);
    }

    /**
     * Log exception with full context
     *
     * @param \Exception $exception Exception to log
     * @param string $level Log level (default: error)
     * @param array $additionalContext Additional context information
     * @return void
     */
    public function logException(\Exception $exception, string $level = 'error', array $additionalContext = []): void
    {
        $context = array_merge([
            'exception_class' => get_class($exception),
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ], $additionalContext);

        if ($exception instanceof \AuditSystem\Exceptions\AuditException) {
            $context = array_merge($context, $exception->getContext());
        }

        $this->log($level, 'Exception occurred: ' . $exception->getMessage(), $context);
    }

    /**
     * Log error recovery attempt
     *
     * @param string $errorType Type of error being recovered from
     * @param string $recoveryStrategy Strategy being used for recovery
     * @param string $context Context where recovery is happening
     * @param bool $success Whether recovery was successful
     * @return void
     */
    public function logErrorRecovery(string $errorType, string $recoveryStrategy, string $context, bool $success): void
    {
        $level = $success ? 'info' : 'warning';
        $status = $success ? 'successful' : 'failed';
        
        $this->log($level, "Error recovery {$status}: {$errorType} in {$context} using {$recoveryStrategy}");
    }

    /**
     * Log system resource usage
     *
     * @param string $operation Operation being monitored
     * @param array $resources Resource usage metrics
     * @return void
     */
    public function logResourceUsage(string $operation, array $resources): void
    {
        $timestamp = (new DateTime())->format('Y-m-d H:i:s');
        $resourcesStr = json_encode($resources);
        $logEntry = "[{$timestamp}] RESOURCES: {$operation} - {$resourcesStr}" . PHP_EOL;
        
        file_put_contents($this->performanceLogFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Log audit checkpoint
     *
     * @param string $phase Current audit phase
     * @param array $checkpointData Checkpoint data
     * @return void
     */
    public function logCheckpoint(string $phase, array $checkpointData): void
    {
        $this->info("Audit checkpoint: {$phase}", $checkpointData);
    }

    /**
     * Log graceful degradation event
     *
     * @param string $service Service that is degrading
     * @param string $reason Reason for degradation
     * @param string $fallbackAction Fallback action being taken
     * @return void
     */
    public function logGracefulDegradation(string $service, string $reason, string $fallbackAction): void
    {
        $this->warning("Graceful degradation: {$service} - {$reason}. Fallback: {$fallbackAction}");
    }

    /**
     * Rotate log files if they exceed size limit
     *
     * @param int $maxSizeBytes Maximum size in bytes before rotation
     * @return void
     */
    public function rotateLogs(int $maxSizeBytes = 10485760): void // 10MB default
    {
        $logFiles = [$this->logFile, $this->errorLogFile, $this->performanceLogFile];
        
        foreach ($logFiles as $logFile) {
            if (file_exists($logFile) && filesize($logFile) > $maxSizeBytes) {
                $rotatedFile = $logFile . '.' . date('Y-m-d-H-i-s');
                rename($logFile, $rotatedFile);
                
                // Create new empty log file
                touch($logFile);
                chmod($logFile, 0644);
                
                $this->info("Log file rotated: {$logFile} -> {$rotatedFile}");
            }
        }
    }

    /**
     * Get log file statistics
     *
     * @return array Statistics about log files
     */
    public function getLogStatistics(): array
    {
        $stats = [];
        $logFiles = [
            'audit' => $this->logFile,
            'error' => $this->errorLogFile,
            'performance' => $this->performanceLogFile
        ];
        
        foreach ($logFiles as $type => $file) {
            if (file_exists($file)) {
                $stats[$type] = [
                    'size' => filesize($file),
                    'lines' => count(file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES)),
                    'last_modified' => filemtime($file)
                ];
            } else {
                $stats[$type] = [
                    'size' => 0,
                    'lines' => 0,
                    'last_modified' => null
                ];
            }
        }
        
        return $stats;
    }

    /**
     * Archive old log files
     *
     * @param int $daysToKeep Number of days to keep logs
     * @return void
     */
    public function archiveOldLogs(int $daysToKeep = 30): void
    {
        $logDir = dirname($this->logFile);
        $cutoffTime = time() - ($daysToKeep * 24 * 60 * 60);
        
        $files = glob($logDir . '/*.log.*');
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
                $this->info("Archived old log file: " . basename($file));
            }
        }
    }
}