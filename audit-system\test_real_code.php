<?php

require_once 'vendor/autoload.php';

use AuditSystem\Analyzers\PHPAnalyzer;
use AuditSystem\Models\Finding;

echo "Testing PHP Analyzer with real CMS code...\n\n";

$analyzer = new PHPAnalyzer();

// Read the config.php file
$configPath = '../public_html/config.php';
if (file_exists($configPath)) {
    $content = file_get_contents($configPath);
    
    echo "Analyzing config.php...\n";
    $findings = $analyzer->analyze('config.php', $content);
    
    echo "Found " . count($findings) . " issues:\n\n";
    
    foreach ($findings as $finding) {
        echo "Line {$finding->line}: [{$finding->type}] [{$finding->severity}] {$finding->description}\n";
        echo "  Recommendation: {$finding->recommendation}\n";
        if ($finding->codeSnippet) {
            echo "  Code: " . substr($finding->codeSnippet, 0, 80) . "...\n";
        }
        echo "\n";
    }
} else {
    echo "Config file not found at $configPath\n";
}

// Test with a sample function file
echo "Testing with sample function code...\n";
$sampleCode = '<?php
function get_articles($category = null, $limit = 10) {
    global $pdo;
    
    if ($category) {
        $sql = "SELECT * FROM articles WHERE category = :category ORDER BY created_at DESC LIMIT :limit";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(":category", $category);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
    } else {
        $sql = "SELECT * FROM articles ORDER BY created_at DESC LIMIT :limit";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
    }
    
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function display_article($article) {
    echo "<article>";
    echo "<h2>" . htmlspecialchars($article["title"]) . "</h2>";
    echo "<p>" . htmlspecialchars($article["content"]) . "</p>";
    echo "</article>";
}
';

$findings = $analyzer->analyze('functions.php', $sampleCode);
echo "Found " . count($findings) . " issues in sample code:\n\n";

foreach ($findings as $finding) {
    echo "Line {$finding->line}: [{$finding->type}] [{$finding->severity}] {$finding->description}\n";
    echo "  Recommendation: {$finding->recommendation}\n\n";
}

echo "Analysis completed!\n";