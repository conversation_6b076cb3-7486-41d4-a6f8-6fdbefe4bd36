-- Add auto_link_keywords column if it doesn't exist
ALTER TABLE `articles` 
ADD COLUMN IF NOT EXISTS `auto_link_keywords` TEXT NULL COMMENT 'Comma-separated list of keywords for auto-linking' AFTER `youtube_url`;

-- Add enable_auto_linking column if it doesn't exist
ALTER TABLE `articles` 
ADD COLUMN IF NOT EXISTS `enable_auto_linking` TINYINT(1) NOT NULL DEFAULT 1 COMMENT 'Whether auto-linking is enabled for this article' AFTER `auto_link_keywords`;
