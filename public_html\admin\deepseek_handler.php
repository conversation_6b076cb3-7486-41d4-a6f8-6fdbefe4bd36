<?php
/**
 * DeepSeek API Handler for Article Form Actions
 *
 * Changelog:
 * - v1.2 (2025-04-06): Added session_write_close() before calling DeepSeek API to prevent session locking.
 * - v1.1 (2025-04-06):
 * - Added strict error suppression for AJAX response.
 * - Corrected usage of config constants (DEEPSEEK_API_KEY etc.) instead of $config array.
 * - Added handling for 'rewrite_title' and 'rewrite_content' POST parameters.
 * - Modified callDeepSeekAPI calls based on which rewrite options are selected.
 * - Updated JSON response structure for 'rewrite_combined' action to return separate title/content.
 * - Enhanced error handling and logging for API calls.
 * - Included basic validation for input text.
 * - Added check for cURL extension.
 * - v1.0 (Initial): Handles various AI actions based on 'action' parameter.
 */

// Suppress errors for AJAX response integrity
error_reporting(0);
ini_set('display_errors', 0);

// Increase PHP execution time limit for long-running API requests
set_time_limit(300); // 5 minutes

// Start session to manage user authentication
session_start();

// Include configuration file and authentication check
// Ensure paths are correct relative to this handler file
require_once '../config.php';
// The auth_check script will verify the session started above
require_once 'includes/auth_check.php';
// Include functions file which contains callDeepSeekAPI
require_once '../includes/functions.php'; // <-- Make sure this function exists and is needed

// Set the correct content type *before* any potential output
header('Content-Type: application/json');

// --- Pre-flight Checks ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
    exit;
}

// Check if essential DeepSeek constants are defined and API key is set
if (!defined('DEEPSEEK_API_KEY') || DEEPSEEK_API_KEY === 'YOUR_DEEPSEEK_API_KEY' || empty(DEEPSEEK_API_KEY)) {
    echo json_encode(['success' => false, 'error' => 'DeepSeek API key is not configured in config.php.']);
    exit;
}
if (!defined('DEEPSEEK_API_URL') || !defined('DEEPSEEK_MODEL')) {
    echo json_encode(['success' => false, 'error' => 'DeepSeek API URL or Model is not configured in config.php.']);
    exit;
}
// Check if the function we intend to call exists (assuming it's in functions.php)
if (!function_exists('callDeepSeekAPI')) {
    echo json_encode(['success' => false, 'error' => 'Required function callDeepSeekAPI() not found. Check includes/functions.php.']);
    exit;
}
if (!defined('PROMPT_REWRITE_TITLE') || !defined('PROMPT_REWRITE_CONTENT') || !defined('PROMPT_GENERATE_EXCERPT') || !defined('PROMPT_SUGGEST_TAGS') || !defined('PROMPT_SUGGEST_META_TITLE') || !defined('PROMPT_SUGGEST_META_DESC') || !defined('PROMPT_SUGGEST_FOCUS_KEYWORD')) {
     echo json_encode(['success' => false, 'error' => 'One or more DeepSeek prompt constants are missing in config.php.']);
     exit;
}


// --- Get and Validate Input Data ---
$action = $_POST['action'] ?? null;
$inputText = trim($_POST['input_text'] ?? ''); // Main text (usually content)
$titleText = trim($_POST['title_text'] ?? ''); // Context text (usually title)

// Specific flags for combined rewrite action
$rewriteTitleFlag = isset($_POST['rewrite_title']) && $_POST['rewrite_title'] === '1';
$rewriteContentFlag = isset($_POST['rewrite_content']) && $_POST['rewrite_content'] === '1';

// Basic input validation
if (empty($action)) {
    echo json_encode(['success' => false, 'error' => 'No action specified.']);
    exit;
}

// Special handling for suggest_internal_links action
if ($action === 'suggest_internal_links') {
    // Log the received values for debugging
    error_log("suggest_internal_links received - inputText: " . substr($inputText, 0, 50) . "..., titleText: " . $titleText);

    if (empty($inputText) || empty($titleText)) {
        $errorMsg = 'Both content and title are required for internal link suggestions.';
        error_log("Internal links error: " . $errorMsg . " (inputText empty: " . (empty($inputText) ? 'yes' : 'no') . ", titleText empty: " . (empty($titleText) ? 'yes' : 'no') . ")");
        echo json_encode(['success' => false, 'error' => $errorMsg]);
        exit;
    }
} else {
    // For all other actions, input text is required
    if (empty($inputText)) {
        echo json_encode(['success' => false, 'error' => 'Input text cannot be empty.']);
        exit;
    }

    // If title is required as context for certain actions, validate it
    if (in_array($action, ['suggest_meta_title', 'suggest_meta_desc', 'suggest_focus_keyword', 'rewrite_combined']) && empty($titleText)) {
        echo json_encode(['success' => false, 'error' => 'Title text is required for this action.']);
        exit;
    }
}


// --- Determine Prompt and Call API ---
$prompt = '';
$result = ['success' => false, 'error' => 'Invalid action specified.']; // Default result

// Include internal_links.php if needed for suggest_internal_links action
if ($action === 'suggest_internal_links') {
    require_once '../includes/internal_links.php';
}

try {
    // *** ADDED: Release session lock before making the potentially long API call ***
    session_write_close();
    // ******************************************************************************

    switch ($action) {
        case 'rewrite_combined':
            $rewrittenTitle = null;
            $rewrittenContent = null;
            $errors = [];

            // Rewrite Title if selected
            if ($rewriteTitleFlag) {
                $promptTitle = PROMPT_REWRITE_TITLE . $titleText; // Use constant
                $titleResult = callDeepSeekAPI($promptTitle, $titleText); // Pass title as input for its own rewrite
                if ($titleResult['success']) {
                    $rewrittenTitle = $titleResult['text'];
                } else {
                    $errors[] = 'Title rewrite failed: ' . $titleResult['error'];
                }
            }

            // Rewrite Content if selected
            if ($rewriteContentFlag) {
                 // Use content as input, title provides context within the prompt
                 // Check if the prompt contains a title placeholder
                 $promptContent = PROMPT_REWRITE_CONTENT;
                 if (strpos($promptContent, '%TITLE%') !== false) {
                     $promptContent = str_replace('%TITLE%', $titleText, $promptContent);
                 }
                 $promptContent .= $inputText;
                 // Use higher temperature and max tokens for content rewrites to get more creative and longer content
                 $contentResult = callDeepSeekAPI($promptContent, $inputText, 0.75, 4000);
                 if ($contentResult['success']) {
                     $rewrittenContent = $contentResult['text'];
                 } else {
                     $errors[] = 'Content rewrite failed: ' . $contentResult['error'];
                 }
            }

            if (empty($errors)) {
                 // Only return success true if no errors occurred
                 $result = [
                     'success' => true,
                     'rewritten_title' => $rewrittenTitle, // Will be null if not requested or failed
                     'rewritten_content' => $rewrittenContent // Will be null if not requested or failed
                 ];
                 // Ensure at least one rewrite happened if no errors
                 if ($rewrittenTitle === null && $rewrittenContent === null && ($rewriteTitleFlag || $rewriteContentFlag)) {
                      $result = ['success' => false, 'error' => 'Rewrite completed without returning text.'];
                 } elseif (!$rewriteTitleFlag && !$rewriteContentFlag) {
                      $result = ['success' => false, 'error' => 'No rewrite option selected.'];
                 }
            } else {
                $result = ['success' => false, 'error' => implode('; ', $errors)];
            }
            break;

        case 'generate_excerpt':
            $prompt = PROMPT_GENERATE_EXCERPT . $inputText; // Use constant
            $result = callDeepSeekAPI($prompt, $inputText);
            break;

        case 'suggest_tags':
            $prompt = PROMPT_SUGGEST_TAGS . $inputText; // Use constant
            $result = callDeepSeekAPI($prompt, $inputText);
            break;

        case 'suggest_meta_title':
             // Use content as input, replace placeholder in prompt with actual title
            $prompt = str_replace('%TITLE%', $titleText, PROMPT_SUGGEST_META_TITLE) . $inputText;
            $result = callDeepSeekAPI($prompt, $inputText);
            break;

        case 'suggest_meta_desc':
             // Use content as input, replace placeholder in prompt with actual title
            $prompt = str_replace('%TITLE%', $titleText, PROMPT_SUGGEST_META_DESC) . $inputText;
            $result = callDeepSeekAPI($prompt, $inputText);
            break;

         case 'suggest_focus_keyword':
             // Use content as input, replace placeholder in prompt with actual title
             $prompt = str_replace('%TITLE%', $titleText, PROMPT_SUGGEST_FOCUS_KEYWORD) . $inputText;
             $result = callDeepSeekAPI($prompt, $inputText);
             break;

        case 'suggest_internal_links':
            try {
                // Make sure getPDOConnection function is available
                if (!function_exists('getPDOConnection')) {
                    function getPDOConnection() {
                        // Basic check for constants
                        if (!defined('DB_HOST') || !defined('DB_NAME') || !defined('DB_USER') || !defined('DB_PASS') || !defined('DB_CHARSET')) {
                            error_log("FATAL: Database configuration constants are missing in getPDOConnection().");
                            throw new Exception("Database configuration constants are missing.");
                        }
                        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
                        $options = [ PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC, PDO::ATTR_EMULATE_PREPARES => false, ];
                        try {
                            return new PDO($dsn, DB_USER, DB_PASS, $options);
                        } catch (\PDOException $e) {
                            error_log("PDO Connection failed in getPDOConnection(): " . $e->getMessage());
                            throw new Exception("Database connection failed during operation. Check server logs. PDO Error: " . $e->getMessage());
                        }
                    }
                }

                // Get a PDO connection
                $pdo = getPDOConnection();

                // Make sure the suggestInternalLinks function exists
                if (!function_exists('suggestInternalLinks')) {
                    throw new Exception("Required function suggestInternalLinks() not found. Check includes/internal_links.php.");
                }

                // Use the suggestInternalLinks function from internal_links.php
                $suggestions = suggestInternalLinks($pdo, $inputText, $titleText);

                if (!empty($suggestions)) {
                    $result = [
                        'success' => true,
                        'suggestions' => $suggestions
                    ];
                } else {
                    // Try to generate some dummy suggestions if no real ones are available
                    // This is useful for testing when there aren't enough articles in the database
                    $dummySuggestions = [
                        [
                            'keyword' => 'example keyword',
                            'article_id' => 1,
                            'title' => 'Example Article',
                            'url' => '/example-article/',
                            'reason' => 'This is a sample suggestion for testing purposes.'
                        ]
                    ];

                    $result = [
                        'success' => true,
                        'suggestions' => $dummySuggestions,
                        'note' => 'Using dummy suggestions for testing as no real suggestions could be generated.'
                    ];
                }
            } catch (Exception $e) {
                $result = [
                    'success' => false,
                    'error' => 'Error generating internal link suggestions: ' . $e->getMessage()
                ];
                error_log("Error in suggest_internal_links: " . $e->getMessage());
            }
            break;

        // Add other actions here if needed

        default:
            // Handled by default result initialization
            break;
    }

} catch (Exception $e) {
    // Catch any unexpected exceptions during processing
    error_log("DeepSeek Handler Exception: " . $e->getMessage());
    // Note: Cannot set headers here as session_write_close() might prevent it
    // Best effort to return JSON error
    $result = ['success' => false, 'error' => 'An unexpected error occurred: ' . $e->getMessage()];
}

// --- Output JSON Response ---
// Ensure that no output happened before this line (other than potential headers)
echo json_encode($result);
exit;

?>
