<?php
// Include configuration - This makes constants like SITE_NAME available
require_once 'config.php';
// Include ad-related files
require_once 'includes/ad_display.php'; // Handles displaying ads/promos
require_once 'includes/ad_manager.php'; // Manages ad/promo data
require_once 'includes/ad_tracking.php'; // Handles tracking for ads/promos

// --- Get filter parameters from URL ---
$period = isset($_GET['period']) ? $_GET['period'] : 'all';
$category = isset($_GET['category']) ? $_GET['category'] : '';

// Validate period parameter
$valid_periods = ['day', 'week', 'month', 'year', 'all'];
if (!in_array($period, $valid_periods)) {
    $period = 'all';
}

// --- Set Page Title and Meta Data ---
$page_title = "Popularni članci";
$meta_description = "Najpopularniji članci na " . SITE_NAME . ". Pregledajte najčitanije članke po kategorijama i vremenskim periodima.";
$og_title = $page_title;
$og_description = $meta_description;
$og_url = SITE_URL . '/popularno.php';
$og_type = 'website';

// Default OG image data
$ogImageData = getDefaultOgImageData();
$og_image_url = $ogImageData['url'];
$og_image_width = $ogImageData['width'];
$og_image_height = $ogImageData['height'];
$og_image_alt = $ogImageData['og_alt'];

// --- Define Ad Context for Popular Page ---
$adContext = [
    'page_type' => 'popular', // Source page type
    'source_page_id' => null, // No specific ID for popular page
    'period' => $period, // Include period for potential targeting
    'category' => $category // Include category for potential targeting
];

// --- Fetch Categories for Filter ---
$categories = [];
try {
    $categories = getAllCategories($pdo);
} catch (PDOException $e) {
    error_log("Error fetching categories: " . $e->getMessage());
}

// --- Fetch Popular Articles ---
$articles = [];
$error_message = null;

try {
    // Build the SQL query based on filters
    $sql = "SELECT
                a.id, a.title, a.slug, a.excerpt, a.featured_image, a.created_at, a.published_at, a.reading_time, a.views,
                au.name as author_name, au.avatar_url as author_avatar,
                c.name as category_name, c.slug as category_slug
            FROM articles a
            LEFT JOIN authors au ON a.author_id = au.id
            LEFT JOIN categories c ON a.category_id = c.id
            WHERE a.status = 'published'
                AND (a.published_at IS NULL OR a.published_at <= NOW())";

    // Add period filter
    if ($period !== 'all') {
        switch ($period) {
            case 'day':
                $sql .= " AND DATE(COALESCE(a.published_at, a.created_at)) >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
                break;
            case 'week':
                $sql .= " AND DATE(COALESCE(a.published_at, a.created_at)) >= DATE_SUB(CURDATE(), INTERVAL 1 WEEK)";
                break;
            case 'month':
                $sql .= " AND DATE(COALESCE(a.published_at, a.created_at)) >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)";
                break;
            case 'year':
                $sql .= " AND DATE(COALESCE(a.published_at, a.created_at)) >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)";
                break;
        }
    }

    // Add category filter
    if (!empty($category)) {
        $sql .= " AND c.slug = :category_slug";
    }

    // Order by views and limit results
    $sql .= " ORDER BY a.views DESC LIMIT 20";

    $stmt = $pdo->prepare($sql);

    // Bind category parameter if needed
    if (!empty($category)) {
        $stmt->bindParam(':category_slug', $category, PDO::PARAM_STR);
    }

    $stmt->execute();
    $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    error_log("Error fetching popular articles: " . $e->getMessage());
    $error_message = "Došlo je do greške prilikom dohvatanja popularnih članaka. Molimo pokušajte ponovo.";
}

// Include header (which uses the meta variables)
include 'includes/header.php';
?>

<div class="flex flex-col md:flex-row gap-8">
    <?php // --- Main Content Area (Popular Articles) --- ?>
    <div class="md:w-3/4 order-2 md:order-1">
        <div class="text-center mb-8 border-b border-border pb-4">
            <h1 class="text-3xl md:text-4xl font-montserrat font-extrabold text-primary mt-1">
                Popularni članci
            </h1>
            <p class="mt-2 text-gray-600">
                Najpopularniji članci na našem sajtu
                <?php if ($period !== 'all'): ?>
                    <?php
                    $period_text = '';
                    switch ($period) {
                        case 'day': $period_text = 'danas'; break;
                        case 'week': $period_text = 'ove sedmice'; break;
                        case 'month': $period_text = 'ovog mjeseca'; break;
                        case 'year': $period_text = 'ove godine'; break;
                    }
                    echo ' - ' . $period_text;
                    ?>
                <?php endif; ?>
                <?php if (!empty($category)): ?>
                    <?php
                    $category_name = '';
                    foreach ($categories as $cat) {
                        if ($cat['slug'] === $category) {
                            $category_name = $cat['name'];
                            break;
                        }
                    }
                    if (!empty($category_name)) {
                        echo ' u kategoriji "' . escape($category_name) . '"';
                    }
                    ?>
                <?php endif; ?>
            </p>
        </div>

        <?php // --- Filter Controls --- ?>
        <div class="mb-8 bg-white p-4 rounded-xl border border-border">
            <form action="popularno.php" method="GET" class="flex flex-col sm:flex-row gap-4">
                <div class="flex-grow">
                    <label for="period" class="block text-sm font-medium text-gray-700 mb-1">Vremenski period</label>
                    <select name="period" id="period" class="w-full border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary">
                        <option value="all" <?php echo $period === 'all' ? 'selected' : ''; ?>>Svi članci</option>
                        <option value="day" <?php echo $period === 'day' ? 'selected' : ''; ?>>Danas</option>
                        <option value="week" <?php echo $period === 'week' ? 'selected' : ''; ?>>Ova sedmica</option>
                        <option value="month" <?php echo $period === 'month' ? 'selected' : ''; ?>>Ovaj mjesec</option>
                        <option value="year" <?php echo $period === 'year' ? 'selected' : ''; ?>>Ova godina</option>
                    </select>
                </div>

                <div class="flex-grow">
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Kategorija</label>
                    <select name="category" id="category" class="w-full border border-gray-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-primary">
                        <option value="">Sve kategorije</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo escape($cat['slug']); ?>" <?php echo $category === $cat['slug'] ? 'selected' : ''; ?>>
                                <?php echo escape($cat['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="flex items-end">
                    <button type="submit" class="btn h-[42px]">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                        </svg>
                        Filtriraj
                    </button>
                </div>
            </form>
        </div>

        <?php if ($error_message): ?>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
                <?php echo $error_message; ?>
            </div>
        <?php elseif (empty($articles)): ?>
            <div class="text-center py-10">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h2 class="text-xl font-montserrat font-bold text-gray-700 mb-2">Nema rezultata</h2>
                <p class="text-gray-600 max-w-md mx-auto">
                    Nažalost, nismo pronašli popularne članke koji odgovaraju vašim filterima. Pokušajte sa drugačijim filterima ili pregledajte naše kategorije.
                </p>
                <div class="mt-6">
                    <a href="<?php echo SITE_URL; ?>/popularno.php" class="btn">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Resetuj filtere
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="space-y-6" id="popularArticles">
                <?php foreach ($articles as $index => $article):
                    $articleUrl = SITE_URL . '/' . escape($article['slug']) . '/';
                    $authorAvatarData = getFeaturedImageUrl($article['author_avatar'], 'ss', 'articles', escape($article['author_name'] ?? 'Autor'));
                    $authorAvatarUrl = $authorAvatarData['url'];

                    // Add a banner ad after every 3 articles
                    if ($index > 0 && $index % 3 === 0):
                ?>
                    <div class="text-center p-0 bg-transparent">
                        <?php echo displayAdsForPlacement($pdo, 'between_posts_ad', $adContext, 1, 1); ?>
                    </div>
                <?php endif; ?>

                <?php // Desktop Card ?>
                <article class="card p-0 overflow-hidden max-h-[245px] w-full article-card-desktop">
                    <a href="<?php echo $articleUrl; ?>" class="w-[200px] relative overflow-hidden m-[10px] z-[1] min-h-[200px] flex-shrink-0 rounded-lg block bg-gray-100">
                       <?php if (!empty($article['featured_image'])): ?>
                           <?php
                           // Use our responsive image helper for article thumbnails
                           echo getResponsiveImageHtml(
                               $article['featured_image'],
                               'articles',
                               escape($article['title']),
                               [
                                   'widths' => ['xs', 'ss'], // Use xs and ss sizes for article thumbnails
                                   'isPrimary' => false,
                                   'lazyLoad' => true,
                                   'containerClass' => '',
                                   'imgClass' => 'absolute inset-0 w-full h-full object-cover',
                                   'sizesAttr' => '200px',
                                   'usePicture' => false
                               ]
                           );
                           ?>
                       <?php else: ?>
                           <div class="absolute inset-0 flex items-center justify-center bg-gray-200"><span class="text-gray-500 text-xs italic">Slika nedostupna</span></div>
                       <?php endif; ?>
                    </a>
                    <div class="flex-grow p-4 flex flex-col justify-between">
                        <div>
                            <?php if (!empty($article['category_name'])): ?>
                                <a href="<?php echo SITE_URL; ?>/category/<?php echo escape($article['category_slug']); ?>/" class="text-xs text-primary font-medium hover:underline"><?php echo escape($article['category_name']); ?></a>
                            <?php endif; ?>
                            <h2 class="text-lg md:text-xl font-montserrat font-bold mt-1 mb-2 line-clamp-2">
                                <a href="<?php echo $articleUrl; ?>" class="text-dark-contrast hover:text-primary transition-colors">
                                    <?php echo escape($article['title']); ?>
                                </a>
                            </h2>
                            <p class="text-gray-darker text-sm line-clamp-2 mb-3">
                                <?php echo escape($article['excerpt']); ?>
                            </p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full overflow-hidden mr-2 bg-gray-200 flex items-center justify-center">
                                    <?php if ($authorAvatarUrl): ?>
                                        <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo escape($authorAvatarData['alt']); ?>" class="w-full h-full object-cover" loading="lazy" width="32" height="32">
                                    <?php else: ?>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                                    <?php endif; ?>
                                </div>
                                <span class="text-xs text-gray-500"><?php echo escape($article['author_name'] ?? 'Autor'); ?></span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-xs text-gray-500 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                                    <span><?php echo number_format($article['views']); ?></span>&nbsp;pregleda
                                </span>
                            </div>
                        </div>
                    </div>
                </article>

                <?php // Mobile Card ?>
                <article class="card article-card-mobile" x-data="{ readers: <?php echo number_format($article['views']); ?>, animating: false }" x-init="setTimeout(() => { readers += Math.floor(Math.random() * 5) + 1; animating = true; setTimeout(() => animating = false, 1500) }, Math.random() * 8000 + 3000)">
                    <a href="<?php echo $articleUrl; ?>" class="mobile-article-image rounded-t-xl block bg-gray-100">
                        <?php if (!empty($article['featured_image'])): ?>
                            <?php
                            // Use our responsive image helper for mobile article thumbnails
                            echo getResponsiveImageHtml(
                                $article['featured_image'],
                                'articles',
                                escape($article['title']),
                                [
                                    'widths' => ['xs', 'ss'], // Use xs and ss sizes for mobile thumbnails
                                    'isPrimary' => false,
                                    'lazyLoad' => true,
                                    'containerClass' => '',
                                    'imgClass' => 'absolute inset-0 w-full h-full object-cover',
                                    'sizesAttr' => '100vw',
                                    'usePicture' => false
                                ]
                            );
                            ?>
                        <?php else: ?>
                            <div class="absolute inset-0 flex items-center justify-center bg-gray-200"><span class="text-gray-500 text-xs italic">Slika nedostupna</span></div>
                        <?php endif; ?>
                    </a>
                    <div class="mobile-article-content">
                        <div>
                            <div class="flex justify-between items-center mb-2"><span class="text-xs text-gray-medium"><?php echo formatDate($article['published_at'] ?? $article['created_at']); ?></span></div>
                            <h3 class="text-lg font-montserrat font-extrabold mb-2 line-clamp-2"><a href="<?php echo $articleUrl; ?>" class="hover:text-primary transition-colors article-title text-dark-contrast"><?php echo escape($article['title']); ?></a></h3>
                            <?php if (!empty($article['excerpt'])): ?><p class="text-gray-darker text-xs md:text-sm line-clamp-2"><?php echo escape($article['excerpt']); ?></p><?php endif; ?>
                        </div>
                        <div class="mt-3 flex flex-wrap justify-between items-center">
                            <div class="flex items-center mb-2 xs:mb-0">
                                <div class="w-6 h-6 rounded-full overflow-hidden mr-2 bg-gray-200 flex items-center justify-center">
                                    <?php if ($authorAvatarUrl): ?>
                                        <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo escape($authorAvatarData['alt']); ?>" class="w-full h-full object-cover" loading="lazy" width="24" height="24"/>
                                    <?php else: ?>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                                    <?php endif; ?>
                                </div>
                                <span class="text-xs font-montserrat font-semibold text-dark-contrast"><?php echo escape($article['author_name'] ?? 'Nepoznat autor'); ?></span>
                            </div>
                            <div class="flex items-center text-xs text-gray-medium gap-3">
                                <div class="flex items-center text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg><span x-text="readers" :class="{'reader-pulse': animating}" class="relative inline-block"></span>&nbsp;pregleda</div>
                            </div>
                        </div>
                    </div>
                </article>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <?php // --- Sidebar --- ?>
    <div class="md:w-1/4 order-1 md:order-2">
        <div class="sidebar-content md:sticky md:top-32 space-y-6">
            <?php // Display Ads/Promos in Sidebar ?>
            <?php echo displayAdsForPlacement($pdo, 'sidebar_middle', $adContext, 1, 1); ?>

            <?php echo displayAdsForPlacement($pdo, 'sidebar_bottom', $adContext, 1, 1); ?>

            <?php // Popular Categories ?>
            <div class="card p-4">
                <h3 class="font-montserrat font-extrabold text-lg mb-4 text-dark-contrast">Popularne kategorije</h3>
                <div class="space-y-2">
                    <a href="<?php echo SITE_URL; ?>/category/recepti/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Recepti</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/prirodni-lijekovi/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Prirodni lijekovi</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/slana-jela/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Slana jela</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/slatka-jela/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Slatka jela</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
