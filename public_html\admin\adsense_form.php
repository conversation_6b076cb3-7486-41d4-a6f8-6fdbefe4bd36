<?php
/**
 * Admin AdSense Unit Form
 *
 * Allows administrators to create/edit Google AdSense units
 * for placement throughout the site.
 */

require_once '../config.php';
require_once 'includes/auth_check.php';
require_once '../includes/functions.php';

// Set default values for a new AdSense unit
$edit_mode = false;
$adsense_id = null;
$adsense = [
    'name' => '',
    'ad_code' => '',
    'placements' => ['sidebar_bottom'], // MODIFIED: Array for multiple placements
    'status' => 'active',
    'device_visibility' => 'all',
    'position_index' => 1,
    'custom_css' => '',
    'fixed_width' => 300,
    'fixed_height' => 250,
    'placement_selector' => '',
    'frequency' => 1,
    'skip_paragraphs' => 2,
    'max_ads_per_page' => 3,
    'show_on_homepage' => 1,
    'show_on_categories' => 1,
    'show_on_articles' => 1,
    'show_on_tags' => 1,
    'start_date' => null,
    'end_date' => null,
    'custom_targeting' => '',
    'lazy_load' => 1
];

// Check if editing an existing AdSense unit
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $edit_mode = true;
    $adsense_id = (int)$_GET['id'];
    
    try {
        // Fetch the AdSense unit data
        $stmt = $pdo->prepare("SELECT * FROM adsense_units WHERE id = :id");
        $stmt->bindParam(':id', $adsense_id, PDO::PARAM_INT);
        $stmt->execute();
        $adsense_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($adsense_data) {
            // MODIFIED: Convert placement to placements array
            if (isset($adsense_data['placement'])) {
                // Check if it's already a JSON array
                $placementsArray = json_decode($adsense_data['placement'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($placementsArray)) {
                    $adsense_data['placements'] = $placementsArray;
                } else {
                    // Single value - convert to array
                    $adsense_data['placements'] = [$adsense_data['placement']];
                }
            } else {
                $adsense_data['placements'] = ['sidebar_bottom']; // Default if none set
            }
            
            // Merge with defaults to ensure all keys exist
            $adsense = array_merge($adsense, $adsense_data);
            
            // Format dates for the form
            if (!empty($adsense['start_date'])) {
                $adsense['start_date_formatted'] = date('Y-m-d\TH:i', strtotime($adsense['start_date']));
            }
            if (!empty($adsense['end_date'])) {
                $adsense['end_date_formatted'] = date('Y-m-d\TH:i', strtotime($adsense['end_date']));
            }
        } else {
            $_SESSION['error_message'] = "AdSense unit with ID $adsense_id not found.";
            header('Location: advertising.php');
            exit;
        }
    } catch (PDOException $e) {
        $_SESSION['error_message'] = "Database error: " . $e->getMessage();
        header('Location: advertising.php');
        exit;
    }
}

// Set page title
$admin_page_title = $edit_mode ? "Edit AdSense Unit" : "New AdSense Unit";

// Include the admin header
include 'includes/header.php';
?>

<header class="h-16 bg-white border-b border-border flex items-center justify-between px-6 flex-shrink-0 sticky top-0 z-30">
    <div class="flex items-center">
        <h1 class="text-xl font-montserrat font-bold text-dark"><?php echo escape($admin_page_title); ?></h1>
        <?php if ($edit_mode && isset($adsense['status'])): ?>
            <div class="ml-4">
                <span class="badge <?php echo ($adsense['status'] === 'active') ? 'badge-success' : 'badge-gray'; ?>">
                    <?php echo ucfirst(escape($adsense['status'])); ?>
                </span>
            </div>
        <?php endif; ?>
    </div>
    <div class="flex items-center space-x-3">
        <a href="advertising.php" class="btn-secondary">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12" />
            </svg>
            Cancel
        </a>
        <button type="submit" form="adsenseForm" class="btn-success">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <?php echo $edit_mode ? 'Update AdSense Unit' : 'Save AdSense Unit'; ?>
        </button>
    </div>
</header>

<form id="adsenseForm" action="process_adsense.php" method="POST" class="p-6 h-full">
    <?php if ($edit_mode): ?>
        <input type="hidden" name="adsense_id" value="<?php echo $adsense_id; ?>">
    <?php endif; ?>
    <input type="hidden" name="action" value="<?php echo $edit_mode ? 'update' : 'create'; ?>">
    
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="mb-4 bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded-lg text-sm shadow-sm">
            <?php echo escape($_SESSION['success_message']); unset($_SESSION['success_message']); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm shadow-sm">
            <?php echo escape($_SESSION['error_message']); unset($_SESSION['error_message']); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['form_errors'])): ?>
        <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm space-y-1 shadow-sm">
            <p class="font-semibold">Please correct the following errors:</p>
            <ul class="list-disc list-inside">
                <?php foreach ($_SESSION['form_errors'] as $error): ?>
                    <li><?php echo escape($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php unset($_SESSION['form_errors']); ?>
    <?php endif; ?>
    
    <div class="flex flex-col lg:flex-row gap-6 h-full">
        <!-- Main Content Column -->
        <div class="lg:w-7/12 flex flex-col h-full">
            <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Ad Unit Name *</label>
                <input type="text" id="name" name="name" class="form-input text-xl font-medium py-3" placeholder="Enter a descriptive name (e.g., Sidebar Top Ad)" required value="<?php echo escape($adsense['name']); ?>">
                <p class="text-xs text-gray-500 mt-1">For your reference only - not displayed to users.</p>
            </div>
            
            <div class="mb-4">
                <label for="ad_code" class="block text-sm font-medium text-gray-700 mb-1">AdSense Code *</label>
                <textarea id="ad_code" name="ad_code" rows="8" class="form-textarea font-mono text-sm" placeholder="Paste your full AdSense code here (e.g., <script async src=&quot;https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-...)" required><?php echo escape($adsense['ad_code']); ?></textarea>
                <p class="text-xs text-gray-500 mt-1">Paste the complete AdSense code snippet from your Google AdSense account.</p>
            </div>
            
            <!-- MODIFIED: Replaced dropdown with checkboxes for multiple placements -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-3">Placement Locations *</label>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2">
                    <div class="text-sm font-semibold text-gray-700 col-span-2 mt-2">Sidebar Placements:</div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="placement_sidebar_popular" name="placements[]" value="sidebar_popular" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            <?php echo in_array('sidebar_popular', $adsense['placements']) ? 'checked' : ''; ?>>
                        <label for="placement_sidebar_popular" class="ml-2 text-sm text-gray-700">Sidebar - Popular Articles</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="placement_sidebar_middle" name="placements[]" value="sidebar_middle" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            <?php echo in_array('sidebar_middle', $adsense['placements']) ? 'checked' : ''; ?>>
                        <label for="placement_sidebar_middle" class="ml-2 text-sm text-gray-700">Sidebar - Middle</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="placement_sidebar_bottom" name="placements[]" value="sidebar_bottom" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            <?php echo in_array('sidebar_bottom', $adsense['placements']) ? 'checked' : ''; ?>>
                        <label for="placement_sidebar_bottom" class="ml-2 text-sm text-gray-700">Sidebar - Bottom</label>
                    </div>
                    
                    <div class="text-sm font-semibold text-gray-700 col-span-2 mt-4">Article Placements:</div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="placement_article_beginning" name="placements[]" value="article_beginning" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            <?php echo in_array('article_beginning', $adsense['placements']) ? 'checked' : ''; ?>>
                        <label for="placement_article_beginning" class="ml-2 text-sm text-gray-700">Article Beginning</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="placement_in_content" name="placements[]" value="in_content" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            <?php echo in_array('in_content', $adsense['placements']) ? 'checked' : ''; ?>>
                        <label for="placement_in_content" class="ml-2 text-sm text-gray-700">Within Article Content</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="placement_after_content" name="placements[]" value="after_content" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            <?php echo in_array('after_content', $adsense['placements']) ? 'checked' : ''; ?>>
                        <label for="placement_after_content" class="ml-2 text-sm text-gray-700">After Content (after tags)</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="placement_article_bottom_banner" name="placements[]" value="article_bottom_banner" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            <?php echo in_array('article_bottom_banner', $adsense['placements']) ? 'checked' : ''; ?>>
                        <label for="placement_article_bottom_banner" class="ml-2 text-sm text-gray-700">Article Bottom Banner</label>
                    </div>
                    
                    <div class="text-sm font-semibold text-gray-700 col-span-2 mt-4">Other Placements:</div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="placement_recommended" name="placements[]" value="recommended" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            <?php echo in_array('recommended', $adsense['placements']) ? 'checked' : ''; ?>>
                        <label for="placement_recommended" class="ml-2 text-sm text-gray-700">Recommended Section</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="placement_header" name="placements[]" value="header" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            <?php echo in_array('header', $adsense['placements']) ? 'checked' : ''; ?>>
                        <label for="placement_header" class="ml-2 text-sm text-gray-700">Header</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="placement_footer" name="placements[]" value="footer" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            <?php echo in_array('footer', $adsense['placements']) ? 'checked' : ''; ?>>
                        <label for="placement_footer" class="ml-2 text-sm text-gray-700">Footer</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="placement_custom" name="placements[]" value="custom" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                            <?php echo in_array('custom', $adsense['placements']) ? 'checked' : ''; ?>>
                        <label for="placement_custom" class="ml-2 text-sm text-gray-700">Custom Position</label>
                    </div>
                </div>
                
                <p class="text-xs text-gray-500 mt-3">Select at least one position where this ad will appear on the website.</p>
            </div>

            <div class="mb-4" id="customPlacementContainer" style="display: <?php echo in_array('custom', $adsense['placements']) ? 'block' : 'none'; ?>">
                <label for="placement_selector" class="block text-sm font-medium text-gray-700 mb-1">Custom CSS Selector</label>
                <input type="text" id="placement_selector" name="placement_selector" class="form-input" placeholder="e.g., #sidebar-top or .content-after" value="<?php echo escape($adsense['placement_selector']); ?>">
                <p class="text-xs text-gray-500 mt-1">CSS selector where this ad should be inserted (required for custom placement).</p>
            </div>
            
            <div id="inContentOptions" class="space-y-3 mb-4" style="display: <?php echo in_array('in_content', $adsense['placements']) ? 'block' : 'none'; ?>">
                <div>
                    <label for="frequency" class="block text-sm font-medium text-gray-700 mb-1">Insert After Every X Paragraphs</label>
                    <input type="number" id="frequency" name="frequency" min="1" max="20" class="form-input py-1.5" value="<?php echo escape($adsense['frequency']); ?>">
                    <p class="text-xs text-gray-500 mt-1">For in-content ads, how often should this ad appear?</p>
                </div>
                
                <div>
                    <label for="skip_paragraphs" class="block text-sm font-medium text-gray-700 mb-1">Skip First X Paragraphs</label>
                    <input type="number" id="skip_paragraphs" name="skip_paragraphs" min="0" max="10" class="form-input py-1.5" value="<?php echo escape($adsense['skip_paragraphs']); ?>">
                    <p class="text-xs text-gray-500 mt-1">How many paragraphs to skip before inserting the first ad?</p>
                </div>
                
                <div>
                    <label for="max_ads_per_page" class="block text-sm font-medium text-gray-700 mb-1">Maximum Ads Per Page</label>
                    <input type="number" id="max_ads_per_page" name="max_ads_per_page" min="1" max="10" class="form-input py-1.5" value="<?php echo escape($adsense['max_ads_per_page']); ?>">
                    <p class="text-xs text-gray-500 mt-1">Maximum number of instances of this ad to show on a single page.</p>
                </div>
            </div>

            <div class="mb-4" id="adDimensionsSection">
                <h3 class="font-medium text-gray-700 mb-2 text-sm">Ad Dimensions</h3>
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label for="fixed_width" class="block text-sm font-medium text-gray-700 mb-1">Width (px)</label>
                        <input type="number" id="fixed_width" name="fixed_width" class="form-input py-1.5" value="<?php echo escape($adsense['fixed_width']); ?>">
                    </div>
                    <div>
                        <label for="fixed_height" class="block text-sm font-medium text-gray-700 mb-1">Height (px)</label>
                        <input type="number" id="fixed_height" name="fixed_height" class="form-input py-1.5" value="<?php echo escape($adsense['fixed_height']); ?>">
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-1">Optional dimensions for container size (leave empty for responsive ads).</p>
            </div>
            
            <div class="space-y-2 flex-shrink-0">
                <!-- Custom CSS Section -->
                <div x-data="{ open: false }">
                    <button type="button" @click="open = !open" class="collapsible-header">
                        <span class="text-sm font-semibold text-gray-700">Custom CSS (Optional)</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform transition-transform" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div x-show="open" x-transition class="collapsible-content" style="display: none;">
                        <textarea id="custom_css" name="custom_css" rows="5" class="form-textarea font-mono text-sm" placeholder=".my-ad-container { margin: 20px auto; max-width: 100%; }"><?php echo escape($adsense['custom_css']); ?></textarea>
                        <p class="text-xs text-gray-500 mt-1">Optional custom CSS for styling the ad container.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar/Settings Column -->
        <div class="lg:w-5/12 flex-shrink-0">
            <div class="bg-white border border-border rounded-xl p-4 space-y-4 mb-4 sticky top-20">
                <!-- Status -->
                <div>
                    <h3 class="font-montserrat font-semblance-medium text-gray-700 mb-2 border-b border-gray-200 pb-1">Status</h3>
                    <div class="space-y-3 mt-3">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select id="status" name="status" class="form-select py-1.5" required>
                                <option value="active" <?php echo ($adsense['status'] === 'active') ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo ($adsense['status'] === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                <option value="scheduled" <?php echo ($adsense['status'] === 'scheduled') ? 'selected' : ''; ?>>Scheduled</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Placement Options -->
                <div id="placementOptionsSection">
                    <h3 class="font-montserrat font-semibold text-gray-700 mb-2 border-b border-gray-200 pb-1">Display Options</h3>
                    
                    <div class="space-y-3 mt-3">
                        <!-- Position Index -->
                        <div id="positionIndexContainer">
                            <label for="position_index" class="block text-sm font-medium text-gray-700 mb-1">Position Order</label>
                            <input type="number" id="position_index" name="position_index" min="1" max="10" class="form-input py-1.5" value="<?php echo escape($adsense['position_index']); ?>">
                            <p class="text-xs text-gray-500 mt-1">Order of appearance (1 = first, 2 = second, etc.)</p>
                        </div>
                        
                        <!-- Device Targeting -->
                        <div>
                            <label for="device_visibility" class="block text-sm font-medium text-gray-700 mb-1">Device Targeting</label>
                            <select id="device_visibility" name="device_visibility" class="form-select py-1.5">
                                <option value="all" <?php echo ($adsense['device_visibility'] === 'all') ? 'selected' : ''; ?>>All Devices</option>
                                <option value="desktop" <?php echo ($adsense['device_visibility'] === 'desktop') ? 'selected' : ''; ?>>Desktop Only</option>
                                <option value="mobile" <?php echo ($adsense['device_visibility'] === 'mobile') ? 'selected' : ''; ?>>Mobile Only</option>
                                <option value="tablet" <?php echo ($adsense['device_visibility'] === 'tablet') ? 'selected' : ''; ?>>Tablet Only</option>
                            </select>
                        </div>
                        
                        <!-- Lazy Load -->
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Lazy Load</span>
                            <div x-data="{ enabled: <?php echo $adsense['lazy_load'] ? 'true' : 'false'; ?> }">
                                <input type="hidden" name="lazy_load" :value="enabled ? 1 : 0">
                                <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                    <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">When enabled, ads load only when they become visible.</p>
                    </div>
                </div>
                
                <!-- Page Visibility -->
                <div>
                    <h3 class="font-montserrat font-semibold text-gray-700 mb-2 border-b border-gray-200 pb-1">Page Visibility</h3>
                    <div class="space-y-3 mt-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Show on Homepage</span>
                            <div x-data="{ enabled: <?php echo $adsense['show_on_homepage'] ? 'true' : 'false'; ?> }">
                                <input type="hidden" name="show_on_homepage" :value="enabled ? 1 : 0">
                                <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                    <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Show on Articles</span>
                            <div x-data="{ enabled: <?php echo $adsense['show_on_articles'] ? 'true' : 'false'; ?> }">
                                <input type="hidden" name="show_on_articles" :value="enabled ? 1 : 0">
                                <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                    <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Show on Category Pages</span>
                            <div x-data="{ enabled: <?php echo $adsense['show_on_categories'] ? 'true' : 'false'; ?> }">
                                <input type="hidden" name="show_on_categories" :value="enabled ? 1 : 0">
                                <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                    <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Show on Tag Pages</span>
                            <div x-data="{ enabled: <?php echo $adsense['show_on_tags'] ? 'true' : 'false'; ?> }">
                                <input type="hidden" name="show_on_tags" :value="enabled ? 1 : 0">
                                <button type="button" @click="enabled = !enabled" class="toggle-switch" :class="enabled ? 'bg-primary' : 'bg-gray-200'">
                                    <span class="toggle-thumb" :class="enabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Scheduling -->
                <div x-data="{ schedulingEnabled: <?php echo ($adsense['status'] === 'scheduled') ? 'true' : 'false'; ?> }">
                    <h3 class="font-montserrat font-semibold text-gray-700 mb-2 border-b border-gray-200 pb-1">Scheduling</h3>
                    
                    <div class="space-y-3 mt-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Enable Scheduling</span>
                            <div>
                                <button 
                                    type="button" 
                                    @click="schedulingEnabled = !schedulingEnabled; document.getElementById('status').value = schedulingEnabled ? 'scheduled' : 'active';" 
                                    class="toggle-switch" 
                                    :class="schedulingEnabled ? 'bg-primary' : 'bg-gray-200'"
                                >
                                    <span class="toggle-thumb" :class="schedulingEnabled ? 'transform translate-x-5' : ''"></span>
                                </button>
                            </div>
                        </div>
                        
                        <div x-show="schedulingEnabled">
                            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date/Time</label>
                            <input type="datetime-local" id="start_date" name="start_date" class="form-input py-1.5" value="<?php echo $adsense['start_date_formatted'] ?? ''; ?>" :required="schedulingEnabled">
                            <p class="text-xs text-gray-500 mt-1">When should this ad start displaying?</p>
                        </div>
                        
                        <div x-show="schedulingEnabled">
                            <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date/Time (Optional)</label>
                            <input type="datetime-local" id="end_date" name="end_date" class="form-input py-1.5" value="<?php echo $adsense['end_date_formatted'] ?? ''; ?>">
                            <p class="text-xs text-gray-500 mt-1">When should this ad stop displaying? Leave empty for no end date.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<div class="p-6 bg-yellow-50 border-t border-yellow-200">
    <div class="max-w-4xl mx-auto">
        <div class="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div class="text-sm text-yellow-700">
                <p class="font-medium mb-1">AdSense Guidelines</p>
                <ul class="list-disc list-inside space-y-1 ml-4">
                    <li>Google limits publishers to 3 AdSense for Content units, 3 link units, and 2 search units per page</li>
                    <li>Make sure your ads don't blend in with your content too much</li>
                    <li>Don't place ads where they might be mistaken for navigation or content</li>
                    <li>Avoid placing ads too close to clickable elements or in a way that might lead to accidental clicks</li>
                    <li>Mobile ads should be appropriately sized and not interfere with the user experience</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide custom placement selector based on placement selection
    const customPlacementContainer = document.getElementById('customPlacementContainer');
    const inContentOptions = document.getElementById('inContentOptions');
    
    // Get all placement checkboxes
    const placementCheckboxes = document.querySelectorAll('input[name="placements[]"]');
    
    // Function to check if a placement is selected
    const isPlacementSelected = (value) => {
        let selected = false;
        placementCheckboxes.forEach(checkbox => {
            if (checkbox.value === value && checkbox.checked) {
                selected = true;
            }
        });
        return selected;
    };
    
    // Function to update UI based on selected placements
    const updateUIForPlacements = () => {
        // Show/hide custom placement input
        customPlacementContainer.style.display = isPlacementSelected('custom') ? 'block' : 'none';
        
        // Show/hide in-content options
        inContentOptions.style.display = isPlacementSelected('in_content') ? 'block' : 'none';
    };
    
    // Add event listeners to all placement checkboxes
    placementCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateUIForPlacements);
    });
    
    // Initialize UI on page load
    updateUIForPlacements();
});
</script>

<?php include 'includes/footer.php'; ?>
