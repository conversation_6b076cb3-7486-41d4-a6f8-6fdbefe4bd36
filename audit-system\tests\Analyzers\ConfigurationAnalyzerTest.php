<?php

namespace AuditSystem\Tests\Analyzers;

use PHPUnit\Framework\TestCase;
use AuditSystem\Analyzers\ConfigurationAnalyzer;
use AuditSystem\Models\Finding;

class ConfigurationAnalyzerTest extends TestCase
{
    private ConfigurationAnalyzer $analyzer;

    protected function setUp(): void
    {
        $this->analyzer = new ConfigurationAnalyzer();
    }

    public function testGetSupportedFileTypes(): void
    {
        $supportedTypes = $this->analyzer->getSupportedFileTypes();
        $this->assertContains('php', $supportedTypes);
        $this->assertContains('htaccess', $supportedTypes);
        $this->assertContains('json', $supportedTypes);
    }

    public function testGetName(): void
    {
        $this->assertEquals('Configuration Analyzer', $this->analyzer->getName());
    }

    public function testAnalyzePhpConfigWithWeakPassword(): void
    {
        $content = '<?php
define("DB_HOST", "localhost");
define("DB_PASS", "123");
define("DB_USER", "root");
?>';

        $findings = $this->analyzer->analyze('config.php', $content);
        
        $this->assertNotEmpty($findings);
        $weakPasswordFinding = $this->findFindingByDescription($findings, 'Weak database password detected');
        $this->assertNotNull($weakPasswordFinding);
        $this->assertEquals(Finding::SEVERITY_HIGH, $weakPasswordFinding->severity);
        $this->assertEquals(Finding::PRIORITY_AREA, $weakPasswordFinding->priority);
    }

    public function testAnalyzePhpConfigWithApiKeyInPlainText(): void
    {
        $content = '<?php
define("DEEPSEEK_API_KEY", "sk-1234567890abcdef");
define("FB_APP_SECRET", "secret123");
?>';

        $findings = $this->analyzer->analyze('config.php', $content);
        
        $this->assertNotEmpty($findings);
        $apiKeyFindings = array_filter($findings, function($finding) {
            return strpos($finding->description, 'API key or secret stored in plain text') !== false;
        });
        $this->assertCount(2, $apiKeyFindings);
    }

    public function testAnalyzePhpConfigWithDebugModeEnabled(): void
    {
        $content = '<?php
ini_set("display_errors", 1);
error_reporting(E_ALL);
?>';

        $findings = $this->analyzer->analyze('config.php', $content);
        
        $this->assertNotEmpty($findings);
        
        $debugFinding = $this->findFindingByDescription($findings, 'Debug mode enabled in production configuration');
        $this->assertNotNull($debugFinding);
        $this->assertEquals(Finding::SEVERITY_MEDIUM, $debugFinding->severity);
        
        $errorReportingFinding = $this->findFindingByDescription($findings, 'Full error reporting enabled');
        $this->assertNotNull($errorReportingFinding);
        $this->assertEquals(Finding::SEVERITY_MEDIUM, $errorReportingFinding->severity);
    }

    public function testAnalyzePhpConfigWithInsecureSessionConfig(): void
    {
        $content = '<?php
session_start();
?>';

        $findings = $this->analyzer->analyze('config.php', $content);
        
        $this->assertNotEmpty($findings);
        
        $httpOnlyFinding = $this->findFindingByDescription($findings, 'Session cookies not configured as HttpOnly');
        $this->assertNotNull($httpOnlyFinding);
        
        $sameSiteFinding = $this->findFindingByDescription($findings, 'Session SameSite attribute not configured');
        $this->assertNotNull($sameSiteFinding);
    }

    public function testAnalyzePhpConfigWithSecureSessionConfig(): void
    {
        $content = '<?php
ini_set("session.cookie_httponly", 1);
ini_set("session.cookie_secure", 1);
ini_set("session.cookie_samesite", "Lax");
session_start();
?>';

        $findings = $this->analyzer->analyze('config.php', $content);
        
        // Should not have session security findings
        $sessionFindings = array_filter($findings, function($finding) {
            return strpos($finding->description, 'Session') !== false;
        });
        $this->assertEmpty($sessionFindings);
    }

    public function testAnalyzePhpConfigPerformanceSettings(): void
    {
        $content = '<?php
define("CDN_ENABLED", false);
define("IMAGE_QUALITY", 95);
define("MAX_FILE_SIZE", 20 * 1024 * 1024);
define("ARTICLES_PER_PAGE", 10);
?>';

        $findings = $this->analyzer->analyze('config.php', $content);
        
        $this->assertNotEmpty($findings);
        
        $cdnFinding = $this->findFindingByDescription($findings, 'CDN is disabled');
        $this->assertNotNull($cdnFinding);
        $this->assertEquals(Finding::TYPE_PERFORMANCE, $cdnFinding->type);
        
        $qualityFinding = $this->findFindingByDescription($findings, 'Image quality setting too high');
        $this->assertNotNull($qualityFinding);
        
        $fileSizeFinding = $this->findFindingByDescription($findings, 'Large file upload limit may impact performance');
        $this->assertNotNull($fileSizeFinding);
        
        $cachingFinding = $this->findFindingByDescription($findings, 'No caching configuration detected');
        $this->assertNotNull($cachingFinding);
    }

    public function testAnalyzePhpConfigEnvironmentIssues(): void
    {
        $content = '<?php
define("DB_HOST", "localhost");
define("DB_NAME", "myapp_dev");
?>';

        $findings = $this->analyzer->analyze('config.php', $content);
        
        $this->assertNotEmpty($findings);
        
        $envFinding = $this->findFindingByDescription($findings, 'No environment detection mechanism found');
        $this->assertNotNull($envFinding);
        
        $localhostFinding = $this->findFindingByDescription($findings, 'Database host hardcoded to localhost');
        $this->assertNotNull($localhostFinding);
        
        $devDbFinding = $this->findFindingByDescription($findings, 'Development database name detected');
        $this->assertNotNull($devDbFinding);
        $this->assertEquals(Finding::SEVERITY_HIGH, $devDbFinding->severity);
    }

    public function testAnalyzePhpConfigSensitiveDataInComments(): void
    {
        $content = '<?php
// password: secret123
/* TODO: change API key to production key */
// FIXME: hardcoded secret token needs to be moved
?>';

        $findings = $this->analyzer->analyze('config.php', $content);
        
        $this->assertNotEmpty($findings);
        
        $commentFinding = $this->findFindingByDescription($findings, 'Sensitive information in comments');
        $this->assertNotNull($commentFinding);
        
        $todoFinding = $this->findFindingByDescription($findings, 'TODO/FIXME comment contains sensitive information references');
        $this->assertNotNull($todoFinding);
    }

    public function testAnalyzeHtaccessSecurityIssues(): void
    {
        $content = '
RewriteEngine On
Options +Indexes
';

        $findings = $this->analyzer->analyze('.htaccess', $content);
        
        $this->assertNotEmpty($findings);
        
        $httpsFinding = $this->findFindingByDescription($findings, 'HTTPS redirect not configured');
        $this->assertNotNull($httpsFinding);
        $this->assertEquals(Finding::SEVERITY_HIGH, $httpsFinding->severity);
        
        $directoryFinding = $this->findFindingByDescription($findings, 'Directory listing not disabled');
        $this->assertNotNull($directoryFinding);
        
        $configProtectionFinding = $this->findFindingByDescription($findings, 'Sensitive file config.php not protected');
        $this->assertNotNull($configProtectionFinding);
    }

    public function testAnalyzeHtaccessWithSecureConfig(): void
    {
        $content = '
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

Options -Indexes

<Files config.php>
    Require all denied
</Files>

<Files .htaccess>
    Require all denied
</Files>
';

        $findings = $this->analyzer->analyze('.htaccess', $content);
        
        // Should not have major security findings
        $securityFindings = array_filter($findings, function($finding) {
            return $finding->type === Finding::TYPE_SECURITY && $finding->severity === Finding::SEVERITY_HIGH;
        });
        $this->assertEmpty($securityFindings);
    }

    public function testAnalyzeHtaccessPerformanceIssues(): void
    {
        $content = '
RewriteEngine On
RewriteRule ^old1/(.*)$ /new1/$1 [R=301,L]
RewriteRule ^old2/(.*)$ /new2/$1 [R=301,L]
RewriteRule ^old3/(.*)$ /new3/$1 [R=301,L]
RewriteRule ^old4/(.*)$ /new4/$1 [R=301,L]
RewriteRule ^old5/(.*)$ /new5/$1 [R=301,L]
RewriteRule ^old6/(.*)$ /new6/$1 [R=301,L]
RewriteRule ^old7/(.*)$ /new7/$1 [R=301,L]
RewriteRule ^old8/(.*)$ /new8/$1 [R=301,L]
RewriteRule ^old9/(.*)$ /new9/$1 [R=301,L]
RewriteRule ^old10/(.*)$ /new10/$1 [R=301,L]
RewriteRule ^old11/(.*)$ /new11/$1 [R=301,L]
';

        $findings = $this->analyzer->analyze('.htaccess', $content);
        
        $this->assertNotEmpty($findings);
        
        $compressionFinding = $this->findFindingByDescription($findings, 'GZIP compression not configured');
        $this->assertNotNull($compressionFinding);
        
        $cachingFinding = $this->findFindingByDescription($findings, 'Browser caching not configured');
        $this->assertNotNull($cachingFinding);
        
        $redirectFinding = $this->findFindingByDescription($findings, 'High number of redirect rules detected');
        $this->assertNotNull($redirectFinding);
    }

    public function testAnalyzeHtaccessWithPerformanceOptimizations(): void
    {
        $content = '
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html
</IfModule>

<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
';

        $findings = $this->analyzer->analyze('.htaccess', $content);
        
        // Should not have compression or caching findings
        $performanceFindings = array_filter($findings, function($finding) {
            return strpos($finding->description, 'compression') !== false || 
                   strpos($finding->description, 'caching') !== false;
        });
        $this->assertEmpty($performanceFindings);
    }

    public function testAnalyzeComposerJsonWithOutdatedPhp(): void
    {
        $content = '
{
    "require": {
        "php": "^7.4"
    }
}
';

        $findings = $this->analyzer->analyze('composer.json', $content);
        
        $this->assertNotEmpty($findings);
        
        $phpVersionFinding = $this->findFindingByDescription($findings, 'Outdated PHP version requirement');
        $this->assertNotNull($phpVersionFinding);
        $this->assertEquals(Finding::TYPE_SECURITY, $phpVersionFinding->type);
    }

    public function testAnalyzeComposerJsonWithMissingSecurityPackage(): void
    {
        $content = '
{
    "require": {
        "php": "^8.1"
    }
}
';

        $findings = $this->analyzer->analyze('composer.json', $content);
        
        $this->assertNotEmpty($findings);
        
        $securityPackageFinding = $this->findFindingByDescription($findings, 'Missing security package: roave/security-advisories');
        $this->assertNotNull($securityPackageFinding);
    }

    public function testAnalyzeComposerJsonWithInvalidJson(): void
    {
        $content = '{ invalid json }';

        $findings = $this->analyzer->analyze('composer.json', $content);
        
        $this->assertNotEmpty($findings);
        
        $jsonFinding = $this->findFindingByDescription($findings, 'Invalid JSON in composer.json');
        $this->assertNotNull($jsonFinding);
        $this->assertEquals(Finding::SEVERITY_HIGH, $jsonFinding->severity);
    }

    public function testAnalyzeWithDisabledChecks(): void
    {
        $analyzer = new ConfigurationAnalyzer([
            'check_security_config' => false,
            'check_performance_settings' => false
        ]);

        $content = '<?php
define("DB_PASS", "123");
define("CDN_ENABLED", false);
?>';

        $findings = $analyzer->analyze('config.php', $content);
        
        // Should have fewer findings due to disabled checks
        $securityFindings = array_filter($findings, function($finding) {
            return $finding->type === Finding::TYPE_SECURITY;
        });
        $performanceFindings = array_filter($findings, function($finding) {
            return $finding->type === Finding::TYPE_PERFORMANCE;
        });
        
        $this->assertEmpty($securityFindings);
        $this->assertEmpty($performanceFindings);
    }

    public function testAnalyzeUnsupportedFileType(): void
    {
        $findings = $this->analyzer->analyze('test.txt', 'some content');
        $this->assertEmpty($findings);
    }

    private function findFindingByDescription(array $findings, string $description): ?Finding
    {
        foreach ($findings as $finding) {
            if (strpos($finding->description, $description) !== false) {
                return $finding;
            }
        }
        return null;
    }
}