<?php
require_once 'config.php'; // Includes functions.php which now has fetchComments

header('Content-Type: application/json');

$articleId = filter_input(INPUT_GET, 'article_id', FILTER_VALIDATE_INT);
$offset = filter_input(INPUT_GET, 'offset', FILTER_VALIDATE_INT, ['options' => ['min_range' => 0]]);
$limit = filter_input(INPUT_GET, 'limit', FILTER_VALIDATE_INT, ['options' => ['min_range' => 1, 'default' => 5]]); // Default limit 5

if ($articleId === false || $articleId <= 0 || $offset === false || $offset < 0) {
    echo json_encode(['success' => false, 'error' => 'Nevažeći parametri.']);
    exit;
}

// Assuming $pdo is available from config.php
if (!isset($pdo)) {
     echo json_encode(['success' => false, 'error' => 'Database connection error.']);
     error_log("PDO connection not available in load_comments.php");
     exit;
}

$commentsData = fetchComments($pdo, $articleId, $limit, $offset);

if (isset($commentsData['error'])) {
    echo json_encode(['success' => false, 'error' => $commentsData['error']]);
} else {
    // Add formatted date to each fetched comment for JS rendering
     if (!empty($commentsData['comments'])) {
         foreach ($commentsData['comments'] as &$comment) {
             $comment['time_ago'] = formatCommentDate($comment['created_at']);
             if (!empty($comment['replies'])) {
                 foreach($comment['replies'] as &$reply) {
                     $reply['time_ago'] = formatCommentDate($reply['created_at']);
                 }
                 unset($reply); // break reference
             }
         }
         unset($comment); // break reference
     }
    echo json_encode([
        'success' => true,
        'comments' => $commentsData['comments'],
        'total' => $commentsData['total'] // Send total count back too
    ]);
}
exit;
?>