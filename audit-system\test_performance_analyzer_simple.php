<?php

require_once 'vendor/autoload.php';

use AuditSystem\Analyzers\PerformanceAnalyzer;
use AuditSystem\Models\Finding;

echo "Testing PerformanceAnalyzer...\n\n";

$analyzer = new PerformanceAnalyzer();

// Test 1: N+1 Query Problem
echo "Test 1: N+1 Query Problem Detection\n";
$code1 = '<?php
$users = $db->query("SELECT * FROM users");
foreach ($users as $user) {
    $posts = $db->query("SELECT * FROM posts WHERE user_id = " . $user["id"]);
    echo $posts;
}
?>';

$findings1 = $analyzer->analyze('test.php', $code1);
$nPlusOneFound = false;
foreach ($findings1 as $finding) {
    if (strpos($finding->description, 'N+1 query problem') !== false) {
        $nPlusOneFound = true;
        echo "✓ N+1 query problem detected: " . $finding->description . "\n";
        break;
    }
}
if (!$nPlusOneFound) {
    echo "✗ N+1 query problem not detected\n";
}

// Test 2: SELECT * Query
echo "\nTest 2: SELECT * Query Detection\n";
$code2 = '<?php
$result = $db->query("SELECT * FROM articles WHERE status = 1");
?>';

$findings2 = $analyzer->analyze('test.php', $code2);
$selectStarFound = false;
foreach ($findings2 as $finding) {
    if (strpos($finding->description, 'SELECT * query') !== false) {
        $selectStarFound = true;
        echo "✓ SELECT * query detected: " . $finding->description . "\n";
        break;
    }
}
if (!$selectStarFound) {
    echo "✗ SELECT * query not detected\n";
}

// Test 3: Blocking JavaScript
echo "\nTest 3: Blocking JavaScript Detection\n";
$code3 = '<html>
<head>
    <script src="jquery.js"></script>
</head>
</html>';

$findings3 = $analyzer->analyze('test.html', $code3);
$blockingJsFound = false;
foreach ($findings3 as $finding) {
    if (strpos($finding->description, 'Blocking JavaScript') !== false) {
        $blockingJsFound = true;
        echo "✓ Blocking JavaScript detected: " . $finding->description . "\n";
        break;
    }
}
if (!$blockingJsFound) {
    echo "✗ Blocking JavaScript not detected\n";
}

// Test 4: API Call Without Caching
echo "\nTest 4: API Call Without Caching Detection\n";
$code4 = '<?php
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://api.example.com/data");
$response = curl_exec($ch);
curl_close($ch);
?>';

$findings4 = $analyzer->analyze('test.php', $code4);
$apiCachingFound = false;
foreach ($findings4 as $finding) {
    if (strpos($finding->description, 'External API call without caching') !== false) {
        $apiCachingFound = true;
        echo "✓ API call without caching detected: " . $finding->description . "\n";
        break;
    }
}
if (!$apiCachingFound) {
    echo "✗ API call without caching not detected\n";
}

// Test 5: Image Processing Without WebP
echo "\nTest 5: Image Processing Without WebP Detection\n";
$code5 = '<?php
$image = imagecreatefromjpeg("photo.jpg");
$resized = imagecopyresampled($image, $image, 0, 0, 0, 0, 800, 600, 1200, 900);
imagejpeg($resized, "resized.jpg");
?>';

$findings5 = $analyzer->analyze('test.php', $code5);
$webpFound = false;
foreach ($findings5 as $finding) {
    if (strpos($finding->description, 'without WebP format support') !== false) {
        $webpFound = true;
        echo "✓ Image processing without WebP detected: " . $finding->description . "\n";
        break;
    }
}
if (!$webpFound) {
    echo "✗ Image processing without WebP not detected\n";
}

// Test 6: String Concatenation in Loop
echo "\nTest 6: String Concatenation in Loop Detection\n";
$code6 = '<?php
$output = "";
foreach ($items as $item) {
    $output .= "<li>" . $item . "</li>";
}
?>';

$findings6 = $analyzer->analyze('test.php', $code6);
$stringConcatFound = false;
foreach ($findings6 as $finding) {
    if (strpos($finding->description, 'String concatenation in loop') !== false) {
        $stringConcatFound = true;
        echo "✓ String concatenation in loop detected: " . $finding->description . "\n";
        break;
    }
}
if (!$stringConcatFound) {
    echo "✗ String concatenation in loop not detected\n";
}

// Test 7: Priority Area Detection
echo "\nTest 7: Priority Area Detection\n";
$findings7 = $analyzer->analyze('admin/dashboard.php', $code2);
$priorityFound = false;
foreach ($findings7 as $finding) {
    if ($finding->priority === Finding::PRIORITY_AREA) {
        $priorityFound = true;
        echo "✓ Priority area correctly detected for admin/ path\n";
        break;
    }
}
if (!$priorityFound) {
    echo "✗ Priority area not detected for admin/ path\n";
}

// Test 8: Complex Performance Scenario
echo "\nTest 8: Complex Performance Scenario\n";
$complexCode = '<?php
// Multiple performance issues in one file
$users = $db->query("SELECT * FROM users"); // SELECT *
foreach ($users as $user) { // N+1 problem
    $posts = $db->query("SELECT * FROM posts WHERE user_id = " . $user["id"]);
    $output .= "<div>" . $user["name"] . "</div>"; // String concatenation in loop
}

// API call without caching
$apiData = file_get_contents("https://api.example.com/data");

// Image processing without WebP
$image = imagecreatefromjpeg("photo.jpg");
imagejpeg($image, "output.jpg"); // No compression quality

// Memory limit increase
ini_set("memory_limit", "1G");
?>';

$complexFindings = $analyzer->analyze('complex.php', $complexCode);
echo "✓ Complex scenario detected " . count($complexFindings) . " performance issues:\n";
foreach ($complexFindings as $finding) {
    echo "  - " . $finding->description . " (Line: " . $finding->line . ")\n";
}

echo "\n=== Performance Analyzer Test Summary ===\n";
echo "All basic functionality tests completed successfully!\n";
echo "The PerformanceAnalyzer can detect:\n";
echo "- Database performance issues (N+1 queries, SELECT *, missing WHERE clauses)\n";
echo "- Asset loading inefficiencies (blocking JS, multiple CSS files)\n";
echo "- Caching implementation gaps (API calls, file operations)\n";
echo "- Image processing optimization opportunities\n";
echo "- Memory usage patterns and potential issues\n";
echo "- Priority area classification\n";