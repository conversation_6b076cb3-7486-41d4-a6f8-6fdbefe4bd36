<?php
// Include configuration - This makes constants like SITE_NAME available
require_once 'config.php';
// Include ad-related files
require_once 'includes/ad_display.php'; // Handles displaying ads/promos
require_once 'includes/ad_manager.php'; // Manages ad/promo data
require_once 'includes/ad_tracking.php'; // Handles tracking for ads/promos

// Define DISPLAY_ERRORS for debugging
define('DISPLAY_ERRORS', true);

// Get the search query from the URL
$query = isset($_GET['query']) ? trim($_GET['query']) : '';

// --- Set Page Title and Meta Data ---
$page_title = empty($query) ? "Pretraga" : "Pretraga: " . $query;
$meta_description = empty($query) ? "Pretražite članke na " . SITE_NAME : "Rezultati pretrage za '$query' na " . SITE_NAME;
$og_title = $page_title;
$og_description = $meta_description;
$og_url = SITE_URL . '/search.php' . (empty($query) ? '' : '?query=' . urlencode($query));
$og_type = 'website';

// Default OG image data
$ogImageData = getDefaultOgImageData();
$og_image_url = $ogImageData['url'];
$og_image_width = $ogImageData['width'];
$og_image_height = $ogImageData['height'];
$og_image_alt = $ogImageData['og_alt'];

// --- Define Ad Context for Search Page ---
$adContext = [
    'page_type' => 'search', // Source page type
    'source_page_id' => null, // No specific ID for search page
    'search_query' => $query // Include search query for potential targeting
];

// --- Fetch Search Results ---
$articles = [];
$total_results = 0;
$error_message = null;

// Only search if a query is provided
if (!empty($query)) {
    try {
        // Ultra-simplified search query - just get articles that match the title
        $sql = "SELECT * FROM articles WHERE title LIKE :search_term AND status = 'published' ORDER BY id DESC LIMIT 20";
        $stmt = $pdo->prepare($sql);
        $search_term = '%' . $query . '%';
        $stmt->bindParam(':search_term', $search_term, PDO::PARAM_STR);
        $stmt->execute();
        $articles_raw = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process the articles to add missing fields
        $articles = [];
        foreach ($articles_raw as $article) {
            // Get author info if available
            $author_name = 'Autor';
            $author_avatar = null;

            if (!empty($article['author_id'])) {
                try {
                    $author_sql = "SELECT name, avatar FROM authors WHERE id = :author_id";
                    $author_stmt = $pdo->prepare($author_sql);
                    $author_stmt->bindParam(':author_id', $article['author_id'], PDO::PARAM_INT);
                    $author_stmt->execute();
                    $author = $author_stmt->fetch(PDO::FETCH_ASSOC);

                    if ($author) {
                        $author_name = $author['name'];
                        $author_avatar = $author['avatar'];
                    }
                } catch (Exception $e) {
                    // Silently fail and use defaults
                    error_log("Error fetching author: " . $e->getMessage());
                }
            }

            // Get category info if available
            $category_name = '';
            $category_slug = '';

            if (!empty($article['category_id'])) {
                try {
                    $category_sql = "SELECT name, slug FROM categories WHERE id = :category_id";
                    $category_stmt = $pdo->prepare($category_sql);
                    $category_stmt->bindParam(':category_id', $article['category_id'], PDO::PARAM_INT);
                    $category_stmt->execute();
                    $category = $category_stmt->fetch(PDO::FETCH_ASSOC);

                    if ($category) {
                        $category_name = $category['name'];
                        $category_slug = $category['slug'];
                    }
                } catch (Exception $e) {
                    // Silently fail and use defaults
                    error_log("Error fetching category: " . $e->getMessage());
                }
            }

            // Add processed article to results
            $articles[] = array_merge($article, [
                'author_name' => $author_name,
                'author_avatar' => $author_avatar,
                'category_name' => $category_name,
                'category_slug' => $category_slug
            ]);
        }

        // Simple count query
        $count_sql = "SELECT COUNT(*) FROM articles WHERE title LIKE :search_term AND status = 'published'";
        $count_stmt = $pdo->prepare($count_sql);
        $count_stmt->bindParam(':search_term', $search_term, PDO::PARAM_STR);
        $count_stmt->execute();
        $total_results = $count_stmt->fetchColumn();

    } catch (PDOException $e) {
        // Log the detailed error
        error_log("Search error: " . $e->getMessage());

        // For debugging only - in production, use a generic error message
        if (defined('DISPLAY_ERRORS') && DISPLAY_ERRORS) {
            $error_message = "Database error: " . $e->getMessage();
        } else {
            $error_message = "Došlo je do greške prilikom pretrage. Molimo pokušajte ponovo.";
        }
    } catch (Exception $e) {
        // Catch any other exceptions
        error_log("General search error: " . $e->getMessage());
        $error_message = "Došlo je do greške prilikom pretrage. Molimo pokušajte ponovo.";
    }
}

// Include header (which uses the meta variables)
include 'includes/header.php';
?>

<div class="flex flex-col md:flex-row gap-8">
    <?php // --- Main Content Area (Search Results) --- ?>
    <div class="md:w-3/4 order-2 md:order-1">
        <div class="text-center mb-8 border-b border-border pb-4">
            <h1 class="text-3xl md:text-4xl font-montserrat font-extrabold text-primary mt-1">
                <?php if (empty($query)): ?>
                    Pretraga
                <?php else: ?>
                    Rezultati pretrage: <span class="text-dark-contrast"><?php echo escape($query); ?></span>
                <?php endif; ?>
            </h1>

            <?php if (!empty($query)): ?>
                <p class="mt-2 text-gray-600">
                    <?php echo $total_results; ?> <?php echo ($total_results == 1) ? 'rezultat' : 'rezultata'; ?> pronađeno
                </p>
            <?php endif; ?>
        </div>

        <?php // --- Search Form --- ?>
        <div class="mb-8">
            <form action="search.php" method="GET" class="flex flex-col sm:flex-row gap-2">
                <input type="text" name="query" value="<?php echo escape($query); ?>"
                       placeholder="Unesite pojam za pretragu..."
                       class="flex-grow py-2 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                <button type="submit" class="btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    Pretraži
                </button>
            </form>
        </div>

        <?php if (!empty($query)): ?>
            <?php if ($error_message): ?>
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
                    <?php echo $error_message; ?>
                </div>
            <?php elseif (empty($articles)): ?>
                <div class="text-center py-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h2 class="text-xl font-montserrat font-bold text-gray-700 mb-2">Nema rezultata</h2>
                    <p class="text-gray-600 max-w-md mx-auto">
                        Nažalost, nismo pronašli članke koji odgovaraju vašem upitu. Pokušajte sa drugačijim pojmovima ili pregledajte naše kategorije.
                    </p>
                    <div class="mt-6">
                        <a href="<?php echo SITE_URL; ?>" class="btn">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            Početna stranica
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="space-y-6" id="searchResults">
                    <?php foreach ($articles as $article):
                        $randomReaders = rand(30, 120); // Random reader count for visual effect
                        $articleUrl = SITE_URL . '/' . escape($article['slug']) . '/';
                        // Use a default avatar if author_avatar is null
                        $authorAvatar = !empty($article['author_avatar']) ? $article['author_avatar'] : null;
                        $authorAvatarData = getFeaturedImageUrl($authorAvatar, 'ss', 'authors', escape($article['author_name'] ?? 'Autor'));
                        $authorAvatarUrl = $authorAvatarData['url'];
                    ?>
                        <?php // Desktop Card ?>
                        <article class="card p-0 overflow-hidden max-h-[245px] w-full article-card-desktop" x-data="{ readers: <?php echo $randomReaders; ?>, animating: false }" x-init="setTimeout(() => { readers += Math.floor(Math.random() * 5) + 1; animating = true; setTimeout(() => animating = false, 1500) }, Math.random() * 8000 + 3000)">
                            <a href="<?php echo $articleUrl; ?>" class="w-[200px] relative overflow-hidden m-[10px] z-[1] min-h-[200px] flex-shrink-0 rounded-lg block bg-gray-100">
                               <?php if (!empty($article['featured_image'])): ?>
                                   <?php
                                   // Use our responsive image helper for article thumbnails
                                   echo getResponsiveImageHtml(
                                       $article['featured_image'],
                                       'articles',
                                       escape($article['title']),
                                       [
                                           'widths' => ['xs', 'ss'], // Use xs and ss sizes for article thumbnails
                                           'isPrimary' => false,
                                           'lazyLoad' => true,
                                           'containerClass' => '',
                                           'imgClass' => 'absolute inset-0 w-full h-full object-cover',
                                           'sizesAttr' => '200px',
                                           'usePicture' => false
                                       ]
                                   );
                                   ?>
                               <?php else: ?>
                                   <div class="absolute inset-0 flex items-center justify-center bg-gray-200"><span class="text-gray-500 text-xs italic">Slika nedostupna</span></div>
                               <?php endif; ?>
                            </a>
                            <div class="flex-grow p-4 flex flex-col justify-between">
                                <div>
                                    <?php if (!empty($article['category_name'])): ?>
                                        <a href="<?php echo SITE_URL; ?>/category/<?php echo escape($article['category_slug']); ?>/" class="text-xs text-primary font-medium hover:underline"><?php echo escape($article['category_name']); ?></a>
                                    <?php endif; ?>
                                    <h2 class="text-lg md:text-xl font-montserrat font-bold mt-1 mb-2 line-clamp-2">
                                        <a href="<?php echo $articleUrl; ?>" class="text-dark-contrast hover:text-primary transition-colors">
                                            <?php
                                            // Highlight search term in title
                                            $title = escape($article['title']);
                                            if (!empty($query)) {
                                                $title = preg_replace('/(' . preg_quote($query, '/') . ')/i', '<span class="bg-yellow-100">$1</span>', $title);
                                            }
                                            echo $title;
                                            ?>
                                        </a>
                                    </h2>
                                    <p class="text-gray-darker text-sm line-clamp-2 mb-3">
                                        <?php
                                        // Highlight search term in excerpt
                                        $excerpt = escape($article['excerpt']);
                                        if (!empty($query)) {
                                            $excerpt = preg_replace('/(' . preg_quote($query, '/') . ')/i', '<span class="bg-yellow-100">$1</span>', $excerpt);
                                        }
                                        echo $excerpt;
                                        ?>
                                    </p>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full overflow-hidden mr-2 bg-gray-200 flex items-center justify-center">
                                            <?php if ($authorAvatarUrl): ?>
                                                <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo escape($authorAvatarData['alt']); ?>" class="w-full h-full object-cover" loading="lazy" width="32" height="32">
                                            <?php else: ?>
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                                            <?php endif; ?>
                                        </div>
                                        <span class="text-xs text-gray-500"><?php echo escape($article['author_name'] ?? 'Autor'); ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="text-xs text-gray-500 mr-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                                            <?php echo $article['reading_time']; ?> min
                                        </span>
                                        <span class="text-xs text-gray-500 flex items-center" :class="{ 'reader-pulse': animating }">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                                            <span x-text="readers"></span> čitatelja
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </article>

                        <?php // Mobile Card ?>
                        <article class="card article-card-mobile" x-data="{ readers: <?php echo $randomReaders; ?>, animating: false }" x-init="setTimeout(() => { readers += Math.floor(Math.random() * 5) + 1; animating = true; setTimeout(() => animating = false, 1500) }, Math.random() * 8000 + 3000)">
                            <a href="<?php echo $articleUrl; ?>" class="mobile-article-image rounded-t-xl block bg-gray-100">
                                <?php if (!empty($article['featured_image'])): ?>
                                    <?php
                                    // Use our responsive image helper for mobile article thumbnails
                                    echo getResponsiveImageHtml(
                                        $article['featured_image'],
                                        'articles',
                                        escape($article['title']),
                                        [
                                            'widths' => ['xs', 'ss'], // Use xs and ss sizes for mobile thumbnails
                                            'isPrimary' => false,
                                            'lazyLoad' => true,
                                            'containerClass' => '',
                                            'imgClass' => 'absolute inset-0 w-full h-full object-cover',
                                            'sizesAttr' => '100vw',
                                            'usePicture' => false
                                        ]
                                    );
                                    ?>
                                <?php else: ?>
                                    <div class="absolute inset-0 flex items-center justify-center bg-gray-200"><span class="text-gray-500 text-xs italic">Slika nedostupna</span></div>
                                <?php endif; ?>
                            </a>
                            <div class="mobile-article-content">
                                <div>
                                    <div class="flex justify-between items-center mb-2"><span class="text-xs text-gray-medium"><?php echo formatDate($article['published_at'] ?? $article['created_at']); ?></span></div>
                                    <h3 class="text-lg font-montserrat font-extrabold mb-2 line-clamp-2"><a href="<?php echo $articleUrl; ?>" class="hover:text-primary transition-colors article-title text-dark-contrast"><?php echo $title; ?></a></h3>
                                    <?php if (!empty($article['excerpt'])): ?><p class="text-gray-darker text-xs md:text-sm line-clamp-2"><?php echo $excerpt; ?></p><?php endif; ?>
                                </div>
                                <div class="mt-3 flex flex-wrap justify-between items-center">
                                    <div class="flex items-center mb-2 xs:mb-0">
                                        <div class="w-6 h-6 rounded-full overflow-hidden mr-2 bg-gray-200 flex items-center justify-center">
                                            <?php if ($authorAvatarUrl): ?>
                                                <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo escape($authorAvatarData['alt']); ?>" class="w-full h-full object-cover" loading="lazy" width="24" height="24"/>
                                            <?php else: ?>
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                                            <?php endif; ?>
                                        </div>
                                        <span class="text-xs font-montserrat font-semibold text-dark-contrast"><?php echo escape($article['author_name'] ?? 'Nepoznat autor'); ?></span>
                                    </div>
                                    <div class="flex items-center text-xs text-gray-medium gap-3">
                                        <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span><?php echo $article['reading_time']; ?> min</span></div>
                                        <div class="flex items-center text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg><span x-text="readers" :class="{'reader-pulse': animating}" class="relative inline-block"></span> čitatelja</div>
                                    </div>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>

                <?php if ($total_results > count($articles)): ?>
                    <div class="text-center mt-8">
                        <p class="text-gray-500 mb-2">Prikazano <?php echo count($articles); ?> od <?php echo $total_results; ?> rezultata</p>
                        <a href="<?php echo SITE_URL; ?>/advanced-search.php?query=<?php echo urlencode($query); ?>" class="btn">
                            Napredna pretraga
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-10">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <h2 class="text-xl font-montserrat font-bold text-gray-700 mb-2">Unesite pojam za pretragu</h2>
                <p class="text-gray-600 max-w-md mx-auto">
                    Pretražite naše članke po naslovu, sadržaju, kategoriji ili autoru.
                </p>
            </div>
        <?php endif; ?>
    </div>

    <?php // --- Sidebar --- ?>
    <div class="md:w-1/4 order-1 md:order-2">
        <div class="sidebar-content md:sticky md:top-32 space-y-6">
            <?php // Display Ads/Promos in Sidebar ?>
            <?php echo displayAdsForPlacement($pdo, 'sidebar_middle', $adContext, 1, 1); ?>

            <?php echo displayAdsForPlacement($pdo, 'sidebar_bottom', $adContext, 1, 1); ?>

            <?php // Popular Categories ?>
            <div class="card p-4">
                <h3 class="font-montserrat font-extrabold text-lg mb-4 text-dark-contrast">Popularne kategorije</h3>
                <div class="space-y-2">
                    <a href="<?php echo SITE_URL; ?>/category/recepti/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Recepti</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/prirodni-lijekovi/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Prirodni lijekovi</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/slana-jela/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Slana jela</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/slatka-jela/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Slatka jela</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include 'includes/footer.php';
?>
