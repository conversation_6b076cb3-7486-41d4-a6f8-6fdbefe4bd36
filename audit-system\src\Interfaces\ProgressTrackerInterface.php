<?php

namespace AuditSystem\Interfaces;

use AuditSystem\Models\AuditProgress;

/**
 * Interface for tracking audit progress and persistence
 */
interface ProgressTrackerInterface
{
    /**
     * Load existing audit progress from storage
     *
     * @return AuditProgress|null Existing progress or null if none exists
     */
    public function loadProgress(): ?AuditProgress;

    /**
     * Save current audit progress to storage
     *
     * @param AuditProgress $progress Progress data to save
     * @return bool True if save was successful
     */
    public function saveProgress(AuditProgress $progress): bool;

    /**
     * Mark a file as completed in the progress tracker
     *
     * @param string $filePath Path of the completed file
     * @param array $findings Findings discovered in the file
     * @return void
     */
    public function markFileCompleted(string $filePath, array $findings): void;

    /**
     * Check if a file has already been processed
     *
     * @param string $filePath Path of the file to check
     * @return bool True if file has been processed
     */
    public function isFileCompleted(string $filePath): bool;

    /**
     * Reset progress tracker to start fresh audit
     *
     * @return void
     */
    public function resetProgress(): void;

    /**
     * Initialize progress with list of files to process
     *
     * @param array $files List of files to process
     * @return void
     */
    public function initializeProgress(array $files): void;

    /**
     * Update current audit phase
     *
     * @param string $phase Current phase name
     * @return void
     */
    public function updatePhase(string $phase): void;

    /**
     * Get list of pending files
     *
     * @return array List of pending files
     */
    public function getPendingFiles(): array;

    /**
     * Backup current progress
     *
     * @return bool True if backup was successful
     */
    public function backupProgress(): bool;
}