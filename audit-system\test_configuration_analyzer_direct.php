<?php

// Direct test runner without autoloader issues
require_once 'src/Interfaces/AnalyzerInterface.php';
require_once 'src/Models/Finding.php';
require_once 'src/Analyzers/ConfigurationAnalyzer.php';

use AuditSystem\Analyzers\ConfigurationAnalyzer;
use AuditSystem\Models\Finding;

echo "Running ConfigurationAnalyzer Tests (Direct)...\n\n";

$analyzer = new ConfigurationAnalyzer();

// Test 1: Basic functionality
echo "Test 1: Basic functionality... ";
try {
    $supportedTypes = $analyzer->getSupportedFileTypes();
    $name = $analyzer->getName();
    
    if (in_array('php', $supportedTypes) && in_array('htaccess', $supportedTypes) && 
        in_array('json', $supportedTypes) && $name === 'Configuration Analyzer') {
        echo "PASSED\n";
    } else {
        echo "FAILED: Incorrect supported types or name\n";
    }
} catch (Exception $e) {
    echo "FAILED: " . $e->getMessage() . "\n";
}

// Test 2: Weak password detection
echo "Test 2: Weak password detection... ";
try {
    $content = '<?php
define("DB_HOST", "localhost");
define("DB_PASS", "123");
define("DB_USER", "root");
?>';

    $findings = $analyzer->analyze('config.php', $content);
    
    $weakPasswordFound = false;
    foreach ($findings as $finding) {
        if (strpos($finding->description, 'Weak database password detected') !== false) {
            $weakPasswordFound = true;
            break;
        }
    }
    
    if ($weakPasswordFound) {
        echo "PASSED\n";
    } else {
        echo "FAILED: Weak password not detected\n";
    }
} catch (Exception $e) {
    echo "FAILED: " . $e->getMessage() . "\n";
}

// Test 3: API key detection
echo "Test 3: API key detection... ";
try {
    $content = '<?php
define("DEEPSEEK_API_KEY", "sk-1234567890abcdef");
define("FB_APP_SECRET", "secret123");
?>';

    $findings = $analyzer->analyze('config.php', $content);
    
    $apiKeyCount = 0;
    foreach ($findings as $finding) {
        if (strpos($finding->description, 'API key or secret stored in plain text') !== false) {
            $apiKeyCount++;
        }
    }
    
    if ($apiKeyCount >= 2) {
        echo "PASSED\n";
    } else {
        echo "FAILED: API keys not properly detected (found $apiKeyCount)\n";
    }
} catch (Exception $e) {
    echo "FAILED: " . $e->getMessage() . "\n";
}

// Test 4: Debug mode detection
echo "Test 4: Debug mode detection... ";
try {
    $content = '<?php
ini_set("display_errors", 1);
error_reporting(E_ALL);
?>';

    $findings = $analyzer->analyze('config.php', $content);
    
    $debugFound = false;
    $errorReportingFound = false;
    
    foreach ($findings as $finding) {
        if (strpos($finding->description, 'Debug mode enabled') !== false) {
            $debugFound = true;
        }
        if (strpos($finding->description, 'Full error reporting enabled') !== false) {
            $errorReportingFound = true;
        }
    }
    
    if ($debugFound && $errorReportingFound) {
        echo "PASSED\n";
    } else {
        echo "FAILED: Debug issues not detected (debug: $debugFound, error: $errorReportingFound)\n";
    }
} catch (Exception $e) {
    echo "FAILED: " . $e->getMessage() . "\n";
}

// Test 5: .htaccess security issues
echo "Test 5: .htaccess security issues... ";
try {
    $content = '
RewriteEngine On
Options +Indexes
';

    $findings = $analyzer->analyze('.htaccess', $content);
    
    $httpsFound = false;
    $directoryFound = false;
    $configProtectionFound = false;
    
    foreach ($findings as $finding) {
        if (strpos($finding->description, 'HTTPS redirect not configured') !== false) {
            $httpsFound = true;
        }
        if (strpos($finding->description, 'Directory listing not disabled') !== false) {
            $directoryFound = true;
        }
        if (strpos($finding->description, 'config.php not protected') !== false) {
            $configProtectionFound = true;
        }
    }
    
    if ($httpsFound && $directoryFound && $configProtectionFound) {
        echo "PASSED\n";
    } else {
        echo "FAILED: .htaccess issues not detected (https: $httpsFound, directory: $directoryFound, config: $configProtectionFound)\n";
    }
} catch (Exception $e) {
    echo "FAILED: " . $e->getMessage() . "\n";
}

// Test 6: Performance settings
echo "Test 6: Performance settings... ";
try {
    $content = '<?php
define("CDN_ENABLED", false);
define("IMAGE_QUALITY", 95);
define("MAX_FILE_SIZE", 20 * 1024 * 1024);
define("ARTICLES_PER_PAGE", 10);
?>';

    $findings = $analyzer->analyze('config.php', $content);
    
    $cdnFound = false;
    $qualityFound = false;
    $fileSizeFound = false;
    $cachingFound = false;
    
    foreach ($findings as $finding) {
        if (strpos($finding->description, 'CDN is disabled') !== false) {
            $cdnFound = true;
        }
        if (strpos($finding->description, 'Image quality setting too high') !== false) {
            $qualityFound = true;
        }
        if (strpos($finding->description, 'Large file upload limit') !== false) {
            $fileSizeFound = true;
        }
        if (strpos($finding->description, 'No caching configuration detected') !== false) {
            $cachingFound = true;
        }
    }
    
    if ($cdnFound && $qualityFound && $fileSizeFound && $cachingFound) {
        echo "PASSED\n";
    } else {
        echo "FAILED: Performance issues not detected (cdn: $cdnFound, quality: $qualityFound, filesize: $fileSizeFound, caching: $cachingFound)\n";
    }
} catch (Exception $e) {
    echo "FAILED: " . $e->getMessage() . "\n";
}

// Test 7: Composer.json analysis
echo "Test 7: Composer.json analysis... ";
try {
    $content = '
{
    "require": {
        "php": "^7.4"
    }
}
';

    $findings = $analyzer->analyze('composer.json', $content);
    
    $phpVersionFound = false;
    $securityPackageFound = false;
    
    foreach ($findings as $finding) {
        if (strpos($finding->description, 'Outdated PHP version requirement') !== false) {
            $phpVersionFound = true;
        }
        if (strpos($finding->description, 'Missing security package') !== false) {
            $securityPackageFound = true;
        }
    }
    
    if ($phpVersionFound && $securityPackageFound) {
        echo "PASSED\n";
    } else {
        echo "FAILED: Composer issues not detected (php: $phpVersionFound, security: $securityPackageFound)\n";
    }
} catch (Exception $e) {
    echo "FAILED: " . $e->getMessage() . "\n";
}

echo "\nConfiguration Analyzer tests completed!\n";