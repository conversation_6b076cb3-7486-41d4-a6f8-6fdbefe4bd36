#!/usr/bin/env php
<?php

require_once __DIR__ . '/../vendor/autoload.php';

use AuditSystem\Config\AuditConfig;
use AuditSystem\Controllers\AuditController;
use AuditSystem\Services\ProgressTracker;
use AuditSystem\Services\FileScanner;
use AuditSystem\Services\ReportGenerator;
use AuditSystem\Analyzers\SecurityAnalyzer;

/**
 * Enhanced CLI tool for running CMS audits with comprehensive argument parsing and real-time progress
 */
class AuditCLI
{
    private AuditConfig $config;
    private AuditController $controller;
    private ReportGenerator $reportGenerator;
    private array $options;
    private bool $verbose;
    private bool $quiet;
    private string $outputFormat;
    private $progressHandle;

    public function __construct()
    {
        $this->config = AuditConfig::getInstance();
        $this->config->loadFromFile(__DIR__ . '/../config/audit.json');
        
        $progressTracker = new ProgressTracker($this->config->get('audit.progress_file'));
        $fileScanner = new FileScanner($this->config);
        
        $this->controller = new AuditController($this->config, $progressTracker, $fileScanner);
        $this->reportGenerator = new ReportGenerator();
        
        $this->options = [];
        $this->verbose = false;
        $this->quiet = false;
        $this->outputFormat = 'text';
        $this->progressHandle = null;
        
        // Register analyzers
        $this->registerAnalyzers();
    }

    private function registerAnalyzers(): void
    {
        // Register security analyzer
        if ($this->config->get('analyzers.security.enabled', true)) {
            $securityConfig = $this->config->get('analyzers.security', []);
            $this->controller->registerAnalyzer(new SecurityAnalyzer($securityConfig));
        }
    }

    public function run(array $args): int
    {
        try {
            // Parse command line arguments
            $parsedArgs = $this->parseArguments($args);
            $command = $parsedArgs['command'];
            $this->options = $parsedArgs['options'];
            $this->verbose = $parsedArgs['flags']['verbose'] ?? false;
            $this->quiet = $parsedArgs['flags']['quiet'] ?? false;
            $this->outputFormat = $parsedArgs['options']['format'] ?? 'text';
            
            // Validate conflicting flags
            if ($this->verbose && $this->quiet) {
                $this->error("Cannot use --verbose and --quiet together");
                return 1;
            }
            
            switch ($command) {
                case 'audit':
                case 'start':
                    return $this->runAudit();
                    
                case 'resume':
                    return $this->resumeAudit();
                    
                case 'status':
                    return $this->showStatus();
                    
                case 'watch':
                    return $this->watchProgress();
                    
                case 'config':
                    return $this->showConfig();
                    
                case 'validate':
                    return $this->validateConfiguration();
                    
                case 'help':
                case '--help':
                case '-h':
                    return $this->showHelp();
                    
                case 'version':
                case '--version':
                case '-v':
                    return $this->showVersion();
                    
                default:
                    $this->error("Unknown command: {$command}");
                    $this->output("Use 'php audit.php help' for usage information.");
                    return 1;
            }
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            if ($this->verbose) {
                $this->error("Stack trace:");
                $this->error($e->getTraceAsString());
            }
            return 1;
        }
    }

    private function runAudit(): int
    {
        if (!$this->quiet) {
            $this->output("🔍 Starting Lako & Fino CMS Security Audit...\n");
        }
        
        // Apply configuration overrides from command line options
        $auditOptions = $this->buildAuditOptions();
        
        // Show configuration if verbose
        if ($this->verbose) {
            $this->showAuditConfiguration($auditOptions);
        }
        
        // Validate configuration before starting
        if (!$this->validateAuditSetup($auditOptions)) {
            return 1;
        }
        
        // Start audit with real-time progress monitoring
        $startTime = microtime(true);
        
        if (!$this->quiet) {
            $this->output("Starting audit execution...");
            $this->startProgressMonitoring();
        }
        
        try {
            $result = $this->controller->startAudit($auditOptions);
            $endTime = microtime(true);
            
            $this->stopProgressMonitoring();
            
            $duration = round($endTime - $startTime, 2);
            if (!$this->quiet) {
                $this->output("\n✅ Audit completed in {$duration} seconds\n");
            }
            
            // Generate and save reports
            $this->generateReports($result);
            
            // Show summary
            $this->showSummary($result);
            
            return 0;
            
        } catch (\Exception $e) {
            $this->stopProgressMonitoring();
            $this->error("Audit failed: " . $e->getMessage());
            return 1;
        }
    }

    private function resumeAudit(): int
    {
        $this->output("🔄 Resuming audit from last checkpoint...\n");
        
        $startTime = microtime(true);
        $result = $this->controller->resumeAudit();
        $endTime = microtime(true);
        
        $duration = round($endTime - $startTime, 2);
        $this->output("✅ Audit resumed and completed in {$duration} seconds\n");
        
        $this->generateReports($result);
        $this->showSummary($result);
        
        return 0;
    }

    private function showStatus(): int
    {
        $status = $this->controller->getAuditStatus();
        
        $this->output("📊 Audit Status\n");
        $this->output("Phase: " . $status->phase);
        $this->output("Progress: " . round($status->completionPercentage, 1) . "%");
        $this->output("Files: {$status->processedFiles}/{$status->totalFiles}");
        $this->output("Running: " . ($status->isRunning ? 'Yes' : 'No'));
        
        if ($status->currentFile) {
            $this->output("Current File: " . $status->currentFile);
        }
        
        $this->output("\nStatistics:");
        $this->output("- Total Findings: " . $status->statistics->totalFindings);
        $this->output("- Critical Issues: " . $status->statistics->criticalFindings);
        $this->output("- Priority Area Issues: " . $status->statistics->priorityAreaFindings);
        
        return 0;
    }

    private function generateReports($result): void
    {
        $reportDir = $this->config->get('audit.report_directory');
        $timestamp = date('Y-m-d_H-i-s');
        
        $this->output("📄 Generating reports...");
        
        // Generate markdown report
        $markdownPath = "{$reportDir}/audit_report_{$timestamp}.md";
        if ($this->reportGenerator->exportReport($result, 'markdown', $markdownPath)) {
            $this->output("✅ Markdown report: {$markdownPath}");
        }
        
        // Generate JSON report
        $jsonPath = "{$reportDir}/audit_report_{$timestamp}.json";
        if ($this->reportGenerator->exportReport($result, 'json', $jsonPath)) {
            $this->output("✅ JSON report: {$jsonPath}");
        }
        
        // Generate HTML report
        $htmlPath = "{$reportDir}/audit_report_{$timestamp}.html";
        if ($this->reportGenerator->exportReport($result, 'html', $htmlPath)) {
            $this->output("✅ HTML report: {$htmlPath}");
        }
        
        $this->output("");
    }

    private function showSummary($result): void
    {
        $stats = $result->statistics;
        
        $this->output("📋 Audit Summary");
        $this->output("================");
        $this->output("Total Files Analyzed: " . count($result->fileStatus));
        $this->output("Total Issues Found: " . $stats->totalFindings);
        
        if ($stats->criticalFindings > 0) {
            $this->output("🚨 CRITICAL ISSUES: " . $stats->criticalFindings . " - IMMEDIATE ACTION REQUIRED!");
        }
        
        $this->output("High Priority Issues: " . $stats->highFindings);
        $this->output("Medium Priority Issues: " . $stats->mediumFindings);
        $this->output("Low Priority Issues: " . $stats->lowFindings);
        $this->output("");
        
        $this->output("Issue Breakdown:");
        $this->output("- Security Issues: " . $stats->securityFindings);
        $this->output("- Performance Issues: " . $stats->performanceFindings);
        $this->output("- Quality Issues: " . $stats->qualityFindings);
        $this->output("- Architecture Issues: " . $stats->architectureFindings);
        $this->output("");
        
        $this->output("Files Status:");
        $this->output("- Files with Issues: " . $stats->filesWithIssues);
        $this->output("- Optimal Files: " . $stats->optimalFiles);
        $this->output("- Priority Area Issues: " . $stats->priorityAreaFindings);
        
        if ($stats->criticalFindings > 0) {
            $this->output("\n⚠️  Please review the generated reports and address critical issues immediately!");
        }
    }

    /**
     * Parse command line arguments with comprehensive option support
     */
    private function parseArguments(array $args): array
    {
        $result = [
            'command' => 'audit',
            'options' => [],
            'flags' => []
        ];
        
        // Skip script name
        array_shift($args);
        
        // Get command if provided
        if (!empty($args) && !str_starts_with($args[0], '-')) {
            $result['command'] = array_shift($args);
        }
        
        // Parse remaining arguments
        for ($i = 0; $i < count($args); $i++) {
            $arg = $args[$i];
            
            if (str_starts_with($arg, '--')) {
                $this->parseLongOption($arg, $args, $i, $result);
            } elseif (str_starts_with($arg, '-') && strlen($arg) > 1) {
                $this->parseShortOption($arg, $result);
            } else {
                // Positional argument - could be used for additional parameters
                $result['positional'][] = $arg;
            }
        }
        
        return $result;
    }
    
    /**
     * Parse long options (--option=value or --flag)
     */
    private function parseLongOption(string $arg, array $args, int &$index, array &$result): void
    {
        $option = substr($arg, 2);
        
        if (str_contains($option, '=')) {
            [$key, $value] = explode('=', $option, 2);
            $result['options'][$key] = $value;
        } else {
            // Handle special flags that should change command
            if ($option === 'help') {
                $result['command'] = 'help';
                return;
            }
            
            // Check if next argument is a value (for options that take values)
            $optionsWithValues = ['target', 'config', 'progress-file', 'report-dir', 'timeout', 'max-file-size', 'format'];
            
            if (in_array($option, $optionsWithValues) && isset($args[$index + 1]) && !str_starts_with($args[$index + 1], '-')) {
                $result['options'][$option] = $args[$index + 1];
                $index++; // Skip next argument
            } else {
                // It's a flag
                $result['flags'][$option] = true;
            }
        }
    }
    
    /**
     * Parse short options (-v, -q, etc.)
     */
    private function parseShortOption(string $arg, array &$result): void
    {
        $flags = substr($arg, 1);
        
        for ($i = 0; $i < strlen($flags); $i++) {
            $flag = $flags[$i];
            
            switch ($flag) {
                case 'v':
                    $result['flags']['verbose'] = true;
                    break;
                case 'q':
                    $result['flags']['quiet'] = true;
                    break;
                case 'h':
                    $result['command'] = 'help';
                    break;
                default:
                    $result['flags'][$flag] = true;
            }
        }
    }
    
    /**
     * Build audit options from command line arguments
     */
    private function buildAuditOptions(): array
    {
        $options = [];
        
        // Map command line options to audit configuration
        $optionMap = [
            'target' => 'audit.target_directory',
            'progress-file' => 'audit.progress_file',
            'report-dir' => 'audit.report_directory',
            'timeout' => 'audit.timeout',
            'max-file-size' => 'audit.max_file_size',
            'clear-logs' => 'clear_logs'
        ];
        
        foreach ($optionMap as $cliOption => $configKey) {
            if (isset($this->options[$cliOption])) {
                $value = $this->options[$cliOption];
                
                // Type conversion for specific options
                if (in_array($cliOption, ['timeout', 'max-file-size'])) {
                    $value = (int) $value;
                } elseif ($cliOption === 'clear-logs') {
                    $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                }
                
                $options[$configKey] = $value;
            }
        }
        
        // Load custom config file if specified
        if (isset($this->options['config'])) {
            $configFile = $this->options['config'];
            if (file_exists($configFile)) {
                $this->config->loadFromFile($configFile);
                if ($this->verbose) {
                    $this->output("Loaded configuration from: {$configFile}");
                }
            } else {
                $this->error("Configuration file not found: {$configFile}");
                exit(1);
            }
        }
        
        return $options;
    }

    /**
     * Start real-time progress monitoring
     */
    private function startProgressMonitoring(): void
    {
        if ($this->quiet) {
            return;
        }
        
        // Start progress monitoring in background
        $this->progressHandle = popen('php -r "
            while (true) {
                $status = json_decode(file_get_contents(\"' . $this->config->get('audit.progress_file') . '\"), true);
                if ($status && isset($status[\"phase\"])) {
                    $phase = $status[\"phase\"];
                    $completed = count($status[\"completed_files\"] ?? []);
                    $total = count($status[\"pending_files\"] ?? []) + $completed;
                    $percentage = $total > 0 ? round(($completed / $total) * 100, 1) : 0;
                    
                    echo \"\\r[\" . str_pad($phase, 12) . \"] Progress: {$completed}/{$total} ({$percentage}%)\";
                    
                    if ($phase === \"completed\" || $phase === \"error\") {
                        echo \"\\n\";
                        break;
                    }
                }
                usleep(500000); // 0.5 second delay
            }
        "', 'r');
    }
    
    /**
     * Stop progress monitoring
     */
    private function stopProgressMonitoring(): void
    {
        if ($this->progressHandle) {
            pclose($this->progressHandle);
            $this->progressHandle = null;
        }
    }
    
    /**
     * Watch audit progress in real-time
     */
    private function watchProgress(): int
    {
        $this->output("📊 Watching audit progress (Press Ctrl+C to exit)...\n");
        
        $lastStatus = null;
        
        while (true) {
            $status = $this->controller->getAuditStatus();
            
            // Only update display if status changed
            if ($status != $lastStatus) {
                $this->clearLine();
                $this->displayProgressStatus($status);
                $lastStatus = $status;
            }
            
            // Exit if audit is completed or not running
            if (!$status->isRunning && $status->phase !== 'analyzing') {
                break;
            }
            
            usleep(1000000); // 1 second delay
        }
        
        return 0;
    }
    
    /**
     * Display current progress status
     */
    private function displayProgressStatus($status): void
    {
        $phase = str_pad($status->phase, 15);
        $progress = round($status->completionPercentage, 1);
        $files = "{$status->processedFiles}/{$status->totalFiles}";
        
        $progressBar = $this->createProgressBar($progress);
        
        $this->output("Phase: {$phase} | Progress: {$progressBar} {$progress}% | Files: {$files}");
        
        if ($status->currentFile && $this->verbose) {
            $this->output("Current: " . basename($status->currentFile));
        }
        
        if ($status->statistics) {
            $this->output("Issues: {$status->statistics->totalFindings} | Critical: {$status->statistics->criticalFindings}");
        }
    }
    
    /**
     * Create a visual progress bar
     */
    private function createProgressBar(float $percentage): string
    {
        $width = 20;
        $filled = (int) round(($percentage / 100) * $width);
        $empty = $width - $filled;
        
        return '[' . str_repeat('█', $filled) . str_repeat('░', $empty) . ']';
    }
    
    /**
     * Clear current line for progress updates
     */
    private function clearLine(): void
    {
        if (!$this->quiet) {
            echo "\r\033[K";
        }
    }
    
    /**
     * Show current configuration
     */
    private function showConfig(): int
    {
        // Config command should always show output, even in quiet mode
        echo "📋 Current Audit Configuration\n";
        echo "==============================\n";
        
        $config = [
            'Target Directory' => $this->config->get('audit.target_directory'),
            'Progress File' => $this->config->get('audit.progress_file'),
            'Report Directory' => $this->config->get('audit.report_directory'),
            'Timeout' => $this->config->get('audit.timeout', 300) . ' seconds',
            'Max File Size' => $this->formatBytes($this->config->get('audit.max_file_size', 1048576)),
        ];
        
        foreach ($config as $key => $value) {
            echo sprintf("  %-20s: %s\n", $key, $value);
        }
        
        echo "\nPriority Areas:\n";
        $patterns = $this->config->get('priority_areas.patterns', []);
        foreach ($patterns as $pattern) {
            echo "  - {$pattern}\n";
        }
        
        return 0;
    }
    
    /**
     * Validate audit configuration and setup
     */
    private function validateConfiguration(): int
    {
        $this->output("🔍 Validating Audit Configuration");
        $this->output("=================================");
        
        $errors = [];
        $warnings = [];
        
        // Check target directory
        $targetDir = $this->config->get('audit.target_directory');
        if (!is_dir($targetDir)) {
            $errors[] = "Target directory does not exist: {$targetDir}";
        } elseif (!is_readable($targetDir)) {
            $errors[] = "Target directory is not readable: {$targetDir}";
        } else {
            $this->output("✅ Target directory: {$targetDir}");
        }
        
        // Check progress file directory
        $progressFile = $this->config->get('audit.progress_file');
        $progressDir = dirname($progressFile);
        if (!is_dir($progressDir)) {
            if (!mkdir($progressDir, 0755, true)) {
                $errors[] = "Cannot create progress directory: {$progressDir}";
            } else {
                $this->output("✅ Created progress directory: {$progressDir}");
            }
        } elseif (!is_writable($progressDir)) {
            $errors[] = "Progress directory is not writable: {$progressDir}";
        } else {
            $this->output("✅ Progress file: {$progressFile}");
        }
        
        // Check report directory
        $reportDir = $this->config->get('audit.report_directory');
        if (!is_dir($reportDir)) {
            if (!mkdir($reportDir, 0755, true)) {
                $errors[] = "Cannot create report directory: {$reportDir}";
            } else {
                $this->output("✅ Created report directory: {$reportDir}");
            }
        } elseif (!is_writable($reportDir)) {
            $errors[] = "Report directory is not writable: {$reportDir}";
        } else {
            $this->output("✅ Report directory: {$reportDir}");
        }
        
        // Check PHP extensions
        $requiredExtensions = ['json', 'mbstring'];
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                $errors[] = "Required PHP extension not loaded: {$ext}";
            } else {
                $this->output("✅ PHP extension: {$ext}");
            }
        }
        
        // Check memory limit
        $memoryLimit = ini_get('memory_limit');
        $memoryBytes = $this->parseMemoryLimit($memoryLimit);
        if ($memoryBytes < 128 * 1024 * 1024) { // 128MB
            $warnings[] = "Low memory limit: {$memoryLimit} (recommended: 128M or higher)";
        } else {
            $this->output("✅ Memory limit: {$memoryLimit}");
        }
        
        // Display results
        if (!empty($warnings)) {
            $this->output("\n⚠️  Warnings:");
            foreach ($warnings as $warning) {
                $this->output("  - {$warning}");
            }
        }
        
        if (!empty($errors)) {
            $this->output("\n❌ Errors:");
            foreach ($errors as $error) {
                $this->error("  - {$error}");
            }
            return 1;
        }
        
        $this->output("\n✅ Configuration validation passed!");
        return 0;
    }
    
    /**
     * Show version information
     */
    private function showVersion(): int
    {
        $version = "1.0.0"; // This could be loaded from a version file
        $this->output("Lako & Fino CMS Audit Tool v{$version}");
        $this->output("PHP Version: " . PHP_VERSION);
        $this->output("Platform: " . PHP_OS);
        return 0;
    }
    
    /**
     * Show comprehensive help documentation
     */
    private function showHelp(): int
    {
        $this->output("Lako & Fino CMS Audit Tool");
        $this->output("==========================");
        $this->output("");
        $this->output("DESCRIPTION:");
        $this->output("  Comprehensive security and performance audit tool for the Lako & Fino CMS.");
        $this->output("  Analyzes PHP, HTML, CSS, JavaScript, and configuration files to identify");
        $this->output("  security vulnerabilities, performance issues, and code quality problems.");
        $this->output("");
        $this->output("USAGE:");
        $this->output("  php audit.php [COMMAND] [OPTIONS]");
        $this->output("");
        $this->output("COMMANDS:");
        $this->output("  audit, start    Start a new comprehensive audit (default)");
        $this->output("  resume          Resume an interrupted audit from last checkpoint");
        $this->output("  status          Show current audit status and progress");
        $this->output("  watch           Watch audit progress in real-time");
        $this->output("  config          Display current configuration settings");
        $this->output("  validate        Validate audit configuration and environment");
        $this->output("  help            Show this help message");
        $this->output("  version         Show version information");
        $this->output("");
        $this->output("OPTIONS:");
        $this->output("  --target=PATH           Target directory to audit (default: ../public_html)");
        $this->output("  --config=FILE           Load configuration from custom file");
        $this->output("  --progress-file=FILE    Custom progress file location");
        $this->output("  --report-dir=DIR        Custom report output directory");
        $this->output("  --timeout=SECONDS       Analysis timeout per file (default: 300)");
        $this->output("  --max-file-size=BYTES   Maximum file size to analyze (default: 1MB)");
        $this->output("  --format=FORMAT         Output format: text, json (default: text)");
        $this->output("  --clear-logs            Clear previous audit logs before starting");
        $this->output("");
        $this->output("FLAGS:");
        $this->output("  -v, --verbose           Enable verbose output with detailed information");
        $this->output("  -q, --quiet             Suppress non-essential output");
        $this->output("  -h, --help              Show this help message");
        $this->output("");
        $this->output("EXAMPLES:");
        $this->output("  # Start a basic audit");
        $this->output("  php audit.php");
        $this->output("");
        $this->output("  # Audit specific directory with verbose output");
        $this->output("  php audit.php audit --target=/path/to/cms --verbose");
        $this->output("");
        $this->output("  # Resume interrupted audit");
        $this->output("  php audit.php resume");
        $this->output("");
        $this->output("  # Check audit status");
        $this->output("  php audit.php status");
        $this->output("");
        $this->output("  # Watch progress in real-time");
        $this->output("  php audit.php watch");
        $this->output("");
        $this->output("  # Use custom configuration");
        $this->output("  php audit.php --config=custom-audit.json --clear-logs");
        $this->output("");
        $this->output("  # Validate setup before running");
        $this->output("  php audit.php validate");
        $this->output("");
        $this->output("REPORT FORMATS:");
        $this->output("  The audit generates reports in multiple formats:");
        $this->output("  - Markdown (.md) - Human-readable detailed report");
        $this->output("  - JSON (.json) - Machine-readable structured data");
        $this->output("  - HTML (.html) - Web-viewable formatted report");
        $this->output("");
        $this->output("EXIT CODES:");
        $this->output("  0 - Success");
        $this->output("  1 - Error or validation failure");
        
        return 0;
    }

    /**
     * Show audit configuration before starting
     */
    private function showAuditConfiguration(array $options): void
    {
        $this->output("📋 Audit Configuration:");
        $targetDir = $options['audit.target_directory'] ?? $this->config->get('audit.target_directory');
        $this->output("  Target Directory: {$targetDir}");
        $this->output("  Progress File: " . $this->config->get('audit.progress_file'));
        $this->output("  Report Directory: " . $this->config->get('audit.report_directory'));
        
        if (isset($options['audit.timeout'])) {
            $this->output("  Timeout: " . $options['audit.timeout'] . " seconds");
        }
        
        if (isset($options['audit.max_file_size'])) {
            $this->output("  Max File Size: " . $this->formatBytes($options['audit.max_file_size']));
        }
        
        $this->output("");
    }
    
    /**
     * Validate audit setup before execution
     */
    private function validateAuditSetup(array $options): bool
    {
        $targetDir = $options['audit.target_directory'] ?? $this->config->get('audit.target_directory');
        
        if (!is_dir($targetDir)) {
            $this->error("Target directory does not exist: {$targetDir}");
            return false;
        }
        
        if (!is_readable($targetDir)) {
            $this->error("Target directory is not readable: {$targetDir}");
            return false;
        }
        
        // Check if there are any files to analyze
        $fileCount = $this->countFiles($targetDir);
        if ($fileCount === 0) {
            $this->error("No files found to analyze in: {$targetDir}");
            return false;
        }
        
        if ($this->verbose) {
            $this->output("Found {$fileCount} files to analyze");
        }
        
        return true;
    }
    
    /**
     * Count files in target directory
     */
    private function countFiles(string $directory): int
    {
        $count = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }
    
    /**
     * Parse memory limit string to bytes
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $unit = strtolower(substr($limit, -1));
        $value = (int) substr($limit, 0, -1);
        
        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $limit;
        }
    }
    
    /**
     * Output message (respects quiet flag)
     */
    private function output(string $message): void
    {
        if (!$this->quiet) {
            echo $message . "\n";
        }
    }
    
    /**
     * Output error message (always shown)
     */
    private function error(string $message): void
    {
        fwrite(STDERR, "ERROR: " . $message . "\n");
    }
    
    /**
     * Output verbose message (only in verbose mode)
     */
    private function verbose(string $message): void
    {
        if ($this->verbose) {
            echo "[VERBOSE] " . $message . "\n";
        }
    }
}

// Run the CLI
$cli = new AuditCLI();
exit($cli->run($argv));