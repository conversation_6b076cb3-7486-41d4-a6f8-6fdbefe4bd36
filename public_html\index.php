<?php
require_once 'config.php'; // Includes functions.php
require_once 'includes/ad_display.php'; // Include ad display functions
require_once 'includes/ad_manager.php'; // Include ad manager if needed
require_once 'includes/ad_tracking.php'; // Include ad tracking if needed

// --- Set Homepage Meta Data ---
$page_title = SITE_NAME . ' - Recepti, Savjeti za Zdravlje i Prirodni Lijekovi';
$meta_description = 'Dobrodošli na ' . SITE_NAME . '. Pronađite najnovije ukusne recepte, korisne savjete za zdravlje, i efikasne prirodne lijekove za bolji život.';
$og_title = $page_title;
$og_description = $meta_description;
$homepageOgImageBase = 'homepage-og-image';
$homepageOgImageData = getFeaturedImageUrl($homepageOgImageBase, 'fb', 'articles');
if (!$homepageOgImageData['url']) {
    $homepageOgImageData = getDefaultOgImageData();
}
$og_image_url = $homepageOgImageData['url'];
$og_image_width = $homepageOgImageData['width'];
$og_image_height = $homepageOgImageData['height'];
$og_image_alt = $homepageOgImageData['og_alt'];
$og_url = SITE_URL . '/';
$og_type = 'website';

// --- Define Ad Context for Homepage ---
$adContext = [
    'page_type' => 'homepage', // Source page type
    'source_page_id' => null, // No specific ID for homepage
];

// --- Fetch INITIAL Data for Display ---
$initialArticles = getRecentArticles($pdo, ARTICLES_PER_PAGE);
$popularArticles = getPopularArticles($pdo, 3);
$tags = getAllTags($pdo);

// --- Include Header (which uses the meta variables) ---
include 'includes/header.php';

// Define icons and specific colors for relevant tags
$tagStyles = [
    'recepti' => ['icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" /></svg>', 'color' => 'tag-food'],
    'prirodni-lijekovi' => ['icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" /></svg>', 'color' => 'tag-health'],
    'slana-jela' => ['icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" /></svg>', 'color' => 'tag-food'],
    'slatka-jela' => ['icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" /></svg>', 'color' => 'tag-food'],
];
$genericTagIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M7 7h.01M7 3h5a2 2 0 012 2v1m-2-3h4a2 2 0 012 2v10a2 2 0 01-2 2H7a2 2 0 01-2-2V5a2 2 0 012-2m0 5h.01" /></svg>';

// Helper function (ideally move to functions.php)
function renderPopularItemWithContext($itemContext) {
    global $pdo; // Or pass PDO if not global
    $isAd = $itemContext['is_ad'];
    $item = $itemContext['ad_data'];

    if ($isAd) {
        // Affiliate Ad Data
        $adTitle = $item['title'] ?? "Sponzorirani članak";
        // Generate tracking URL using the full context
        $adTrackingUrl = generateTrackingUrl($item['id'], 'affiliate', $item['external_url'], $itemContext);
        $adUrl = escape($adTrackingUrl);
        $adImageData = !empty($item['featured_image']) ?
            getFeaturedImageUrl($item['featured_image'], 'ss', 'ads', escape($adTitle)) :
            getGenericPlaceholderData(64, 64);
        $adImageUrl = $adImageData['url'];
        $adImageAlt = $adImageData['alt'];
        $adDate = $item['published_at'] ?? date('Y-m-d H:i:s');
        $adReadingTime = $item['reading_time'] ?? null;
        $targetAttribute = !empty($item['open_in_new_tab']) ? 'target="_blank" rel="noopener noreferrer sponsored"' : 'rel="sponsored"';
        $showSponsoredLabel = !empty($item['show_sponsored_label']);

        // Impression tracking (JS is preferred)
        // recordAdImpression($pdo, $item['id'], 'affiliate', $itemContext);
    } else {
        // Actual Article Data
        $adTitle = $item['title'];
        $adUrl = SITE_URL . '/' . escape($item['slug']) . '/'; // Direct link
        $adImageData = getFeaturedImageUrl($item['featured_image'], 'ss', 'articles', escape($item['title']));
        $adImageUrl = $adImageData['url'];
        $adImageAlt = $adImageData['alt'];
        $adDate = $item['published_at'] ?? $item['created_at'];
        $adReadingTime = $item['reading_time'] ?? null;
        $targetAttribute = '';
        $showSponsoredLabel = false;
    }
    ?>
    <div class="flex gap-3">
        <?php if (!empty($item['featured_image'])): ?>
            <?php
            // Use our responsive image helper for sidebar thumbnails
            echo getResponsiveImageHtml(
                $item['featured_image'],
                $isAd ? 'ads' : 'articles',
                escape($adTitle),
                [
                    'widths' => ['xs'], // Only use the smallest size for sidebar thumbnails
                    'isPrimary' => false,
                    'lazyLoad' => true,
                    'linkUrl' => $adUrl,
                    'containerClass' => 'flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden block bg-gray-200',
                    'imgClass' => 'w-full h-full object-cover rounded-lg',
                    'usePicture' => false // Simple img with srcset is sufficient here
                ]
            );
            ?>
        <?php else: // No image available, show placeholder ?>
            <a href="<?php echo $adUrl; ?>" <?php echo $targetAttribute; ?> class="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden block bg-gray-200">
                <div class="w-full h-full flex items-center justify-center bg-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                </div>
            </a>
        <?php endif; ?>
        <div class="min-w-0">
            <h4 class="font-montserrat font-semibold text-sm line-clamp-2">
                 <a href="<?php echo $adUrl; ?>" <?php echo $targetAttribute; ?> class="hover:text-primary cursor-pointer text-dark-contrast"><?php echo escape($adTitle); ?></a>
                 <?php if ($isAd && $showSponsoredLabel): ?>
                 <span class="inline-block text-xs text-gray-500 ml-1">• Sponzorirano</span>
                 <?php endif; ?>
            </h4>
            <p class="text-xs text-gray-500 mt-1">
                <?php echo formatDate($adDate); ?>
                <?php if (!empty($adReadingTime)): ?>
                    • <?php echo escape($adReadingTime); ?> min čitanja
                <?php endif; ?>
            </p>
        </div>
    </div>
    <?php
}
?>

<div class="flex flex-col md:flex-row gap-8">

    <?php // --- Main Content Area (Articles) --- ?>
    <div class="md:w-3/4 order-2 md:order-1">

        <div class="text-center mb-8">
            <h2 class="text-2xl md:text-3xl font-montserrat font-extrabold text-primary pb-2 border-b-2 border-primary inline-block">
                Najnoviji članci
            </h2>
        </div>

        <div class="space-y-6" id="articlesContainer">
            <?php if (empty($initialArticles)): ?>
                <p class="text-center text-gray-darker">Trenutno nema objavljenih članaka.</p>
            <?php else: ?>
                <?php foreach ($initialArticles as $article):
                    $randomReaders = rand(50, 150);
                    $articleUrl = SITE_URL . '/' . escape($article['slug']) . '/';
                    $authorAvatarData = getFeaturedImageUrl($article['author_avatar'], 'ss', 'articles', escape($article['author_name'] ?? 'Autor'));
                    $authorAvatarUrl = $authorAvatarData['url'];
                ?>
                    <article class="card p-0 overflow-hidden max-h-[245px] w-full article-card-desktop" x-data="{ readers: <?php echo $randomReaders; ?>, animating: false }" x-init="setTimeout(() => { readers += Math.floor(Math.random() * 5) + 1; animating = true; setTimeout(() => animating = false, 1500) }, Math.random() * 8000 + 3000)">
                        <a href="<?php echo $articleUrl; ?>" class="w-[200px] relative overflow-hidden m-[10px] z-[1] min-h-[200px] flex-shrink-0 rounded-lg block bg-gray-100">
                           <?php if (!empty($article['featured_image'])): ?>
                               <?php
                               // Use our responsive image helper for article thumbnails
                               echo getResponsiveImageHtml(
                                   $article['featured_image'],
                                   'articles',
                                   escape($article['title']),
                                   [
                                       'widths' => ['xs', 'ss'], // Use xs and ss sizes for article thumbnails
                                       'isPrimary' => false,
                                       'lazyLoad' => true,
                                       'containerClass' => '',
                                       'imgClass' => 'absolute inset-0 w-full h-full object-cover',
                                       'sizesAttr' => '200px',
                                       'usePicture' => false
                                   ]
                               );
                               ?>
                           <?php else: ?>
                            <div class="absolute inset-0 flex items-center justify-center bg-gray-200"><span class="text-gray-500 text-xs italic">Slika nedostupna</span></div>
                           <?php endif; ?>
                        </a>
                        <div class="w-3/5 p-5 flex flex-col justify-between">
                            <div>
                                <div class="flex justify-between items-center mb-2"><span class="text-xs text-gray-medium"><?php echo formatDate($article['published_at'] ?? $article['created_at']); ?></span></div>
                                <h3 class="text-xl font-montserrat font-extrabold mb-2 line-clamp-2"><a href="<?php echo $articleUrl; ?>" class="hover:text-primary transition-colors article-title text-dark-contrast"><?php echo escape($article['title']); ?></a></h3>
                                <?php if (!empty($article['excerpt'])): ?><p class="text-gray-darker text-sm line-clamp-3"><?php echo escape($article['excerpt']); ?></p><?php endif; ?>
                            </div>
                            <div class="mt-3 flex justify-between items-center">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full overflow-hidden mr-2 bg-gray-200 flex items-center justify-center">
                                        <?php if ($authorAvatarUrl): ?>
                                            <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo $authorAvatarData['alt']; ?>" class="w-full h-full object-cover" loading="lazy" width="32" height="32"/>
                                        <?php else: ?>
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                                        <?php endif; ?>
                                    </div>
                                    <span class="text-sm font-montserrat font-semibold text-dark-contrast"><?php echo escape($article['author_name'] ?? 'Nepoznat autor'); ?></span>
                                </div>
                                <div class="flex items-center text-xs text-gray-medium gap-4 flex-nowrap whitespace-nowrap">
                                    <?php if (!empty($article['reading_time'])): ?>
                                    <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span><?php echo escape($article['reading_time']); ?> min</span></div>
                                    <?php endif; ?>
                                    <div class="flex items-center text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg><span x-text="readers + ' trenutno čita'" :class="{'reader-pulse': animating}" class="relative inline-block"></span></div>
                                </div>
                            </div>
                        </div>
                    </article>

                    <article class="card article-card-mobile" x-data="{ readers: <?php echo $randomReaders; ?>, animating: false }" x-init="setTimeout(() => { readers += Math.floor(Math.random() * 5) + 1; animating = true; setTimeout(() => animating = false, 1500) }, Math.random() * 8000 + 3000)">
                        <a href="<?php echo $articleUrl; ?>" class="mobile-article-image rounded-t-xl block bg-gray-100">
                            <?php if (!empty($article['featured_image'])): ?>
                                <?php
                                // Use our responsive image helper for mobile article thumbnails
                                echo getResponsiveImageHtml(
                                    $article['featured_image'],
                                    'articles',
                                    escape($article['title']),
                                    [
                                        'widths' => ['xs', 'ss'], // Use xs and ss sizes for mobile thumbnails
                                        'isPrimary' => false,
                                        'lazyLoad' => true,
                                        'containerClass' => '',
                                        'imgClass' => 'absolute inset-0 w-full h-full object-cover',
                                        'sizesAttr' => '100vw',
                                        'usePicture' => false
                                    ]
                                );
                                ?>
                            <?php else: ?>
                                <div class="absolute inset-0 flex items-center justify-center bg-gray-200"><span class="text-gray-500 text-xs italic">Slika nedostupna</span></div>
                            <?php endif; ?>
                        </a>
                        <div class="mobile-article-content">
                            <div>
                                <div class="flex justify-between items-center mb-2"><span class="text-xs text-gray-medium"><?php echo formatDate($article['published_at'] ?? $article['created_at']); ?></span></div>
                                <h3 class="text-lg font-montserrat font-extrabold mb-2 line-clamp-2"><a href="<?php echo $articleUrl; ?>" class="hover:text-primary transition-colors article-title text-dark-contrast"><?php echo escape($article['title']); ?></a></h3>
                                <?php if (!empty($article['excerpt'])): ?><p class="text-gray-darker text-xs md:text-sm line-clamp-2"><?php echo escape($article['excerpt']); ?></p><?php endif; ?>
                            </div>
                            <div class="mt-3 flex flex-wrap justify-between items-center">
                                <div class="flex items-center mb-2 xs:mb-0">
                                    <div class="w-6 h-6 rounded-full overflow-hidden mr-2 bg-gray-200 flex items-center justify-center">
                                        <?php if ($authorAvatarUrl): ?>
                                            <img src="<?php echo $authorAvatarUrl; ?>" alt="<?php echo $authorAvatarData['alt']; ?>" class="w-full h-full object-cover" loading="lazy" width="24" height="24"/>
                                        <?php else: ?>
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                                        <?php endif; ?>
                                    </div>
                                    <span class="text-xs font-montserrat font-semibold text-dark-contrast"><?php echo escape($article['author_name'] ?? 'Nepoznat autor'); ?></span>
                                </div>
                                <div class="flex items-center text-xs text-gray-medium gap-3">
                                    <?php if (!empty($article['reading_time'])): ?>
                                    <div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span><?php echo escape($article['reading_time']); ?> min</span></div>
                                    <?php endif; ?>
                                    <div class="flex items-center text-primary"><svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg><span x-text="readers" :class="{'reader-pulse': animating}" class="relative inline-block"></span></div>
                                </div>
                            </div>
                        </div>
                    </article>
                <?php endforeach; ?>
            <?php endif; ?>

            <div class="text-center mt-10" id="loadMoreContainer">
                <?php $moreArticlesInitially = count($initialArticles) === ARTICLES_PER_PAGE; ?>
                <button id="loadMoreBtn"
                        data-next-page="2"
                        class="btn px-8 font-montserrat font-bold <?php echo !$moreArticlesInitially ? 'hidden' : ''; ?>"
                        >
                    Učitaj više članaka
                </button>
                <p id="loadingIndicator" class="text-gray-500 italic mt-4 hidden">Učitavanje...</p>
            </div>

            <div class="mt-12 p-0 bg-transparent text-center">
                <?php
                 // Display AdSense Banner Ad, passing homepage context
                 echo displayAdsForPlacement($pdo, 'between_posts_ad', $adContext, 1, 1);
                 ?>
            </div>
        </div> <?php // End #articlesContainer ?>
    </div> <?php // End Main Content Area ?>

    <?php // --- Sidebar --- ?>
    <div class="md:w-1/4 order-1 md:order-2">
        <div class="sidebar-content md:sticky md:top-32">
            <div class="mb-6">
                <?php
                // Display AdSense Unit in Sidebar, passing homepage context
                echo displayAdsForPlacement($pdo, 'sidebar_middle', $adContext, 1, 1);
                ?>
            </div>

            <div class="mb-6">
                <?php
                // Display AdSense Unit in Sidebar (top position), passing homepage context
                echo displayAdsForPlacement($pdo, 'sidebar_top', $adContext, 1, 1);
                ?>
            </div>

            <?php if (!empty($popularArticles)): ?>
            <div class="card p-4">
                <h3 class="font-montserrat font-extrabold text-lg mb-4 text-dark-contrast">Popularni članci</h3>
                <div class="space-y-4">
                     <?php
                     // Get affiliate ads for the sidebar popular section, passing homepage context
                     $sidebarPopularAds = getActiveAdsForPlacement($pdo, 'sidebar_popular', $adContext, 1); // Get 1 ad
                     $adInserted = false;

                     foreach ($popularArticles as $index => $popArticle):
                         // Example: Insert an ad after the first popular article
                         if ($index === 0 && !empty($sidebarPopularAds) && !$adInserted) {
                             $adData = $sidebarPopularAds[0];
                             $adItemContext = $adContext; // Start with page context
                             $adItemContext['current_placement'] = 'sidebar_popular';
                             $adItemContext['click_position'] = 'sidebar_popular_1'; // Position in list
                             $adItemContext['ad_data'] = $adData;
                             $adItemContext['is_ad'] = true;
                             renderPopularItemWithContext($adItemContext); // Use the modified render function
                             $adInserted = true;
                         }

                         // Render the popular article
                         $articleItemContext = $adContext;
                         $articleItemContext['ad_data'] = $popArticle;
                         $articleItemContext['is_ad'] = false;
                         renderPopularItemWithContext($articleItemContext); // Use the modified render function
                     endforeach;
                     ?>
                </div>
            </div>
             <?php endif; ?>

             <div class="mt-6">
                <?php
                // Display AdSense Unit in Sidebar (bottom position), passing homepage context
                echo displayAdsForPlacement($pdo, 'sidebar_bottom', $adContext, 1, 1);
                ?>
             </div>

             <div class="card p-4 mt-6">
                <h3 class="font-montserrat font-extrabold text-lg mb-4 text-dark-contrast">Popularne kategorije</h3>
                <div class="space-y-2">
                    <a href="<?php echo SITE_URL; ?>/category/recepti/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Recepti</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/prirodni-lijekovi/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Prirodni lijekovi</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/slana-jela/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Slana jela</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <a href="<?php echo SITE_URL; ?>/category/slatka-jela/" class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-darker">Slatka jela</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>
        </div> <?php // End .sidebar-content ?>
    </div> <?php // End Sidebar ?>
</div> <?php // End Flex container ?>

<?php // --- Footer --- ?>
<?php // Load More Script (remains the same) ... ?>
<?php include 'includes/footer.php'; ?>
