<?php
/**
 * Admin Advertising Management Page
 *
 * Allows administrators to manage various types of advertisements:
 * - Affiliate ads (article-like ads that link to external sites)
 * - AdSense ads (configurable Google AdSense units)
 */

// Core configuration and authentication
require_once '../config.php';
require_once 'includes/auth_check.php'; // Check if admin is logged in
require_once '../includes/functions.php'; // General functions

// Set page title for the header
$admin_page_title = 'Advertising';

// --- Fetch Affiliate Ads ---
$affiliate_ads = [];
try {
    $stmt = $pdo->query("
        SELECT 
            a.id, a.title, a.featured_image, a.external_url, a.status, 
            a.created_at, a.updated_at, a.views, a.clicks, a.display_position,
            c.name as category_name
        FROM affiliate_ads a
        LEFT JOIN categories c ON a.category_id = c.id
        ORDER BY a.updated_at DESC
        LIMIT 10
    ");
    $affiliate_ads = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Process affiliate ads to handle multiple display positions
    foreach ($affiliate_ads as &$ad) {
        if (isset($ad['display_position'])) {
            // Check if it's already a JSON array
            $positionsArray = json_decode($ad['display_position'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($positionsArray)) {
                $ad['display_positions'] = $positionsArray;
            } else {
                // Single value - convert to array
                $ad['display_positions'] = [$ad['display_position']];
            }
        } else {
            $ad['display_positions'] = ['sidebar_popular']; // Default if none set
        }
    }
    unset($ad); // Unset reference

} catch (PDOException $e) {
    $error_message = "Database error fetching affiliate ads: " . $e->getMessage();
    error_log("Admin Advertising DB Error: " . $e->getMessage());
}

// --- Fetch AdSense Units ---
$adsense_units = [];
try {
    $stmt = $pdo->query("
        SELECT 
            id, name, ad_code, placement, status, 
            device_visibility, created_at, updated_at 
        FROM adsense_units
        ORDER BY placement, updated_at DESC
    ");
    $adsense_units = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Process AdSense units to handle multiple placements
    foreach ($adsense_units as &$unit) {
        if (isset($unit['placement'])) {
            // Check if it's already a JSON array
            $placementsArray = json_decode($unit['placement'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($placementsArray)) {
                $unit['placements'] = $placementsArray;
            } else {
                // Single value - convert to array
                $unit['placements'] = [$unit['placement']];
            }
        } else {
            $unit['placements'] = ['sidebar_bottom']; // Default if none set
        }
    }
    unset($unit); // Unset reference
} catch (PDOException $e) {
    $error_message = "Database error fetching AdSense units: " . $e->getMessage();
    error_log("Admin Advertising DB Error: " . $e->getMessage());
}

// Get counts of active ads
$active_affiliate_count = 0;
$active_adsense_count = 0;

foreach ($affiliate_ads as $ad) {
    if ($ad['status'] === 'active') {
        $active_affiliate_count++;
    }
}

foreach ($adsense_units as $unit) {
    if ($unit['status'] === 'active') {
        $active_adsense_count++;
    }
}

// Get placement labels for displaying in the admin interface
$placementLabels = [
    'header' => 'Header',
    'sidebar_popular' => 'Sidebar - Popular',
    'sidebar_middle' => 'Sidebar - Middle',
    'sidebar_bottom' => 'Sidebar - Bottom',
    'in_content' => 'In-Content',
    'after_content' => 'After Content',
    'article_beginning' => 'Article Beginning',
    'article_bottom_banner' => 'Article Bottom Banner',
    'footer' => 'Footer',
    'recommended' => 'Recommended Section',
    'custom' => 'Custom Position'
];

// Include the admin header
include 'includes/header.php';
?>

<header class="h-16 bg-white border-b border-border flex items-center justify-between px-6 flex-shrink-0 sticky top-0 z-30">
    <h1 class="text-xl font-montserrat font-bold text-dark"><?php echo escape($admin_page_title); ?></h1>
    <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
            <a href="ad_form.php" class="btn">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                New Affiliate Ad
            </a>
            <a href="adsense_form.php" class="btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                New AdSense Unit
            </a>
        </div>
    </div>
</header>

<div class="p-6 space-y-6">
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded-lg text-sm">
            <?php echo escape($_SESSION['success_message']); unset($_SESSION['success_message']); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error_message)): ?>
        <div class="bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm">
            <?php echo escape($error_message); ?>
        </div>
    <?php endif; ?>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="card bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 p-6">
            <h3 class="text-lg font-semibold text-blue-800 mb-2">Total Affiliate Ads</h3>
            <p class="text-4xl font-bold text-blue-900"><?php echo count($affiliate_ads); ?></p>
            <p class="text-sm text-blue-600 mt-2"><?php echo $active_affiliate_count; ?> active</p>
        </div>
        
        <div class="card bg-gradient-to-br from-green-50 to-green-100 border-green-200 p-6">
            <h3 class="text-lg font-semibold text-green-800 mb-2">Total AdSense Units</h3>
            <p class="text-4xl font-bold text-green-900"><?php echo count($adsense_units); ?></p>
            <p class="text-sm text-green-600 mt-2"><?php echo $active_adsense_count; ?> active</p>
        </div>
        
        <!-- These would typically be populated with real metrics from your database -->
        <div class="card bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200 p-6">
            <h3 class="text-lg font-semibold text-yellow-800 mb-2">Ad Clicks</h3>
            <p class="text-4xl font-bold text-yellow-900">--</p>
            <p class="text-sm text-yellow-600 mt-2">Configure tracking in settings</p>
        </div>
        
        <div class="card bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 p-6">
            <h3 class="text-lg font-semibold text-purple-800 mb-2">Ad Impressions</h3>
            <p class="text-4xl font-bold text-purple-900">--</p>
            <p class="text-sm text-purple-600 mt-2">Configure tracking in settings</p>
        </div>
    </div>
    
    <!-- Tabs for different ad types -->
    <div class="card p-0" x-data="{ activeTab: 'affiliate' }">
        <div class="border-b border-gray-200">
            <nav class="flex flex-wrap">
                <button @click="activeTab = 'affiliate'" class="px-6 py-3 text-sm font-medium" :class="activeTab === 'affiliate' ? 'border-b-2 border-primary text-primary' : 'text-gray-500 hover:text-gray-700'">
                    Affiliate Ads
                </button>
                <button @click="activeTab = 'adsense'" class="px-6 py-3 text-sm font-medium" :class="activeTab === 'adsense' ? 'border-b-2 border-primary text-primary' : 'text-gray-500 hover:text-gray-700'">
                    AdSense Units
                </button>
            </nav>
        </div>
        
        <!-- Affiliate Ads Tab -->
        <div x-show="activeTab === 'affiliate'" class="p-4">
            <div class="overflow-x-auto">
                <?php if (empty($affiliate_ads)): ?>
                    <div class="text-center py-8">
                        <p class="text-gray-500 mb-4">No affiliate ads found.</p>
                        <a href="ad_form.php" class="btn">Create your first affiliate ad</a>
                    </div>
                <?php else: ?>
                    <table class="min-w-full table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Category</th>
                                <th>External URL</th>
                                <th>Positions</th>
                                <th>Status</th>
                                <th>Views</th>
                                <th>Clicks</th>
                                <th>CTR</th>
                                <th>Last Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-border">
                            <?php foreach ($affiliate_ads as $ad): ?>
                                <tr>
                                    <td class="font-medium text-gray-900">
                                        <div class="flex items-center">
                                            <?php 
                                            // Check if there's a featured image
                                            $imageData = !empty($ad['featured_image']) ? 
                                                getFeaturedImageUrl($ad['featured_image'], 'ss', 'ads', escape($ad['title'])) : 
                                                null;
                                            ?>
                                            <?php if (!empty($imageData) && !empty($imageData['url'])): ?>
                                                <div class="w-10 h-10 rounded overflow-hidden bg-gray-200 mr-3 flex-shrink-0">
                                                    <img src="<?php echo $imageData['url']; ?>" alt="<?php echo escape($ad['title']); ?>" class="w-full h-full object-cover">
                                                </div>
                                            <?php else: ?>
                                                <div class="w-10 h-10 rounded bg-gray-200 mr-3 flex items-center justify-center flex-shrink-0">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                </div>
                                            <?php endif; ?>
                                            <a href="ad_form.php?id=<?php echo $ad['id']; ?>" class="hover:text-primary">
                                                <?php echo escape(mb_strimwidth($ad['title'], 0, 40, '...')); ?>
                                            </a>
                                        </div>
                                    </td>
                                    <td><?php echo escape($ad['category_name'] ?? 'N/A'); ?></td>
                                    <td class="max-w-xs truncate">
                                        <a href="<?php echo escape($ad['external_url']); ?>" target="_blank" class="text-blue-600 hover:underline" title="<?php echo escape($ad['external_url']); ?>">
                                            <?php echo escape(mb_strimwidth($ad['external_url'], 0, 30, '...')); ?>
                                        </a>
                                    </td>
                                    <td>
                                        <?php
                                        // Display position badges for multiple positions
                                        if (!empty($ad['display_positions'])) {
                                            $positionLabelsForAd = [];
                                            foreach($ad['display_positions'] as $position) {
                                                $positionLabelsForAd[] = $placementLabels[$position] ?? $position;
                                            }
                                            echo '<div class="flex flex-wrap gap-1">';
                                            $positionCount = count($positionLabelsForAd);
                                            $displayCount = min($positionCount, 2); // Show at most 2 badges
                                            
                                            for ($i = 0; $i < $displayCount; $i++) {
                                                echo '<span class="badge badge-info text-xs px-1.5 py-0.5">' . escape($positionLabelsForAd[$i]) . '</span>';
                                            }
                                            
                                            if ($positionCount > 2) {
                                                echo '<span class="badge badge-gray text-xs px-1.5 py-0.5" title="' . escape(implode(', ', $positionLabelsForAd)) . '">+' . ($positionCount - 2) . ' more</span>';
                                            }
                                            echo '</div>';
                                        } else {
                                            echo '<span class="text-gray-500 italic">None</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $ad['status'] === 'active' ? 'badge-success' : 'badge-gray'; ?>">
                                            <?php echo ucfirst(escape($ad['status'])); ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($ad['views'] ?? 0); ?></td>
                                    <td><?php echo number_format($ad['clicks'] ?? 0); ?></td>
                                    <td>
                                        <?php
                                        $ctr = 0;
                                        if (!empty($ad['views']) && $ad['views'] > 0 && !empty($ad['clicks'])) {
                                            $ctr = round(($ad['clicks'] / $ad['views']) * 100, 2);
                                        }
                                        echo $ctr . '%';
                                        ?>
                                    </td>
                                    <td><?php echo formatDate($ad['updated_at'], 'M j, Y'); ?></td>
                                    <td>
                                        <div class="flex space-x-2">
                                            <a href="ad_form.php?id=<?php echo $ad['id']; ?>" class="text-primary hover:text-primary/80" title="Edit">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                </svg>
                                            </a>
                                            <a href="ad_preview.php?id=<?php echo $ad['id']; ?>" class="text-blue-600 hover:text-blue-800" title="Preview">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                </svg>
                                            </a>
                                            <form action="process_ad.php" method="POST" onsubmit="return confirm('Are you sure you want to delete this ad?');" class="inline">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="ad_id" value="<?php echo $ad['id']; ?>">
                                                <button type="submit" class="text-red-600 hover:text-red-800" title="Delete">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- AdSense Units Tab -->
        <div x-show="activeTab === 'adsense'" class="p-4">
            <div class="overflow-x-auto">
                <?php if (empty($adsense_units)): ?>
                    <div class="text-center py-8">
                        <p class="text-gray-500 mb-4">No AdSense units found.</p>
                        <a href="adsense_form.php" class="btn">Create your first AdSense unit</a>
                    </div>
                <?php else: ?>
                    <table class="min-w-full table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Ad Code</th>
                                <th>Placements</th>
                                <th>Device</th>
                                <th>Status</th>
                                <th>Last Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-border">
                            <?php foreach ($adsense_units as $unit): ?>
                                <tr>
                                    <td class="font-medium text-gray-900">
                                        <a href="adsense_form.php?id=<?php echo $unit['id']; ?>" class="hover:text-primary">
                                            <?php echo escape($unit['name']); ?>
                                        </a>
                                    </td>
                                    <td class="font-mono text-sm">
                                        <?php 
                                            // Show a shortened version of the ad code
                                            $code = $unit['ad_code'] ?? '';
                                            echo escape(mb_substr($code, 0, 30) . (mb_strlen($code) > 30 ? '...' : ''));
                                        ?>
                                    </td>
                                    <td>
                                        <?php
                                        // Display placement badges for multiple placements
                                        if (!empty($unit['placements'])) {
                                            $placementLabelsForUnit = [];
                                            foreach($unit['placements'] as $placement) {
                                                $placementLabelsForUnit[] = $placementLabels[$placement] ?? $placement;
                                            }
                                            echo '<div class="flex flex-wrap gap-1">';
                                            $placementCount = count($placementLabelsForUnit);
                                            $displayCount = min($placementCount, 2); // Show at most 2 badges
                                            
                                            for ($i = 0; $i < $displayCount; $i++) {
                                                echo '<span class="badge badge-info text-xs px-1.5 py-0.5">' . escape($placementLabelsForUnit[$i]) . '</span>';
                                            }
                                            
                                            if ($placementCount > 2) {
                                                echo '<span class="badge badge-gray text-xs px-1.5 py-0.5" title="' . escape(implode(', ', $placementLabelsForUnit)) . '">+' . ($placementCount - 2) . ' more</span>';
                                            }
                                            echo '</div>';
                                        } else {
                                            echo '<span class="text-gray-500 italic">None</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php 
                                        $deviceLabels = [
                                            'all' => 'All Devices',
                                            'desktop' => 'Desktop Only',
                                            'mobile' => 'Mobile Only',
                                            'tablet' => 'Tablet Only'
                                        ];
                                        echo $deviceLabels[$unit['device_visibility']] ?? escape($unit['device_visibility']);
                                        ?>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $unit['status'] === 'active' ? 'badge-success' : 'badge-gray'; ?>">
                                            <?php echo ucfirst(escape($unit['status'])); ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatDate($unit['updated_at'], 'M j, Y'); ?></td>
                                    <td>
                                        <div class="flex space-x-2">
                                            <a href="adsense_form.php?id=<?php echo $unit['id']; ?>" class="text-primary hover:text-primary/80" title="Edit">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                </svg>
                                            </a>
                                            <form action="process_adsense.php" method="POST" onsubmit="return confirm('Are you sure you want to delete this AdSense unit?');" class="inline">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="adsense_id" value="<?php echo $unit['id']; ?>">
                                                <button type="submit" class="text-red-600 hover:text-red-800" title="Delete">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Quick Reference Info Card -->
    <div x-data="{ open: false }" class="card">
        <div class="flex justify-between items-center cursor-pointer" @click="open = !open">
            <h3 class="text-lg font-semibold text-gray-800">Advertising Quick Guide</h3>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transform transition-transform" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
        </div>
        
        <div x-show="open" class="mt-4 space-y-4" x-transition style="display: none;">
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-montserrat font-semibold text-base mb-2 border-b border-gray-200 pb-1">Affiliate Ads</h4>
                    <ul class="list-disc list-inside text-sm space-y-2">
                        <li><span class="font-medium">Appearance:</span> Look like regular articles but redirect to external sites</li>
                        <li><span class="font-medium">Best use:</span> For promoting affiliate products relevant to your content</li>
                        <li><span class="font-medium">Placement:</span> In article listings, sidebar, recommended sections</li>
                        <li><span class="font-medium">Tips:</span> Use high-quality images and compelling titles that match your site's style</li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-montserrat font-semibold text-base mb-2 border-b border-gray-200 pb-1">AdSense Units</h4>
                    <ul class="list-disc list-inside text-sm space-y-2">
                        <li><span class="font-medium">Appearance:</span> Standard Google AdSense ad formats</li>
                        <li><span class="font-medium">Best use:</span> For monetizing with contextual advertising</li>
                        <li><span class="font-medium">Placement:</span> Headers, sidebars, within content, after content</li>
                        <li><span class="font-medium">Tips:</span> Don't overload pages with too many ads; test different formats and placements</li>
                    </ul>
                </div>
            </div>
            
            <div class="bg-blue-50 border border-blue-200 p-3 rounded-lg text-sm">
                <div class="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                        <p class="font-medium text-blue-800 mb-1">Available Placement Positions:</p>
                        <ul class="list-disc list-inside text-blue-700 space-y-1">
                            <li><strong>sidebar_popular</strong> - Appears in the sidebar popular section</li>
                            <li><strong>sidebar_middle</strong> - Appears in the middle of the sidebar</li>
                            <li><strong>sidebar_bottom</strong> - Appears at the bottom of the sidebar</li>
                            <li><strong>in_content</strong> - Appears within article content</li>
                            <li><strong>after_content</strong> - Appears after article content (after tags)</li>
                            <li><strong>article_beginning</strong> - Appears at the beginning of articles</li>
                            <li><strong>article_bottom_banner</strong> - Appears as a banner at the bottom of articles</li>
                            <li><strong>recommended</strong> - Appears in the recommended articles section</li>
                            <li><strong>header</strong> - Appears in the header area</li>
                            <li><strong>footer</strong> - Appears in the footer area</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="bg-yellow-50 border border-yellow-200 p-3 rounded-lg text-sm">
                <div class="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                        <p class="font-medium text-yellow-800 mb-1">Important Notes:</p>
                        <ul class="list-disc list-inside text-yellow-700 space-y-1">
                            <li>Clearly label sponsored or affiliate content to comply with regulations</li>
                            <li>Review and follow Google AdSense policies to avoid account suspension</li>
                            <li>Consider user experience - too many ads can drive visitors away</li>
                            <li>Test ad performance regularly and adjust placement and formats accordingly</li>
                            <li>You can now select multiple positions for each ad to display in different locations</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>