<?php
/**
 * Ad Targeting - User/Content Targeting Logic
 *
 * Determines if an ad should be shown based on various rules.
 */

// Prevent direct access
if (!defined('ABSPATH') && !defined('SITE_URL')) {
    if (file_exists('../config.php')) {
        require_once '../config.php';
    } else {
        exit('Direct script access denied.');
    }
}

/**
 * Checks if an ad should be displayed based on various targeting rules.
 *
 * @param array $adData Ad unit data (must include targeting rules like status, dates, device visibility, page types etc.).
 * @param array $context Contextual information (e.g., ['page_type' => 'article', 'category_id' => 5, 'tag_ids' => [1, 7]]).
 * @return bool True if the ad should be displayed, false otherwise.
 */
function shouldDisplayAd(array $adData, array $context = []): bool {
    // 1. Basic Status Check
    if (empty($adData) || !isset($adData['status']) || $adData['status'] !== 'active') {
        return false; // Don't display inactive or invalid ads
    }

    // 2. Scheduling Check
    $now = time();
    // Check start date
    if (!empty($adData['start_date'])) {
        $startTime = strtotime($adData['start_date']);
        // Check if strtotime succeeded and if the current time is before the start time
        if ($startTime !== false && $now < $startTime) {
            return false; // Not started yet
        }
    }
    // Check end date
    if (!empty($adData['end_date'])) {
        $endTime = strtotime($adData['end_date']);
        // Check if strtotime succeeded and if the current time is after the end time
        if ($endTime !== false && $now > $endTime) {
            return false; // Expired
        }
    }

    // 3. Device Targeting Check
    // Use specific visibility columns if they exist (e.g., from affiliate_ads)
    $showDesktop = (bool)($adData['desktop_visibility'] ?? true); // Default to true if not set
    $showMobile = (bool)($adData['mobile_visibility'] ?? true);
    $showTablet = (bool)($adData['tablet_visibility'] ?? true);

    // Or use the general 'device_visibility' if specific ones aren't present (e.g., from adsense_units)
    $generalVisibility = $adData['device_visibility'] ?? 'all';

    $isMobile = isMobileDevice();
    $isTablet = isTabletDevice();
    $isDesktop = !$isMobile && !$isTablet;

    if ($generalVisibility !== 'all') { // Check general setting first
        if (($generalVisibility === 'desktop' && !$isDesktop) ||
            ($generalVisibility === 'mobile' && !$isMobile) ||
            ($generalVisibility === 'tablet' && !$isTablet)) {
            return false;
        }
    } else { // Check specific settings if general is 'all' or not present
         if ((!$showDesktop && $isDesktop) ||
             (!$showMobile && $isMobile) ||
             (!$showTablet && $isTablet)) {
            return false;
         }
    }

    // 4. Page Type Targeting Check (Requires context)
    $currentPageType = $context['page_type'] ?? 'unknown'; // e.g., 'homepage', 'article', 'category', 'tag'
    // Assumes boolean columns like 'show_on_homepage', 'show_on_articles', etc. exist in adData
    $showOnHomepage = (bool)($adData['show_on_homepage'] ?? true); // Default true if not set
    $showOnArticles = (bool)($adData['show_on_articles'] ?? true);
    $showOnCategories = (bool)($adData['show_on_categories'] ?? true);
    $showOnTags = (bool)($adData['show_on_tags'] ?? true);
    // Add other page types as needed (e.g., search results)

    if (($currentPageType === 'homepage' && !$showOnHomepage) ||
        ($currentPageType === 'article' && !$showOnArticles) ||
        ($currentPageType === 'category' && !$showOnCategories) ||
        ($currentPageType === 'tag' && !$showOnTags)) {
       return false; // Ad is disabled for this page type
    }

    // 5. Category Targeting (Requires context & ad data with category rules)
    $currentCategoryId = $context['category_id'] ?? null;
    // Assumes ad data might have 'include_categories' and 'exclude_categories' (comma-separated IDs or JSON array)
    $includeCategories = parseIdList($adData['include_categories'] ?? '');
    $excludeCategories = parseIdList($adData['exclude_categories'] ?? '');

    if ($currentCategoryId) {
        // If specific categories are targeted, the current one MUST be included
        if (!empty($includeCategories) && !in_array($currentCategoryId, $includeCategories)) {
            return false;
        }
        // If specific categories are excluded, the current one MUST NOT be excluded
        if (!empty($excludeCategories) && in_array($currentCategoryId, $excludeCategories)) {
            return false;
        }
    }

    // 6. Tag Targeting (Requires context & ad data with tag rules)
    $currentTagIds = $context['tag_ids'] ?? []; // Expects an array of tag IDs
    // Assumes ad data might have 'include_tags' and 'exclude_tags'
    $includeTags = parseIdList($adData['include_tags'] ?? '');
    $excludeTags = parseIdList($adData['exclude_tags'] ?? '');

    if (!empty($currentTagIds)) {
        // If specific tags are targeted, at least one current tag MUST match
        if (!empty($includeTags) && count(array_intersect($currentTagIds, $includeTags)) === 0) {
            return false;
        }
        // If specific tags are excluded, NONE of the current tags should be excluded
        if (!empty($excludeTags) && count(array_intersect($currentTagIds, $excludeTags)) > 0) {
            return false;
        }
    }

    // 7. Custom Targeting (Example: Geo-location - Requires GeoIP lookup)
    // $targetCountries = parseCsvList($adData['target_countries'] ?? '');
    // $excludeCountries = parseCsvList($adData['exclude_countries'] ?? '');
    // $userCountry = getUserCountryCode(); // Assume this function exists

    // if (!empty($targetCountries) && !in_array($userCountry, $targetCountries)) {
    //     return false;
    // }
    // if (!empty($excludeCountries) && in_array($userCountry, $excludeCountries)) {
    //     return false;
    // }

    // If all checks passed, the ad should be displayed
    return true;
}

/**
 * Helper function to parse comma-separated lists of IDs or codes.
 *
 * @param string $listString Comma-separated string.
 * @return array Array of trimmed, non-empty values.
 */
function parseCsvList(string $listString): array {
    if (empty($listString)) return [];
    return array_filter(array_map('trim', explode(',', $listString)));
}

/**
* Helper function to parse comma-separated lists of IDs into an array of integers.
* Also handles JSON arrays if stored that way.
*
* @param string|null $listString Comma-separated string or JSON array string.
* @return array Array of integers.
*/
function parseIdList(?string $listString): array {
   if (empty($listString)) return [];

   // Check if it looks like a JSON array
   if (strpos(trim($listString), '[') === 0 && strpos(trim($listString), ']') === strlen(trim($listString))-1) {
       $decoded = json_decode($listString, true);
       if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
           return array_map('intval', array_filter($decoded, 'is_numeric'));
       }
   }

   // Fallback to comma-separated parsing
   $ids = array_filter(array_map('trim', explode(',', $listString)), 'is_numeric');
   return array_map('intval', $ids);
}


/**
 * Placeholder function for Mobile Device Detection.
 * Consider using a library like Mobile_Detect for better accuracy.
 * @return bool
 */
function isMobileDevice(): bool {
    if (!isset($_SERVER['HTTP_USER_AGENT'])) return false;
    // Basic check - Refine this for better accuracy
    return (bool) preg_match('/(android|iphone|ipod|blackberry|iemobile|opera mini)/i', $_SERVER['HTTP_USER_AGENT']);
}

/**
 * Placeholder function for Tablet Device Detection.
 * Needs refinement.
 * @return bool
 */
function isTabletDevice(): bool {
    if (!isset($_SERVER['HTTP_USER_AGENT'])) return false;
    // Basic check - might include large phones
    return (bool) preg_match('/(ipad|tablet|nexus 7|nexus 10|galaxy tab)/i', $_SERVER['HTTP_USER_AGENT']) && !isMobileDevice();
}

/**
 * Placeholder function to get User Country Code.
 * Requires GeoIP lookup (e.g., using MaxMind GeoIP2 database or an API).
 * @return string|null ISO country code (e.g., 'US', 'GB', 'BA') or null.
 */
function getUserCountryCode(): ?string {
    // Example using common server variables (less reliable)
    if (isset($_SERVER['HTTP_CF_IPCOUNTRY'])) { // Cloudflare
        return strtoupper($_SERVER['HTTP_CF_IPCOUNTRY']);
    }
    // Add checks for other proxy headers or implement GeoIP lookup here
    return null; // Unknown
}

?>