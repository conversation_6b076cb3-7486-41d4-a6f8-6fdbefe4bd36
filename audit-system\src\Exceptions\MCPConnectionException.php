<?php

namespace AuditSystem\Exceptions;

/**
 * Exception thrown when MCP server connection or communication fails
 */
class MCPConnectionException extends AuditException
{


    /**
     * Create exception for connection timeout
     *
     * @param int $timeout Timeout value in seconds
     * @return static
     */
    public static function timeout(int $timeout): self
    {
        return new self("MCP server connection timed out after {$timeout} seconds");
    }

    /**
     * Create exception for server unavailable
     *
     * @param string $serverUrl Server URL that was unavailable
     * @return static
     */
    public static function serverUnavailable(string $serverUrl): self
    {
        return new self("MCP server at '{$serverUrl}' is unavailable");
    }

    /**
     * Create exception for invalid response
     *
     * @param string $response Invalid response received
     * @return static
     */
    public static function invalidResponse(string $response): self
    {
        return new self("Invalid response from MCP server: {$response}");
    }

    /**
     * Create exception for authentication failure
     *
     * @return static
     */
    public static function authenticationFailed(): self
    {
        return new self("Authentication with MCP server failed");
    }
}