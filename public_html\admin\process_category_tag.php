<?php
require_once '../config.php'; // Includes DB constants, functions.php, session_start() etc.
require_once 'includes/auth_check.php'; // Check if admin is logged in
require_once '../includes/functions.php'; // General functions (like generateSlug)

// --- Check Request Method ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: manage_categories_tags.php');
    exit;
}

// --- Ensure Session is Started ---
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// --- Get Action ---
$action = $_POST['action'] ?? null;

// --- Helper Function to Get PDO Connection ---
function getDbConnection() {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $options = [ PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC, PDO::ATTR_EMULATE_PREPARES => false, ];
    try {
        return new PDO($dsn, DB_USER, DB_PASS, $options);
    } catch (\PDOException $e) {
        error_log("PDO Connection failed in process_category_tag.php: " . $e->getMessage());
        $_SESSION['error_message'] = "Database connection failed. Please check server logs.";
        header('Location: manage_categories_tags.php');
        exit;
    }
}

$pdo = getDbConnection();

try {
    switch ($action) {
        // --- Add Category ---
        case 'add_category':
            $category_name = trim($_POST['category_name'] ?? '');
            if (empty($category_name)) {
                $_SESSION['error_message'] = "Category name cannot be empty.";
                break;
            }
            // *** ADDED: Generate slug for category ***
            $category_slug = generateSlug($category_name); // Assuming generateSlug function exists

            // Check if category already exists by name or slug
            $stmt_check = $pdo->prepare("SELECT id FROM categories WHERE name = :name OR slug = :slug");
            $stmt_check->bindParam(':name', $category_name, PDO::PARAM_STR);
            $stmt_check->bindParam(':slug', $category_slug, PDO::PARAM_STR); // *** ADDED: Check slug too ***
            $stmt_check->execute();
            if ($stmt_check->fetch()) {
                 $_SESSION['error_message'] = "Category '{$category_name}' already exists (or has a conflicting slug).";
                 break;
            }

            // Insert new category with name and slug
            // *** MODIFIED: Added slug column and parameter ***
            $stmt_insert = $pdo->prepare("INSERT INTO categories (name, slug) VALUES (:name, :slug)");
            $stmt_insert->bindParam(':name', $category_name, PDO::PARAM_STR);
            $stmt_insert->bindParam(':slug', $category_slug, PDO::PARAM_STR); // *** ADDED: Bind slug ***
            $stmt_insert->execute();

            $_SESSION['success_message'] = "Category '{$category_name}' added successfully.";
            break;

        // --- Delete Category ---
        case 'delete_category':
            $category_id = filter_input(INPUT_POST, 'category_id', FILTER_VALIDATE_INT);
            if (!$category_id) {
                 $_SESSION['error_message'] = "Invalid Category ID.";
                 break;
            }
            // Optional: Check if category is used by articles before deleting
            // $stmt_check_usage = $pdo->prepare("SELECT COUNT(*) FROM articles WHERE category_id = :id");
            // $stmt_check_usage->bindParam(':id', $category_id, PDO::PARAM_INT);
            // $stmt_check_usage->execute();
            // if ($stmt_check_usage->fetchColumn() > 0) {
            //    $_SESSION['error_message'] = "Cannot delete category: It is currently assigned to one or more articles.";
            //    break;
            // }

            // Delete category
            $stmt_delete = $pdo->prepare("DELETE FROM categories WHERE id = :id");
            $stmt_delete->bindParam(':id', $category_id, PDO::PARAM_INT);
            $stmt_delete->execute();
             if ($stmt_delete->rowCount() > 0) {
                $_SESSION['success_message'] = "Category deleted successfully.";
            } else {
                $_SESSION['error_message'] = "Category not found or could not be deleted.";
            }
            break;

        // --- Add Tag ---
        case 'add_tag':
            $tag_name = trim($_POST['tag_name'] ?? '');
            if (empty($tag_name)) {
                $_SESSION['error_message'] = "Tag name cannot be empty.";
                break;
            }
             // Check if tag already exists by name or slug
            $tag_slug = generateSlug($tag_name); // Assuming generateSlug function exists
            $stmt_check = $pdo->prepare("SELECT id FROM tags WHERE name = :name OR slug = :slug");
            $stmt_check->bindParam(':name', $tag_name, PDO::PARAM_STR);
            $stmt_check->bindParam(':slug', $tag_slug, PDO::PARAM_STR);
            $stmt_check->execute();
            if ($stmt_check->fetch()) {
                 $_SESSION['error_message'] = "Tag '{$tag_name}' already exists (or has a conflicting slug).";
                 break;
            }
             // Insert new tag
            $stmt_insert = $pdo->prepare("INSERT INTO tags (name, slug) VALUES (:name, :slug)");
            $stmt_insert->bindParam(':name', $tag_name, PDO::PARAM_STR);
            $stmt_insert->bindParam(':slug', $tag_slug, PDO::PARAM_STR);
            $stmt_insert->execute();
            $_SESSION['success_message'] = "Tag '{$tag_name}' added successfully.";
            break;

        // --- Delete Tag ---
        case 'delete_tag':
             $tag_id = filter_input(INPUT_POST, 'tag_id', FILTER_VALIDATE_INT);
             if (!$tag_id) {
                 $_SESSION['error_message'] = "Invalid Tag ID.";
                 break;
             }
             // Need to delete relationships first due to potential foreign key constraints
             $pdo->beginTransaction();
             try {
                 // Delete from linking table
                 $stmt_delete_relations = $pdo->prepare("DELETE FROM article_tags WHERE tag_id = :tag_id");
                 $stmt_delete_relations->bindParam(':tag_id', $tag_id, PDO::PARAM_INT);
                 $stmt_delete_relations->execute();

                 // Delete the tag itself
                 $stmt_delete_tag = $pdo->prepare("DELETE FROM tags WHERE id = :id");
                 $stmt_delete_tag->bindParam(':id', $tag_id, PDO::PARAM_INT);
                 $stmt_delete_tag->execute();

                 $pdo->commit();

                 if ($stmt_delete_tag->rowCount() > 0) {
                    $_SESSION['success_message'] = "Tag deleted successfully and removed from articles.";
                 } else {
                    $_SESSION['error_message'] = "Tag not found or could not be deleted.";
                 }
             } catch (PDOException $e) {
                 $pdo->rollBack();
                 error_log("Error deleting tag ID {$tag_id}: " . $e->getMessage());
                 $_SESSION['error_message'] = "Error deleting tag: " . $e->getMessage();
             }
             break;

        // --- Default Case ---
        default:
            $_SESSION['error_message'] = "Invalid action specified.";
            break;
    }

} catch (PDOException $e) {
    // Catch any database errors during operations
    error_log("Database error in process_category_tag.php (Action: {$action}): " . $e->getMessage());
    // Provide a slightly more specific error message if it's the known slug issue
    if (strpos($e->getMessage(), 'slug') !== false && strpos($e->getMessage(), 'doesn\'t have a default value') !== false) {
        $_SESSION['error_message'] = "Database error: The 'slug' field likely needs a value. Please contact support if the issue persists.";
    } else {
        $_SESSION['error_message'] = "A database error occurred: " . $e->getMessage();
    }

     // Rollback if transaction was started (though only delete_tag uses one currently)
     if ($pdo->inTransaction()) {
         try { $pdo->rollBack(); } catch (Exception $rbEx) {}
     }
} finally {
    $pdo = null; // Close connection
}

// --- Redirect Back ---
header('Location: manage_categories_tags.php');
exit;
?>