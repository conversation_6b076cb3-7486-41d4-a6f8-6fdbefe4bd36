<?php

namespace AuditSystem\Tests\Integration;

use PHPUnit\Framework\TestCase;
use AuditSystem\Analyzers\PerformanceAnalyzer;
use AuditSystem\Models\Finding;

/**
 * Performance Bottleneck Validation Test Suite
 * 
 * Tests detection of known performance issues in the CMS codebase
 */
class PerformanceBottleneckValidationTest extends TestCase
{
    private PerformanceAnalyzer $performanceAnalyzer;
    private string $cmsPath;

    protected function setUp(): void
    {
        parent::setUp();
        $this->performanceAnalyzer = new PerformanceAnalyzer();
        $this->cmsPath = dirname(__DIR__, 3) . '/public_html';
    }

    /**
     * Test detection of N+1 query problems
     * 
     * Known issue: Multiple database queries in loops
     */
    public function testDetectsN1QueryProblems(): void
    {
        $indexPath = $this->cmsPath . '/index.php';
        $this->assertFileExists($indexPath);
        
        $content = file_get_contents($indexPath);
        
        // Look for patterns that might indicate N+1 queries
        $hasLoops = (strpos($content, 'foreach') !== false || 
                    strpos($content, 'for(') !== false ||
                    strpos($content, 'while') !== false);
        
        $hasQueries = (strpos($content, '$pdo->') !== false ||
                      strpos($content, 'prepare(') !== false ||
                      strpos($content, 'query(') !== false);
        
        if ($hasLoops && $hasQueries) {
            $findings = $this->performanceAnalyzer->analyzeFile($indexPath);
            
            $n1QueryFindings = array_filter($findings, function($finding) {
                return $finding->getType() === 'performance' &&
                       (strpos($finding->getDescription(), 'N+1') !== false ||
                        strpos($finding->getDescription(), 'query in loop') !== false ||
                        strpos($finding->getDescription(), 'multiple queries') !== false);
            });
            
            $this->assertNotEmpty($n1QueryFindings, 
                'Should detect potential N+1 query problems');
        } else {
            $this->markTestSkipped('No loop+query patterns found to test N+1 detection');
        }
    }

    /**
     * Test detection of missing database indexes
     * 
     * Analyze queries for potential missing indexes
     */
    public function testDetectsMissingDatabaseIndexes(): void
    {
        $functionsPath = $this->cmsPath . '/includes/functions.php';
        $this->assertFileExists($functionsPath);
        
        $content = file_get_contents($functionsPath);
        
        // Look for WHERE clauses that might need indexes
        $hasWhereClause = (strpos($content, 'WHERE') !== false ||
                          strpos($content, 'where') !== false);
        
        if ($hasWhereClause) {
            $findings = $this->performanceAnalyzer->analyzeFile($functionsPath);
            
            $indexFindings = array_filter($findings, function($finding) {
                return $finding->getType() === 'performance' &&
                       (strpos($finding->getDescription(), 'index') !== false ||
                        strpos($finding->getDescription(), 'WHERE clause') !== false ||
                        strpos($finding->getDescription(), 'query optimization') !== false);
            });
            
            // May or may not find index issues - this validates detection capability
            $this->assertIsArray($indexFindings);
        }
    }

    /**
     * Test detection of inefficient image processing
     * 
     * Known issue: Large images processed without optimization
     */
    public function testDetectsInefficiientImageProcessing(): void
    {
        $articlePath = $this->cmsPath . '/article.php';
        $imagePath = $this->cmsPath . '/image.php';
        
        $files = [$articlePath, $imagePath];
        $imageFindings = [];
        
        foreach ($files as $file) {
            if (!file_exists($file)) continue;
            
            $content = file_get_contents($file);
            
            // Look for image processing patterns
            $hasImageProcessing = (strpos($content, 'image') !== false ||
                                  strpos($content, 'getimagesize') !== false ||
                                  strpos($content, 'imagecreatefrom') !== false ||
                                  strpos($content, 'resize') !== false);
            
            if ($hasImageProcessing) {
                $findings = $this->performanceAnalyzer->analyzeFile($file);
                
                $fileImageFindings = array_filter($findings, function($finding) {
                    return $finding->getType() === 'performance' &&
                           (strpos($finding->getDescription(), 'image') !== false ||
                            strpos($finding->getDescription(), 'optimization') !== false ||
                            strpos($finding->getDescription(), 'resize') !== false ||
                            strpos($finding->getDescription(), 'compression') !== false);
                });
                
                $imageFindings = array_merge($imageFindings, $fileImageFindings);
            }
        }
        
        $this->assertIsArray($imageFindings);
        
        if (!empty($imageFindings)) {
            $this->assertGreaterThan(0, count($imageFindings), 
                'Should detect image processing performance issues');
        }
    }

    /**
     * Test detection of missing caching mechanisms
     * 
     * Look for repeated expensive operations without caching
     */
    public function testDetectsMissingCaching(): void
    {
        $indexPath = $this->cmsPath . '/index.php';
        $articlePath = $this->cmsPath . '/article.php';
        
        $files = [$indexPath, $articlePath];
        $cachingFindings = [];
        
        foreach ($files as $file) {
            if (!file_exists($file)) continue;
            
            $content = file_get_contents($file);
            
            // Look for expensive operations that could benefit from caching
            $hasExpensiveOps = (strpos($content, 'getRecentArticles') !== false ||
                               strpos($content, 'getPopularArticles') !== false ||
                               strpos($content, 'SELECT') !== false ||
                               strpos($content, 'file_get_contents') !== false);
            
            // Check if caching is implemented
            $hasCaching = (strpos($content, 'cache') !== false ||
                          strpos($content, 'memcache') !== false ||
                          strpos($content, 'redis') !== false ||
                          strpos($content, 'apc') !== false);
            
            if ($hasExpensiveOps && !$hasCaching) {
                $findings = $this->performanceAnalyzer->analyzeFile($file);
                
                $fileCachingFindings = array_filter($findings, function($finding) {
                    return $finding->getType() === 'performance' &&
                           (strpos($finding->getDescription(), 'cach') !== false ||
                            strpos($finding->getDescription(), 'repeated') !== false ||
                            strpos($finding->getDescription(), 'expensive') !== false);
                });
                
                $cachingFindings = array_merge($cachingFindings, $fileCachingFindings);
            }
        }
        
        $this->assertIsArray($cachingFindings);
    }

    /**
     * Test detection of inefficient asset loading
     * 
     * Check for CSS/JS loading optimization issues
     */
    public function testDetectsInefficiientAssetLoading(): void
    {
        $headerPath = $this->cmsPath . '/includes/header.php';
        
        if (!file_exists($headerPath)) {
            $this->markTestSkipped('Header file not found');
        }
        
        $content = file_get_contents($headerPath);
        
        // Look for CSS/JS loading patterns
        $hasAssets = (strpos($content, '<link') !== false ||
                     strpos($content, '<script') !== false ||
                     strpos($content, '.css') !== false ||
                     strpos($content, '.js') !== false);
        
        if ($hasAssets) {
            $findings = $this->performanceAnalyzer->analyzeFile($headerPath);
            
            $assetFindings = array_filter($findings, function($finding) {
                return $finding->getType() === 'performance' &&
                       (strpos($finding->getDescription(), 'asset') !== false ||
                        strpos($finding->getDescription(), 'CSS') !== false ||
                        strpos($finding->getDescription(), 'JavaScript') !== false ||
                        strpos($finding->getDescription(), 'loading') !== false ||
                        strpos($finding->getDescription(), 'minif') !== false);
            });
            
            $this->assertIsArray($assetFindings);
        }
    }

    /**
     * Test detection of memory usage issues
     * 
     * Look for potential memory leaks or excessive usage
     */
    public function testDetectsMemoryUsageIssues(): void
    {
        $functionsPath = $this->cmsPath . '/includes/functions.php';
        $content = file_get_contents($functionsPath);
        
        // Look for patterns that might cause memory issues
        $hasLargeArrays = (strpos($content, 'array_merge') !== false ||
                          strpos($content, 'file_get_contents') !== false ||
                          strpos($content, 'fetchAll') !== false);
        
        if ($hasLargeArrays) {
            $findings = $this->performanceAnalyzer->analyzeFile($functionsPath);
            
            $memoryFindings = array_filter($findings, function($finding) {
                return $finding->getType() === 'performance' &&
                       (strpos($finding->getDescription(), 'memory') !== false ||
                        strpos($finding->getDescription(), 'large array') !== false ||
                        strpos($finding->getDescription(), 'fetchAll') !== false);
            });
            
            $this->assertIsArray($memoryFindings);
        }
    }

    /**
     * Test detection of slow database queries
     * 
     * Analyze query patterns for potential performance issues
     */
    public function testDetectsSlowDatabaseQueries(): void
    {
        $functionsPath = $this->cmsPath . '/includes/functions.php';
        $content = file_get_contents($functionsPath);
        
        // Look for complex query patterns
        $hasComplexQueries = (strpos($content, 'JOIN') !== false ||
                             strpos($content, 'ORDER BY') !== false ||
                             strpos($content, 'GROUP BY') !== false ||
                             strpos($content, 'LIKE') !== false);
        
        if ($hasComplexQueries) {
            $findings = $this->performanceAnalyzer->analyzeFile($functionsPath);
            
            $queryFindings = array_filter($findings, function($finding) {
                return $finding->getType() === 'performance' &&
                       (strpos($finding->getDescription(), 'query') !== false ||
                        strpos($finding->getDescription(), 'JOIN') !== false ||
                        strpos($finding->getDescription(), 'LIKE') !== false ||
                        strpos($finding->getDescription(), 'slow') !== false);
            });
            
            $this->assertIsArray($queryFindings);
        }
    }

    /**
     * Test detection of inefficient file operations
     * 
     * Look for file I/O that could be optimized
     */
    public function testDetectsInefficiientFileOperations(): void
    {
        $files = [
            $this->cmsPath . '/index.php',
            $this->cmsPath . '/article.php',
            $this->cmsPath . '/includes/functions.php'
        ];
        
        $fileOpFindings = [];
        
        foreach ($files as $file) {
            if (!file_exists($file)) continue;
            
            $content = file_get_contents($file);
            
            // Look for file operations
            $hasFileOps = (strpos($content, 'file_get_contents') !== false ||
                          strpos($content, 'file_exists') !== false ||
                          strpos($content, 'fopen') !== false ||
                          strpos($content, 'readfile') !== false);
            
            if ($hasFileOps) {
                $findings = $this->performanceAnalyzer->analyzeFile($file);
                
                $fileFindings = array_filter($findings, function($finding) {
                    return $finding->getType() === 'performance' &&
                           (strpos($finding->getDescription(), 'file') !== false ||
                            strpos($finding->getDescription(), 'I/O') !== false ||
                            strpos($finding->getDescription(), 'disk') !== false);
                });
                
                $fileOpFindings = array_merge($fileOpFindings, $fileFindings);
            }
        }
        
        $this->assertIsArray($fileOpFindings);
    }

    /**
     * Test comprehensive performance analysis
     * 
     * Run performance analysis on all critical files
     */
    public function testComprehensivePerformanceAnalysis(): void
    {
        $criticalFiles = [
            'index.php',
            'article.php',
            'includes/functions.php',
            'process_comment.php'
        ];
        
        $totalPerformanceFindings = 0;
        $highImpactFindings = 0;
        $mediumImpactFindings = 0;
        
        $results = [];
        
        foreach ($criticalFiles as $file) {
            $filePath = $this->cmsPath . '/' . $file;
            
            if (!file_exists($filePath)) {
                continue;
            }
            
            $findings = $this->performanceAnalyzer->analyzeFile($filePath);
            $performanceFindings = array_filter($findings, function($finding) {
                return $finding->getType() === 'performance';
            });
            
            $fileHighImpact = 0;
            $fileMediumImpact = 0;
            
            foreach ($performanceFindings as $finding) {
                $totalPerformanceFindings++;
                
                if ($finding->getSeverity() === 'high') {
                    $highImpactFindings++;
                    $fileHighImpact++;
                } elseif ($finding->getSeverity() === 'medium') {
                    $mediumImpactFindings++;
                    $fileMediumImpact++;
                }
            }
            
            $results[$file] = [
                'total_findings' => count($performanceFindings),
                'high_impact' => $fileHighImpact,
                'medium_impact' => $fileMediumImpact
            ];
        }
        
        // Should find some performance issues
        $this->assertGreaterThan(0, $totalPerformanceFindings, 
            'Should find performance issues in CMS files');
        
        echo "\nPerformance Analysis Results:\n";
        echo "Total Performance Findings: $totalPerformanceFindings\n";
        echo "High Impact Findings: $highImpactFindings\n";
        echo "Medium Impact Findings: $mediumImpactFindings\n";
        
        foreach ($results as $file => $data) {
            echo "$file: {$data['total_findings']} findings ({$data['high_impact']} high, {$data['medium_impact']} medium)\n";
        }
    }

    /**
     * Test performance regression detection
     * 
     * Ensure known performance issues are consistently detected
     */
    public function testPerformanceRegressionDetection(): void
    {
        // Test specific known performance patterns
        
        // 1. Check for image processing without optimization
        $articlePath = $this->cmsPath . '/article.php';
        if (file_exists($articlePath)) {
            $content = file_get_contents($articlePath);
            
            // Look for image processing patterns
            $hasImageProcessing = (strpos($content, 'getResponsiveImageHtml') !== false ||
                                  strpos($content, 'getFeaturedImageUrl') !== false);
            
            if ($hasImageProcessing) {
                $findings = $this->performanceAnalyzer->analyzeFile($articlePath);
                
                // Should detect some image-related performance considerations
                $imageFindings = array_filter($findings, function($finding) {
                    return strpos($finding->getDescription(), 'image') !== false;
                });
                
                $this->assertIsArray($imageFindings, 
                    'Should analyze image processing performance');
            }
        }
        
        // 2. Check for database query patterns
        $indexPath = $this->cmsPath . '/index.php';
        if (file_exists($indexPath)) {
            $content = file_get_contents($indexPath);
            
            // Look for multiple database calls
            $queryCount = substr_count($content, '$pdo->') + 
                         substr_count($content, 'prepare(') + 
                         substr_count($content, 'query(');
            
            if ($queryCount > 3) {
                $findings = $this->performanceAnalyzer->analyzeFile($indexPath);
                
                // Should detect potential query optimization opportunities
                $queryFindings = array_filter($findings, function($finding) {
                    return strpos($finding->getDescription(), 'query') !== false ||
                           strpos($finding->getDescription(), 'database') !== false;
                });
                
                $this->assertIsArray($queryFindings, 
                    'Should analyze database query patterns');
            }
        }
    }
}