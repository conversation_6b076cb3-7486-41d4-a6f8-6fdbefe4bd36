<?php

namespace AuditSystem\Interfaces;

use AuditSystem\Models\AuditResult;
use AuditSystem\Models\AuditStatus;

/**
 * Interface for the main audit controller that orchestrates the audit process
 */
interface AuditControllerInterface
{
    /**
     * Initialize and begin audit process
     *
     * @param array $options Configuration options for the audit
     * @return AuditResult The result of the audit process
     */
    public function startAudit(array $options): AuditResult;

    /**
     * Continue audit from last checkpoint
     *
     * @return AuditResult The result of the resumed audit process
     */
    public function resumeAudit(): AuditResult;

    /**
     * Return current progress and statistics
     *
     * @return AuditStatus Current audit status and progress information
     */
    public function getAuditStatus(): AuditStatus;
}