<?php
// Core configuration and authentication
require_once '../config.php'; // Adjust path as needed
require_once 'includes/auth_check.php'; // Check if admin is logged in
require_once '../includes/functions.php'; // General functions like escape(), formatDate(), getFeaturedImageUrl()

$admin_page_title = 'Articles'; // Set the page title

// --- Fetch data (Keep existing fetching logic, pagination/filtering needs separate implementation) ---
$articles = [];
$categories = [];
$authors = [];
$tags = [];
$total_articles = 0;
$published_articles = 0;
$draft_articles = 0;
$error_message = null; // Initialize error message

try {
    // Fetch articles with necessary joins
    // Added published_at to the SELECT list
    $sql_articles = "SELECT
                a.id, a.title, a.slug, a.status, a.created_at, a.updated_at, a.published_at, a.views, a.featured_image,
                au.name as author_name,
                c.name as category_name, c.slug as category_slug,
                (SELECT COUNT(*) FROM comments WHERE article_id = a.id AND is_approved = 1) as comment_count
            FROM articles a
            LEFT JOIN authors au ON a.author_id = au.id
            LEFT JOIN categories c ON a.category_id = c.id
            ORDER BY a.updated_at DESC"; // Default order
    $stmt_articles = $pdo->query($sql_articles);
    $articles = $stmt_articles->fetchAll(PDO::FETCH_ASSOC);

    // Fetch data for filters and stats
    $categories = $pdo->query("SELECT id, name, slug FROM categories ORDER BY name ASC")->fetchAll(PDO::FETCH_ASSOC);
    $authors = $pdo->query("SELECT id, name FROM authors WHERE status = 'active' ORDER BY name ASC")->fetchAll(PDO::FETCH_ASSOC); // Fetch only active authors for filtering
    $tags = $pdo->query("SELECT id, name, slug FROM tags ORDER BY name ASC")->fetchAll(PDO::FETCH_ASSOC);

    // Basic Stats (can be expanded)
    $total_articles = $pdo->query("SELECT COUNT(*) FROM articles")->fetchColumn();
    $published_articles = $pdo->query("SELECT COUNT(*) FROM articles WHERE status = 'published'")->fetchColumn();
    $draft_articles = $pdo->query("SELECT COUNT(*) FROM articles WHERE status = 'draft'")->fetchColumn();
    // Add more stats if needed (views, comments - requires more complex queries potentially)
    $total_views_stmt = $pdo->query("SELECT SUM(views) FROM articles");
    $total_views = $total_views_stmt ? $total_views_stmt->fetchColumn() : 0;
    $total_comments_stmt = $pdo->query("SELECT COUNT(*) FROM comments WHERE is_approved = 1"); // Count approved comments
    $total_comments = $total_comments_stmt ? $total_comments_stmt->fetchColumn() : 0;


} catch (PDOException $e) {
    $error_message = "Database error fetching data: " . $e->getMessage();
    // Log error in a real application
    error_log("Admin Articles DB Error: " . $e->getMessage());
}

// Include the admin header
include 'includes/header.php';
?>

<header class="h-16 bg-white border-b border-border flex items-center justify-between px-6 flex-shrink-0 sticky top-0 z-30">
    <h1 class="text-xl font-montserrat font-bold text-dark"><?php echo escape($admin_page_title); ?></h1>
    <div class="flex items-center space-x-4">
        <a href="article_form.php" class="btn">
            <svg xmlns="http://www.w3.org/2000/svg " class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            New Article
        </a>
    </div>
</header>

<div class="flex-1 overflow-y-auto bg-background">
    <div class="p-6">

        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="mb-4 bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded-lg text-sm shadow-sm">
                <?php echo escape($_SESSION['success_message']); unset($_SESSION['success_message']); ?>
            </div>
        <?php endif; ?>
        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm shadow-sm">
                <?php echo escape($_SESSION['error_message']); unset($_SESSION['error_message']); ?>
            </div>
        <?php endif; ?>
        <?php if (!empty($error_message)): ?>
            <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm shadow-sm">
                <?php echo escape($error_message); ?>
            </div>
        <?php endif; ?>

        <div class="bg-white rounded-xl shadow-sm border border-border overflow-hidden">
            <div class="bg-gray-50 border-b border-border p-4">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div class="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                        <div class="relative w-full sm:w-64">
                            <input
                                type="text"
                                placeholder="Search articles..."
                                class="form-input pl-9 py-2 text-sm"
                                id="articleSearchInput"
                            >
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg " class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </div>
                        </div>

                        <div class="w-full sm:w-40">
                            <select class="form-select py-2 text-sm" id="statusFilter">
                                <option value="">All Statuses</option>
                                <option value="published">Published</option>
                                <option value="draft">Draft</option>
                                <option value="scheduled">Scheduled</option>
                                <option value="archived">Archived</option>
                            </select>
                        </div>

                        <div class="w-full sm:w-40">
                            <select class="form-select py-2 text-sm" id="categoryFilter">
                                <option value="">All Categories</option>
                                <?php foreach($categories as $cat): ?>
                                    <option value="<?php echo escape($cat['slug']); ?>"><?php echo escape($cat['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="flex items-center gap-3">
                        <div class="flex border border-gray-300 rounded overflow-hidden">
                            <button
                                id="listViewBtn"
                                class="px-3 py-1 flex items-center transition-colors bg-primary text-white"
                                title="List View"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg " class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                                </svg>
                            </button>
                            <button
                                id="gridViewBtn"
                                class="px-3 py-1 flex items-center transition-colors bg-white text-gray-700"
                                title="Grid View (UI Only)"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg " class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                </svg>
                            </button>
                        </div>

                        <div class="w-48">
                            <select class="form-select py-2 text-sm" id="sortFilter">
                                <option value="updated_desc">Newest First</option>
                                <option value="updated_asc">Oldest First</option>
                                <option value="title_asc">Title A-Z</option>
                                <option value="title_desc">Title Z-A</option>
                                <option value="views_desc">Most Views</option>
                                <option value="comments_desc">Most Comments</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div id="advancedFiltersContainer">
                    <div class="mt-3 flex items-center">
                        <button id="advancedFiltersToggle" class="text-sm text-gray-600 flex items-center hover:text-primary">
                            <svg xmlns="http://www.w3.org/2000/svg " class="h-4 w-4 mr-1 transition-transform" id="advancedFiltersIcon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                            Advanced Filters
                        </button>
                    </div>

                    <div id="advancedFiltersContent" class="mt-3 grid grid-cols-1 md:grid-cols-3 gap-3" style="display: none;">
                        <div class="flex items-center gap-2">
                            <span class="text-sm text-gray-600 whitespace-nowrap">Date:</span>
                            <input type="date" class="form-input py-1 text-sm" placeholder="From" id="dateFromFilter">
                            <span class="text-sm text-gray-400">to</span>
                            <input type="date" class="form-input py-1 text-sm" placeholder="To" id="dateToFilter">
                        </div>

                        <div>
                            <select class="form-select py-1 text-sm" id="authorFilter">
                                <option value="">All Authors</option>
                                <?php foreach($authors as $author): ?>
                                    <option value="<?php echo escape($author['id']); ?>"><?php echo escape($author['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div>
                            <select class="form-select py-1 text-sm" id="tagFilter">
                                <option value="">All Tags</option>
                                <?php foreach($tags as $tag): ?>
                                    <option value="<?php echo escape($tag['slug']); ?>"><?php echo escape($tag['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mt-3 flex flex-wrap gap-2" id="activeFiltersContainer" style="display: none;">
                        <!-- Active filters will be added here by JavaScript -->
                        <button id="clearAllFilters" class="text-primary text-xs hover:underline" style="display: none;">Clear All</button>
                    </div>
                </div>
            </div>

            <div class="bg-white border-b border-border p-3 flex flex-wrap items-center justify-between gap-3">
                <div class="flex items-center gap-3 flex-grow">
                    <label class="inline-flex items-center">
                        <input
                            type="checkbox"
                            class="form-checkbox"
                            id="selectAllCheckbox"
                        >
                        <span class="ml-2 text-sm text-gray-700">Select All</span>
                    </label>

                    <div class="flex items-center gap-2" id="bulkActionsContainer" style="display: none;">
                        <span class="text-sm text-gray-600"><span id="selectedCount">0</span> items selected</span>
                        <div class="h-4 border-r border-gray-300"></div>
                        <div class="flex gap-2">
                            <button class="btn-sm bg-success text-white rounded-md px-2 py-1 text-xs" title="Publish Selected" id="bulkPublishBtn">Publish</button>
                            <button class="btn-sm bg-warning text-white rounded-md px-2 py-1 text-xs" title="Unpublish Selected" id="bulkUnpublishBtn">Unpublish</button>
                            <button class="btn-sm bg-danger text-white rounded-md px-2 py-1 text-xs" title="Delete Selected" id="bulkDeleteBtn">Delete</button>
                        </div>
                    </div>
                </div>

                <div class="flex items-center text-sm text-gray-600" id="countDisplay">
                    <?php
                    // Basic pagination display (replace with actual pagination later)
                    $start_item = !empty($articles) ? 1 : 0;
                    $end_item = count($articles);
                    $total_items_display = $total_articles; // Use the fetched total count
                    ?>
                    Showing <span class="font-medium mx-1"><?php echo $start_item; ?>-<?php echo $end_item; ?></span> of <span class="font-medium mx-1"><?php echo $total_items_display; ?></span> articles
                </div>
            </div>

            <div class="divide-y divide-border" id="articlesContainer">
                 <?php if (empty($articles)): ?>
                    <div class="p-6 text-center text-gray-600">
                        No articles found matching your criteria. <a href="article_form.php" class="text-primary hover:underline">Add a new article</a>.
                    </div>
                 <?php else: ?>
                    <?php foreach ($articles as $article):
                        // Get a small thumbnail URL using the function
                        $imageData = getFeaturedImageUrl($article['featured_image'], 'ss', 'articles', escape($article['title'])); // Request small size, pass title as alt
                        $thumbUrl = $imageData['url']; // Get the URL from the returned array (will be null if missing)
                        $thumbWidth = $imageData['width'];
                        $thumbHeight = $imageData['height'];
                        $thumbAlt = $imageData['alt']; // Get alt text
                        $liveArticleUrl = SITE_URL . '/' . escape($article['slug']) . '/'; // Construct live URL
                    ?>
                    <div class="p-4 hover:bg-gray-50/50 transition-colors flex items-center article-item" 
                         data-id="<?php echo $article['id']; ?>"
                         data-title="<?php echo escape($article['title']); ?>"
                         data-status="<?php echo escape($article['status']); ?>"
                         data-category="<?php echo escape($article['category_name'] ?? ''); ?>"
                         data-author="<?php echo escape($article['author_name'] ?? ''); ?>"
                         data-date="<?php echo formatDate($article['published_at'] ?? $article['created_at'], 'd.M.Y'); ?>"
                         data-views="<?php echo (int)($article['views'] ?? 0); ?>"
                         data-comments="<?php echo (int)($article['comment_count'] ?? 0); ?>">
                        <div class="mr-4">
                            <input
                                type="checkbox"
                                class="form-checkbox article-checkbox"
                                value="<?php echo $article['id']; ?>"
                            >
                        </div>
                        <div class="w-16 h-16 rounded overflow-hidden bg-gray-100 flex-shrink-0 mr-4 border border-border flex items-center justify-center">
                            <a href="article_form.php?id=<?php echo $article['id']; ?>" title="Edit <?php echo escape($article['title']); ?>">
                                <?php if ($thumbUrl): // *** FIX: Check if thumbUrl is not null *** ?>
                                    <img src="<?php echo escape($thumbUrl); ?>" alt="<?php echo escape($thumbAlt); ?>" class="w-full h-full object-cover" width="<?php echo $thumbWidth; ?>" height="<?php echo $thumbHeight; ?>" loading="lazy">
                                <?php else: // Show placeholder if URL is null ?>
                                    <svg xmlns="http://www.w3.org/2000/svg " class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                                <?php endif; ?>
                            </a>
                        </div>
                        <div class="flex-grow min-w-0">
                            <h3 class="font-montserrat font-bold text-base text-gray-800 truncate mb-1">
                                <a href="article_form.php?id=<?php echo $article['id']; ?>" class="hover:text-primary transition-colors" title="<?php echo escape($article['title']); ?>">
                                    <?php echo escape($article['title']); ?>
                                </a>
                            </h3>
                            <div class="flex flex-wrap items-center text-xs text-gray-500 gap-x-3 gap-y-1">
                                <span class="flex items-center" title="Published/Created Date">
                                    <svg xmlns="http://www.w3.org/2000/svg " class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <?php echo formatDate($article['published_at'] ?? $article['created_at'], 'd.M.Y'); // Show publish date or creation date ?>
                                </span>
                                <span class="flex items-center" title="Author">
                                    <svg xmlns="http://www.w3.org/2000/svg " class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    <?php echo escape($article['author_name'] ?? 'N/A'); ?>
                                </span>
                                <?php if (!empty($article['category_name'])): ?>
                                <span class="flex items-center" title="Category">
                                    <svg xmlns="http://www.w3.org/2000/svg " class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                    </svg>
                                    <?php echo escape($article['category_name']); ?>
                                </span>
                                <?php endif; ?>
                                <span class="flex items-center" title="Views">
                                    <svg xmlns="http://www.w3.org/2000/svg " class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                    <?php echo number_format($article['views'] ?? 0); ?>
                                </span>
                                <span class="flex items-center" title="Comments">
                                    <svg xmlns="http://www.w3.org/2000/svg " class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                                    </svg>
                                    <?php echo number_format($article['comment_count'] ?? 0); ?>
                                </span>
                            </div>
                        </div>
                        <div class="flex items-center gap-3 ml-4 flex-shrink-0">
                            <span class="badge
                                <?php echo ($article['status'] === 'published') ? 'badge-success' : ''; ?>
                                <?php echo ($article['status'] === 'draft') ? 'badge-warning' : ''; ?>
                                <?php echo ($article['status'] === 'scheduled') ? 'badge-info' : ''; ?>
                                <?php echo ($article['status'] === 'archived') ? 'badge-gray' : ''; ?>
                            ">
                                <?php echo ucfirst(escape($article['status'])); ?>
                            </span>
                            <div class="flex gap-1">
                                <a href="article_form.php?id=<?php echo $article['id']; ?>" class="p-1 text-gray-400 hover:text-primary rounded-md" title="Edit">
                                    <svg xmlns="http://www.w3.org/2000/svg " class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                    </svg>
                                </a>
                                <a href="<?php echo $liveArticleUrl; ?>" target="_blank" class="p-1 text-gray-400 hover:text-info rounded-md" title="View Live Article">
                                    <svg xmlns="http://www.w3.org/2000/svg " class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                </a>
                                <div class="relative dropdown-container">
                                    <button class="p-1 text-gray-400 hover:text-gray-600 rounded-md dropdown-toggle" title="More Options">
                                        <svg xmlns="http://www.w3.org/2000/svg " class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                        </svg>
                                    </button>
                                    <div class="dropdown-menu" style="display: none;">
                                        <a href="#" class="dropdown-item opacity-50 cursor-not-allowed" title="Coming Soon">
                                            <svg xmlns="http://www.w3.org/2000/svg " class="h-4 w-4 mr-2 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                            </svg>
                                            Duplicate
                                        </a>
                                        <a href="#" class="dropdown-item opacity-50 cursor-not-allowed" title="Coming Soon">
                                            <svg xmlns="http://www.w3.org/2000/svg " class="h-4 w-4 mr-2 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                            </svg>
                                            Share
                                        </a>
                                        <a href="#" class="dropdown-item opacity-50 cursor-not-allowed" title="Coming Soon">
                                            <svg xmlns="http://www.w3.org/2000/svg " class="h-4 w-4 mr-2 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                            </svg>
                                            Archive
                                        </a>
                                        <div class="border-t border-gray-100 my-1"></div>
                                        <form action="delete_article.php" method="POST" onsubmit="return confirm('Are you sure you want to delete this article? This action cannot be undone.');" class="block">
                                            <input type="hidden" name="article_id" value="<?php echo $article['id']; ?>">
                                            <button type="submit" class="dropdown-item text-red-600 hover:bg-red-50 w-full text-left">
                                                <svg xmlns="http://www.w3.org/2000/svg " class="h-4 w-4 mr-2 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                 <?php endif; ?>
            </div>

            <div class="bg-white border-t border-border p-4 flex items-center justify-between">
                <div>
                    <select class="form-select h-9 text-sm py-0 pl-2 pr-8 w-auto" id="itemsPerPage">
                        <option>10</option>
                        <option selected>20</option>
                        <option>50</option>
                         <option>100</option>
                    </select>
                    <span class="text-sm text-gray-600 ml-2">items per page</span>
                </div>

                <div class="flex items-center">
                    <span class="text-sm text-gray-600 mr-4">Page 1 of 1</span> <div class="flex border border-gray-300 rounded overflow-hidden">
                        <button class="px-3 py-1 bg-white text-gray-600 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <svg xmlns="http://www.w3.org/2000/svg " class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                        <button class="px-3 py-1 bg-white text-gray-600 hover:bg-gray-100 border-l border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <svg xmlns="http://www.w3.org/2000/svg " class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="card bg-white p-4 flex flex-col">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-montserrat font-bold text-gray-700">Total Articles</h3>
                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">All time</span>
                </div>
                <div class="flex items-baseline">
                    <span class="text-3xl font-bold text-gray-900"><?php echo number_format($total_articles); ?></span>
                    </div>
            </div>

            <div class="card bg-white p-4 flex flex-col">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-montserrat font-bold text-gray-700">Published</h3>
                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Status</span>
                </div>
                <div class="flex items-baseline">
                    <span class="text-3xl font-bold text-gray-900"><?php echo number_format($published_articles); ?></span>
                    <?php if ($total_articles > 0): ?>
                    <span class="ml-2 text-xs text-gray-500"><?php echo round(($published_articles / $total_articles) * 100); ?>% of total</span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card bg-white p-4 flex flex-col">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-montserrat font-bold text-gray-700">Total Views</h3>
                    <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full">All time</span>
                </div>
                <div class="flex items-baseline">
                    <span class="text-3xl font-bold text-gray-900"><?php echo number_format($total_views); ?></span>
                     </div>
            </div>

            <div class="card bg-white p-4 flex flex-col">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-montserrat font-bold text-gray-700">Total Comments</h3>
                    <span class="bg-indigo-100 text-indigo-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Approved</span>
                </div>
                <div class="flex items-baseline">
                    <span class="text-3xl font-bold text-gray-900"><?php echo number_format($total_comments); ?></span>
                     </div>
            </div>
        </div>
    </div> 
</div> 

<!-- Add custom CSS for dropdown menus -->
<style>
.dropdown-menu {
    position: absolute;
    right: 0;
    top: 100%;
    z-index: 20;
    min-width: 12rem;
    padding: 0.5rem 0;
    margin-top: 0.125rem;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #4b5563;
    cursor: pointer;
    transition: background-color 0.2s;
}

.dropdown-item:hover:not(.opacity-50) {
    background-color: #f3f4f6;
}
</style>

<!-- Add the JavaScript for filters, sorting, and dropdowns -->
<script>
// Use a self-invoking function to avoid global scope pollution
(function() {
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, initializing filters and dropdowns...');
        
        // Initialize dropdowns
        initializeDropdowns();
        
        // Initialize the advanced filters toggle
        initializeAdvancedFilters();
        
        // Initialize view toggle (list/grid)
        initializeViewToggle();
        
        // Initialize article filtering and sorting
        initializeArticleFiltering();
        
        // Initialize checkbox selection
        initializeCheckboxSelection();
    });
    
    // Initialize dropdown menus
    function initializeDropdowns() {
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
        
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const container = this.closest('.dropdown-container');
                const menu = container.querySelector('.dropdown-menu');
                
                // Close all other menus first
                document.querySelectorAll('.dropdown-menu').forEach(otherMenu => {
                    if (otherMenu !== menu && otherMenu.style.display === 'block') {
                        otherMenu.style.display = 'none';
                    }
                });
                
                // Toggle this menu
                menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
            });
        });
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown-container')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.style.display = 'none';
                });
            }
        });
    }
    
    // Initialize advanced filters toggle
    function initializeAdvancedFilters() {
        const toggle = document.getElementById('advancedFiltersToggle');
        const content = document.getElementById('advancedFiltersContent');
        const icon = document.getElementById('advancedFiltersIcon');
        
        if (toggle && content && icon) {
            toggle.addEventListener('click', function() {
                if (content.style.display === 'none') {
                    content.style.display = 'grid';
                    icon.classList.add('transform', 'rotate-90');
                } else {
                    content.style.display = 'none';
                    icon.classList.remove('transform', 'rotate-90');
                }
            });
        }
    }
    
    // Initialize view toggle (list/grid)
    function initializeViewToggle() {
        const listBtn = document.getElementById('listViewBtn');
        const gridBtn = document.getElementById('gridViewBtn');
        
        if (listBtn && gridBtn) {
            listBtn.addEventListener('click', function() {
                listBtn.classList.add('bg-primary', 'text-white');
                listBtn.classList.remove('bg-white', 'text-gray-700');
                
                gridBtn.classList.add('bg-white', 'text-gray-700');
                gridBtn.classList.remove('bg-primary', 'text-white');
                
                // Implement actual view change if needed
            });
            
            gridBtn.addEventListener('click', function() {
                gridBtn.classList.add('bg-primary', 'text-white');
                gridBtn.classList.remove('bg-white', 'text-gray-700');
                
                listBtn.classList.add('bg-white', 'text-gray-700');
                listBtn.classList.remove('bg-primary', 'text-white');
                
                // Implement actual view change if needed
            });
        }
    }
    
    // Initialize article filtering and sorting
    function initializeArticleFiltering() {
        // Get all filter elements
        const searchInput = document.getElementById('articleSearchInput');
        const statusFilter = document.getElementById('statusFilter');
        const categoryFilter = document.getElementById('categoryFilter');
        const dateFromFilter = document.getElementById('dateFromFilter');
        const dateToFilter = document.getElementById('dateToFilter');
        const authorFilter = document.getElementById('authorFilter');
        const tagFilter = document.getElementById('tagFilter');
        const sortFilter = document.getElementById('sortFilter');
        const clearAllBtn = document.getElementById('clearAllFilters');
        
        // Get all article elements
        const articles = Array.from(document.querySelectorAll('.article-item')).map(element => {
            return {
                element: element,
                id: element.dataset.id,
                title: element.dataset.title.toLowerCase(),
                status: element.dataset.status.toLowerCase(),
                category: element.dataset.category.toLowerCase(),
                author: element.dataset.author.toLowerCase(),
                date: element.dataset.date,
                views: parseInt(element.dataset.views) || 0,
                comments: parseInt(element.dataset.comments) || 0
            };
        });
        
        console.log(`Found ${articles.length} articles for filtering`);
        
        // Add event listeners to filter inputs
        if (searchInput) searchInput.addEventListener('input', applyFilters);
        if (statusFilter) statusFilter.addEventListener('change', applyFilters);
        if (categoryFilter) categoryFilter.addEventListener('change', applyFilters);
        if (dateFromFilter) dateFromFilter.addEventListener('change', applyFilters);
        if (dateToFilter) dateToFilter.addEventListener('change', applyFilters);
        if (authorFilter) authorFilter.addEventListener('change', applyFilters);
        if (tagFilter) tagFilter.addEventListener('change', applyFilters);
        if (sortFilter) sortFilter.addEventListener('change', applyFilters);
        
        // Add clear filters button handler
        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', function() {
                // Reset all filters
                if (searchInput) searchInput.value = '';
                if (statusFilter) statusFilter.selectedIndex = 0;
                if (categoryFilter) categoryFilter.selectedIndex = 0;
                if (dateFromFilter) dateFromFilter.value = '';
                if (dateToFilter) dateToFilter.value = '';
                if (authorFilter) authorFilter.selectedIndex = 0;
                if (tagFilter) tagFilter.selectedIndex = 0;
                
                // Apply the reset filters
                applyFilters();
            });
        }
        
        // Function to apply filters
        function applyFilters() {
            // Get current filter values
            const searchValue = searchInput ? searchInput.value.toLowerCase() : '';
            const statusValue = statusFilter && statusFilter.selectedIndex > 0 ? statusFilter.options[statusFilter.selectedIndex].value.toLowerCase() : '';
            const categoryValue = categoryFilter && categoryFilter.selectedIndex > 0 ? categoryFilter.options[categoryFilter.selectedIndex].value.toLowerCase() : '';
            const authorValue = authorFilter && authorFilter.selectedIndex > 0 ? authorFilter.options[authorFilter.selectedIndex].value : '';
            const tagValue = tagFilter && tagFilter.selectedIndex > 0 ? tagFilter.options[tagFilter.selectedIndex].value.toLowerCase() : '';
            const dateFromValue = dateFromFilter ? dateFromFilter.value : '';
            const dateToValue = dateToFilter ? dateToFilter.value : '';
            const sortValue = sortFilter ? sortFilter.value : 'updated_desc';
            
            console.log('Applying filters with values:', {
                search: searchValue,
                status: statusValue,
                category: categoryValue,
                author: authorValue,
                tag: tagValue,
                dateFrom: dateFromValue,
                dateTo: dateToValue,
                sort: sortValue
            });
            
            // Filter the articles
            const filteredArticles = articles.filter(article => {
                // Search filter
                if (searchValue && !article.title.includes(searchValue)) {
                    return false;
                }
                
                // Status filter
                if (statusValue && article.status !== statusValue) {
                    return false;
                }
                
                // Category filter - simplified for client-side, would be better with slugs
                if (categoryValue && !article.category.includes(categoryValue)) {
                    return false;
                }
                
                // Author filter - very basic matching
                if (authorValue && authorFilter) {
                    const authorName = authorFilter.options[authorFilter.selectedIndex].text.toLowerCase();
                    if (!article.author.includes(authorName)) {
                        return false;
                    }
                }
                
                // Date filters
                if (dateFromValue || dateToValue) {
                    const articleDate = parseSimpleDate(article.date);
                    
                    if (dateFromValue) {
                        const fromDate = new Date(dateFromValue);
                        if (fromDate > articleDate) {
                            return false;
                        }
                    }
                    
                    if (dateToValue) {
                        const toDate = new Date(dateToValue);
                        // Add one day to make it inclusive
                        toDate.setDate(toDate.getDate() + 1);
                        if (toDate <= articleDate) {
                            return false;
                        }
                    }
                }
                
                // Tag filter would go here, but we don't have enough info in the dataset
                
                return true;
            });
            
            // Sort the filtered articles
            sortArticles(filteredArticles, sortValue);
            
            // Update the UI
            updateArticlesDisplay(filteredArticles);
            updateActiveFilters(searchValue, statusValue, categoryValue, dateFromValue, dateToValue, authorValue, tagValue);
        }
        
        // Function to sort articles
        function sortArticles(articlesToSort, sortValue) {
            switch (sortValue) {
                case 'updated_asc':
                    // Default is descending, so we just reverse
                    articlesToSort.reverse();
                    break;
                    
                case 'title_asc':
                    articlesToSort.sort((a, b) => a.title.localeCompare(b.title));
                    break;
                    
                case 'title_desc':
                    articlesToSort.sort((a, b) => b.title.localeCompare(a.title));
                    break;
                    
                case 'views_desc':
                    articlesToSort.sort((a, b) => b.views - a.views);
                    break;
                    
                case 'comments_desc':
                    articlesToSort.sort((a, b) => b.comments - a.comments);
                    break;
                    
                // Default is 'updated_desc', which is already the DOM order
            }
        }
        
        // Function to update articles display
        function updateArticlesDisplay(filteredArticles) {
            // Hide all articles first
            articles.forEach(article => {
                article.element.style.display = 'none';
            });
            
            // Show filtered articles
            filteredArticles.forEach(article => {
                article.element.style.display = 'flex';
            });
            
            // Show or create "no results" message if needed
            const container = document.getElementById('articlesContainer');
            let noResultsMsg = document.querySelector('#articlesContainer > .p-6.text-center');
            
            if (filteredArticles.length === 0) {
                if (!noResultsMsg) {
                    noResultsMsg = document.createElement('div');
                    noResultsMsg.className = 'p-6 text-center text-gray-600';
                    noResultsMsg.innerHTML = 'No articles found matching your criteria. <a href="article_form.php" class="text-primary hover:underline">Add a new article</a>.';
                    container.appendChild(noResultsMsg);
                } else {
                    noResultsMsg.style.display = 'block';
                }
            } else if (noResultsMsg) {
                noResultsMsg.style.display = 'none';
            }
            
            // Update the count display
            const countDisplay = document.getElementById('countDisplay');
            if (countDisplay) {
                countDisplay.innerHTML = `Showing <span class="font-medium mx-1">1-${filteredArticles.length}</span> of <span class="font-medium mx-1">${articles.length}</span> articles`;
            }
        }
        
        // Function to update active filters display
        function updateActiveFilters(search, status, category, dateFrom, dateTo, author, tag) {
            const container = document.getElementById('activeFiltersContainer');
            const clearAllBtn = document.getElementById('clearAllFilters');
            
            if (!container) return;
            
            // Clear existing filter pills (except the clear all button)
            Array.from(container.querySelectorAll('div')).forEach(div => div.remove());
            
            // Create filter pills
            let hasFilters = false;
            
            if (search) {
                addFilterPill(container, 'Search', search);
                hasFilters = true;
            }
            
            if (status && statusFilter) {
                addFilterPill(container, 'Status', statusFilter.options[statusFilter.selectedIndex].text);
                hasFilters = true;
            }
            
            if (category && categoryFilter) {
                addFilterPill(container, 'Category', categoryFilter.options[categoryFilter.selectedIndex].text);
                hasFilters = true;
            }
            
            if (dateFrom) {
                addFilterPill(container, 'Date From', formatDate(dateFrom));
                hasFilters = true;
            }
            
            if (dateTo) {
                addFilterPill(container, 'Date To', formatDate(dateTo));
                hasFilters = true;
            }
            
            if (author && authorFilter) {
                addFilterPill(container, 'Author', authorFilter.options[authorFilter.selectedIndex].text);
                hasFilters = true;
            }
            
            if (tag && tagFilter) {
                addFilterPill(container, 'Tag', tagFilter.options[tagFilter.selectedIndex].text);
                hasFilters = true;
            }
            
            // Show or hide container and clear all button
            if (hasFilters) {
                container.style.display = 'flex';
                if (clearAllBtn) clearAllBtn.style.display = 'block';
            } else {
                container.style.display = 'none';
                if (clearAllBtn) clearAllBtn.style.display = 'none';
            }
        }
        
        // Function to add a filter pill
        function addFilterPill(container, type, value) {
            const pill = document.createElement('div');
            pill.className = 'bg-gray-100 rounded-full px-3 py-1 text-xs flex items-center shadow-sm';
            pill.innerHTML = `
                <span class="text-gray-500 mr-1">${type}:</span>
                <span class="font-medium text-gray-700">${value}</span>
                <button class="ml-1 text-gray-400 hover:text-gray-600" data-filter-type="${type}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            `;
            
            // Add event listener to remove button
            const removeBtn = pill.querySelector('button');
            removeBtn.addEventListener('click', function() {
                removeFilter(type);
            });
            
            container.insertBefore(pill, container.lastChild);
        }
        
        // Function to remove a filter
        function removeFilter(filterType) {
            switch (filterType) {
                case 'Search':
                    if (searchInput) searchInput.value = '';
                    break;
                case 'Status':
                    if (statusFilter) statusFilter.selectedIndex = 0;
                    break;
                case 'Category':
                    if (categoryFilter) categoryFilter.selectedIndex = 0;
                    break;
                case 'Date From':
                    if (dateFromFilter) dateFromFilter.value = '';
                    break;
                case 'Date To':
                    if (dateToFilter) dateToFilter.value = '';
                    break;
                case 'Author':
                    if (authorFilter) authorFilter.selectedIndex = 0;
                    break;
                case 'Tag':
                    if (tagFilter) tagFilter.selectedIndex = 0;
                    break;
            }
            
            applyFilters();
        }
        
        // Helper function to parse date in format d.M.Y (e.g., 24.12.2023)
        function parseSimpleDate(dateStr) {
            if (!dateStr) return new Date(0);
            
            const parts = dateStr.split('.');
            if (parts.length !== 3) return new Date(0);
            
            // parts[0] = day, parts[1] = month, parts[2] = year
            return new Date(parts[2], parts[1] - 1, parts[0]);
        }
        
        // Helper function to format date
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'short', 
                day: 'numeric' 
            });
        }
        
        // Initialize by applying filters once
        applyFilters();
    }
    
    // Initialize checkbox selection
    function initializeCheckboxSelection() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const articleCheckboxes = document.querySelectorAll('.article-checkbox');
        const bulkActionsContainer = document.getElementById('bulkActionsContainer');
        const selectedCountEl = document.getElementById('selectedCount');
        
        if (selectAllCheckbox && articleCheckboxes.length > 0) {
            // Handle "Select All" checkbox
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                
                articleCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
                
                updateSelectedCount();
            });
            
            // Handle individual checkboxes
            articleCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedCount);
            });
            
            // Handle bulk action buttons
            const bulkPublishBtn = document.getElementById('bulkPublishBtn');
            const bulkUnpublishBtn = document.getElementById('bulkUnpublishBtn');
            const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
            
            if (bulkPublishBtn) {
                bulkPublishBtn.addEventListener('click', function() {
                    // Get selected IDs
                    const selectedIds = getSelectedArticleIds();
                    if (selectedIds.length === 0) return;
                    
                    if (confirm(`Are you sure you want to publish ${selectedIds.length} articles?`)) {
                        // TODO: Implement actual bulk publish action
                        console.log('Bulk publish:', selectedIds);
                        alert('Bulk publish action would happen here.');
                    }
                });
            }
            
            if (bulkUnpublishBtn) {
                bulkUnpublishBtn.addEventListener('click', function() {
                    // Get selected IDs
                    const selectedIds = getSelectedArticleIds();
                    if (selectedIds.length === 0) return;
                    
                    if (confirm(`Are you sure you want to unpublish ${selectedIds.length} articles?`)) {
                        // TODO: Implement actual bulk unpublish action
                        console.log('Bulk unpublish:', selectedIds);
                        alert('Bulk unpublish action would happen here.');
                    }
                });
            }
            
           if (bulkDeleteBtn) {
    bulkDeleteBtn.addEventListener('click', function() {
        // Get selected IDs
        const selectedIds = getSelectedArticleIds();
        if (selectedIds.length === 0) return;
        
        if (confirm(`Are you sure you want to delete ${selectedIds.length} articles? This action cannot be undone.`)) {
            // Create a form dynamically
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'bulk_delete_articles.php';
            form.style.display = 'none';
            
            // Add each selected ID as a hidden input
            selectedIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'article_ids[]';
                input.value = id;
                form.appendChild(input);
            });
            
            // Add form to document and submit it
            document.body.appendChild(form);
            form.submit();
        }
    });
}
            
            // Function to update selected count
            function updateSelectedCount() {
                const selectedCount = document.querySelectorAll('.article-checkbox:checked').length;
                
                // Update the count
                if (selectedCountEl) {
                    selectedCountEl.textContent = selectedCount;
                }
                
                // Show/hide bulk actions container
                if (bulkActionsContainer) {
                    bulkActionsContainer.style.display = selectedCount > 0 ? 'flex' : 'none';
                }
                
                // Update "Select All" checkbox state
                selectAllCheckbox.checked = selectedCount > 0 && selectedCount === articleCheckboxes.length;
                selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < articleCheckboxes.length;
            }
            
            // Function to get selected article IDs
            function getSelectedArticleIds() {
                return Array.from(document.querySelectorAll('.article-checkbox:checked')).map(cb => cb.value);
            }
        }
    }
})();
</script>

<?php
// Include the admin footer
include 'includes/footer.php';
?>
