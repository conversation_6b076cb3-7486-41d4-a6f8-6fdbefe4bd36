<?php
/**
 * Ad Manager - Core Ad Management Functions
 *
 * Fetches and manages ad unit data from the database.
 */

// Prevent direct access
if (!defined('ABSPATH') && !defined('SITE_URL')) { // Check for common constants or SITE_URL
    if (file_exists('../config.php')) {
        require_once '../config.php'; // Adjust path if needed
    } else {
        exit('Direct script access denied.');
    }
}

// Include necessary dependencies if not already included where functions are called
// require_once __DIR__ . '/functions.php'; // Assuming functions.php is in the same directory

/**
 * Fetches a specific AdSense unit by its ID.
 *
 * @param PDO $pdo PDO database connection object.
 * @param int $id AdSense Unit ID.
 * @return array|null AdSense unit data or null if not found.
 */
function getAdSenseUnitById(PDO $pdo, int $id): ?array {
    try {
        $stmt = $pdo->prepare("SELECT * FROM adsense_units WHERE id = :id LIMIT 1");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        $unit = $stmt->fetch(PDO::FETCH_ASSOC);
        return $unit ?: null;
    } catch (PDOException $e) {
        error_log("Error fetching AdSense unit ID {$id}: " . $e->getMessage());
        return null;
    }
}

/**
 * Fetches a specific Affiliate Ad by its ID.
 *
 * @param PDO $pdo PDO database connection object.
 * @param int $id Affiliate Ad ID.
 * @return array|null Affiliate ad data or null if not found.
 */
function getAffiliateAdById(PDO $pdo, int $id): ?array {
    try {
        $stmt = $pdo->prepare("SELECT * FROM affiliate_ads WHERE id = :id LIMIT 1");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        $ad = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Process display_position field (convert JSON to array if needed)
        if ($ad && isset($ad['display_position'])) {
            $positionsData = json_decode($ad['display_position'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($positionsData)) {
                $ad['display_positions'] = $positionsData;
            } else {
                // Legacy format: single position as string
                $ad['display_positions'] = [$ad['display_position']];
            }
        }
        
        return $ad ?: null;
    } catch (PDOException $e) {
        error_log("Error fetching Affiliate Ad ID {$id}: " . $e->getMessage());
        return null;
    }
}


/**
 * Fetches active ads (both types) for a specific placement, considering targeting rules.
 *
 * @param PDO $pdo PDO database connection object.
 * @param string $placement Placement identifier (e.g., 'sidebar', 'in_content').
 * @param array $context Contextual information for targeting (e.g., ['page_type' => 'article', 'category_id' => 5]).
 * @param int $limit Maximum number of ads to return (default: all matching ads).
 * @return array List of active and targeted ad units for the placement.
 */
function getActiveAdsForPlacement(PDO $pdo, string $placement, array $context = [], int $limit = 0): array {
    // Include targeting functions if not already globally available
    require_once __DIR__ . '/ad_targeting.php';

    $activeAds = [];

    // --- Fetch AdSense Units ---
    try {
        // Base query for active AdSense units for the placement
        // Assumes common columns like status, start_date, end_date, placement, device_visibility, etc.
        $sqlAdsense = "SELECT * FROM adsense_units
                       WHERE placement = :placement
                         AND status = 'active'
                         AND (start_date IS NULL OR start_date <= NOW())
                         AND (end_date IS NULL OR end_date >= NOW())
                       ORDER BY position_index ASC"; // Example ordering

        $stmtAdsense = $pdo->prepare($sqlAdsense);
        $stmtAdsense->bindParam(':placement', $placement, PDO::PARAM_STR);
        $stmtAdsense->execute();
        $adsenseUnits = $stmtAdsense->fetchAll(PDO::FETCH_ASSOC);

        foreach ($adsenseUnits as $unit) {
            $unit['type'] = 'adsense'; // Add type identifier
            // Check targeting rules before adding to the list
            if (shouldDisplayAd($unit, $context)) {
                $activeAds[] = $unit;
            }
        }
    } catch (PDOException $e) {
        error_log("Error fetching AdSense ads for placement '$placement': " . $e->getMessage());
    }

    // --- Fetch Affiliate Ads ---
    try {
        // MODIFIED: Query to find affiliate ads that contain the requested placement in their JSON array
        // The SQL looks for display_position values that are either:
        // 1. The exact placement name (legacy string format)
        // 2. A JSON array containing the placement name
        // 3. A JSON string that contains the placement name surrounded by quotes (JSON array element)
        $sqlAffiliate = "SELECT * FROM affiliate_ads
                         WHERE (
                             display_position = :placement OR
                             JSON_CONTAINS(display_position, :placement_json) = 1 OR
                             display_position LIKE :placement_like
                         )
                         AND status = 'active'
                         AND (start_date IS NULL OR start_date <= NOW())
                         AND (end_date IS NULL OR end_date >= NOW())
                         ORDER BY placement_priority DESC, RAND()"; // Example: prioritize then randomize

        $stmtAffiliate = $pdo->prepare($sqlAffiliate);
        $stmtAffiliate->bindParam(':placement', $placement, PDO::PARAM_STR);
        $placement_json = json_encode($placement); // Convert to JSON string for JSON_CONTAINS
        $stmtAffiliate->bindParam(':placement_json', $placement_json, PDO::PARAM_STR);
        $placement_like = '%"' . $placement . '"%'; // For LIKE search in JSON array
        $stmtAffiliate->bindParam(':placement_like', $placement_like, PDO::PARAM_STR);
        $stmtAffiliate->execute();
        $affiliateAds = $stmtAffiliate->fetchAll(PDO::FETCH_ASSOC);

        foreach ($affiliateAds as $ad) {
            $ad['type'] = 'affiliate'; // Add type identifier
            
            // Process display_position field for each ad (convert JSON to array if needed)
            if (isset($ad['display_position'])) {
                $positionsData = json_decode($ad['display_position'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($positionsData)) {
                    $ad['display_positions'] = $positionsData;
                } else {
                    // Legacy format: single position as string
                    $ad['display_positions'] = [$ad['display_position']];
                }
                
                // Set current_placement for tracking & display
                $ad['current_placement'] = $placement;
            }
             
             // Check targeting rules before adding to the list
             // Affiliate ads might have different visibility columns, adjust shouldDisplayAd if needed
            if (shouldDisplayAd($ad, $context)) {
                $activeAds[] = $ad;
            }
        }
    } catch (PDOException $e) {
        error_log("Error fetching Affiliate ads for placement '$placement': " . $e->getMessage());
    }

    // --- Apply limit if specified ---
    if ($limit > 0 && count($activeAds) > $limit) {
        $activeAds = array_slice($activeAds, 0, $limit);
    }

    return $activeAds;
}

/**
 * Placeholder function to get settings relevant to ads.
 * In a real app, these might come from a dedicated settings table or config file.
 *
 * @param PDO $pdo Database connection (optional, might read from file).
 * @param string $settingKey The specific setting key (e.g., 'ads_global_enabled', 'adsense_client_id').
 * @param mixed $defaultValue Default value if setting is not found.
 * @return mixed The setting value.
 */
function getAdSetting(PDO $pdo, string $settingKey, $defaultValue = null) {
    // TODO: Implement reading from your settings storage (DB or file)
    // Example using a hypothetical settings table:
    /*
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM site_settings WHERE setting_key = :key LIMIT 1");
        $stmt->bindParam(':key', $settingKey, PDO::PARAM_STR);
        $stmt->execute();
        $value = $stmt->fetchColumn();
        return ($value !== false) ? $value : $defaultValue;
    } catch (PDOException $e) {
        error_log("Error fetching setting '$settingKey': " . $e->getMessage());
        return $defaultValue;
    }
    */
    // Example reading from config constants:
    if (defined(strtoupper($settingKey))) {
        return constant(strtoupper($settingKey));
    }

    return $defaultValue;
}

?>