<?php

use PHPUnit\Framework\TestCase;
use AuditSystem\Services\AuditLogger;
use AuditSystem\Exceptions\FileAccessException;
use AuditSystem\Exceptions\AnalysisException;

class AuditLoggerTest extends TestCase
{
    private AuditLogger $logger;
    private string $tempDir;
    private string $logDir;

    protected function setUp(): void
    {
        $this->tempDir = sys_get_temp_dir() . '/audit_logger_test_' . uniqid();
        $this->logDir = $this->tempDir . '/logs';
        mkdir($this->logDir, 0755, true);
        
        $this->logger = new AuditLogger($this->logDir);
    }

    protected function tearDown(): void
    {
        $this->removeDirectory($this->tempDir);
    }

    public function testBasicLogging()
    {
        $this->logger->info('Test info message');
        $this->logger->error('Test error message');
        $this->logger->debug('Test debug message');

        $logFile = $this->logDir . '/audit.log';
        $this->assertFileExists($logFile);

        $logContent = file_get_contents($logFile);
        $this->assertStringContainsString('info: Test info message', $logContent);
        $this->assertStringContainsString('error: Test error message', $logContent);
        $this->assertStringContainsString('debug: Test debug message', $logContent);
    }

    public function testErrorLogSeparation()
    {
        $this->logger->info('Info message');
        $this->logger->error('Error message');
        $this->logger->critical('Critical message');

        $errorLogFile = $this->logDir . '/error.log';
        $this->assertFileExists($errorLogFile);

        $errorLogContent = file_get_contents($errorLogFile);
        $this->assertStringNotContainsString('Info message', $errorLogContent);
        $this->assertStringContainsString('Error message', $errorLogContent);
        $this->assertStringContainsString('Critical message', $errorLogContent);
    }

    public function testLoggingWithContext()
    {
        $context = ['file' => 'test.php', 'line' => 42];
        $this->logger->info('Test message with context', $context);

        $logFile = $this->logDir . '/audit.log';
        $logContent = file_get_contents($logFile);
        
        $this->assertStringContainsString('Test message with context', $logContent);
        $this->assertStringContainsString('"file":"test.php"', $logContent);
        $this->assertStringContainsString('"line":42', $logContent);
    }

    public function testPerformanceLogging()
    {
        $this->logger->logPerformance('test_operation', 1.5, ['files_processed' => 10]);

        $performanceLogFile = $this->logDir . '/performance.log';
        $this->assertFileExists($performanceLogFile);

        $performanceContent = file_get_contents($performanceLogFile);
        $this->assertStringContainsString('PERFORMANCE: test_operation took 1.5s', $performanceContent);
        $this->assertStringContainsString('"files_processed":10', $performanceContent);
    }

    public function testProgressLogging()
    {
        $this->logger->logProgress('analyzing', 50, 100, 25);

        $logFile = $this->logDir . '/audit.log';
        $logContent = file_get_contents($logFile);
        
        $this->assertStringContainsString('Audit progress: analyzing - 50/100 files (50%) - 25 findings', $logContent);
    }

    public function testFileAnalysisLogging()
    {
        $mockAnalyzers = [
            new class { public function __toString() { return 'MockAnalyzer1'; } },
            new class { public function __toString() { return 'MockAnalyzer2'; } }
        ];

        $this->logger->logFileAnalysisStart('/test/file.php', $mockAnalyzers);
        $this->logger->logFileAnalysisComplete('/test/file.php', 5, 2.3);

        $logFile = $this->logDir . '/audit.log';
        $logContent = file_get_contents($logFile);
        
        $this->assertStringContainsString('Starting analysis of /test/file.php', $logContent);
        $this->assertStringContainsString('Completed analysis of /test/file.php: 5 findings in 2.3s', $logContent);
    }

    public function testAnalyzerFailureLogging()
    {
        $exception = new AnalysisException('Test analyzer failure');
        $this->logger->logAnalyzerFailure('TestAnalyzer', '/test/file.php', $exception);

        $logFile = $this->logDir . '/audit.log';
        $logContent = file_get_contents($logFile);
        
        $this->assertStringContainsString('Analyzer TestAnalyzer failed for /test/file.php', $logContent);
        $this->assertStringContainsString('Test analyzer failure', $logContent);
    }

    public function testExceptionLogging()
    {
        $exception = FileAccessException::fileNotFound('/nonexistent/file.php');
        $additionalContext = ['operation' => 'file_scan'];

        $this->logger->logException($exception, 'error', $additionalContext);

        $logFile = $this->logDir . '/audit.log';
        $logContent = file_get_contents($logFile);
        
        $this->assertStringContainsString('Exception occurred', $logContent);
        $this->assertStringContainsString('FileAccessException', $logContent);
        $this->assertStringContainsString('operation":"file_scan', $logContent);
    }

    public function testErrorRecoveryLogging()
    {
        $this->logger->logErrorRecovery('FileAccessException', 'retry_with_delay', 'file_analysis', true);
        $this->logger->logErrorRecovery('AnalysisException', 'skip_analyzer', 'php_analysis', false);

        $logFile = $this->logDir . '/audit.log';
        $logContent = file_get_contents($logFile);
        
        $this->assertStringContainsString('Error recovery successful: FileAccessException', $logContent);
        $this->assertStringContainsString('Error recovery failed: AnalysisException', $logContent);
    }

    public function testResourceUsageLogging()
    {
        $resources = [
            'memory_usage' => 1024000,
            'cpu_time' => 5.2,
            'files_processed' => 50
        ];

        $this->logger->logResourceUsage('file_scanning', $resources);

        $performanceLogFile = $this->logDir . '/performance.log';
        $performanceContent = file_get_contents($performanceLogFile);
        
        $this->assertStringContainsString('RESOURCES: file_scanning', $performanceContent);
        $this->assertStringContainsString('"memory_usage":1024000', $performanceContent);
        $this->assertStringContainsString('"cpu_time":5.2', $performanceContent);
    }

    public function testCheckpointLogging()
    {
        $checkpointData = [
            'completed_files' => 25,
            'pending_files' => 75,
            'findings_count' => 12
        ];

        $this->logger->logCheckpoint('analyzing', $checkpointData);

        $logFile = $this->logDir . '/audit.log';
        $logContent = file_get_contents($logFile);
        
        $this->assertStringContainsString('Audit checkpoint: analyzing', $logContent);
        $this->assertStringContainsString('"completed_files":25', $logContent);
    }

    public function testGracefulDegradationLogging()
    {
        $this->logger->logGracefulDegradation('MCP Server', 'Connection timeout', 'use local analysis');

        $logFile = $this->logDir . '/audit.log';
        $logContent = file_get_contents($logFile);
        
        $this->assertStringContainsString('Graceful degradation: MCP Server - Connection timeout', $logContent);
        $this->assertStringContainsString('Fallback: use local analysis', $logContent);
    }

    public function testLogRotation()
    {
        // Fill log with data to exceed size limit
        $largeMessage = str_repeat('A', 1000);
        for ($i = 0; $i < 15; $i++) { // Should exceed 10KB
            $this->logger->info($largeMessage);
        }

        $logFile = $this->logDir . '/audit.log';
        $originalSize = filesize($logFile);

        // Rotate logs with small size limit
        $this->logger->rotateLogs(10000); // 10KB limit

        // Original log should be smaller now
        $newSize = filesize($logFile);
        $this->assertLessThan($originalSize, $newSize);

        // Should have created a rotated file
        $rotatedFiles = glob($this->logDir . '/audit.log.*');
        $this->assertNotEmpty($rotatedFiles);
    }

    public function testLogStatistics()
    {
        $this->logger->info('Test message 1');
        $this->logger->error('Test message 2');
        $this->logger->logPerformance('test_op', 1.0);

        $stats = $this->logger->getLogStatistics();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('audit', $stats);
        $this->assertArrayHasKey('error', $stats);
        $this->assertArrayHasKey('performance', $stats);

        $this->assertGreaterThan(0, $stats['audit']['size']);
        $this->assertGreaterThan(0, $stats['audit']['lines']);
        $this->assertGreaterThan(0, $stats['error']['size']);
        $this->assertGreaterThan(0, $stats['performance']['size']);
    }

    public function testArchiveOldLogs()
    {
        // Create some old log files
        $oldLogFile = $this->logDir . '/audit.log.2023-01-01-12-00-00';
        file_put_contents($oldLogFile, 'old log content');
        
        // Set file time to be older than 30 days
        touch($oldLogFile, time() - (31 * 24 * 60 * 60));

        $this->logger->archiveOldLogs(30);

        $this->assertFileDoesNotExist($oldLogFile);
    }

    public function testRecentLogsRetrieval()
    {
        $this->logger->info('Message 1');
        $this->logger->error('Message 2');
        $this->logger->debug('Message 3');

        $recentLogs = $this->logger->getRecentLogs(10);
        $this->assertIsArray($recentLogs);
        $this->assertCount(3, $recentLogs);

        // Test filtering by level
        $errorLogs = $this->logger->getRecentLogs(10, 'error');
        $this->assertCount(1, $errorLogs);
        $this->assertStringContainsString('Message 2', $errorLogs[0]);
    }

    public function testClearLogs()
    {
        $this->logger->info('Test message');
        $this->logger->error('Test error');
        $this->logger->logPerformance('test_op', 1.0);

        // Verify logs exist
        $this->assertFileExists($this->logDir . '/audit.log');
        $this->assertFileExists($this->logDir . '/error.log');
        $this->assertFileExists($this->logDir . '/performance.log');

        $this->logger->clearLogs();

        // Verify logs are empty
        $this->assertEquals('', file_get_contents($this->logDir . '/audit.log'));
        $this->assertEquals('', file_get_contents($this->logDir . '/error.log'));
        $this->assertEquals('', file_get_contents($this->logDir . '/performance.log'));
    }

    public function testLogDirectoryCreation()
    {
        $newLogDir = $this->tempDir . '/new_logs';
        $this->assertDirectoryDoesNotExist($newLogDir);

        $newLogger = new AuditLogger($newLogDir);
        $newLogger->info('Test message');

        $this->assertDirectoryExists($newLogDir);
        $this->assertFileExists($newLogDir . '/audit.log');
    }

    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        rmdir($dir);
    }
}