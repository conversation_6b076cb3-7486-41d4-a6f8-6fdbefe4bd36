-- Create internal_links table
CREATE TABLE IF NOT EXISTS `internal_links` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `keyword` varchar(255) NOT NULL,
  `url` varchar(255) NOT NULL,
  `target_article_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `match_type` enum('exact', 'case_insensitive', 'partial') NOT NULL DEFAULT 'case_insensitive',
  `max_replacements` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `target_article_id` (`target_article_id`),
  CONSTRAINT `internal_links_ibfk_1` FOREIGN KEY (`target_article_id`) REFERENCES `articles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add index for faster keyword matching
CREATE INDEX `idx_internal_links_keyword` ON `internal_links` (`keyword`);
CREATE INDEX `idx_internal_links_is_active` ON `internal_links` (`is_active`);
