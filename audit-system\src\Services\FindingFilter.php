<?php

namespace AuditSystem\Services;

use AuditSystem\Models\Finding;

/**
 * Utility class for filtering and sorting audit findings
 */
class FindingFilter
{
    /**
     * Filter findings by priority area
     *
     * @param array $findings Array of Finding objects
     * @param bool $priorityAreaOnly If true, return only priority area findings
     * @return array Filtered findings
     */
    public static function filterByPriority(array $findings, bool $priorityAreaOnly = true): array
    {
        return array_filter($findings, function (Finding $finding) use ($priorityAreaOnly) {
            return $priorityAreaOnly ? $finding->isPriorityArea() : !$finding->isPriorityArea();
        });
    }

    /**
     * Filter findings by severity levels
     *
     * @param array $findings Array of Finding objects
     * @param array $severities Array of severity levels to include
     * @return array Filtered findings
     */
    public static function filterBySeverity(array $findings, array $severities): array
    {
        return array_filter($findings, function (Finding $finding) use ($severities) {
            return in_array($finding->getSeverity(), $severities);
        });
    }

    /**
     * Filter findings by type
     *
     * @param array $findings Array of Finding objects
     * @param array $types Array of finding types to include
     * @return array Filtered findings
     */
    public static function filterByType(array $findings, array $types): array
    {
        return array_filter($findings, function (Finding $finding) use ($types) {
            return in_array($finding->getType(), $types);
        });
    }

    /**
     * Filter findings by file pattern
     *
     * @param array $findings Array of Finding objects
     * @param string $pattern Regex pattern to match against file paths
     * @return array Filtered findings
     */
    public static function filterByFilePattern(array $findings, string $pattern): array
    {
        return array_filter($findings, function (Finding $finding) use ($pattern) {
            return preg_match($pattern, $finding->getFile());
        });
    }

    /**
     * Get critical and high severity findings only
     *
     * @param array $findings Array of Finding objects
     * @return array Critical and high severity findings
     */
    public static function getCriticalFindings(array $findings): array
    {
        return self::filterBySeverity($findings, [
            Finding::SEVERITY_CRITICAL,
            Finding::SEVERITY_HIGH
        ]);
    }

    /**
     * Get security-related findings only
     *
     * @param array $findings Array of Finding objects
     * @return array Security findings
     */
    public static function getSecurityFindings(array $findings): array
    {
        return self::filterByType($findings, [Finding::TYPE_SECURITY]);
    }

    /**
     * Sort findings by importance (priority and severity)
     *
     * @param array $findings Array of Finding objects
     * @return array Sorted findings (most important first)
     */
    public static function sortByImportance(array $findings): array
    {
        $sorted = $findings;
        usort($sorted, function (Finding $a, Finding $b) {
            return $b->getImportanceScore() - $a->getImportanceScore();
        });
        return $sorted;
    }

    /**
     * Sort findings by file path
     *
     * @param array $findings Array of Finding objects
     * @return array Sorted findings
     */
    public static function sortByFile(array $findings): array
    {
        $sorted = $findings;
        usort($sorted, function (Finding $a, Finding $b) {
            $fileCompare = strcmp($a->getFile(), $b->getFile());
            if ($fileCompare === 0) {
                return $a->getLine() - $b->getLine();
            }
            return $fileCompare;
        });
        return $sorted;
    }

    /**
     * Group findings by file
     *
     * @param array $findings Array of Finding objects
     * @return array Associative array with file paths as keys and findings arrays as values
     */
    public static function groupByFile(array $findings): array
    {
        $grouped = [];
        foreach ($findings as $finding) {
            $file = $finding->getFile();
            if (!isset($grouped[$file])) {
                $grouped[$file] = [];
            }
            $grouped[$file][] = $finding;
        }

        // Sort findings within each file by line number
        foreach ($grouped as $file => $fileFindings) {
            usort($grouped[$file], function (Finding $a, Finding $b) {
                return $a->getLine() - $b->getLine();
            });
        }

        return $grouped;
    }

    /**
     * Group findings by type
     *
     * @param array $findings Array of Finding objects
     * @return array Associative array with types as keys and findings arrays as values
     */
    public static function groupByType(array $findings): array
    {
        $grouped = [];
        foreach ($findings as $finding) {
            $type = $finding->getType();
            if (!isset($grouped[$type])) {
                $grouped[$type] = [];
            }
            $grouped[$type][] = $finding;
        }

        // Sort findings within each type by importance
        foreach ($grouped as $type => $typeFindings) {
            $grouped[$type] = self::sortByImportance($typeFindings);
        }

        return $grouped;
    }

    /**
     * Group findings by severity
     *
     * @param array $findings Array of Finding objects
     * @return array Associative array with severities as keys and findings arrays as values
     */
    public static function groupBySeverity(array $findings): array
    {
        $grouped = [];
        $severityOrder = [
            Finding::SEVERITY_CRITICAL,
            Finding::SEVERITY_HIGH,
            Finding::SEVERITY_MEDIUM,
            Finding::SEVERITY_LOW
        ];

        // Initialize groups in order
        foreach ($severityOrder as $severity) {
            $grouped[$severity] = [];
        }

        // Group findings
        foreach ($findings as $finding) {
            $severity = $finding->getSeverity();
            $grouped[$severity][] = $finding;
        }

        // Sort findings within each severity by priority
        foreach ($grouped as $severity => $severityFindings) {
            usort($grouped[$severity], function (Finding $a, Finding $b) {
                return $b->getPriorityWeight() - $a->getPriorityWeight();
            });
        }

        return $grouped;
    }

    /**
     * Get summary statistics for findings
     *
     * @param array $findings Array of Finding objects
     * @return array Summary statistics
     */
    public static function getSummaryStats(array $findings): array
    {
        $stats = [
            'total' => count($findings),
            'priority_area' => 0,
            'non_priority' => 0,
            'by_severity' => [
                Finding::SEVERITY_CRITICAL => 0,
                Finding::SEVERITY_HIGH => 0,
                Finding::SEVERITY_MEDIUM => 0,
                Finding::SEVERITY_LOW => 0
            ],
            'by_type' => [
                Finding::TYPE_SECURITY => 0,
                Finding::TYPE_PERFORMANCE => 0,
                Finding::TYPE_QUALITY => 0,
                Finding::TYPE_ARCHITECTURE => 0
            ],
            'files_affected' => 0,
            'avg_importance_score' => 0
        ];

        if (empty($findings)) {
            return $stats;
        }

        $totalImportance = 0;
        $uniqueFiles = [];

        foreach ($findings as $finding) {
            // Priority breakdown
            if ($finding->isPriorityArea()) {
                $stats['priority_area']++;
            } else {
                $stats['non_priority']++;
            }

            // Severity breakdown
            $stats['by_severity'][$finding->getSeverity()]++;

            // Type breakdown
            $stats['by_type'][$finding->getType()]++;

            // Track unique files
            $uniqueFiles[$finding->getFile()] = true;

            // Sum importance scores
            $totalImportance += $finding->getImportanceScore();
        }

        $stats['files_affected'] = count($uniqueFiles);
        $stats['avg_importance_score'] = round($totalImportance / count($findings), 2);

        return $stats;
    }

    /**
     * Find the most critical findings (top N by importance)
     *
     * @param array $findings Array of Finding objects
     * @param int $limit Maximum number of findings to return
     * @return array Top critical findings
     */
    public static function getTopCritical(array $findings, int $limit = 10): array
    {
        $sorted = self::sortByImportance($findings);
        return array_slice($sorted, 0, $limit);
    }

    /**
     * Apply multiple filters and return results
     *
     * @param array $findings Array of Finding objects
     * @param array $filters Associative array of filter criteria
     * @return array Filtered findings
     */
    public static function applyFilters(array $findings, array $filters): array
    {
        $result = $findings;

        if (isset($filters['priority_area_only']) && $filters['priority_area_only']) {
            $result = self::filterByPriority($result, true);
        }

        if (isset($filters['severities']) && !empty($filters['severities'])) {
            $result = self::filterBySeverity($result, $filters['severities']);
        }

        if (isset($filters['types']) && !empty($filters['types'])) {
            $result = self::filterByType($result, $filters['types']);
        }

        if (isset($filters['file_pattern']) && !empty($filters['file_pattern'])) {
            $result = self::filterByFilePattern($result, $filters['file_pattern']);
        }

        if (isset($filters['sort_by'])) {
            switch ($filters['sort_by']) {
                case 'importance':
                    $result = self::sortByImportance($result);
                    break;
                case 'file':
                    $result = self::sortByFile($result);
                    break;
            }
        }

        if (isset($filters['limit']) && $filters['limit'] > 0) {
            $result = array_slice($result, 0, $filters['limit']);
        }

        return $result;
    }
}