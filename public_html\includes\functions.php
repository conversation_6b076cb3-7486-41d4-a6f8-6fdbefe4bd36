<?php
/**
 * includes/functions.php
 * Common Helper Functions
 *
 * Changelog:
 * - v1.5 (2025-04-20): Added getCategoryBySlug and getArticlesByCategory functions.
 * - v1.4 (2025-04-17): Added functions for comment handling: fetchComments, addComment.
 * - v1.3 (2025-04-15): Added selected() helper function for dropdowns.
 * - v1.2 (2025-04-06): Added return_bytes() function.
 * - v1.1 (2025-04-06): Increased max_tokens in callDeepSeekAPI from 500 to 2000.
 * - v1.0 (Initial): Base functions.
 */

// =========================================================================
// Basic Helpers
// =========================================================================

/**
 * Escapes HTML special characters for safe output.
 *
 * @param string|null $string The string to escape.
 * @return string The escaped string.
 */
function escape(?string $string): string {
    return htmlspecialchars($string ?? '', ENT_QUOTES, 'UTF-8');
}

/**
 * Generates a URL-friendly slug from a string.
 *
 * @param string $text The input string.
 * @param string $divider The divider character (default is '-').
 * @return string The generated slug.
 */
function generateSlug(string $text, string $divider = '-'): string {
    // Replace non-letter or digits by divider
    $text = preg_replace('~[^\pL\d]+~u', $divider, $text);
    // Transliterate
    if (function_exists('iconv')) {
        $transliterated = @iconv('utf-8', 'us-ascii//TRANSLIT//IGNORE', $text);
        if ($transliterated !== false) {
            $text = $transliterated;
        }
        // Remove characters that are not alphanumeric or the divider after transliteration
        $text = preg_replace('/[^a-zA-Z0-9' . preg_quote($divider, '/') . ']/', '', $text);
    } else {
         // Basic removal if iconv is not available
         $text = preg_replace('/[^a-zA-Z0-9' . preg_quote($divider, '/') . ']/', '', $text);
    }
    // Cleanup: remove divider from start/end, replace multiple dividers with one
    $text = trim($text, $divider);
    $text = preg_replace('~' . preg_quote($divider, '~') . '+~', $divider, $text); // Use preg_quote for the divider
    $text = strtolower($text);

    if (empty($text)) {
        // Generate a short random fallback if the slug becomes empty
        return 'n-a-' . bin2hex(random_bytes(3));
    }
    return $text;
}

/**
 * Generates a random string suitable for filenames.
 * Ensures even length for bin2hex.
 *
 * @param int $length Desired length of the random string (will be rounded up to the nearest even number).
 * @return string Random hexadecimal string.
 */
function generateRandomFilename(int $length = 16): string {
    // Ensure length is even for bin2hex
    if ($length % 2 !== 0) {
        $length++;
    }
    // Generate random bytes and convert to hex
    try {
        return bin2hex(random_bytes($length / 2));
    } catch (Exception $e) {
        // Fallback in case random_bytes fails (highly unlikely)
        return substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstuvwxyz', ceil($length / 36))), 1, $length);
    }
}

/**
 * Simple function to calculate estimated reading time based on word count.
 *
 * @param string $content The article content.
 * @param int $wpm Words per minute (average reading speed).
 * @return int Estimated reading time in minutes.
 */
function estimateReadingTime(string $content, int $wpm = 200): int {
    // Decode HTML entities and strip tags to get plain text
    $plainText = html_entity_decode(strip_tags($content), ENT_QUOTES | ENT_HTML5, 'UTF-8');
    // Count words
    $wordCount = str_word_count($plainText);
    // Ensure WPM is positive
    if ($wpm <= 0) $wpm = 200;
    // Calculate minutes and round up
    $minutes = ceil($wordCount / $wpm);
    // Return at least 1 minute
    return max(1, (int)$minutes);
}

/**
 * Helper function to convert php.ini size notations (e.g., '16M', '1G') to bytes.
 *
 * @param string $val The size string from php.ini.
 * @return float The size in bytes.
 */
function return_bytes(string $val): float {
    $val = trim($val);
    if (empty($val)) return 0; // Handle empty string case
    $last = strtolower($val[strlen($val)-1]);
    $num = (float)$val; // Use float for potentially large numbers
    switch($last) {
        case 'g': $num *= 1024; // Fallthrough intended
        case 'm': $num *= 1024; // Fallthrough intended
        case 'k': $num *= 1024;
    }
    return $num;
}

/**
 * Helper function to output 'selected' if the values match (for dropdowns).
 *
 * @param mixed $currentValue The current value of the setting/field.
 * @param mixed $optionValue The value of the specific option.
 * @return void Echoes ' selected' if values match.
 */
function selected($currentValue, $optionValue): void {
    // Use loose comparison (==) to handle potential type differences (e.g., '1' vs 1)
    if ($currentValue == $optionValue) {
        echo ' selected';
    }
}


// =========================================================================
// Placeholder Image Data Functions (Defined BEFORE getFeaturedImageUrl)
// =========================================================================

/**
 * Gets the data for the default OG image (default.webp).
 *
 * @return array Associative array with 'url', 'width', 'height', 'alt', 'og_alt'.
 */
function getDefaultOgImageData(): array {
    // Ensure SITE_URL is defined before using it
    if (!defined('SITE_URL')) {
        error_log("SITE_URL constant not defined when calling getDefaultOgImageData.");
        // Return some safe defaults or handle the error appropriately
        return [
            'url' => null, 'width' => 400, 'height' => 225,
            'alt' => 'Default image', 'og_alt' => 'Default image'
        ];
    }
    $defaultImagePath = '/uploads/images/site/default.webp'; // Path relative to document root
    $defaultImageUrl = rtrim(SITE_URL, '/') . $defaultImagePath;
    $defaultImageWidth = 400; // Actual width of default.webp
    $defaultImageHeight = 225; // Actual height of default.webp
    $defaultImageAlt = 'Fresh Caprese salad with cherry tomatoes, mozzarella, and basil in a white bowl, served with crusty bread and sea salt on a pastel pink background. A fork and knife are placed beside the text "Lako & Fino," a food recipe website.';
    $defaultOgImageAlt = 'A beautifully arranged Caprese salad with tomatoes, mozzarella, and basil, accompanied by a slice of bread, sea salt, and elegant cutlery. The "Lako & Fino" logo is featured, representing a delicious food recipe website.';

    return [
        'url' => $defaultImageUrl,
        'width' => $defaultImageWidth,
        'height' => $defaultImageHeight,
        'alt' => $defaultImageAlt,
        'og_alt' => $defaultOgImageAlt
    ];
}

/**
 * Gets the data for a generic placeholder (when an image is missing for regular display).
 * Returns null URL so templates can easily check and hide the image element.
 *
 * @param int $width Target width (optional).
 * @param int $height Target height (optional).
 * @return array Associative array with null 'url', dimensions, and generic alt text.
 */
function getGenericPlaceholderData(int $width = 0, int $height = 0): array {
    return [
        'url' => null, // Return null URL to indicate no image should be shown
        'width' => $width,
        'height' => $height,
        'alt' => 'Image not available',
        'og_alt' => 'Image not available' // Same generic alt for consistency
    ];
}

// =========================================================================
// Image URL Generation (Uses Placeholder Functions)
// =========================================================================

/**
 * Gets the full URL and dimensions for a featured image, handling different sizes, CDN, and placeholders.
 * Checks for WebP first, then common fallbacks (jpg, png, gif).
 * Returns specific placeholder data based on context (OG vs generic).
 *
 * @param string|null $baseFilename The base filename stored in the database (e.g., 'randomletters').
 * @param string $sizeSuffix The desired size suffix ('ls', 'ms', 'ss', 'fb') or 'original'.
 * @param string $uploadSubDir Subdirectory within UPLOAD_DIR where the image resides (e.g., 'articles').
 * @param string $defaultAlt Fallback alt text if specific alt text isn't available for the image.
 * @return array Associative array with 'url', 'width', 'height', 'alt', 'og_alt'.
 */
function getFeaturedImageUrl(?string $baseFilename, string $sizeSuffix = 'ls', string $uploadSubDir = '', string $defaultAlt = 'Article image'): array {
    // --- Configuration Check ---
    if (!defined('IMAGE_SIZES') || !defined('UPLOAD_DIR') || !defined('SITE_URL')) {
        error_log("Required constants (IMAGE_SIZES, UPLOAD_DIR, SITE_URL) not defined in config.php for getFeaturedImageUrl.");
        // Return appropriate placeholder based on context
        return ($sizeSuffix === 'fb') ? getDefaultOgImageData() : getGenericPlaceholderData();
    }

    // --- Determine target dimensions based on suffix ---
    $defaultWidth = 400; // Default fallback width
    $defaultHeight = 225; // Default fallback height
    $targetWidth = IMAGE_SIZES[$sizeSuffix] ?? $defaultWidth;
    $targetHeight = $defaultHeight; // Default height, will be calculated or fixed

    if ($sizeSuffix === 'fb') {
        $targetWidth = IMAGE_SIZES['fb'] ?? 1200;
        $targetHeight = 630;
    } elseif (isset(IMAGE_SIZES[$sizeSuffix])) {
        // Estimate height initially - This is just a fallback if file dimensions can't be read
        $estimatedAspectRatio = 9 / 16; // Default aspect ratio estimate (16:9)
        $targetHeight = round($targetWidth * $estimatedAspectRatio);
    } else {
        // Fallback to default dimensions if suffix is invalid
        $targetWidth = $defaultWidth;
        $targetHeight = $defaultHeight;
    }

    // --- Handle Empty Base Filename ---
    if (empty($baseFilename)) {
        // If no base filename, return the correct placeholder based on size suffix
        return ($sizeSuffix === 'fb') ? getDefaultOgImageData() : getGenericPlaceholderData($targetWidth, $targetHeight);
    }

    // --- Construct Path Components ---
    // Ensure DOCUMENT_ROOT is available and reliable
    $docRoot = rtrim($_SERVER['DOCUMENT_ROOT'], '/');
    if (empty($docRoot)) {
        error_log("DOCUMENT_ROOT is empty in getFeaturedImageUrl.");
        return ($sizeSuffix === 'fb') ? getDefaultOgImageData() : getGenericPlaceholderData($targetWidth, $targetHeight);
    }
    $baseUploadPath = trim(UPLOAD_DIR, '/');
    $subDirPath = !empty($uploadSubDir) ? trim($uploadSubDir, '/') . '/' : '';
    $fullUploadDir = $docRoot . '/' . $baseUploadPath . '/' . $subDirPath;

    // --- Check for Image Files ---
    $preferredExtension = 'webp';
    $fallbackExtensions = ['jpg', 'png', 'gif'];
    $finalRelativePath = null;
    $foundExtension = null;
    $actualWidth = null;
    $actualHeight = null;

    // Check for WebP first
    $filenameWebp = $baseFilename . '_' . $sizeSuffix . '.' . $preferredExtension;
    if (file_exists($fullUploadDir . $filenameWebp)) {
        $finalRelativePath = $baseUploadPath . '/' . $subDirPath . $filenameWebp;
        $foundExtension = $preferredExtension;
        // Attempt to get image dimensions
        $imageInfo = @getimagesize($fullUploadDir . $filenameWebp);
        if ($imageInfo) {
            $actualWidth = $imageInfo[0];
            $actualHeight = $imageInfo[1];
        }
    } else {
        // Check fallback extensions if WebP not found
        foreach ($fallbackExtensions as $ext) {
            $filenameFallback = $baseFilename . '_' . $sizeSuffix . '.' . $ext;
            if (file_exists($fullUploadDir . $filenameFallback)) {
                $finalRelativePath = $baseUploadPath . '/' . $subDirPath . $filenameFallback;
                $foundExtension = $ext;
                $imageInfo = @getimagesize($fullUploadDir . $filenameFallback);
                 if ($imageInfo) {
                     $actualWidth = $imageInfo[0];
                     $actualHeight = $imageInfo[1];
                 }
                break; // Stop checking once a fallback is found
            }
        }
    }

    // --- Handle Not Found ---
    if ($finalRelativePath === null) {
        // If image file not found, return the correct placeholder based on size suffix
        return ($sizeSuffix === 'fb') ? getDefaultOgImageData() : getGenericPlaceholderData($targetWidth, $targetHeight);
    }

    // --- Recalculate Target Height based on Actual Dimensions if found ---
    // Only recalculate if not the fixed FB size and dimensions were successfully read
    if ($actualWidth && $actualHeight && $actualWidth > 0 && $sizeSuffix !== 'fb') {
        $targetHeight = round($targetWidth * ($actualHeight / $actualWidth));
    }

    // Ensure dimensions are at least 1 pixel
    $finalWidth = max(1, $targetWidth);
    $finalHeight = max(1, $targetHeight);

    // --- Determine Base URL (CDN or Site URL) ---
    $baseUrl = (defined('CDN_ENABLED') && CDN_ENABLED && defined('CDN_BASE_URL') && !empty(CDN_BASE_URL))
        ? rtrim(CDN_BASE_URL, '/') . '/' // Use CDN URL
        : rtrim(SITE_URL, '/') . '/';     // Use Site URL
    $fullUrl = $baseUrl . $finalRelativePath;

    // --- Alt Text Handling ---
    // Use provided default alt for the actual image found
    $finalAlt = $defaultAlt;
    $finalOgAlt = $defaultAlt; // Use the same for OG alt for now, can be customized

    return [
        'url' => $fullUrl,
        'width' => $finalWidth,   // Use the final calculated/fixed width
        'height' => $finalHeight, // Use the final calculated/fixed height
        'alt' => $finalAlt,       // Alt text for the specific image
        'og_alt' => $finalOgAlt    // OG Alt text for the specific image
    ];
}

// =========================================================================
// Advanced Dynamic Responsive Image Generation
// =========================================================================

/**
 * Generates responsive image HTML using dynamic image processing.
 * This approach uses a single source image and generates optimized versions on-demand.
 *
 * @param string|null $baseFilename The base filename stored in the database
 * @param string $uploadSubDir Subdirectory within uploads (e.g., 'articles')
 * @param string $defaultAlt Alt text for the image
 * @param array $options Additional options:
 *   - 'widths': array of widths to include in srcset (default: [400, 800, 1200, 1600])
 *   - 'isPrimary': whether this is a primary/LCP image (default: false)
 *   - 'lazyLoad': whether to use lazy loading (default: true)
 *   - 'linkUrl': URL to wrap the image in (default: null)
 *   - 'imgClass': CSS classes for the img element (default: 'w-full h-auto object-cover')
 *   - 'containerClass': CSS classes for the container (default: '')
 *   - 'usePicture': whether to use picture element (default: true)
 *   - 'sizesAttr': sizes attribute for responsive images (default: '(max-width: 767px) 100vw, 720px')
 *   - 'fit': fit method (default: 'cover', options: 'cover', 'contain', 'fill')
 *   - 'quality': image quality (default: 85, range: 10-100)
 * @return string HTML markup for the responsive image
 */
function getResponsiveImageHtml(?string $baseFilename, string $uploadSubDir = '', string $defaultAlt = 'Image', array $options = []): string {
    if (empty($baseFilename)) {
        return ''; // No image to display
    }

    // Default options
    $defaults = [
        'widths' => [360], // Single width - most images display at this size on mobile and desktop
        'isPrimary' => false,
        'lazyLoad' => true,
        'linkUrl' => null,
        'imgClass' => 'w-full h-auto object-cover rounded-xl',
        'containerClass' => '',
        'usePicture' => false, // Switched to simpler img with srcset
        'sizesAttr' => '100vw', // Simplest size attribute
        'fit' => 'cover',
        'quality' => 70 // Further reduced quality for better compression
    ];

    // Merge provided options with defaults
    $options = array_merge($defaults, $options);

    // Extract options for readability
    $widths = $options['widths'];
    $isPrimary = $options['isPrimary'];
    $lazyLoad = $options['lazyLoad'] && !$isPrimary; // Never lazy load primary images
    $linkUrl = $options['linkUrl'];
    $imgClass = $options['imgClass'];
    $containerClass = $options['containerClass'];
    $usePicture = $options['usePicture'];
    $sizesAttr = $options['sizesAttr'];
    $fit = $options['fit'];
    $quality = $options['quality'];

    // Map string size identifiers to actual pixel widths
    $sizeMap = [
        'xs' => 320,  // Extra small
        'ss' => 400,  // Small size
        'ms' => 800,  // Medium size
        'ls' => 1200, // Large size
        'fb' => 1200  // Facebook/OG image size
    ];

    // Convert string width identifiers to numeric values
    $numericWidths = [];
    foreach ($widths as $width) {
        if (is_string($width) && isset($sizeMap[$width])) {
            $numericWidths[] = $sizeMap[$width];
        } elseif (is_numeric($width)) {
            $numericWidths[] = (int)$width;
        } else {
            // Default to small size if unknown
            $numericWidths[] = 400;
        }
    }

    // Replace string widths with numeric widths
    $widths = $numericWidths;

    // Clean the filename to prevent directory traversal
    $baseFilename = str_replace('../', '', $baseFilename);
    $baseFilename = ltrim($baseFilename, '/');

    // Construct the path to the original image
    $originalPath = $uploadSubDir . '/' . $baseFilename;

    // Get the original image dimensions if possible
    $originalWidth = 1600; // Default fallback width
    $originalHeight = 900; // Default fallback height

    // Try to find the image in the root directory first
    $originalImagePath = __DIR__ . '/../' . $originalPath;

    // If not found, try the uploads directory
    if (!file_exists($originalImagePath)) {
        $originalImagePath = __DIR__ . '/../uploads/' . $originalPath;
    }

    if (file_exists($originalImagePath)) {
        $imageInfo = getimagesize($originalImagePath);
        if ($imageInfo) {
            $originalWidth = $imageInfo[0];
            $originalHeight = $imageInfo[1];
        }
    }

    // Start building HTML
    $html = '';

    // Define image width for container
    $containerWidth = 360; // Fixed width for container

    // Add container if specified
    if (!empty($containerClass)) {
        // Remove any rounded corners or overflow classes from the container
        $cleanedClass = str_replace(['rounded-xl', 'overflow-hidden', 'bg-gray-100'], '', $containerClass);
        $html .= '<div class="' . trim($cleanedClass) . ' flex justify-center">';
        $html .= '<div style="max-width: ' . $containerWidth . 'px; width: 100%;">';
    }

    // Add link opening tag if URL provided
    if ($linkUrl) {
        $html .= '<a href="' . escape($linkUrl) . '">';
    }

    // Generate image URLs for different widths
    $srcsetParts = [];
    foreach ($widths as $width) {
        // Ensure width is numeric
        $width = (int)$width;

        // Calculate height proportionally
        $height = round($width * ($originalHeight / $originalWidth));

        // Generate URL for this size
        // Make sure we're using the correct path format for image.php
        // For articles, the path should be 'articles/filename'
        $imageUrl = '/image.php?src=' . urlencode($uploadSubDir . '/' . $baseFilename) .
                   '&w=' . $width .
                   '&h=' . $height .
                   '&q=' . $quality .
                   '&fit=' . $fit .
                   '&f=webp'; // WebP format for better compression

        $srcsetParts[] = $imageUrl . ' ' . $width . 'w';
    }

    // We're using a single width (360px) for all images
    // No need to calculate different sizes

    // Generate fallback image URL with fixed width
    $fallbackWidth = 360; // Fixed width for fallback image
    // Ensure dimensions are numeric
    $fallbackWidth = (int)$fallbackWidth;
    $originalWidth = (int)$originalWidth;
    $originalHeight = (int)$originalHeight;
    $fallbackHeight = round($fallbackWidth * ($originalHeight / $originalWidth));
    $fallbackUrl = '/image.php?src=' . urlencode($uploadSubDir . '/' . $baseFilename) .
                  '&w=' . $fallbackWidth .
                  '&h=' . $fallbackHeight .
                  '&q=' . $quality .
                  '&fit=' . $fit .
                  '&f=webp'; // WebP format for better compression

    if ($usePicture) {
        // Use <picture> element for more precise control
        $html .= '<picture>';

        // Define breakpoints and corresponding widths - single size for all devices
        $breakpoints = [
            '(min-width: 1px)' => 360                 // Single size for all devices
        ];

        // Ensure breakpoint widths are numeric
        foreach ($breakpoints as $media => $width) {
            if (is_string($width) && isset($sizeMap[$width])) {
                $breakpoints[$media] = $sizeMap[$width];
            }
        }

        // Add sources for each breakpoint
        foreach ($breakpoints as $media => $width) {
            // Ensure width is numeric
            $width = (int)$width;
            $height = round($width * ($originalHeight / $originalWidth));
            $sourceUrl = '/image.php?src=' . urlencode($uploadSubDir . '/' . $baseFilename) .
                        '&w=' . $width .
                        '&h=' . $height .
                        '&q=' . $quality .
                        '&fit=' . $fit .
                        '&f=webp'; // WebP format for better compression

            $html .= '<source media="' . $media . '" srcset="' . $sourceUrl . '">';
        }

        // Add fallback img element
        $fallbackWidth = 360; // Fixed width for fallback image
        // Ensure dimensions are numeric
        $fallbackWidth = (int)$fallbackWidth;
        $fallbackHeight = round($fallbackWidth * ($originalHeight / $originalWidth));
        $imgAttributes = [
            'src' => $fallbackUrl,
            'alt' => escape($defaultAlt),
            'width' => $fallbackWidth,
            'height' => $fallbackHeight,
            'class' => $imgClass
        ];

        // Add loading attribute if not primary
        if ($isPrimary) {
            $imgAttributes['fetchpriority'] = 'high';
        } elseif ($lazyLoad) {
            $imgAttributes['loading'] = 'lazy';
        }

        // Build img tag
        $html .= '<img';
        foreach ($imgAttributes as $attr => $value) {
            $html .= ' ' . $attr . '="' . $value . '"';
        }
        $html .= '>';

        $html .= '</picture>';
    } else {
        // Use srcset/sizes approach
        // Build img attributes
        $imgAttributes = [
            'src' => $fallbackUrl,
            'alt' => escape($defaultAlt),
            'width' => $fallbackWidth, // Use fallbackWidth instead of smallestWidth
            'height' => $fallbackHeight, // Use fallbackHeight instead of smallestHeight
            'class' => $imgClass,
            'srcset' => implode(', ', $srcsetParts),
            'sizes' => $sizesAttr
        ];

        // Add loading attribute if not primary
        if ($isPrimary) {
            $imgAttributes['fetchpriority'] = 'high';
        } elseif ($lazyLoad) {
            $imgAttributes['loading'] = 'lazy';
        }

        // Build img tag
        $html .= '<img';
        foreach ($imgAttributes as $attr => $value) {
            $html .= ' ' . $attr . '="' . $value . '"';
        }
        $html .= '>';
    }

    // Close link tag if opened
    if ($linkUrl) {
        $html .= '</a>';
    }

    // Close container if opened
    if (!empty($containerClass)) {
        $html .= '</div>'; // Close inner div
        $html .= '</div>'; // Close outer container
    }

    return $html;
}

// =========================================================================
// Date Formatting (Uses IntlDateFormatter if available)
// =========================================================================

/**
 * Formats a date string. Uses IntlDateFormatter for better localization if available.
 *
 * @param string|null $dateString The date string (e.g., from database timestamp).
 * @param string $format The desired output format (default: 'd. M. Y.') or IntlDateFormatter pattern.
 * @param string $locale The locale for formatting (default: 'bs_BA').
 * @return string The formatted date string or empty string if input is invalid.
 */
function formatDate(?string $dateString, string $format = 'd. M. Y.', string $locale = 'bs_BA'): string {
    if (empty($dateString)) {
        return '';
    }
    try {
        $date = new DateTime($dateString);

        // Use IntlDateFormatter if available for better localization
        if (extension_loaded('intl')) {
            // Determine pattern type based on format string
            $dateType = IntlDateFormatter::MEDIUM; // Default date type
            $timeType = IntlDateFormatter::NONE;   // Default time type
            // Check if format string includes time components
            if (str_contains($format, 'H') || str_contains($format, 'h') || str_contains($format, 'i') || str_contains($format, 's')) {
                 $timeType = IntlDateFormatter::SHORT; // Include short time if format has time chars
            }

            // Define explicit ICU patterns for common PHP formats to ensure accuracy
            $icuPattern = match ($format) {
                'd. MMMM Y.' => 'd. MMMM yyyy.', // Corrected ICU pattern for full month name
                'd. M. Y.' => 'd. M. yyyy.', // ICU pattern for day.numeric_month.year.
                'd.M.Y H:i' => 'dd.MM.yyyy HH:mm',
                'd.M.Y' => 'dd.MM.yyyy',
                'M j, Y H:i' => 'MMM d, yyyy HH:mm', // Corrected ICU pattern
                'M j, Y' => 'MMM d, yyyy', // Added mapping for date only
                // Add more mappings as needed
                default => $format // Use the PHP format string directly as ICU pattern if no match (might work for simple cases)
            };

            // Create the formatter
            $formatter = new IntlDateFormatter(
                $locale,
                $dateType, // Use determined date type
                $timeType, // Use determined time type
                null, // Timezone (null for server default)
                IntlDateFormatter::GREGORIAN,
                $icuPattern // Use the determined ICU pattern
            );

            if ($formatter) {
                $formatted = $formatter->format($date);
                // Check if formatting was successful
                if ($formatted !== false) {
                    return $formatted;
                } else {
                     // Log error if Intl formatting fails
                     error_log("IntlDateFormatter failed for pattern: " . $icuPattern . " | Locale: " . $locale . " | Error: " . intl_get_error_message());
                }
            }
        }

        // --- Fallback Methods if Intl is not available or failed ---
        // Fallback 1: Using strftime (may be deprecated in future PHP versions)
        if (function_exists('strftime')) {
            // Set locale for strftime
            setlocale(LC_TIME, $locale . '.UTF-8', $locale);
            // Convert PHP date format characters to strftime format characters
            $strftime_format = str_replace(
                ['d', 'M', 'Y', 'H', 'i', 'm', 'y', 'j', 'F', 'l', 'D'], // PHP chars
                ['%d', '%m', '%Y', '%H', '%M', '%m', '%y', '%e', '%B', '%A', '%a'], // strftime equivalents (%m for numeric month)
                $format
            );
             // Handle specific cases like 'M.' which might become '%m.' - use abbreviated month '%b' instead
             $strftime_format = str_replace('%m.', '%b.', $strftime_format);

            $formatted = strftime($strftime_format, $date->getTimestamp());
            // Check if strftime returned a non-empty string
            if ($formatted) return $formatted;
        }

        // Absolute fallback using date() and manual month replacement (Least ideal for localization)
        $months_bs = [
            'January' => 'januar', 'February' => 'februar', 'March' => 'mart', 'April' => 'april',
            'May' => 'maj', 'June' => 'jun', 'July' => 'jul', 'August' => 'avgust', // Corrected spelling
            'September' => 'septembar', 'October' => 'oktobar', 'November' => 'novembar', 'December' => 'decembar',
            'Jan' => 'jan', 'Feb' => 'feb', 'Mar' => 'mar', 'Apr' => 'apr',
            'May' => 'maj', 'Jun' => 'jun', 'Jul' => 'jul', 'Aug' => 'avg', // Corrected spelling
            'Sep' => 'sep', 'Oct' => 'okt', 'Nov' => 'nov', 'Dec' => 'dec'
        ];
        // Map MMMM and MMM to PHP's F and M for full/abbreviated month names
        $phpDateFormat = str_replace(['MMMM', 'MMM'], ['F', 'M'], $format);
        $formatted = $date->format($phpDateFormat);
        // Translate month names if present
        return strtr($formatted, $months_bs);

    } catch (Exception $e) {
        // Log any date parsing/formatting errors
        error_log("Error formatting date '$dateString': " . $e->getMessage());
        return ''; // Return empty string on error
    }
}

// =========================================================================
// Image Upload and Processing (Main Function)
// =========================================================================

/**
 * Handles image upload, processing, and resizing.
 * Supports both file uploads via $_FILES and fetching from a URL.
 * Attempts to use Imagick, falls back to GD.
 *
 * @param array|string $source Either the $_FILES array entry for the upload (e.g., $_FILES['image']) or a URL string.
 * @param string $uploadSubDir Subdirectory within UPLOAD_DIR (e.g., 'articles'). Defaults to root of UPLOAD_DIR.
 * @return string|array The base random filename (without suffix/extension) on success, or an array ['error' => 'message'] on failure.
 */
function handleImageUpload($source, string $uploadSubDir = ''): string|array {
    // --- Configuration Check ---
    if (!defined('UPLOAD_DIR') || !defined('ALLOWED_MIME_TYPES') || !defined('MAX_FILE_SIZE') || !defined('IMAGE_QUALITY') || !defined('IMAGE_SIZES')) {
        return ['error' => 'Image processing configuration constants are missing in config.php.'];
    }

    // --- Determine Input Type and Validate ---
    $tempPath = null;
    $originalFilename = 'image.jpg'; // Default original name
    $mimeType = null;
    $isUrl = false;

    // Check if input is a URL string
    if (is_string($source) && filter_var($source, FILTER_VALIDATE_URL)) {
        $isUrl = true;
        $pathInfo = pathinfo(parse_url($source, PHP_URL_PATH));
        // Basic check for common image extensions in URL
        $extension = strtolower($pathInfo['extension'] ?? '');
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array($extension, $allowedExtensions)) {
             return ['error' => 'Invalid image extension found in URL.'];
        }
        $originalFilename = $pathInfo['basename'] ?? $originalFilename;

        // --- Download Image using cURL (more robust than file_get_contents) ---
        $ch = curl_init($source);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return content instead of outputting it
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Follow redirects
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);          // Timeout in seconds
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true); // Verify SSL certificate (recommended)
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);   // Verify hostname in certificate
        $imageData = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        // Check for download errors
        if ($imageData === false || $httpCode !== 200) {
            $errorMsg = 'Failed to download image from URL.';
            if ($curlError) $errorMsg .= ' cURL Error: ' . $curlError;
            if ($httpCode !== 200) $errorMsg .= ' HTTP Status: ' . $httpCode;
            return ['error' => $errorMsg];
        }

        // Create a temporary file to store the downloaded image
        $tempPath = tempnam(sys_get_temp_dir(), 'img_');
        if ($tempPath === false || file_put_contents($tempPath, $imageData) === false) {
            if ($tempPath && file_exists($tempPath)) @unlink($tempPath); // Clean up if temp file created but write failed
            return ['error' => 'Failed to create temporary file for downloaded image. Check system temp directory permissions.'];
        }
        // Get Mime Type using finfo (more reliable than trusting URL extension)
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $tempPath);
        finfo_close($finfo);

    // Check if input is a valid file upload array
    } elseif (is_array($source) && isset($source['error']) && $source['error'] === UPLOAD_ERR_OK) {
        // Validate if it's actually an uploaded file
        if (!isset($source['tmp_name']) || !is_uploaded_file($source['tmp_name'])) {
             return ['error' => 'Invalid file upload provided.'];
        }
        $tempPath = $source['tmp_name'];
        $originalFilename = $source['name'];

        // Get Mime Type using finfo (more reliable than browser-provided type)
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $tempPath);
        finfo_close($finfo);
        // Fallback to browser-provided type if finfo fails (less reliable)
        if (!$mimeType && isset($source['type'])) {
             $mimeType = $source['type'];
        }

        // Validate file size
        if ($source['size'] > MAX_FILE_SIZE) {
            // No need to unlink tempPath here, PHP handles uploaded temp files
            return ['error' => 'File size exceeds the limit of ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB.'];
        }
    } else {
        // Handle other upload errors or invalid input
        $uploadErrors = [
            UPLOAD_ERR_INI_SIZE   => 'File exceeds upload_max_filesize directive in php.ini.',
            UPLOAD_ERR_FORM_SIZE  => 'File exceeds MAX_FILE_SIZE directive specified in the HTML form.',
            UPLOAD_ERR_PARTIAL    => 'File was only partially uploaded.',
            UPLOAD_ERR_NO_FILE    => 'No file was uploaded.',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder on server.',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk on server.',
            UPLOAD_ERR_EXTENSION  => 'A PHP extension stopped the file upload.',
        ];
        $errorCode = is_array($source) && isset($source['error']) ? $source['error'] : UPLOAD_ERR_NO_FILE;
        // If the error is NO_FILE, return a specific message that process_article can check
        if ($errorCode === UPLOAD_ERR_NO_FILE) {
             return ['error_code' => UPLOAD_ERR_NO_FILE, 'error' => $uploadErrors[$errorCode]];
        }
        return ['error' => $uploadErrors[$errorCode] ?? 'Unknown upload error or invalid input provided.'];
    }

    // --- Validate MIME Type ---
    if (!$mimeType || !in_array($mimeType, ALLOWED_MIME_TYPES)) {
        if ($tempPath && file_exists($tempPath)) @unlink($tempPath); // Clean up temp file
        return ['error' => 'Invalid file type (' . ($mimeType ?: 'unknown') . '). Allowed types: ' . implode(', ', ALLOWED_MIME_TYPES)];
    }

    // --- Prepare Paths and Filename ---
    $docRoot = rtrim($_SERVER['DOCUMENT_ROOT'], '/');
    if (empty($docRoot)) {
         if ($tempPath && file_exists($tempPath)) @unlink($tempPath);
         return ['error' => 'Could not determine server document root. Check server configuration.'];
    }
    // Construct the full absolute path for uploads
    $uploadBasePath = $docRoot . '/' . trim(UPLOAD_DIR, '/');
    $uploadPath = rtrim($uploadBasePath . '/' . trim($uploadSubDir, '/'), '/'); // Ensure no double slash at the end

    // Ensure the upload directory exists and is writable
    if (!is_dir($uploadPath)) {
        // Attempt to create the directory recursively
        if (!mkdir($uploadPath, 0755, true)) { // Use 0755 permissions, recursive creation
            if ($tempPath && file_exists($tempPath)) @unlink($tempPath);
            return ['error' => 'Upload directory does not exist and could not be created: ' . $uploadPath];
        }
    }
    if (!is_writable($uploadPath)) {
        if ($tempPath && file_exists($tempPath)) @unlink($tempPath);
        return ['error' => 'Upload directory is not writable: ' . $uploadPath];
    }

    // Generate a unique base filename
    $randomFilenameBase = generateRandomFilename();
    // Define preferred and fallback output formats
    $outputFormat = 'webp'; // Prefer WebP
    $fallbackFormat = match ($mimeType) {
        'image/jpeg' => 'jpg',
        'image/png' => 'png',
        'image/gif' => 'gif', // Keep GIF as GIF if needed, or convert to webp/png
        default => 'jpg', // Default fallback
    };
    $finalExtension = $outputFormat; // Assume WebP will be used

    // --- Processing Logic ---
    $useImagick = extension_loaded('imagick');
    $useGd = extension_loaded('gd');

    // Check if at least one processing library is available
    if (!$useImagick && !$useGd) {
        if ($tempPath && file_exists($tempPath)) @unlink($tempPath);
        return ['error' => 'Image processing requires the GD or Imagick PHP extension, but neither is enabled.'];
    }

    $processingSuccess = false; // Flag to track if at least one size was saved

    try {
        if ($useImagick) {
            // --- Imagick Processing ---
            $imagick = new Imagick($tempPath);
            // Handle multi-layer images (like animated GIFs) - flatten to first frame
            if ($imagick->getNumberImages() > 1) {
                $imagick = $imagick->coalesceImages(); // Get individual frames
                $imagick = $imagick->deconstructImages(); // Optimize frame disposal
                $imagick = $imagick->getImage(); // Get the first frame
            }
            $imagick->setImageFormat($outputFormat); // Convert to WebP
            $imagick->setImageCompressionQuality(IMAGE_QUALITY); // Set compression quality
            $imagick->stripImage(); // Remove metadata (EXIF, etc.)

            // Loop through defined image sizes
            foreach (IMAGE_SIZES as $suffix => $targetWidth) {
                $img = clone $imagick; // Work on a copy for each size
                $currentWidth = $img->getImageWidth();
                $currentHeight = $img->getImageHeight();
                if ($currentWidth <= 0) continue; // Skip if width is invalid

                $aspectRatio = $currentHeight / $currentWidth;
                $targetHeight = round($targetWidth * $aspectRatio);

                // Special handling for Facebook size (crop to 1200x630)
                if ($suffix === 'fb') {
                    $targetHeight = 630;
                    // Resize to cover the target dimensions, then crop from center
                    $img->resizeImage($targetWidth, $targetHeight, Imagick::FILTER_LANCZOS, 1, true); // Use bestfit flag
                    $img->cropThumbnailImage($targetWidth, $targetHeight); // Crop to exact dimensions
                } else {
                    // Standard resize for other sizes
                    $img->resizeImage($targetWidth, $targetHeight, Imagick::FILTER_LANCZOS, 1);
                }

                // Define output path and save
                $outputFilename = $randomFilenameBase . '_' . $suffix . '.' . $outputFormat;
                $outputPath = $uploadPath . '/' . $outputFilename;
                if ($img->writeImage($outputPath)) {
                    $processingSuccess = true; // Mark success if write is successful
                } else {
                    // Log error if saving fails for a specific size
                    error_log("Imagick failed to save image size '{$suffix}': " . $outputPath);
                }
                $img->clear(); // Clear the cloned object
            }
            $imagick->clear(); // Clear the original object

        } else {
            // --- GD Processing ---
            $finalExtension = $fallbackFormat; // Use fallback extension if GD doesn't handle WebP well
            // Create image resource from temporary file based on MIME type
            $image = match ($mimeType) {
                'image/jpeg' => @imagecreatefromjpeg($tempPath),
                'image/png' => @imagecreatefrompng($tempPath),
                'image/gif' => @imagecreatefromgif($tempPath),
                'image/webp' => function_exists('imagecreatefromwebp') ? @imagecreatefromwebp($tempPath) : false, // Check if webp is supported
                default => false,
            };
            if ($image === false) throw new Exception('GD failed to load image from temporary path (' . $mimeType . ').');

            // Preserve transparency for PNG and GIF
            if ($mimeType == 'image/png' || $mimeType == 'image/gif') {
                imagealphablending($image, false); // Required for transparency
                imagesavealpha($image, true);      // Save alpha channel
            }
            $currentWidth = imagesx($image);
            $currentHeight = imagesy($image);
            if ($currentWidth <= 0) throw new Exception('Invalid image width detected by GD.');

            // Loop through defined image sizes
            foreach (IMAGE_SIZES as $suffix => $targetWidth) {
                $aspectRatio = $currentHeight / $currentWidth;
                $targetHeight = round($targetWidth * $aspectRatio);
                // Create a new true color image resource for the resized image
                $resizedImage = imagecreatetruecolor($targetWidth, $targetHeight);

                // Handle transparency for the destination image if needed
                if ($finalExtension == 'png' || $finalExtension == 'gif') {
                    imagealphablending($resizedImage, false);
                    imagesavealpha($resizedImage, true);
                    $transparent = imagecolorallocatealpha($resizedImage, 255, 255, 255, 127); // Fully transparent
                    imagefilledrectangle($resizedImage, 0, 0, $targetWidth, $targetHeight, $transparent);
                    if ($finalExtension == 'gif') imagecolortransparent($resizedImage, $transparent); // Set transparent color for GIF
                }
                // Resize the original image into the new resource
                imagecopyresampled($resizedImage, $image, 0, 0, 0, 0, $targetWidth, $targetHeight, $currentWidth, $currentHeight);

                // Define output path and save based on the final extension
                $outputFilename = $randomFilenameBase . '_' . $suffix . '.' . $finalExtension;
                $outputPath = $uploadPath . '/' . $outputFilename;
                $saveResult = match ($finalExtension) {
                    'jpg' => imagejpeg($resizedImage, $outputPath, IMAGE_QUALITY),
                    'png' => imagepng($resizedImage, $outputPath, 9 - round((IMAGE_QUALITY / 100) * 9)), // PNG quality 0-9
                    'gif' => imagegif($resizedImage, $outputPath),
                    // Add webp saving if GD supports it (requires check)
                    // 'webp' => function_exists('imagewebp') ? imagewebp($resizedImage, $outputPath, IMAGE_QUALITY) : false,
                    default => false,
                };
                // Check if saving was successful
                if ($saveResult) {
                    $processingSuccess = true;
                } else {
                    error_log("GD failed to save image size '{$suffix}' as {$finalExtension}: " . $outputPath);
                }
                imagedestroy($resizedImage); // Free memory for the resized image
            }
            imagedestroy($image); // Free memory for the original loaded image
        }
    } catch (Exception $e) {
        // Catch any exception during processing (Imagick or GD)
        if ($tempPath && file_exists($tempPath)) @unlink($tempPath); // Clean up temp file on error
        return ['error' => 'Image processing failed: ' . $e->getMessage()];
    }

    // --- Cleanup Temporary File ---
    // Ensure temp file is deleted if it still exists (especially after URL download)
    if ($tempPath && file_exists($tempPath)) {
        @unlink($tempPath);
    }

    // --- Return Result ---
    if ($processingSuccess) {
        // Return the base filename if at least one size was successfully saved
        return $randomFilenameBase;
    } else {
        // Return error if processing finished but no files were saved
        return ['error' => 'Image processing completed, but failed to save any required image sizes. Check logs and permissions.'];
    }
}


// =========================================================================
// Database Fetching Functions
// =========================================================================

/**
 * Fetches a single article by its slug.
 * Includes author and primary category information.
 * Only fetches 'published' articles that are currently viewable.
 * Note: Use getArticleCategories() to get all categories for the article.
 *
 * @param PDO $pdo The PDO database connection object.
 * @param string $slug The article slug.
 * @return array|null The article data as an associative array, or null if not found or not published.
 */
function getArticleBySlug(PDO $pdo, string $slug): ?array {
    $sql = "SELECT
                a.*, -- Select all columns from articles table
                au.id as author_id, au.name as author_name,
                au.avatar_url as author_avatar, au.bio as author_bio, -- Author details
                c.id as category_id, c.name as category_name, c.slug as category_slug -- Primary category details
            FROM articles a
            LEFT JOIN authors au ON a.author_id = au.id -- Join with authors table
            LEFT JOIN categories c ON a.category_id = c.id -- Join with primary category
            WHERE a.slug = :slug -- Match the provided slug
              AND a.status = 'published' -- Ensure article is published
              AND (a.published_at IS NULL OR a.published_at <= NOW()) -- Ensure publish date is now or in the past (or null)
            LIMIT 1"; // Fetch only one article
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
    $stmt->execute();
    $article = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch as associative array
    return $article ?: null; // Return article data or null if not found
}

/**
 * Fetches recent articles for the homepage or listings.
 * Includes author and primary category information.
 * Only fetches 'published' articles that are currently viewable.
 * Note: This only returns the primary category. Use getArticleCategories() to get all categories.
 *
 * @param PDO $pdo The PDO database connection object.
 * @param int $limit Number of articles to fetch. Defaults to ARTICLES_PER_PAGE constant if defined, otherwise 6.
 * @return array An array of article data.
 */
function getRecentArticles(PDO $pdo, int $limit = 0): array {
    // Use constant if defined, otherwise default to 6
    $effectiveLimit = ($limit > 0) ? $limit : (defined('ARTICLES_PER_PAGE') ? ARTICLES_PER_PAGE : 6);

    $sql = "SELECT
                a.id, a.title, a.slug, a.excerpt, a.featured_image, a.created_at, a.published_at, a.reading_time,
                au.name as author_name, au.avatar_url as author_avatar, -- Author details
                c.name as category_name, c.slug as category_slug -- Primary category details
            FROM articles a
            LEFT JOIN authors au ON a.author_id = au.id
            LEFT JOIN categories c ON a.category_id = c.id
            WHERE a.status = 'published' AND (a.published_at IS NULL OR a.published_at <= NOW()) -- Filter for published and viewable
            ORDER BY COALESCE(a.published_at, a.created_at) DESC -- Order by publish date (fallback to creation date)
            LIMIT :limit"; // Limit the number of results
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':limit', $effectiveLimit, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC); // Return all fetched articles
}


/**
 * Fetches popular articles (e.g., based on views).
 * Optionally excludes a specific article ID.
 * Note: This only returns basic article data without categories.
 * Use getArticleCategories() to get all categories for an article.
 *
 * @param PDO $pdo The PDO database connection object.
 * @param int $limit Number of articles to fetch.
 * @param int|null $excludeArticleId The ID of the article to exclude (optional).
 * @return array An array of popular article data.
 */
function getPopularArticles(PDO $pdo, int $limit = 3, ?int $excludeArticleId = null): array {
    // Base SQL query
    $sql = "SELECT a.id, a.title, a.slug, a.featured_image, a.created_at, a.published_at, a.reading_time, a.views
            FROM articles a
            WHERE a.status = 'published' AND (a.published_at IS NULL OR a.published_at <= NOW())";

    // Parameters for binding
    $params = [':limit' => $limit];

    // Add clause to exclude a specific article if ID is provided
    if ($excludeArticleId !== null) {
        $sql .= " AND a.id != :exclude_id ";
        $params[':exclude_id'] = $excludeArticleId;
    }

    // Add ordering and limit
    $sql .= " ORDER BY a.views DESC, COALESCE(a.published_at, a.created_at) DESC
              LIMIT :limit";

    // Prepare and execute the statement
    $stmt = $pdo->prepare($sql);
    // Bind parameters dynamically based on whether exclude_id is present
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value, is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR);
    }
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Fetches all categories ordered by name.
 *
 * @param PDO $pdo The PDO database connection object.
 * @return array An array of category data (id, name, slug).
 */
function getAllCategories(PDO $pdo): array {
    $sql = "SELECT id, name, slug FROM categories ORDER BY name ASC";
    $stmt = $pdo->query($sql); // Simple query, no parameters needed
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Fetches all tags ordered by name.
 *
 * @param PDO $pdo The PDO database connection object.
 * @return array An array of tag data (id, name, slug).
 */
function getAllTags(PDO $pdo): array {
    $sql = "SELECT id, name, slug FROM tags ORDER BY name ASC";
    $stmt = $pdo->query($sql); // Simple query
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Fetches tags associated with a specific article.
 *
 * @param PDO $pdo The PDO database connection object.
 * @param int $articleId The ID of the article.
 * @return array An array of tag data (id, name, slug) for the article.
 */
function getArticleTags(PDO $pdo, int $articleId): array {
    $sql = "SELECT t.id, t.name, t.slug
            FROM tags t
            JOIN article_tags at ON t.id = at.tag_id -- Join tags and the junction table
            WHERE at.article_id = :article_id -- Filter by article ID
            ORDER BY t.name ASC"; // Order tags alphabetically
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':article_id', $articleId, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Fetches categories associated with a specific article.
 *
 * @param PDO $pdo The PDO database connection object.
 * @param int $articleId The ID of the article.
 * @return array An array of category data (id, name, slug) for the article.
 */
function getArticleCategories(PDO $pdo, int $articleId): array {
    $sql = "SELECT c.id, c.name, c.slug
            FROM categories c
            JOIN article_categories ac ON c.id = ac.category_id -- Join categories and the junction table
            WHERE ac.article_id = :article_id -- Filter by article ID
            ORDER BY c.name ASC"; // Order categories alphabetically
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':article_id', $articleId, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * NEW FUNCTION: Fetches a single category by its slug.
 *
 * @param PDO $pdo The PDO database connection object.
 * @param string $slug The category slug.
 * @return array|null The category data (id, name, slug, potentially description) or null if not found.
 */
function getCategoryBySlug(PDO $pdo, string $slug): ?array {
    // Add description or other needed fields to the SELECT statement if they exist
    $sql = "SELECT id, name, slug FROM categories WHERE slug = :slug LIMIT 1";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':slug', $slug, PDO::PARAM_STR);
    $stmt->execute();
    $category = $stmt->fetch(PDO::FETCH_ASSOC);
    return $category ?: null;
}

/**
 * Fetches articles belonging to a specific category ID.
 * Includes author info. Fetches published, viewable articles. Supports pagination.
 * Checks both primary category and article_categories junction table.
 *
 * @param PDO $pdo The PDO database connection object.
 * @param int $categoryId The ID of the category.
 * @param int $limit Max number of articles per page.
 * @param int $offset Number of articles to skip.
 * @return array An array of article data.
 */
function getArticlesByCategory(PDO $pdo, int $categoryId, int $limit = 10, int $offset = 0): array {
    if ($categoryId <= 0) {
        return []; // Return empty if category ID is invalid
    }
    // Ensure limit and offset are non-negative integers
    $limit = max(1, (int)$limit);
    $offset = max(0, (int)$offset);

    $sql = "SELECT DISTINCT
                a.id, a.title, a.slug, a.excerpt, a.featured_image, a.created_at, a.published_at, a.reading_time,
                au.name as author_name, au.avatar_url as author_avatar -- Author details
                -- Note: Category info is already known since we are filtering by it
            FROM articles a
            LEFT JOIN authors au ON a.author_id = au.id
            LEFT JOIN article_categories ac ON a.id = ac.article_id
            WHERE (a.category_id = :category_id_primary OR ac.category_id = :category_id_junction) -- Check both primary and junction table
              AND a.status = 'published' -- Ensure article is published
              AND (a.published_at IS NULL OR a.published_at <= NOW()) -- Ensure publish date is valid
            ORDER BY COALESCE(a.published_at, a.created_at) DESC -- Order by publish date (fallback to creation)
            LIMIT :limit OFFSET :offset"; // Apply pagination

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':category_id_primary', $categoryId, PDO::PARAM_INT);
    $stmt->bindParam(':category_id_junction', $categoryId, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC); // Return the fetched articles
}


// =========================================================================
// DeepSeek API Function
// =========================================================================

/**
 * Calls the DeepSeek API with a given prompt and input text.
 * Uses cURL to send the request.
 *
 * @param string $prompt The system prompt or instruction for the AI model.
 * @param string $inputText The user input text (e.g., article title, content) to be processed by the AI.
 * @param float $temperature Controls randomness (0.0 to 1.0). Lower values are more deterministic.
 * @param int $maxTokens Maximum number of tokens (roughly words/subwords) to generate in the response.
 * @return array Returns an associative array:
 * - On success: ['success' => true, 'text' => 'Generated text']
 * - On failure: ['success' => false, 'error' => 'Error message describing the issue']
 */
function callDeepSeekAPI(string $prompt, string $inputText, float $temperature = 0.7, int $maxTokens = 4000): array { // *** Increased maxTokens for longer content ***
    // --- Pre-flight Checks ---
    // Check if essential constants are defined and API key is set
    if (!defined('DEEPSEEK_API_KEY') || DEEPSEEK_API_KEY === 'YOUR_DEEPSEEK_API_KEY' || empty(DEEPSEEK_API_KEY)) {
        return ['success' => false, 'error' => 'DeepSeek API key is not configured in config.php. Please add your key.'];
    }
    if (!defined('DEEPSEEK_API_URL') || !defined('DEEPSEEK_MODEL')) {
         return ['success' => false, 'error' => 'DeepSeek API URL or Model is not configured in config.php.'];
    }
    // Check if the cURL extension is loaded in PHP
    if (!function_exists('curl_init')) {
        return ['success' => false, 'error' => 'cURL PHP extension is not enabled. It is required to call the DeepSeek API.'];
    }

    // --- Prepare API Request Payload ---
    // Construct the data structure expected by the DeepSeek API
    $payloadData = [
        'model' => DEEPSEEK_MODEL, // Use the model defined in config.php
        'messages' => [
            // System message sets the context/instructions for the AI
            ['role' => 'system', 'content' => $prompt],
            // User message provides the specific input text
            ['role' => 'user', 'content' => $inputText]
        ],
        'temperature' => $temperature, // Control creativity/randomness
        'max_tokens' => $maxTokens,     // Limit response length (Increased!)
        'stream' => false               // We want the complete response, not a stream
    ];
    // Encode the data into JSON format
    $payload = json_encode($payloadData);
    // Check for JSON encoding errors
    if ($payload === false) {
        return ['success' => false, 'error' => 'Failed to encode API request payload into JSON. Error: ' . json_last_error_msg()];
    }

    // --- Initialize and Configure cURL ---
    $ch = curl_init(DEEPSEEK_API_URL); // Initialize cURL session for the API endpoint
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return the response as a string instead of outputting it
    curl_setopt($ch, CURLOPT_POST, true);           // Set request method to POST
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload); // Attach the JSON payload
    // Set necessary HTTP headers
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . DEEPSEEK_API_KEY, // Authentication header with API key
        'Accept: application/json'                   // Indicate we accept JSON responses
    ]);
    // Set extended timeouts for longer content generation
    curl_setopt($ch, CURLOPT_TIMEOUT, 180); // Total timeout for the request in seconds (increased to 3 minutes)
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // Connection timeout in seconds (increased to 30 seconds)
    // Enable SSL verification (important for security)
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);

    // --- Execute cURL Request and Get Response ---
    $response = curl_exec($ch); // Execute the request
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE); // Get the HTTP status code
    $curlError = curl_error($ch); // Get any cURL-specific error message
    curl_close($ch); // Close the cURL session

    // --- Handle cURL Errors ---
    if ($response === false) {
        // If curl_exec failed (e.g., network issue, timeout)
        return ['success' => false, 'error' => 'cURL request failed: ' . $curlError];
    }

    // --- Process API Response ---
    // Decode the JSON response body
    $responseData = json_decode($response, true);
    // Check for JSON decoding errors
    if ($responseData === null && json_last_error() !== JSON_ERROR_NONE) {
        return ['success' => false, 'error' => 'Failed to decode API JSON response: ' . json_last_error_msg()];
    }

    // Check for non-200 HTTP status codes or API-level errors in the response data
    if ($httpCode !== 200 || isset($responseData['error'])) {
        $apiError = 'DeepSeek API request failed.';
        // Append specific error message if available
        if (isset($responseData['error']['message'])) {
            $apiError .= ' Message: ' . $responseData['error']['message'];
        } elseif (isset($responseData['error'])) {
             // Fallback to encoding the whole error object if message isn't present
             $apiError .= ' Details: ' . json_encode($responseData['error']);
        }
         $apiError .= ' (HTTP Code: ' . $httpCode . ')'; // Include HTTP status code for context
        return ['success' => false, 'error' => $apiError];
    }

    // --- Extract Generated Text ---
    // Check if the expected data structure is present in the response
    if (isset($responseData['choices'][0]['message']['content'])) {
        // Get the generated text content
        $generatedText = trim($responseData['choices'][0]['message']['content']);
        // Optional: Remove surrounding quotes if the API sometimes adds them
        $generatedText = trim($generatedText, '"\'');
        // Return success with the extracted text
        return ['success' => true, 'text' => $generatedText];
    } else {
        // Check if the response indicates finish reason due to length limit
        if (isset($responseData['choices'][0]['finish_reason']) && $responseData['choices'][0]['finish_reason'] === 'length') {
            // Return the truncated text but indicate it was cut short
            $truncatedText = isset($responseData['choices'][0]['message']['content']) ? trim($responseData['choices'][0]['message']['content']) : '';
            $truncatedText = trim($truncatedText, '"\'');
            return ['success' => true, 'text' => $truncatedText, 'warning' => 'Response may be truncated due to reaching max_tokens limit.'];
        }
        // Return error if the expected content is missing and not due to length limit
        return ['success' => false, 'error' => 'Could not find generated text in the API response structure. Response: ' . $response];
    }
}


// =========================================================================
// Date and Time Formatting Functions
// =========================================================================

/**
 * Formats a date for comments, showing only the date without time.
 *
 * @param string $datetime The datetime string to format
 * @return string The formatted date string (e.g., "12. maj 2023.")
 */
function formatCommentDate(string $datetime): string {
    // Use the formatDate function with a date-only format
    return formatDate($datetime, 'd. M Y.');
}

// =========================================================================
// Comment Handling Functions
// =========================================================================

/**
 * Fetches comments for a given article ID, optionally with pagination.
 * Fetches only approved comments. Handles nested replies.
 *
 * @param PDO $pdo PDO Database connection object.
 * @param int $articleId The ID of the article.
 * @param int $limit Maximum number of top-level comments to fetch.
 * @param int $offset Number of top-level comments to skip (for pagination).
 * @return array An array containing comments, replies, and total count.
 * Structure: ['comments' => [...], 'total' => count]
 * Each comment in 'comments' may have a 'replies' key with nested comments.
 */
function fetchComments(PDO $pdo, int $articleId, int $limit = 10, int $offset = 0): array {
    $comments = [];
    $repliesMap = []; // To store replies indexed by parent_id

    try {
        // --- Fetch Total Count (Approved Top-Level Comments) ---
        $sqlCount = "SELECT COUNT(*) FROM comments WHERE article_id = :article_id AND parent_id IS NULL AND is_approved = 1";
        $stmtCount = $pdo->prepare($sqlCount);
        $stmtCount->bindParam(':article_id', $articleId, PDO::PARAM_INT);
        $stmtCount->execute();
        $totalComments = (int) $stmtCount->fetchColumn();

        // --- Fetch Top-Level Comments (Approved only) with Pagination ---
        $sqlTopLevel = "SELECT * FROM comments
                        WHERE article_id = :article_id AND parent_id IS NULL AND is_approved = 1
                        ORDER BY created_at DESC
                        LIMIT :limit OFFSET :offset";
        $stmtTopLevel = $pdo->prepare($sqlTopLevel);
        $stmtTopLevel->bindParam(':article_id', $articleId, PDO::PARAM_INT);
        $stmtTopLevel->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmtTopLevel->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmtTopLevel->execute();
        $topLevelComments = $stmtTopLevel->fetchAll(PDO::FETCH_ASSOC);

        if (empty($topLevelComments)) {
            return ['comments' => [], 'total' => $totalComments];
        }

        // Get IDs of the fetched top-level comments to fetch their replies
        $topLevelIds = array_column($topLevelComments, 'id');

        // --- Fetch Replies for the fetched Top-Level Comments (Approved only) ---
        if (!empty($topLevelIds)) {
            // Create placeholders for the IN clause (e.g., :id_0, :id_1)
            $placeholders = implode(',', array_map(fn($i) => ":id_$i", array_keys($topLevelIds)));

            $sqlReplies = "SELECT * FROM comments
                           WHERE parent_id IN ($placeholders) AND is_approved = 1
                           ORDER BY created_at ASC"; // Order replies chronologically

            $stmtReplies = $pdo->prepare($sqlReplies);
            // Bind each ID placeholder
            foreach ($topLevelIds as $index => $id) {
                $stmtReplies->bindValue(":id_$index", $id, PDO::PARAM_INT);
            }
            $stmtReplies->execute();
            $allReplies = $stmtReplies->fetchAll(PDO::FETCH_ASSOC);

            // Group replies by their parent_id
            foreach ($allReplies as $reply) {
                $repliesMap[$reply['parent_id']][] = $reply;
            }
        }

        // --- Structure the comments array with replies ---
        foreach ($topLevelComments as $comment) {
            $comment['replies'] = $repliesMap[$comment['id']] ?? []; // Assign replies from the map
            $comments[] = $comment;
        }

    } catch (PDOException $e) {
        error_log("Error fetching comments for article ID {$articleId}: " . $e->getMessage());
        return ['comments' => [], 'total' => 0, 'error' => $e->getMessage()];
    }

    return ['comments' => $comments, 'total' => $totalComments];
}


/**
 * Adds a new comment to the database.
 *
 * @param PDO $pdo PDO Database connection object.
 * @param int $articleId The ID of the article being commented on.
 * @param string $userName The name of the commenter.
 * @param string $commentText The text of the comment.
 * @param int|null $parentId The ID of the parent comment if this is a reply, otherwise null.
 * @param int $isApproved The initial approval status (default 1 = approved).
 * @return array ['success' => bool, 'comment_id' => int|null, 'error' => string|null]
 */
function addComment(PDO $pdo, int $articleId, string $userName, string $commentText, ?int $parentId = null, int $isApproved = 1): array {
    // Basic validation
    if (empty(trim($userName)) || empty(trim($commentText)) || $articleId <= 0) {
        return ['success' => false, 'error' => 'Ime i tekst komentara su obavezni.'];
    }
    if ($parentId !== null && $parentId <= 0) {
        return ['success' => false, 'error' => 'Nevažeći ID roditeljskog komentara.'];
    }

    // Limit username length
    if (mb_strlen($userName) > 100) {
         $userName = mb_substr($userName, 0, 100);
    }
    // Consider adding more validation/sanitization (e.g., profanity filter, length limits)

    try {
        // Check if article exists
        $stmtCheckArticle = $pdo->prepare("SELECT id FROM articles WHERE id = :article_id LIMIT 1");
        $stmtCheckArticle->bindParam(':article_id', $articleId, PDO::PARAM_INT);
        $stmtCheckArticle->execute();
        if ($stmtCheckArticle->fetch() === false) {
            return ['success' => false, 'error' => 'Članak nije pronađen.'];
        }

        // Check if parent comment exists if parentId is provided
        if ($parentId !== null) {
            $stmtCheckParent = $pdo->prepare("SELECT id FROM comments WHERE id = :parent_id LIMIT 1");
            $stmtCheckParent->bindParam(':parent_id', $parentId, PDO::PARAM_INT);
            $stmtCheckParent->execute();
            if ($stmtCheckParent->fetch() === false) {
                return ['success' => false, 'error' => 'Roditeljski komentar nije pronađen.'];
            }
        }

        // Insert the comment
        $sql = "INSERT INTO comments (article_id, parent_id, user_name, comment_text, is_approved, created_at)
                VALUES (:article_id, :parent_id, :user_name, :comment_text, :is_approved, NOW())";
        $stmt = $pdo->prepare($sql);

        $stmt->bindParam(':article_id', $articleId, PDO::PARAM_INT);
        $stmt->bindParam(':parent_id', $parentId, $parentId === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
        $stmt->bindParam(':user_name', $userName, PDO::PARAM_STR);
        $stmt->bindParam(':comment_text', $commentText, PDO::PARAM_STR);
        $stmt->bindParam(':is_approved', $isApproved, PDO::PARAM_INT);

        if ($stmt->execute()) {
            return ['success' => true, 'comment_id' => $pdo->lastInsertId()];
        } else {
            $errorInfo = $stmt->errorInfo();
            return ['success' => false, 'error' => 'Greška pri spremanju komentara: ' . ($errorInfo[2] ?? 'Unknown DB error')];
        }

    } catch (PDOException $e) {
        error_log("Error adding comment for article ID {$articleId}: " . $e->getMessage());
        return ['success' => false, 'error' => 'Došlo je do greške u bazi podataka.'];
    }
}

?>
