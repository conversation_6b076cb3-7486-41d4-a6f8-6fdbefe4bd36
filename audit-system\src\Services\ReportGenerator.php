<?php

namespace AuditSystem\Services;

use AuditSystem\Interfaces\ReportGeneratorInterface;
use AuditSystem\Models\AuditResult;
use AuditSystem\Models\Finding;

/**
 * Comprehensive report generation system
 */
class ReportGenerator implements ReportGeneratorInterface
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'include_code_snippets' => true,
            'max_snippet_lines' => 10,
            'group_by_priority' => true,
            'include_statistics' => true
        ], $config);
    }

    /**
     * Generate a comprehensive audit report
     *
     * @param AuditResult $auditResult The audit results to generate report from
     * @return string Generated report content
     */
    public function generateReport(AuditResult $auditResult): string
    {
        $report = [];
        
        $report[] = $this->generateHeader($auditResult);
        $report[] = $this->generateSummary($auditResult);
        $report[] = $this->generatePriorityFindings($auditResult);
        $report[] = $this->generateDetailedFindings($auditResult);
        $report[] = $this->generateFileStatus($auditResult);
        $report[] = $this->generateRecommendations($auditResult);
        
        return implode("\n\n", array_filter($report));
    }

    /**
     * Export report in specified format
     *
     * @param AuditResult $auditResult The audit results to export
     * @param string $format Export format (json, html, markdown)
     * @param string $outputPath Path to save the exported report
     * @return bool True if export was successful
     */
    public function exportReport(AuditResult $auditResult, string $format, string $outputPath): bool
    {
        try {
            $content = match($format) {
                'json' => $this->generateJsonReport($auditResult),
                'html' => $this->generateHtmlReport($auditResult),
                'markdown' => $this->generateMarkdownReport($auditResult),
                default => throw new \InvalidArgumentException("Unsupported format: {$format}")
            };

            // Ensure directory exists
            $dir = dirname($outputPath);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }

            return file_put_contents($outputPath, $content) !== false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get supported export formats
     *
     * @return string[] Array of supported formats
     */
    public function getSupportedFormats(): array
    {
        return ['json', 'html', 'markdown'];
    }

    /**
     * Generate report header
     *
     * @param AuditResult $auditResult
     * @return string
     */
    private function generateHeader(AuditResult $auditResult): string
    {
        return "# Lako & Fino CMS Security Audit Report\n\n" .
               "**Generated:** " . $auditResult->completedAt->format('Y-m-d H:i:s') . "\n" .
               "**Version:** " . $auditResult->version . "\n" .
               "**Total Files Analyzed:** " . count($auditResult->fileStatus);
    }

    /**
     * Generate summary section
     *
     * @param AuditResult $auditResult
     * @return string
     */
    private function generateSummary(AuditResult $auditResult): string
    {
        $stats = $auditResult->statistics;
        $priorityFindings = $auditResult->getFindingsByPriority();
        
        $summary = "## Executive Summary\n\n";
        
        if ($stats->criticalFindings > 0) {
            $summary .= "⚠️  **CRITICAL ISSUES FOUND** - Immediate attention required!\n\n";
        }
        
        $summary .= "### Key Metrics\n";
        $summary .= "- **Total Issues:** {$stats->totalFindings}\n";
        $summary .= "- **Critical Issues:** {$stats->criticalFindings}\n";
        $summary .= "- **High Priority Issues:** {$stats->highFindings}\n";
        $summary .= "- **Priority Area Issues:** {$stats->priorityAreaFindings}\n";
        $summary .= "- **Files with Issues:** {$stats->filesWithIssues}\n";
        $summary .= "- **Optimal Files:** {$stats->optimalFiles}\n\n";
        
        $summary .= "### Issue Breakdown by Type\n";
        $summary .= "- **Security Issues:** {$stats->securityFindings}\n";
        $summary .= "- **Performance Issues:** {$stats->performanceFindings}\n";
        $summary .= "- **Quality Issues:** {$stats->qualityFindings}\n";
        $summary .= "- **Architecture Issues:** {$stats->architectureFindings}\n";
        
        return $summary;
    }

    /**
     * Generate priority findings section
     *
     * @param AuditResult $auditResult
     * @return string
     */
    private function generatePriorityFindings(AuditResult $auditResult): string
    {
        $priorityFindings = $auditResult->getFindingsByPriority()['priority_area'];
        
        if (empty($priorityFindings)) {
            return "## Priority Areas\n\n✅ No critical issues found in priority areas.";
        }
        
        $section = "## 🚨 Priority Areas - Immediate Action Required\n\n";
        $section .= "These issues are in critical system components and should be addressed first:\n\n";
        
        $criticalFindings = array_filter($priorityFindings, fn($f) => $f->severity === Finding::SEVERITY_CRITICAL);
        $highFindings = array_filter($priorityFindings, fn($f) => $f->severity === Finding::SEVERITY_HIGH);
        
        if (!empty($criticalFindings)) {
            $section .= "### 🔴 Critical Issues\n\n";
            foreach ($criticalFindings as $finding) {
                $section .= $this->formatFinding($finding) . "\n";
            }
        }
        
        if (!empty($highFindings)) {
            $section .= "### 🟠 High Priority Issues\n\n";
            foreach ($highFindings as $finding) {
                $section .= $this->formatFinding($finding) . "\n";
            }
        }
        
        return $section;
    }

    /**
     * Generate detailed findings section
     *
     * @param AuditResult $auditResult
     * @return string
     */
    private function generateDetailedFindings(AuditResult $auditResult): string
    {
        $section = "## Detailed Findings\n\n";
        
        foreach ($auditResult->findings as $filePath => $findings) {
            if (empty($findings)) continue;
            
            $section .= "### " . basename($filePath) . "\n";
            $section .= "**Path:** `{$filePath}`\n\n";
            
            foreach ($findings as $finding) {
                $section .= $this->formatFinding($finding) . "\n";
            }
            
            $section .= "---\n\n";
        }
        
        return $section;
    }

    /**
     * Generate file status section
     *
     * @param AuditResult $auditResult
     * @return string
     */
    private function generateFileStatus(AuditResult $auditResult): string
    {
        $section = "## File Status Overview\n\n";
        
        $needsChange = array_filter($auditResult->fileStatus, fn($status) => $status === 'needs_change');
        $optimal = array_filter($auditResult->fileStatus, fn($status) => $status === 'optimal');
        
        $section .= "### Files Requiring Changes (" . count($needsChange) . ")\n\n";
        foreach (array_keys($needsChange) as $file) {
            $issueCount = count($auditResult->findings[$file] ?? []);
            $section .= "- `{$file}` ({$issueCount} issues)\n";
        }
        
        $section .= "\n### Optimal Files (" . count($optimal) . ")\n\n";
        foreach (array_keys($optimal) as $file) {
            $section .= "- `{$file}` ✅\n";
        }
        
        return $section;
    }

    /**
     * Generate recommendations section
     *
     * @param AuditResult $auditResult
     * @return string
     */
    private function generateRecommendations(AuditResult $auditResult): string
    {
        $section = "## Recommendations\n\n";
        
        $stats = $auditResult->statistics;
        
        if ($stats->criticalFindings > 0) {
            $section .= "### 🚨 Immediate Actions\n";
            $section .= "1. **Address all critical security issues immediately**\n";
            $section .= "2. **Review and fix SQL injection vulnerabilities**\n";
            $section .= "3. **Implement proper input validation and output escaping**\n\n";
        }
        
        if ($stats->securityFindings > 0) {
            $section .= "### 🔒 Security Improvements\n";
            $section .= "1. **Implement CSRF protection for all forms**\n";
            $section .= "2. **Use prepared statements for all database queries**\n";
            $section .= "3. **Add proper session security configuration**\n";
            $section .= "4. **Validate and sanitize all file uploads**\n\n";
        }
        
        if ($stats->performanceFindings > 0) {
            $section .= "### ⚡ Performance Optimizations\n";
            $section .= "1. **Optimize database queries to prevent N+1 problems**\n";
            $section .= "2. **Implement proper caching strategies**\n";
            $section .= "3. **Optimize image loading and processing**\n\n";
        }
        
        $section .= "### 📋 Next Steps\n";
        $section .= "1. **Prioritize fixes based on severity and business impact**\n";
        $section .= "2. **Test all changes in a development environment first**\n";
        $section .= "3. **Re-run the audit after implementing fixes**\n";
        $section .= "4. **Consider implementing automated security testing**\n";
        
        return $section;
    }

    /**
     * Format a single finding
     *
     * @param Finding $finding
     * @return string
     */
    private function formatFinding(Finding $finding): string
    {
        $severityIcon = match($finding->severity) {
            Finding::SEVERITY_CRITICAL => '🔴',
            Finding::SEVERITY_HIGH => '🟠',
            Finding::SEVERITY_MEDIUM => '🟡',
            Finding::SEVERITY_LOW => '🔵',
            default => '⚪'
        };
        
        $output = "#### {$severityIcon} {$finding->description}\n\n";
        $output .= "**File:** `{$finding->file}` (Line {$finding->line})\n";
        $output .= "**Type:** {$finding->type}\n";
        $output .= "**Severity:** {$finding->severity}\n";
        $output .= "**Priority:** {$finding->priority}\n\n";
        $output .= "**Issue:** {$finding->description}\n\n";
        $output .= "**Recommendation:** {$finding->recommendation}\n\n";
        
        if ($this->config['include_code_snippets'] && $finding->codeSnippet) {
            $output .= "**Code:**\n```php\n{$finding->codeSnippet}\n```\n\n";
        }
        
        if (!empty($finding->references)) {
            $output .= "**References:**\n";
            foreach ($finding->references as $ref) {
                $output .= "- {$ref}\n";
            }
            $output .= "\n";
        }
        
        return $output;
    }

    /**
     * Generate JSON report
     *
     * @param AuditResult $auditResult
     * @return string
     */
    private function generateJsonReport(AuditResult $auditResult): string
    {
        return json_encode($auditResult->toArray(), JSON_PRETTY_PRINT);
    }

    /**
     * Generate HTML report
     *
     * @param AuditResult $auditResult
     * @return string
     */
    private function generateHtmlReport(AuditResult $auditResult): string
    {
        $markdown = $this->generateMarkdownReport($auditResult);
        
        // Simple markdown to HTML conversion
        $html = htmlspecialchars($markdown);
        $html = preg_replace('/^# (.+)$/m', '<h1>$1</h1>', $html);
        $html = preg_replace('/^## (.+)$/m', '<h2>$1</h2>', $html);
        $html = preg_replace('/^### (.+)$/m', '<h3>$1</h3>', $html);
        $html = preg_replace('/^\*\*(.+)\*\*$/m', '<strong>$1</strong>', $html);
        $html = preg_replace('/`([^`]+)`/', '<code>$1</code>', $html);
        $html = nl2br($html);
        
        return "<!DOCTYPE html>
<html>
<head>
    <title>Audit Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1, h2, h3 { color: #333; }
        code { background: #f4f4f4; padding: 2px 4px; }
        .critical { color: #d32f2f; }
        .high { color: #f57c00; }
    </style>
</head>
<body>
{$html}
</body>
</html>";
    }

    /**
     * Generate Markdown report
     *
     * @param AuditResult $auditResult
     * @return string
     */
    private function generateMarkdownReport(AuditResult $auditResult): string
    {
        return $this->generateReport($auditResult);
    }
}