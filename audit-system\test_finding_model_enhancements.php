<?php

require_once 'vendor/autoload.php';

use AuditSystem\Models\Finding;

echo "Testing Finding Model Enhancements...\n\n";

// Create test findings
$criticalSecurityFinding = new Finding(
    'public_html/config.php',
    25,
    Finding::TYPE_SECURITY,
    Finding::SEVERITY_CRITICAL,
    Finding::PRIORITY_AREA,
    'SQL injection vulnerability detected',
    'Use PDO prepared statements',
    '$query = "SELECT * FROM users WHERE id = " . $_GET["id"];',
    ['https://owasp.org/sql-injection']
);

$highPerformanceFinding = new Finding(
    'public_html/article.php',
    45,
    Finding::TYPE_PERFORMANCE,
    Finding::SEVERITY_HIGH,
    Finding::PRIORITY_AREA,
    'N+1 query problem detected',
    'Implement eager loading',
    'foreach ($articles as $article) { getComments($article->id); }',
    []
);

$lowQualityFinding = new Finding(
    'public_html/privacy-policy.php',
    10,
    Finding::TYPE_QUALITY,
    Finding::SEVERITY_LOW,
    Finding::NON_PRIORITY,
    'Inconsistent variable naming',
    'Follow PSR naming conventions',
    '$userName = $_POST["user_name"];',
    []
);

// Test priority area detection
echo "Test 1: Priority area detection\n";
echo "Critical security finding is priority area: " . ($criticalSecurityFinding->isPriorityArea() ? 'YES' : 'NO') . "\n";
echo "Low quality finding is priority area: " . ($lowQualityFinding->isPriorityArea() ? 'YES' : 'NO') . "\n\n";

// Test severity checks
echo "Test 2: Severity checks\n";
echo "Critical finding is critical: " . ($criticalSecurityFinding->isCritical() ? 'YES' : 'NO') . "\n";
echo "High finding is high severity: " . ($highPerformanceFinding->isHighSeverity() ? 'YES' : 'NO') . "\n";
echo "Low finding is critical: " . ($lowQualityFinding->isCritical() ? 'YES' : 'NO') . "\n\n";

// Test type checks
echo "Test 3: Type checks\n";
echo "Security finding is security type: " . ($criticalSecurityFinding->isSecurityFinding() ? 'YES' : 'NO') . "\n";
echo "Performance finding is security type: " . ($highPerformanceFinding->isSecurityFinding() ? 'YES' : 'NO') . "\n\n";

// Test severity weights
echo "Test 4: Severity weights\n";
echo "Critical severity weight: " . $criticalSecurityFinding->getSeverityWeight() . "\n";
echo "High severity weight: " . $highPerformanceFinding->getSeverityWeight() . "\n";
echo "Low severity weight: " . $lowQualityFinding->getSeverityWeight() . "\n\n";

// Test priority weights
echo "Test 5: Priority weights\n";
echo "Priority area weight: " . $criticalSecurityFinding->getPriorityWeight() . "\n";
echo "Non-priority weight: " . $lowQualityFinding->getPriorityWeight() . "\n\n";

// Test importance scores
echo "Test 6: Importance scores\n";
echo "Critical security (priority) score: " . $criticalSecurityFinding->getImportanceScore() . "\n";
echo "High performance (priority) score: " . $highPerformanceFinding->getImportanceScore() . "\n";
echo "Low quality (non-priority) score: " . $lowQualityFinding->getImportanceScore() . "\n\n";

// Test sorting by importance
echo "Test 7: Sorting by importance\n";
$findings = [$lowQualityFinding, $criticalSecurityFinding, $highPerformanceFinding];
usort($findings, fn($a, $b) => $b->getImportanceScore() - $a->getImportanceScore());

echo "Sorted findings by importance:\n";
foreach ($findings as $i => $finding) {
    echo ($i + 1) . ". " . $finding->formatForDisplay() . " (Score: " . $finding->getImportanceScore() . ")\n";
}
echo "\n";

// Test display formatting
echo "Test 8: Display formatting\n";
echo "Critical security: " . $criticalSecurityFinding->formatForDisplay() . "\n";
echo "High performance: " . $highPerformanceFinding->formatForDisplay() . "\n";
echo "Low quality: " . $lowQualityFinding->formatForDisplay() . "\n\n";

// Test matching criteria
echo "Test 9: Matching criteria\n";
$securityCriteria = ['type' => Finding::TYPE_SECURITY, 'severity' => Finding::SEVERITY_CRITICAL];
$priorityCriteria = ['priority' => Finding::PRIORITY_AREA];
$severityCriteria = ['severity' => [Finding::SEVERITY_CRITICAL, Finding::SEVERITY_HIGH]];

echo "Critical security matches security criteria: " . ($criticalSecurityFinding->matches($securityCriteria) ? 'YES' : 'NO') . "\n";
echo "High performance matches priority criteria: " . ($highPerformanceFinding->matches($priorityCriteria) ? 'YES' : 'NO') . "\n";
echo "Low quality matches priority criteria: " . ($lowQualityFinding->matches($priorityCriteria) ? 'YES' : 'NO') . "\n";
echo "Critical security matches severity array: " . ($criticalSecurityFinding->matches($severityCriteria) ? 'YES' : 'NO') . "\n";
echo "Low quality matches severity array: " . ($lowQualityFinding->matches($severityCriteria) ? 'YES' : 'NO') . "\n\n";

echo "All Finding model enhancement tests completed successfully!\n";