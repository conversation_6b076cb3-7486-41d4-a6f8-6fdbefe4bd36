<?php
/**
 * process_page_views.php
 *
 * Script to aggregate page views from the buffer table into the main aggregated table.
 * This script should be run periodically via a cron job (e.g., every 5-10 minutes).
 */

// Prevent direct web access if needed, or ensure it's run via CLI/cron
// if (php_sapi_name() !== 'cli' && !isset($_GET['cron_secret'])) { // Basic security check
//     die('Access denied.');
// }

// Set a longer execution time limit if needed for processing large buffers
set_time_limit(300); // 5 minutes

// Get the absolute path to the config file
$configPath = __DIR__ . '/config.php';

// Include configuration and database connection
if (file_exists($configPath)) {
    require_once $configPath;
} else {
    error_log("process_page_views.php: Failed to include config.php at path: " . $configPath);
    die("Configuration file not found.");
}

// Check if $pdo is available
if (!isset($pdo) || !$pdo instanceof PDO) {
    error_log("process_page_views.php: PDO database connection is not available.");
    die("Database connection error.");
}

echo "Starting page view aggregation process...\n";
$processed_count = 0;
$buffer_entries = [];
$buffer_ids = [];

try {
    // Start transaction
    $pdo->beginTransaction();

    // Select entries from the buffer table and lock them for update
    $stmt_select = $pdo->query("SELECT id, view_date, view_hour, url_hash, url, view_count FROM page_views_buffer FOR UPDATE");
    $buffer_entries = $stmt_select->fetchAll(PDO::FETCH_ASSOC);
    $processed_count = count($buffer_entries);

    if ($processed_count > 0) {
        echo "Found " . $processed_count . " entries in the buffer.\n";

        // Prepare data for aggregated table
        $aggregated_data = [];
        foreach ($buffer_entries as $entry) {
            $buffer_ids[] = $entry['id']; // Collect IDs for deletion later
            $key = $entry['view_date'] . '_' . $entry['view_hour'] . '_' . $entry['url_hash'];

            if (!isset($aggregated_data[$key])) {
                $aggregated_data[$key] = [
                    'view_date' => $entry['view_date'],
                    'view_hour' => $entry['view_hour'],
                    'url' => $entry['url'], // Store the full URL
                    'view_count' => 0
                ];
            }
            $aggregated_data[$key]['view_count'] += $entry['view_count'];
        }

        // Prepare statement for inserting/updating aggregated data
        $sql_aggregate = "INSERT INTO page_views_aggregated (view_date, view_hour, url, view_count)
                          VALUES (:view_date, :view_hour, :url, :view_count)
                          ON DUPLICATE KEY UPDATE view_count = view_count + VALUES(view_count)";
        $stmt_aggregate = $pdo->prepare($sql_aggregate);

        // Execute updates for aggregated data
        echo "Aggregating data...\n";
        foreach ($aggregated_data as $data) {
            $stmt_aggregate->execute([
                ':view_date' => $data['view_date'],
                ':view_hour' => $data['view_hour'],
                ':url' => $data['url'],
                ':view_count' => $data['view_count']
            ]);
        }

        // Delete processed entries from the buffer table
        if (!empty($buffer_ids)) {
            echo "Deleting processed entries from buffer...\n";
            // Create placeholders for the IN clause
            $placeholders = implode(',', array_fill(0, count($buffer_ids), '?'));
            $sql_delete = "DELETE FROM page_views_buffer WHERE id IN ($placeholders)";
            $stmt_delete = $pdo->prepare($sql_delete);
            // Bind each ID individually (PDO requires this for IN clauses)
            foreach ($buffer_ids as $k => $id) {
                $stmt_delete->bindValue(($k + 1), $id, PDO::PARAM_INT);
            }
            $stmt_delete->execute();
            echo "Deleted " . $stmt_delete->rowCount() . " entries.\n";
        }

        // Commit transaction
        $pdo->commit();
        echo "Aggregation successful. Processed " . $processed_count . " buffer entries.\n";

    } else {
        // No entries to process, just commit the transaction (which did nothing)
        $pdo->commit();
        echo "No entries found in the buffer to process.\n";
    }

} catch (PDOException $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("process_page_views.php: Aggregation failed: " . $e->getMessage());
    echo "Error during aggregation: " . $e->getMessage() . "\n";
    die("Aggregation process failed."); // Stop script on error
}

echo "Page view aggregation process finished.\n";

?>