<?php

namespace AuditSystem\Analyzers;

use AuditSystem\Interfaces\AnalyzerInterface;
use AuditSystem\Models\Finding;

/**
 * Comprehensive PHP code analyzer for quality, architecture, and maintainability
 */
class PHPAnalyzer implements AnalyzerInterface
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'check_naming_conventions' => true,
            'check_complexity' => true,
            'check_duplication' => true,
            'check_architecture' => true,
            'check_maintainability' => true,
            'max_function_length' => 50,
            'max_cyclomatic_complexity' => 10,
            'max_parameters' => 5
        ], $config);
    }

    /**
     * Analyze a file and return findings
     *
     * @param string $filePath Path to the file to analyze
     * @param string $content File content to analyze
     * @return Finding[] Array of findings discovered in the file
     */
    public function analyze(string $filePath, string $content): array
    {
        $findings = [];

        if ($this->config['check_naming_conventions']) {
            $findings = array_merge($findings, $this->checkNamingConventions($filePath, $content));
        }

        if ($this->config['check_complexity']) {
            $findings = array_merge($findings, $this->checkComplexity($filePath, $content));
        }

        if ($this->config['check_duplication']) {
            $findings = array_merge($findings, $this->checkDuplication($filePath, $content));
        }

        if ($this->config['check_architecture']) {
            $findings = array_merge($findings, $this->checkArchitecture($filePath, $content));
        }

        if ($this->config['check_maintainability']) {
            $findings = array_merge($findings, $this->checkMaintainability($filePath, $content));
        }

        return $findings;
    }

    /**
     * Compatibility wrapper for tests expecting analyzeFile($filePath)
     */
    public function analyzeFile(string $filePath): array
    {
        $content = @file_get_contents($filePath) ?: '';
        return $this->analyze($filePath, $content);
    }

    /**
     * Check naming conventions compliance
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkNamingConventions(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++; // 1-based line numbers

            // Check class naming (should be PascalCase)
            if (preg_match('/class\s+([a-z][a-zA-Z0-9_]*)\s*/', $line, $matches)) {
                $className = $matches[1];
                if (!preg_match('/^[A-Z][a-zA-Z0-9]*$/', $className)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_LOW,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Class name '$className' should use PascalCase convention",
                        "Rename class to follow PascalCase naming (e.g., 'MyClassName')",
                        trim($line),
                        ['https://www.php-fig.org/psr/psr-1/#class-names']
                    );
                }
            }

            // Check function naming (should be camelCase or snake_case)
            if (preg_match('/function\s+([A-Z][a-zA-Z0-9_]*)\s*\(/', $line, $matches)) {
                $functionName = $matches[1];
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_LOW,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Function name '$functionName' should use camelCase or snake_case convention",
                    "Rename function to follow camelCase (e.g., 'myFunction') or snake_case (e.g., 'my_function')",
                    trim($line),
                    ['https://www.php-fig.org/psr/psr-1/#method-names']
                );
            }

            // Check variable naming (should be camelCase or snake_case, not PascalCase)
            if (preg_match('/\$([A-Z][a-zA-Z0-9_]*)\s*=/', $line, $matches)) {
                $variableName = $matches[1];
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_LOW,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Variable name '\$$variableName' should use camelCase or snake_case convention",
                    "Rename variable to follow camelCase (e.g., '\$myVariable') or snake_case (e.g., '\$my_variable')",
                    trim($line),
                    ['https://www.php-fig.org/psr/psr-1/#property-names']
                );
            }

            // Check constant naming (should be UPPER_CASE)
            if (preg_match('/const\s+([a-z][a-zA-Z0-9_]*)\s*=/', $line, $matches)) {
                $constantName = $matches[1];
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_LOW,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Constant name '$constantName' should use UPPER_CASE convention",
                    "Rename constant to follow UPPER_CASE naming (e.g., 'MY_CONSTANT')",
                    trim($line),
                    ['https://www.php-fig.org/psr/psr-1/#constants']
                );
            }
        }

        return $findings;
    }

    /**
     * Check code complexity metrics
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkComplexity(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);
        
        // Track function boundaries and complexity
        $inFunction = false;
        $functionStart = 0;
        $functionName = '';
        $braceLevel = 0;
        $complexity = 1; // Base complexity
        $functionLength = 0;
        $parameterCount = 0;

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++; // 1-based line numbers
            $trimmedLine = trim($line);

            // Skip empty lines and comments for length calculation
            if (!empty($trimmedLine) && !preg_match('/^\s*\/\//', $trimmedLine) && !preg_match('/^\s*\/\*/', $trimmedLine)) {
                if ($inFunction) {
                    $functionLength++;
                }
            }

            // Detect function start
            if (preg_match('/function\s+(\w+)\s*\(([^)]*)\)/', $line, $matches)) {
                $inFunction = true;
                $functionStart = $lineNumber;
                $functionName = $matches[1];
                $complexity = 1;
                $functionLength = 1;
                $braceLevel = 0;
                
                // Count parameters
                $params = trim($matches[2]);
                if (!empty($params)) {
                    $parameterCount = count(array_filter(explode(',', $params)));
                } else {
                    $parameterCount = 0;
                }

                // Check parameter count
                if ($parameterCount > $this->config['max_parameters']) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Function '$functionName' has too many parameters ($parameterCount > {$this->config['max_parameters']})",
                        "Consider using parameter objects or breaking down the function into smaller parts",
                        trim($line),
                        ['https://refactoring.guru/smells/long-parameter-list']
                    );
                }
            }

            if ($inFunction) {
                // Count braces to detect function end
                $braceLevel += substr_count($line, '{') - substr_count($line, '}');

                // Count complexity-increasing constructs
                if (preg_match('/(if|else if|elseif|while|for|foreach|case|catch|\?|&&|\|\|)/', $line)) {
                    $complexity++;
                }

                // Function ends when brace level returns to 0
                if ($braceLevel <= 0 && preg_match('/}/', $line)) {
                    $inFunction = false;

                    // Check function length
                    if ($functionLength > $this->config['max_function_length']) {
                        $findings[] = new Finding(
                            $filePath,
                            $functionStart,
                            Finding::TYPE_QUALITY,
                            Finding::SEVERITY_MEDIUM,
                            $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                            "Function '$functionName' is too long ($functionLength lines > {$this->config['max_function_length']})",
                            "Break down the function into smaller, more focused functions",
                            "function $functionName() { ... } // $functionLength lines",
                            ['https://refactoring.guru/smells/long-method']
                        );
                    }

                    // Check cyclomatic complexity
                    if ($complexity > $this->config['max_cyclomatic_complexity']) {
                        $findings[] = new Finding(
                            $filePath,
                            $functionStart,
                            Finding::TYPE_QUALITY,
                            Finding::SEVERITY_MEDIUM,
                            $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                            "Function '$functionName' has high cyclomatic complexity ($complexity > {$this->config['max_cyclomatic_complexity']})",
                            "Simplify the function by reducing conditional statements and loops",
                            "function $functionName() { ... } // complexity: $complexity",
                            ['https://en.wikipedia.org/wiki/Cyclomatic_complexity']
                        );
                    }
                }
            }
        }

        return $findings;
    }    /**

     * Check for code duplication
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkDuplication(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);
        $codeBlocks = [];

        // Extract meaningful code blocks (ignore whitespace and comments)
        foreach ($lines as $lineNumber => $line) {
            $trimmedLine = trim($line);
            if (!empty($trimmedLine) && 
                !preg_match('/^\s*\/\//', $trimmedLine) && 
                !preg_match('/^\s*\/\*/', $trimmedLine) &&
                !preg_match('/^\s*\*/', $trimmedLine) &&
                strlen($trimmedLine) > 10) { // Ignore very short lines
                
                $codeBlocks[] = [
                    'line' => $lineNumber + 1,
                    'code' => $trimmedLine,
                    'normalized' => $this->normalizeCode($trimmedLine)
                ];
            }
        }

        // Look for duplicated blocks (3+ consecutive similar lines)
        for ($i = 0; $i < count($codeBlocks) - 2; $i++) {
            for ($j = $i + 3; $j < count($codeBlocks) - 2; $j++) {
                $similarity = 0;
                $blockSize = min(5, count($codeBlocks) - $i, count($codeBlocks) - $j);
                
                for ($k = 0; $k < $blockSize; $k++) {
                    if (isset($codeBlocks[$i + $k]) && isset($codeBlocks[$j + $k])) {
                        if (similar_text($codeBlocks[$i + $k]['normalized'], $codeBlocks[$j + $k]['normalized']) > 0.8 * strlen($codeBlocks[$i + $k]['normalized'])) {
                            $similarity++;
                        }
                    }
                }

                if ($similarity >= 3) {
                    $findings[] = new Finding(
                        $filePath,
                        $codeBlocks[$i]['line'],
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Code duplication detected: similar block found at line {$codeBlocks[$j]['line']}",
                        "Extract common code into a reusable function or method",
                        $codeBlocks[$i]['code'],
                        ['https://refactoring.guru/smells/duplicate-code']
                    );
                    break; // Avoid multiple reports for the same block
                }
            }
        }

        // Check for repeated string literals
        $stringLiterals = [];
        foreach ($lines as $lineNumber => $line) {
            if (preg_match_all('/["\']([^"\']{10,})["\']/', $line, $matches)) {
                foreach ($matches[1] as $literal) {
                    if (!isset($stringLiterals[$literal])) {
                        $stringLiterals[$literal] = [];
                    }
                    $stringLiterals[$literal][] = $lineNumber + 1;
                }
            }
        }

        foreach ($stringLiterals as $literal => $lineNumbers) {
            if (count($lineNumbers) > 2) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumbers[0],
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_LOW,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Repeated string literal found in " . count($lineNumbers) . " places",
                    "Define as a constant to improve maintainability",
                    "\"$literal\"",
                    ['https://refactoring.guru/smells/duplicate-code']
                );
            }
        }

        return $findings;
    }

    /**
     * Check architecture patterns and adherence
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkArchitecture(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        // Check for MVC pattern adherence
        $hasBusinessLogic = false;
        $hasHTMLOutput = false;
        $hasDirectDBAccess = false;

        foreach ($lines as $lineNumber => $line) {
            $lineNumber++; // 1-based line numbers

            // Detect HTML output in PHP files
            if (preg_match('/(echo|print).*?<(html|body|div|p|h[1-6]|table|form)/', $line)) {
                $hasHTMLOutput = true;
            }

            // Detect direct database access
            if (preg_match('/(mysql_|mysqli_|PDO|new\s+PDO)/', $line)) {
                $hasDirectDBAccess = true;
            }

            // Detect business logic patterns
            if (preg_match('/(if.*?\$_(GET|POST)|for.*?count|while.*?\$)/', $line)) {
                $hasBusinessLogic = true;
            }

            // Check for global variable usage
            if (preg_match('/global\s+\$/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_ARCHITECTURE,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Global variable usage detected',
                    'Use dependency injection or parameter passing instead of global variables',
                    trim($line),
                    ['https://refactoring.guru/smells/global-data']
                );
            }

            // Check for direct superglobal access in functions
            if (preg_match('/function.*?\{.*?\$_(GET|POST|REQUEST|SESSION|COOKIE)/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_ARCHITECTURE,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Direct superglobal access in function',
                    'Pass required data as parameters instead of accessing superglobals directly',
                    trim($line),
                    ['https://www.php-fig.org/psr/psr-1/#side-effects']
                );
            }

            // Check for mixed concerns (database + HTML in same file)
            if ($hasBusinessLogic && $hasHTMLOutput && $hasDirectDBAccess) {
                $findings[] = new Finding(
                    $filePath,
                    1,
                    Finding::TYPE_ARCHITECTURE,
                    Finding::SEVERITY_HIGH,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Mixed concerns: database access, business logic, and presentation in same file',
                    'Separate concerns into different layers (Model-View-Controller pattern)',
                    'File contains database access, business logic, and HTML output',
                    ['https://en.wikipedia.org/wiki/Model%E2%80%93view%E2%80%93controller']
                );
                break; // Only report once per file
            }

            // Check for include/require without proper path validation
            if (preg_match('/(include|require)(_once)?\s*\(?\s*["\']?([^"\';\)]+)/', $line, $matches)) {
                $includePath = $matches[3];
                if (strpos($includePath, '$') !== false) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_ARCHITECTURE,
                        Finding::SEVERITY_MEDIUM,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        'Dynamic include/require path detected',
                        'Use autoloading or validate include paths to prevent security issues',
                        trim($line),
                        ['https://owasp.org/www-community/attacks/Path_Traversal']
                    );
                }
            }
        }

        return $findings;
    }

    /**
     * Check maintainability metrics
     *
     * @param string $filePath
     * @param string $content
     * @return Finding[]
     */
    private function checkMaintainability(string $filePath, string $content): array
    {
        $findings = [];
        $lines = explode("\n", $content);

        // Check for missing documentation
        $functions = [];
        $classes = [];
        
        foreach ($lines as $lineNumber => $line) {
            $lineNumber++; // 1-based line numbers

            // Find functions and check for documentation
            if (preg_match('/function\s+(\w+)\s*\(/', $line, $matches)) {
                $functionName = $matches[1];
                $hasDocBlock = false;
                
                // Look for docblock in previous lines
                for ($i = max(0, $lineNumber - 10); $i < $lineNumber - 1; $i++) {
                    if (isset($lines[$i]) && preg_match('/\/\*\*/', $lines[$i])) {
                        $hasDocBlock = true;
                        break;
                    }
                }

                if (!$hasDocBlock && !in_array($functionName, ['__construct', '__destruct', '__get', '__set'])) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_LOW,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Function '$functionName' lacks documentation",
                        'Add PHPDoc comment block describing parameters, return value, and purpose',
                        trim($line),
                        ['https://docs.phpdoc.org/3.0/guide/getting-started/what-is-a-docblock.html']
                    );
                }
            }

            // Find classes and check for documentation
            if (preg_match('/class\s+(\w+)/', $line, $matches)) {
                $className = $matches[1];
                $hasDocBlock = false;
                
                // Look for docblock in previous lines
                for ($i = max(0, $lineNumber - 10); $i < $lineNumber - 1; $i++) {
                    if (isset($lines[$i]) && preg_match('/\/\*\*/', $lines[$i])) {
                        $hasDocBlock = true;
                        break;
                    }
                }

                if (!$hasDocBlock) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_LOW,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Class '$className' lacks documentation",
                        'Add PHPDoc comment block describing the class purpose and usage',
                        trim($line),
                        ['https://docs.phpdoc.org/3.0/guide/getting-started/what-is-a-docblock.html']
                    );
                }
            }

            // Check for TODO/FIXME comments
            if (preg_match('/(TODO|FIXME|HACK|XXX)/', $line, $matches)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_LOW,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    "Technical debt marker found: {$matches[1]}",
                    'Address the technical debt or create a proper issue tracker entry',
                    trim($line),
                    ['https://en.wikipedia.org/wiki/Technical_debt']
                );
            }

            // Check for error suppression operator
            if (preg_match('/@\s*\w+\s*\(/', $line)) {
                $findings[] = new Finding(
                    $filePath,
                    $lineNumber,
                    Finding::TYPE_QUALITY,
                    Finding::SEVERITY_MEDIUM,
                    $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                    'Error suppression operator (@) used',
                    'Handle errors properly instead of suppressing them',
                    trim($line),
                    ['https://www.php.net/manual/en/language.operators.errorcontrol.php']
                );
            }

            // Check for deprecated functions
            $deprecatedFunctions = [
                'mysql_connect', 'mysql_query', 'mysql_fetch_array', 'mysql_close',
                'ereg', 'eregi', 'split', 'each', 'create_function'
            ];
            
            foreach ($deprecatedFunctions as $deprecated) {
                if (preg_match("/\\b$deprecated\\s*\\(/", $line)) {
                    $findings[] = new Finding(
                        $filePath,
                        $lineNumber,
                        Finding::TYPE_QUALITY,
                        Finding::SEVERITY_HIGH,
                        $this->isPriorityArea($filePath) ? Finding::PRIORITY_AREA : Finding::NON_PRIORITY,
                        "Deprecated function '$deprecated' used",
                        'Replace with modern equivalent (e.g., PDO for database functions)',
                        trim($line),
                        ['https://www.php.net/manual/en/migration70.deprecated.php']
                    );
                }
            }
        }

        return $findings;
    }

    /**
     * Normalize code for comparison (remove variables, strings, etc.)
     *
     * @param string $code
     * @return string
     */
    private function normalizeCode(string $code): string
    {
        // Replace variables with placeholder
        $normalized = preg_replace('/\$\w+/', '$VAR', $code);
        
        // Replace string literals with placeholder
        $normalized = preg_replace('/["\'][^"\']*["\']/', 'STRING', $normalized);
        
        // Replace numbers with placeholder
        $normalized = preg_replace('/\b\d+\b/', 'NUM', $normalized);
        
        // Remove extra whitespace
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        
        return trim($normalized);
    }

    /**
     * Get the types of files this analyzer can handle
     *
     * @return string[] Array of file extensions or patterns this analyzer supports
     */
    public function getSupportedFileTypes(): array
    {
        return ['php'];
    }

    /**
     * Get the analyzer name for identification
     *
     * @return string Name of the analyzer
     */
    public function getName(): string
    {
        return 'PHP Code Quality Analyzer';
    }

    /**
     * Check if file is in priority area
     *
     * @param string $filePath
     * @return bool
     */
    private function isPriorityArea(string $filePath): bool
    {
        $priorityPatterns = [
            'admin/',
            'includes/',
            'config.php',
            'ad_',
            'smrsaj',
            'image.php',
            'process_'
        ];

        foreach ($priorityPatterns as $pattern) {
            if (strpos($filePath, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }
}