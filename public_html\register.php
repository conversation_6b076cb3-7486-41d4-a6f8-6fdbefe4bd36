<?php
require_once 'config.php'; // Adjust path as needed

$error_message = '';
$success_message = '';

// Default values for form pre-filling on error
$input_name = '';
$input_email = '';
$input_role = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input_name = trim($_POST['name'] ?? '');
    $input_email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? ''; // Don't trim password
    $confirm_password = $_POST['confirm_password'] ?? '';
    $input_role = trim($_POST['role'] ?? 'Author'); // Default role if empty
    $status = 'active'; // New authors are active by default

    // --- Validation ---
    $errors = [];
    if (empty($input_name)) {
        $errors[] = 'Ime je obavezno.';
    }
    if (empty($input_email)) {
        $errors[] = 'Email je obavezan.';
    } elseif (!filter_var($input_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Uneseni email nije validan.';
    }
    if (empty($password)) {
        $errors[] = 'Lozinka je obavezna.';
    } elseif (strlen($password) < 8) {
        $errors[] = 'Lozinka mora imati najmanje 8 karaktera.';
    }
    if ($password !== $confirm_password) {
        $errors[] = 'Lozinke se ne podudaraju.';
    }

    // Check if email already exists
    if (empty($errors)) {
        try {
            $sql_check = "SELECT id FROM authors WHERE email = :email LIMIT 1";
            $stmt_check = $pdo->prepare($sql_check);
            $stmt_check->bindParam(':email', $input_email, PDO::PARAM_STR);
            $stmt_check->execute();
            if ($stmt_check->fetch()) {
                $errors[] = 'Autor sa ovom email adresom već postoji.';
            }
        } catch (PDOException $e) {
            $errors[] = 'Greška pri provjeri email adrese: ' . $e->getMessage();
            // Log error
        }
    }

    // If validation passes, insert into database
    if (empty($errors)) {
        // Hash the password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        if ($hashedPassword === false) {
            $error_message = 'Greška prilikom generisanja hash-a lozinke.';
        } else {
            try {
                $sql_insert = "INSERT INTO authors (name, email, password, role, status, created_at)
                               VALUES (:name, :email, :password, :role, :status, NOW())";
                $stmt_insert = $pdo->prepare($sql_insert);

                $stmt_insert->bindParam(':name', $input_name, PDO::PARAM_STR);
                $stmt_insert->bindParam(':email', $input_email, PDO::PARAM_STR);
                $stmt_insert->bindParam(':password', $hashedPassword, PDO::PARAM_STR);
                $stmt_insert->bindParam(':role', $input_role, PDO::PARAM_STR);
                $stmt_insert->bindParam(':status', $status, PDO::PARAM_STR);

                $stmt_insert->execute();

                $success_message = 'Novi autor uspješno registrovan.';
                // Clear input fields after successful registration
                $input_name = '';
                $input_email = '';
                $input_role = '';

            } catch (PDOException $e) {
                $error_message = 'Greška pri registraciji autora: ' . $e->getMessage();
                // Log error
            }
        }
    } else {
        // Prepare error message string from errors array
        $error_message = implode('<br>', $errors);
    }
}


$admin_page_title = 'Registruj novog autora'; // Set page title
include 'includes/header.php'; // Include the admin header
?>

<div class="max-w-2xl mx-auto">
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-montserrat font-bold text-gray-800"><?php echo $admin_page_title; ?></h2>
         <a href="index.php" class="btn-secondary text-sm">
             <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" /></svg>
            Nazad na Dashboard
        </a>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="mb-4 bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded-lg text-sm">
            <?php echo $success_message; // Already escaped if needed, or just plain text ?>
        </div>
    <?php endif; ?>
    <?php if (!empty($error_message)): ?>
        <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm">
            <?php echo $error_message; // Allows <br> tags from implode ?>
        </div>
    <?php endif; ?>

    <form action="register.php" method="POST" class="card space-y-4">

        <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Ime *</label>
            <input type="text" id="name" name="name" class="form-input" placeholder="Puno ime autora" required value="<?php echo escape($input_name); ?>">
        </div>

        <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
            <input type="email" id="email" name="email" class="form-input" placeholder="Email adresa za prijavu" required value="<?php echo escape($input_email); ?>">
        </div>

        <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Lozinka *</label>
            <input type="password" id="password" name="password" class="form-input" placeholder="Minimalno 8 karaktera" required>
        </div>

        <div>
            <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">Potvrdi lozinku *</label>
            <input type="password" id="confirm_password" name="confirm_password" class="form-input" placeholder="Ponovite lozinku" required>
        </div>

        <div>
            <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Uloga (Opcionalno)</label>
            <input type="text" id="role" name="role" class="form-input" placeholder="Npr. Administrator, Editor, Autor" value="<?php echo escape($input_role); ?>">
        </div>

         <p class="text-xs text-gray-500">Status novog autora će biti postavljen na 'active'.</p>

        <div class="pt-2">
            <button type="submit" class="btn w-full md:w-auto">Registruj Autora</button>
        </div>

    </form>
</div>

<?php include 'includes/footer.php'; // Include the admin footer ?>
```

**How it works:**

1.  **Authentication Check:** It first runs `auth_check.php` to make sure only logged-in admins can see this page.
2.  **Form Submission:** When the form is submitted via POST:
    * It retrieves the name, email, password, confirmation, and role.
    * It performs validation checks (required fields, email format, password match, password length).
    * It checks if an author with the entered email already exists in the database.
    * If any errors occur, it displays them above the form.
3.  **Password Hashing:** If validation passes, it securely hashes the entered password using `password_hash()`.
4.  **Database Insertion:** It inserts the new author's details (name, email, hashed password, role, and 'active' status) into the `authors` table.
5.  **Success Message:** If the insertion is successful, it shows a success message.
6.  **Form Display:** The HTML part displays the registration form, showing error or success messages as needed.

Now, logged-in admins can navigate to `admin/register.php` to add more users to the syst<?php
// Include configuration to potentially use $pdo for quoting, although not strictly necessary for outputting SQL
// Adjust path if this file is not directly in the admin/ folder relative to config.php
require_once '../config.php';
require_once '../includes/functions.php'; // For escape() function

$output_sql = '';
$output_hash = '';
$error_message = '';
$input_email = '';
$input_password = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input_email = trim($_POST['email'] ?? '');
    $input_password = $_POST['password'] ?? ''; // Don't trim password

    if (empty($input_email) || empty($input_password)) {
        $error_message = 'Molimo unesite i email i lozinku.';
    } elseif (!filter_var($input_email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Uneseni email nije validan.';
    } else {
        // Generate the password hash
        $hashedPassword = password_hash($input_password, PASSWORD_DEFAULT);

        if ($hashedPassword === false) {
            $error_message = 'Greška prilikom generisanja hash-a lozinke.';
        } else {
            $output_hash = $hashedPassword;

            // Generate the SQL UPDATE command
            // Use $pdo->quote() for safety if $pdo is available from config.php
            if (isset($pdo)) {
                 $quoted_email = $pdo->quote($input_email);
                 $quoted_hash = $pdo->quote($hashedPassword);
                 $output_sql = "UPDATE `authors` SET `password` = " . $quoted_hash . " WHERE `email` = " . $quoted_email . ";";
            } else {
                 // Fallback if $pdo is not available (less safe, relies on user copying correctly)
                 // Use htmlspecialchars just in case it gets rendered somewhere unexpected
                 $escaped_email = escape($input_email);
                 $escaped_hash = escape($hashedPassword);
                 $output_sql = "UPDATE `authors` SET `password` = '" . $escaped_hash . "' WHERE `email` = '" . $escaped_email . "';";
                 $error_message = "Upozorenje: PDO konekcija nije dostupna za sigurno citiranje. Provjerite SQL prije izvršavanja.";
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="bs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow"> <title>Admin Password Hash Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        background: '#FFF4F5', primary: '#ff6481', secondary: '#ffd6dd',
                        dark: '#333333', light: '#ffffff', border: '#feeaec',
                        danger: '#ef4444', success: '#10b981',
                    },
                    fontFamily: {
                        sans: ['Open Sans', 'sans-serif'],
                        montserrat: ['Montserrat', 'sans-serif'],
                    },
                    borderRadius: { 'xl': '1rem', '2xl': '1.5rem' },
                }
            }
        };
    </script>
    <style type="text/tailwindcss">
        @layer components {
            .btn { @apply bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 font-montserrat shadow; }
            .form-input { @apply w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200; }
            .code-block { @apply bg-gray-800 text-white p-4 rounded-lg font-mono text-sm break-all whitespace-pre-wrap; }
        }
    </style>
</head>
<body class="bg-gray-100 font-sans p-6 md:p-10">
    <div class="max-w-xl mx-auto bg-white p-6 md:p-8 rounded-xl shadow-md border border-gray-200">
        <h1 class="text-xl font-montserrat font-bold text-dark mb-4 text-center">Admin Password Hash Generator</h1>
        <p class="text-sm text-gray-600 mb-6 text-center">Unesite email autora kojeg želite postaviti kao admina i željenu lozinku. Skripta će generisati hash lozinke i SQL komandu za ažuriranje baze podataka.</p>

        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6 rounded-r-lg" role="alert">
            <p class="font-bold">Upozorenje!</p>
            <p class="text-sm">Ovu skriptu koristite samo jednom za postavljanje inicijalne lozinke. Nakon upotrebe, <strong>obrišite ovu datoteku (`generate_password_hash.php`) sa servera</strong> iz sigurnosnih razloga.</p>
        </div>

        <?php if (!empty($error_message)): ?>
        <div class="mb-4 bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-lg text-sm">
            <?php echo escape($error_message); ?>
        </div>
        <?php endif; ?>

        <form method="POST" action="generate_password_hash.php" class="space-y-4">
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Admin Email Adresa *</label>
                <input type="email" id="email" name="email" class="form-input" placeholder="npr. <EMAIL>" required value="<?php echo escape($input_email); ?>">
                <p class="text-xs text-gray-500 mt-1">Email adresa autora iz `authors` tabele.</p>
            </div>
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Nova Lozinka *</label>
                <input type="password" id="password" name="password" class="form-input" placeholder="Unesite jaku lozinku" required>
            </div>
            <div>
                <button type="submit" class="btn w-full">Generiši Hash i SQL</button>
            </div>
        </form>

        <?php if (!empty($output_sql) && !empty($output_hash)): ?>
        <div class="mt-8 space-y-4 border-t border-gray-200 pt-6">
            <div>
                <h2 class="text-lg font-semibold text-gray-800 mb-2">Generisani Hash Lozinke:</h2>
                <p class="text-sm text-gray-600 mb-1">Kopirajte ovu vrijednost ako je trebate ručno unijeti:</p>
                <div class="code-block">
                    <?php echo escape($output_hash); ?>
                </div>
            </div>
            <div>
                <h2 class="text-lg font-semibold text-gray-800 mb-2">SQL Komanda za Ažuriranje:</h2>
                <p class="text-sm text-gray-600 mb-1">Kopirajte i izvršite ovu SQL komandu u vašem phpMyAdmin-u (ili sličnom alatu) da postavite lozinku za navedenog autora:</p>
                 <div class="relative">
                    <textarea id="sqlOutput" class="code-block w-full h-24 resize-none" readonly><?php echo escape($output_sql); ?></textarea>
                    <button
                        onclick="copySqlToClipboard()"
                        class="absolute top-2 right-2 bg-gray-600 hover:bg-gray-500 text-white px-2 py-1 rounded text-xs"
                        title="Kopiraj SQL"
                    >
                        Kopiraj
                    </button>
                </div>
            </div>
             <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mt-4 rounded-r-lg" role="alert">
                <p class="font-bold">Uspjeh!</p>
                <p class="text-sm">SQL komanda je generisana. Izvršite je u bazi podataka i zatim <strong>obavezno obrišite ovu skriptu</strong>.</p>
            </div>
        </div>
        <?php endif; ?>

    </div>

    <script>
        function copySqlToClipboard() {
            const sqlTextarea = document.getElementById('sqlOutput');
            sqlTextarea.select();
            sqlTextarea.setSelectionRange(0, 99999); // For mobile devices
            try {
                document.execCommand('copy');
                alert('SQL komanda kopirana u clipboard!');
            } catch (err) {
                alert('Greška pri kopiranju. Molimo kopirajte ručno.');
            }
            // Deselect text
            window.getSelection().removeAllRanges();
        }
    </script>

</body>
</html>
```

**How to use it:**

1.  Save this code as `generate_password_hash.php` inside your `admin` directory.
2.  Make sure you have added the `password` column to your `authors` table (see Step 1 in the previous response).
3.  Access this file through your browser (e.g., `http://yourdomain.com/admin/generate_password_hash.php`).
4.  Enter the email address of the author you want to make an admin.
5.  Enter the desired strong password for that admin.
6.  Click "Generiši Hash i SQL".
7.  Copy the generated SQL command.
8.  Go to your phpMyAdmin, select your `lako_fino_cms` database, go to the "SQL" tab, paste the command, and run it.
9.  **Crucially: Delete the `generate_password_hash.php` file from your server.**
10. You should now be able to log in using the email and password you just set via the `admin/login.php` page.

This provides a secure way to set the initial admin password without hardcoding it or storing it insecure