<?php

use PHPUnit\Framework\TestCase;
use AuditSystem\Controllers\AuditController;
use AuditSystem\Config\AuditConfig;
use AuditSystem\Services\ProgressTracker;
use AuditSystem\Services\FileScanner;
use AuditSystem\Services\AuditLogger;
use AuditSystem\Services\ErrorRecoveryService;
use AuditSystem\Analyzers\PHPAnalyzer;
use AuditSystem\Exceptions\FileAccessException;
use AuditSystem\Exceptions\AnalysisException;
use AuditSystem\Exceptions\MCPConnectionException;
use AuditSystem\Exceptions\ConfigurationException;

class ErrorHandlingIntegrationTest extends TestCase
{
    private string $tempDir;
    private string $testCodebaseDir;
    private AuditController $auditController;
    private AuditLogger $logger;
    private ErrorRecoveryService $recoveryService;

    protected function setUp(): void
    {
        $this->tempDir = sys_get_temp_dir() . '/audit_error_test_' . uniqid();
        $this->testCodebaseDir = $this->tempDir . '/test_codebase';
        mkdir($this->testCodebaseDir, 0755, true);

        $this->createTestCodebase();
        $this->setupAuditController();
    }

    protected function tearDown(): void
    {
        $this->removeDirectory($this->tempDir);
    }

    private function createTestCodebase(): void
    {
        // Create valid PHP file
        file_put_contents($this->testCodebaseDir . '/valid.php', '<?php echo "Hello World"; ?>');

        // Create invalid PHP file (syntax error)
        file_put_contents($this->testCodebaseDir . '/invalid.php', '<?php echo "Unclosed string');

        // Create large file that might cause memory issues
        file_put_contents($this->testCodebaseDir . '/large.php', '<?php ' . str_repeat('// Large comment' . PHP_EOL, 10000));

        // Create unreadable file (will be made unreadable in test)
        file_put_contents($this->testCodebaseDir . '/unreadable.php', '<?php echo "test"; ?>');

        // Create directory structure
        mkdir($this->testCodebaseDir . '/subdir', 0755, true);
        file_put_contents($this->testCodebaseDir . '/subdir/nested.php', '<?php class TestClass {} ?>');
    }

    private function setupAuditController(): void
    {
        $config = new AuditConfig([
            'audit' => [
                'target_directory' => $this->testCodebaseDir,
                'progress_file' => $this->tempDir . '/progress.json',
                'max_file_size' => 50000, // Small limit to test file size errors
                'timeout' => 10
            ]
        ]);

        $this->logger = new AuditLogger($this->tempDir . '/logs');
        $progressTracker = new ProgressTracker($config, $this->logger);
        $fileScanner = new FileScanner($config, $this->logger);

        $this->recoveryService = new ErrorRecoveryService($this->logger);

        $this->auditController = new AuditController($config, $progressTracker, $fileScanner, $this->logger);

        // Register a test analyzer that can fail
        $this->auditController->registerAnalyzer(new TestFailingAnalyzer());
    }

    public function testFileAccessErrorRecovery()
    {
        // Make a file unreadable
        chmod($this->testCodebaseDir . '/unreadable.php', 0000);

        try {
            $result = $this->auditController->startAudit(['clear_logs' => true]);

            // Audit should complete despite unreadable file
            $this->assertInstanceOf(\AuditSystem\Models\AuditResult::class, $result);

            // Check that error was logged
            $errorLogs = $this->logger->getRecentLogs(100, 'error');
            $this->assertNotEmpty($errorLogs);

            $hasFileAccessError = false;
            foreach ($errorLogs as $log) {
                if (strpos($log, 'unreadable.php') !== false) {
                    $hasFileAccessError = true;
                    break;
                }
            }
            $this->assertTrue($hasFileAccessError, 'File access error should be logged');

        } finally {
            // Restore file permissions for cleanup
            chmod($this->testCodebaseDir . '/unreadable.php', 0644);
        }
    }

    public function testAnalysisErrorRecovery()
    {
        // The TestFailingAnalyzer will fail on files containing 'invalid'
        $result = $this->auditController->startAudit(['clear_logs' => true]);

        $this->assertInstanceOf(\AuditSystem\Models\AuditResult::class, $result);

        // Check that analyzer failure was logged and recovered
        $logs = $this->logger->getRecentLogs(100);
        $hasAnalyzerFailure = false;
        $hasRecovery = false;

        foreach ($logs as $log) {
            if (strpos($log, 'Analyzer') !== false && strpos($log, 'failed') !== false) {
                $hasAnalyzerFailure = true;
            }
            if (strpos($log, 'Error recovery') !== false) {
                $hasRecovery = true;
            }
        }

        $this->assertTrue($hasAnalyzerFailure, 'Analyzer failure should be logged');
        $this->assertTrue($hasRecovery, 'Error recovery should be logged');
    }

    public function testFileSizeLimitErrorRecovery()
    {
        // The large.php file should exceed the size limit
        $result = $this->auditController->startAudit(['clear_logs' => true]);

        $this->assertInstanceOf(\AuditSystem\Models\AuditResult::class, $result);

        // Check that file size error was handled
        $logs = $this->logger->getRecentLogs(100);
        $hasFileSizeError = false;

        foreach ($logs as $log) {
            if (strpos($log, 'large.php') !== false && strpos($log, 'too large') !== false) {
                $hasFileSizeError = true;
                break;
            }
        }

        $this->assertTrue($hasFileSizeError, 'File size error should be logged');
    }

    public function testConfigurationErrorHandling()
    {
        // Test with invalid configuration
        try {
            $this->auditController->startAudit([
                'audit.timeout' => 'invalid_timeout', // Should be integer
                'clear_logs' => true
            ]);
            $this->fail('Should have thrown ConfigurationException');
        } catch (ConfigurationException $e) {
            $this->assertStringContainsString('Invalid configuration', $e->getMessage());
            $this->assertStringContainsString('audit.timeout', $e->getMessage());
        }
    }

    public function testProgressCorruptionRecovery()
    {
        // Create corrupted progress file
        $progressFile = $this->tempDir . '/progress.json';
        file_put_contents($progressFile, '{"invalid": json syntax');

        // Audit should handle corrupted progress and start fresh
        $result = $this->auditController->startAudit(['clear_logs' => true]);

        $this->assertInstanceOf(\AuditSystem\Models\AuditResult::class, $result);

        // Check that progress corruption was handled
        $logs = $this->logger->getRecentLogs(100);
        $hasProgressHandling = false;

        foreach ($logs as $log) {
            if (strpos($log, 'progress') !== false &&
                (strpos($log, 'Reset') !== false || strpos($log, 'fresh') !== false)) {
                $hasProgressHandling = true;
                break;
            }
        }

        $this->assertTrue($hasProgressHandling, 'Progress corruption should be handled');
    }

    public function testMCPConnectionErrorGracefulDegradation()
    {
        // Register an analyzer that depends on MCP
        $this->auditController->registerAnalyzer(new TestMCPDependentAnalyzer());

        $result = $this->auditController->startAudit(['clear_logs' => true]);

        $this->assertInstanceOf(\AuditSystem\Models\AuditResult::class, $result);

        // Check for graceful degradation logging
        $logs = $this->logger->getRecentLogs(100);
        $hasGracefulDegradation = false;

        foreach ($logs as $log) {
            if (strpos($log, 'Graceful degradation') !== false ||
                strpos($log, 'MCP') !== false) {
                $hasGracefulDegradation = true;
                break;
            }
        }

        // MCP errors should be handled gracefully
        $this->assertTrue($hasGracefulDegradation || count($logs) > 0, 'Should handle MCP issues gracefully');
    }

    public function testAuditResumeAfterFailure()
    {
        // Start audit and let it create some progress
        try {
            $this->auditController->startAudit(['clear_logs' => true]);
        } catch (\Exception $e) {
            // Ignore any failures for this test
        }

        // Now try to resume
        $result = $this->auditController->resumeAudit();

        $this->assertInstanceOf(\AuditSystem\Models\AuditResult::class, $result);

        // Check that resume was logged
        $logs = $this->logger->getRecentLogs(100);
        $hasResumeLog = false;

        foreach ($logs as $log) {
            if (strpos($log, 'resume') !== false || strpos($log, 'checkpoint') !== false) {
                $hasResumeLog = true;
                break;
            }
        }

        $this->assertTrue($hasResumeLog, 'Resume operation should be logged');
    }

    public function testErrorRecoveryStatistics()
    {
        // Run audit which should generate some errors
        $this->auditController->startAudit(['clear_logs' => true]);

        $stats = $this->recoveryService->getRecoveryStatistics();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_attempts', $stats);
        $this->assertArrayHasKey('unique_errors', $stats);
        $this->assertArrayHasKey('by_exception', $stats);
    }

    public function testLogRotationDuringAudit()
    {
        // Generate lots of log entries to trigger rotation
        for ($i = 0; $i < 100; $i++) {
            $this->logger->info("Test log entry {$i}");
        }

        // Trigger log rotation
        $this->logger->rotateLogs(1000); // Small size limit

        // Continue audit
        $result = $this->auditController->startAudit(['clear_logs' => false]);

        $this->assertInstanceOf(\AuditSystem\Models\AuditResult::class, $result);

        // Check that rotated files exist
        $rotatedFiles = glob($this->tempDir . '/logs/*.log.*');
        $this->assertNotEmpty($rotatedFiles, 'Should have rotated log files');
    }

    public function testResourceMonitoringDuringErrors()
    {
        // Run audit and check resource logging
        $result = $this->auditController->startAudit(['clear_logs' => true]);

        $this->assertInstanceOf(\AuditSystem\Models\AuditResult::class, $result);

        // Check performance logs
        $performanceLogFile = $this->tempDir . '/logs/performance.log';
        if (file_exists($performanceLogFile)) {
            $performanceContent = file_get_contents($performanceLogFile);
            $this->assertNotEmpty($performanceContent, 'Should have performance metrics');
        }

        // Get log statistics
        $stats = $this->logger->getLogStatistics();
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('audit', $stats);
        $this->assertGreaterThan(0, $stats['audit']['size']);
    }

    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                // Ensure file is writable before deletion
                chmod($path, 0644);
                unlink($path);
            }
        }
        rmdir($dir);
    }
}

/**
 * Test analyzer that fails on certain files
 */
class TestFailingAnalyzer implements \AuditSystem\Interfaces\AnalyzerInterface
{
    public function analyze(string $filePath, string $content): array
    {
        if (strpos($filePath, 'invalid') !== false) {
            throw new AnalysisException("Intentional failure for testing");
        }

        return []; // No findings for successful analysis
    }

    public function getSupportedFileTypes(): array
    {
        return ['php'];
    }

    public function getName(): string
    {
        return 'TestFailingAnalyzer';
    }
}

/**
 * Test analyzer that depends on MCP server
 */
class TestMCPDependentAnalyzer implements \AuditSystem\Interfaces\AnalyzerInterface
{
    public function analyze(string $filePath, string $content): array
    {
        // Simulate MCP connection failure
        throw new MCPConnectionException("MCP server unavailable for testing");
    }


    public function getName(): string
    {
        return 'TestMCPDependentAnalyzer';
    }

    public function getSupportedFileTypes(): array
    {
        return ['php'];
    }
}