<?php

namespace AuditSystem\Tests\Services;

use PHPUnit\Framework\TestCase;
use AuditSystem\Services\ProgressTracker;
use AuditSystem\Models\AuditProgress;
use AuditSystem\Models\Finding;

class ProgressTrackerTest extends TestCase
{
    private string $testProgressFile;
    private ProgressTracker $tracker;

    protected function setUp(): void
    {
        $this->testProgressFile = sys_get_temp_dir() . '/test_progress_' . uniqid() . '.json';
        $this->tracker = new ProgressTracker($this->testProgressFile);
    }

    protected function tearDown(): void
    {
        if (file_exists($this->testProgressFile)) {
            unlink($this->testProgressFile);
        }
    }

    public function testLoadProgressReturnsNullWhenFileDoesNotExist()
    {
        $progress = $this->tracker->loadProgress();
        $this->assertNull($progress);
    }

    public function testSaveAndLoadProgress()
    {
        $progress = new AuditProgress();
        $progress->currentPhase = 'testing';
        $progress->setPendingFiles(['file1.php', 'file2.php']);

        $this->assertTrue($this->tracker->saveProgress($progress));
        
        $loadedProgress = $this->tracker->loadProgress();
        $this->assertNotNull($loadedProgress);
        $this->assertEquals('testing', $loadedProgress->currentPhase);
        $this->assertEquals(['file1.php', 'file2.php'], $loadedProgress->pendingFiles);
    }

    public function testMarkFileCompleted()
    {
        $this->tracker->initializeProgress(['file1.php', 'file2.php']);
        
        $finding = new Finding(
            'file1.php',
            10,
            Finding::TYPE_SECURITY,
            Finding::SEVERITY_HIGH,
            Finding::PRIORITY_AREA,
            'Test finding',
            'Test recommendation'
        );

        $this->tracker->markFileCompleted('file1.php', [$finding]);
        
        $this->assertTrue($this->tracker->isFileCompleted('file1.php'));
        $this->assertFalse($this->tracker->isFileCompleted('file2.php'));
        
        $progress = $this->tracker->loadProgress();
        $this->assertEquals(1, $progress->statistics['processedFiles']);
        $this->assertEquals(1, $progress->statistics['totalFindings']);
    }

    public function testResetProgress()
    {
        $this->tracker->initializeProgress(['file1.php']);
        $this->assertFileExists($this->testProgressFile);
        
        $this->tracker->resetProgress();
        $this->assertFileDoesNotExist($this->testProgressFile);
    }

    public function testGetStatistics()
    {
        $stats = $this->tracker->getStatistics();
        $this->assertEquals(0, $stats['totalFiles']);
        $this->assertEquals(0.0, $stats['completionPercentage']);
        
        $this->tracker->initializeProgress(['file1.php', 'file2.php']);
        $this->tracker->markFileCompleted('file1.php', []);
        
        $stats = $this->tracker->getStatistics();
        $this->assertEquals(2, $stats['totalFiles']);
        $this->assertEquals(1, $stats['processedFiles']);
        $this->assertEquals(50.0, $stats['completionPercentage']);
    }

    public function testUpdatePhase()
    {
        $this->tracker->initializeProgress(['file1.php']);
        $this->tracker->updatePhase('analyzing');
        
        $progress = $this->tracker->loadProgress();
        $this->assertEquals('analyzing', $progress->currentPhase);
    }

    public function testGetPendingFiles()
    {
        $files = ['file1.php', 'file2.php', 'file3.php'];
        $this->tracker->initializeProgress($files);
        $this->tracker->markFileCompleted('file1.php', []);
        
        $pending = $this->tracker->getPendingFiles();
        $this->assertEquals(['file2.php', 'file3.php'], $pending);
    }
}