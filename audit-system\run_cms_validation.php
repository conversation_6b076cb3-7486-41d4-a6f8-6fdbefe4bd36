<?php

/**
 * CMS Validation Test Executor
 * 
 * Simple script to run the validation test suite against the real CMS codebase
 */

require_once __DIR__ . '/vendor/autoload.php';

echo "=== CMS Audit System Validation ===\n";
echo "Testing audit system against real Lako & Fino CMS files...\n\n";

// Check if CMS files exist
$cmsPath = __DIR__ . '/../public_html';
if (!is_dir($cmsPath)) {
    echo "❌ CMS files not found at: $cmsPath\n";
    echo "Please ensure the CMS files are available for testing.\n";
    exit(1);
}

echo "✅ CMS files found at: $cmsPath\n";

// List key files to validate
$keyFiles = [
    'config.php',
    'index.php', 
    'article.php',
    'process_comment.php',
    'admin/index.php',
    'includes/functions.php'
];

echo "\nChecking key CMS files:\n";
$missingFiles = [];
foreach ($keyFiles as $file) {
    $filePath = $cmsPath . '/' . $file;
    if (file_exists($filePath)) {
        echo "✅ $file\n";
    } else {
        echo "❌ $file (missing)\n";
        $missingFiles[] = $file;
    }
}

if (!empty($missingFiles)) {
    echo "\n⚠️  Some key files are missing. Validation may be incomplete.\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Running PHPUnit validation tests...\n";
echo str_repeat("=", 50) . "\n\n";

// Run PHPUnit tests
$testCommand = 'vendor/bin/phpunit tests/Integration/RealCMSValidationTest.php --verbose';
$output = [];
$returnCode = 0;

exec($testCommand . ' 2>&1', $output, $returnCode);

foreach ($output as $line) {
    echo $line . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";

if ($returnCode === 0) {
    echo "🎉 VALIDATION TESTS PASSED!\n";
    echo "The audit system successfully validates against the CMS codebase.\n";
} else {
    echo "❌ VALIDATION TESTS FAILED\n";
    echo "Some tests did not pass. Check the output above for details.\n";
}

echo "\nNext steps:\n";
echo "1. Run individual test suites for detailed analysis:\n";
echo "   - Security: vendor/bin/phpunit tests/Integration/SecurityVulnerabilityValidationTest.php\n";
echo "   - Performance: vendor/bin/phpunit tests/Integration/PerformanceBottleneckValidationTest.php\n";
echo "   - False Positives: vendor/bin/phpunit tests/Integration/FalsePositiveMinimizationTest.php\n";
echo "   - Regression: vendor/bin/phpunit tests/Integration/RegressionTestSuite.php\n";
echo "\n2. Run full audit on CMS: php bin/audit.php ../public_html\n";

exit($returnCode);