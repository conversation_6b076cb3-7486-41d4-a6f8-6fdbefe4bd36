<?php
/**
 * Ad Impression Recording Script (Endpoint for JS) - AGGREGATION VERSION
 *
 * Records ad impressions into a buffer table for later aggregation.
 * Expects POST data from JavaScript.
 * v2.0: Modified to write to ad_impressions_buffer.
 * v1.1: Changed to POST, expects more context.
 */

// --- Configuration ---
// Get the absolute path to the config file relative to this script's directory
$configPath = __DIR__ . '/config.php';

// Include configuration and database connection
if (file_exists($configPath)) {
    require_once $configPath;
} else {
    // Log error and exit cleanly if config is missing
    error_log("record_impression.php: Failed to include config.php at path: " . $configPath);
    // Output GIF even on fatal config error to avoid breaking tracking pixel expectations
    header('Content-Type: image/gif');
    echo base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
    exit;
}
// NOTE: We no longer need includes/ad_tracking.php here

// --- Set Headers ---
// Set CORS headers if your JS is on a different subdomain or port
if (defined('SITE_URL')) {
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    $site_url_parsed = parse_url(SITE_URL);
    $origin_parsed = parse_url($origin);

    if (isset($site_url_parsed['scheme'], $site_url_parsed['host']) &&
        isset($origin_parsed['scheme'], $origin_parsed['host']) &&
        $site_url_parsed['scheme'] === $origin_parsed['scheme'] &&
        $site_url_parsed['host'] === $origin_parsed['host'])
    {
        header("Access-Control-Allow-Origin: " . $origin);
    } else {
        header("Access-Control-Allow-Origin: " . SITE_URL);
    }
} else {
     header("Access-Control-Allow-Origin: *"); // Fallback
}

header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// Handle potential OPTIONS preflight request from browser
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(204); // No Content
    exit;
}

// --- Process Request ---
header('Content-Type: image/gif'); // Always return GIF regardless of outcome
$responseGif = base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log("[Record Impression Buffer] Invalid request method: " . $_SERVER['REQUEST_METHOD']);
    echo $responseGif;
    exit;
}

// Get parameters from the POST request
$adId = isset($_POST['ad_id']) ? (int)$_POST['ad_id'] : 0;
$adType = isset($_POST['ad_type']) ? trim($_POST['ad_type']) : '';
$placement = isset($_POST['placement']) ? trim($_POST['placement']) : null; // Allow null placement
// Other context variables are not needed for the buffer table but could be logged if required
// $pageType = isset($_POST['page_type']) ? trim($_POST['page_type']) : 'unknown';
// $pageId = isset($_POST['page_id']) && trim($_POST['page_id']) !== '' && is_numeric($_POST['page_id']) ? (int)$_POST['page_id'] : null;


// Basic validation
if ($adId <= 0 || empty($adType)) {
    error_log("[Record Impression Buffer] Invalid or missing ad_id or ad_type. ID: {$adId}, Type: {$adType}");
    echo $responseGif;
    exit;
}

// Check if $pdo is available
if (!isset($pdo) || !$pdo instanceof PDO) {
    error_log("[Record Impression Buffer] PDO connection not available.");
    echo $responseGif;
    exit;
}

// --- Write to Buffer Table ---
try {
    // Prepare the INSERT ... ON DUPLICATE KEY UPDATE statement
    $sql = "INSERT INTO ad_impressions_buffer (view_date, view_hour, ad_id, ad_type, placement, view_count)
            VALUES (:view_date, :view_hour, :ad_id, :ad_type, :placement, 1)
            ON DUPLICATE KEY UPDATE view_count = view_count + 1";

    $view_date = date('Y-m-d');
    $view_hour = date('G'); // Hour in 24-format without leading zeros

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':view_date', $view_date, PDO::PARAM_STR);
    $stmt->bindParam(':view_hour', $view_hour, PDO::PARAM_INT);
    $stmt->bindParam(':ad_id', $adId, PDO::PARAM_INT);
    $stmt->bindParam(':ad_type', $adType, PDO::PARAM_STR);
    $stmt->bindParam(':placement', $placement, PDO::PARAM_STR); // Bind placement

    $stmt->execute();

} catch (PDOException $e) {
    // Log error but don't stop execution or reveal error details
    error_log("[Record Impression Buffer] Database error: " . $e->getMessage() . " | Ad ID: {$adId}, Type: {$adType}, Placement: {$placement}");
    // Fall through to return GIF
} catch (Exception $e) {
    // Catch any other unexpected errors
     error_log("[Record Impression Buffer] General error: " . $e->getMessage() . " | Ad ID: {$adId}, Type: {$adType}, Placement: {$placement}");
     // Fall through to return GIF
}

// Return a 1x1 transparent GIF image
echo $responseGif;
exit;
?>
