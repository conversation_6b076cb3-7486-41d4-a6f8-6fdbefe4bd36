{"completedFiles": ["C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_1.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_10.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_11.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_12.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_13.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_14.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_15.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_16.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_17.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_18.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_19.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_2.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_20.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_21.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_22.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_23.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_24.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_25.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_26.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_27.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_28.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_29.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_3.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_30.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_31.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_32.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_33.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_34.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_35.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_36.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_37.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_38.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_39.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_4.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_40.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_41.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_42.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_43.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_44.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_45.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_46.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_47.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_48.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_49.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_5.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_50.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_6.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_7.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_8.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_9.php", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test.css", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test.html", "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test.js"], "pendingFiles": [], "findings": {"C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_1.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_1.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_1.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_10.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_10.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_10.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_11.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_11.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_11.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_12.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_12.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_12.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_13.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_13.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_13.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_14.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_14.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_14.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_15.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_15.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_15.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_16.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_16.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_16.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_17.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_17.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_17.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_18.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_18.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_18.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_19.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_19.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_19.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_2.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_2.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_2.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_20.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_20.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_20.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_21.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_21.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_21.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_22.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_22.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_22.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_23.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_23.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_23.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_24.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_24.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_24.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_25.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_25.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_25.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_26.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_26.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_26.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_27.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_27.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_27.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_28.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_28.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_28.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_29.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_29.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_29.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_3.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_3.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_3.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_30.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_30.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_30.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_31.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_31.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_31.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_32.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_32.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_32.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_33.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_33.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_33.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_34.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_34.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_34.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_35.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_35.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_35.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_36.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_36.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_36.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_37.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_37.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_37.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_38.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_38.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_38.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_39.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_39.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_39.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_4.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_4.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_4.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_40.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_40.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_40.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_41.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_41.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_41.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_42.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_42.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_42.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_43.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_43.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_43.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_44.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_44.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_44.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_45.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_45.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_45.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_46.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_46.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_46.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_47.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_47.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_47.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_48.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_48.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_48.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_49.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_49.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_49.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_5.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_5.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_5.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_50.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_50.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_50.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_6.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_6.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_6.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_7.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_7.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_7.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_8.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_8.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_8.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_9.php": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_9.php", "line": 1, "type": "quality", "severity": "low", "priority": "NON_PRIORITY", "description": "<PERSON><PERSON> finding", "recommendation": "<PERSON><PERSON> recommendation", "codeSnippet": null, "references": []}, {"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test_9.php", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test.css": [], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test.html": [{"file": "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test.html", "line": 1, "type": "security", "severity": "medium", "priority": "PRIORITY_AREA", "description": "Mock security finding", "recommendation": "Mock security recommendation", "codeSnippet": null, "references": []}], "C:\\Users\\<USER>\\Documents\\cms 12 04\\cms finalizacija\\v2 09.05.2025 za kiro\\web\\mercislike.art\\audit-system\\tests\\Integration/../../test_data/sample_code\\test.js": []}, "lastUpdate": "2025-08-11 17:47:04", "currentPhase": "completed", "statistics": {"totalFiles": 54, "processedFiles": 54, "totalFindings": 103, "criticalFindings": 0, "priorityAreaFindings": 52}}