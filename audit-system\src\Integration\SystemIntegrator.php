<?php

namespace AuditSystem\Integration;

use AuditSystem\Controllers\AuditController;
use AuditSystem\Config\AuditConfig;
use AuditSystem\Services\ProgressTracker;
use AuditSystem\Services\FileScanner;
use AuditSystem\Services\ReportGenerator;
use AuditSystem\Services\AuditLogger;
use AuditSystem\Analyzers\SecurityAnalyzer;
use AuditSystem\Analyzers\PerformanceAnalyzer;
use AuditSystem\Analyzers\PHPAnalyzer;
use AuditSystem\Analyzers\FrontendAnalyzer;
use AuditSystem\Analyzers\ConfigurationAnalyzer;
use AuditSystem\Services\BestPracticesChecker;
use AuditSystem\Services\FindingClassifier;
use AuditSystem\Exceptions\AuditException;

/**
 * System Integrator - Wires all audit system components together
 * 
 * This class handles the complete integration of all audit system components,
 * ensuring proper initialization, configuration, and orchestration of the
 * entire audit process.
 */
class SystemIntegrator
{
    private AuditConfig $config;
    private AuditController $controller;
    private array $analyzers = [];
    private array $services = [];
    private bool $isInitialized = false;
    private AuditLogger $logger;

    public function __construct(?AuditConfig $config = null)
    {
        $this->config = $config ?? AuditConfig::getInstance();
        $this->logger = new AuditLogger();
        $this->logger->info('SystemIntegrator initialized');
    }

    /**
     * Initialize and wire all system components
     *
     * @return void
     * @throws AuditException If initialization fails
     */
    public function initialize(): void
    {
        if ($this->isInitialized) {
            $this->logger->debug('System already initialized, skipping');
            return;
        }

        $this->logger->info('Starting system integration and component wiring');

        try {
            // Step 1: Initialize core services
            $this->initializeCoreServices();
            
            // Step 2: Initialize and register analyzers
            $this->initializeAnalyzers();
            
            // Step 3: Initialize audit controller with all components
            $this->initializeAuditController();
            
            // Step 4: Validate system integration
            $this->validateSystemIntegration();
            
            $this->isInitialized = true;
            $this->logger->info('System integration completed successfully', [
                'analyzers_count' => count($this->analyzers),
                'services_count' => count($this->services)
            ]);

        } catch (\Exception $e) {
            $this->logger->critical('System integration failed: ' . $e->getMessage(), [
                'exception_class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw new AuditException('Failed to initialize audit system: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Initialize core services
     *
     * @return void
     */
    private function initializeCoreServices(): void
    {
        $this->logger->info('Initializing core services');

        // Progress Tracker
        $progressFile = $this->config->get('audit.progress_file', 'audit-system/data/progress.json');
        $this->services['progress_tracker'] = new ProgressTracker($progressFile);
        $this->logger->debug('Progress tracker initialized', ['file' => $progressFile]);

        // File Scanner
        $this->services['file_scanner'] = new FileScanner($this->config);
        $this->logger->debug('File scanner initialized');

        // Report Generator
        $this->services['report_generator'] = new ReportGenerator();
        $this->logger->debug('Report generator initialized');

        // Best Practices Checker (with MCP integration)
        $mcpConfig = $this->config->get('mcp', []);
        
        // Create MCP client if configuration exists
        if (!empty($mcpConfig)) {
            try {
                $mcpClient = new \AuditSystem\Services\MCPClient($this->config);
                $this->services['best_practices_checker'] = new BestPracticesChecker($mcpClient, $this->config);
                $this->logger->debug('Best practices checker initialized with MCP client');
            } catch (\Exception $e) {
                // Fallback to null MCP client for local operation
                $this->services['best_practices_checker'] = new BestPracticesChecker(new \AuditSystem\Services\NullMCPClient(), $this->config);
                $this->logger->warning('MCP client initialization failed, using fallback', ['error' => $e->getMessage()]);
            }
        } else {
            // Use null MCP client for local operation
            $this->services['best_practices_checker'] = new BestPracticesChecker(new \AuditSystem\Services\NullMCPClient(), $this->config);
            $this->logger->debug('Best practices checker initialized without MCP client');
        }

        // Finding Classifier
        $this->services['finding_classifier'] = new FindingClassifier($this->config);
        $this->logger->debug('Finding classifier initialized');

        $this->logger->info('Core services initialization completed', [
            'services' => array_keys($this->services)
        ]);
    }

    /**
     * Initialize and configure all analyzers
     *
     * @return void
     */
    private function initializeAnalyzers(): void
    {
        $this->logger->info('Initializing analyzers');

        // Security Analyzer
        if ($this->config->get('analyzers.security.enabled', true)) {
            $securityConfig = $this->config->get('analyzers.security', []);
            $this->analyzers['security'] = new SecurityAnalyzer($securityConfig);
            $this->logger->debug('Security analyzer initialized');
        }

        // Performance Analyzer
        if ($this->config->get('analyzers.performance.enabled', true)) {
            $performanceConfig = $this->config->get('analyzers.performance', []);
            $this->analyzers['performance'] = new PerformanceAnalyzer($performanceConfig);
            $this->logger->debug('Performance analyzer initialized');
        }

        // PHP Code Analyzer
        if ($this->config->get('analyzers.php.enabled', true)) {
            $phpConfig = $this->config->get('analyzers.php', []);
            $this->analyzers['php'] = new PHPAnalyzer($phpConfig);
            $this->logger->debug('PHP analyzer initialized');
        }

        // Frontend Analyzer
        if ($this->config->get('analyzers.frontend.enabled', true)) {
            $frontendConfig = $this->config->get('analyzers.frontend', []);
            $this->analyzers['frontend'] = new FrontendAnalyzer($frontendConfig);
            $this->logger->debug('Frontend analyzer initialized');
        }

        // Configuration Analyzer
        if ($this->config->get('analyzers.configuration.enabled', true)) {
            $configAnalyzerConfig = $this->config->get('analyzers.configuration', []);
            $this->analyzers['configuration'] = new ConfigurationAnalyzer($configAnalyzerConfig);
            $this->logger->debug('Configuration analyzer initialized');
        }

        // Inject dependencies into analyzers
        $this->injectAnalyzerDependencies();

        $this->logger->info('Analyzers initialization completed', [
            'enabled_analyzers' => array_keys($this->analyzers)
        ]);
    }

    /**
     * Inject dependencies into analyzers
     *
     * @return void
     */
    private function injectAnalyzerDependencies(): void
    {
        $this->logger->debug('Injecting dependencies into analyzers');

        foreach ($this->analyzers as $name => $analyzer) {
            // Inject best practices checker if analyzer supports it
            if (method_exists($analyzer, 'setBestPracticesChecker')) {
                $analyzer->setBestPracticesChecker($this->services['best_practices_checker']);
                $this->logger->debug("Injected best practices checker into {$name} analyzer");
            }

            // Inject finding classifier if analyzer supports it
            if (method_exists($analyzer, 'setFindingClassifier')) {
                $analyzer->setFindingClassifier($this->services['finding_classifier']);
                $this->logger->debug("Injected finding classifier into {$name} analyzer");
            }

            // Inject logger if analyzer supports it
            if (method_exists($analyzer, 'setLogger')) {
                $analyzer->setLogger($this->logger);
                $this->logger->debug("Injected logger into {$name} analyzer");
            }
        }
    }

    /**
     * Initialize audit controller with all components
     *
     * @return void
     */
    private function initializeAuditController(): void
    {
        $this->logger->info('Initializing audit controller');

        $this->controller = new AuditController(
            $this->config,
            $this->services['progress_tracker'],
            $this->services['file_scanner'],
            $this->logger
        );

        // Register all analyzers with the controller
        foreach ($this->analyzers as $name => $analyzer) {
            $this->controller->registerAnalyzer($analyzer);
            $this->logger->debug("Registered {$name} analyzer with controller");
        }

        $this->logger->info('Audit controller initialization completed', [
            'registered_analyzers' => count($this->analyzers)
        ]);
    }

    /**
     * Validate system integration
     *
     * @return void
     * @throws AuditException If validation fails
     */
    private function validateSystemIntegration(): void
    {
        $this->logger->info('Validating system integration');

        $validationErrors = [];

        // Validate controller is initialized
        if (!$this->controller instanceof AuditController) {
            $validationErrors[] = 'Audit controller not properly initialized';
        }

        // Validate at least one analyzer is registered
        if (empty($this->analyzers)) {
            $validationErrors[] = 'No analyzers registered';
        }

        // Validate core services are available
        $requiredServices = ['progress_tracker', 'file_scanner', 'report_generator'];
        foreach ($requiredServices as $service) {
            if (!isset($this->services[$service])) {
                $validationErrors[] = "Required service not initialized: {$service}";
            }
        }

        // Validate configuration
        $requiredConfig = [
            'audit.target_directory',
            'audit.progress_file',
            'audit.report_directory'
        ];
        foreach ($requiredConfig as $configKey) {
            if ($this->config->get($configKey) === null) {
                $validationErrors[] = "Required configuration missing: {$configKey}";
            }
        }

        // Validate file system permissions
        $progressFile = $this->config->get('audit.progress_file');
        $progressDir = dirname($progressFile);
        if (!is_dir($progressDir) && !mkdir($progressDir, 0755, true)) {
            $validationErrors[] = "Cannot create progress directory: {$progressDir}";
        }

        $reportDir = $this->config->get('audit.report_directory');
        if (!is_dir($reportDir) && !mkdir($reportDir, 0755, true)) {
            $validationErrors[] = "Cannot create report directory: {$reportDir}";
        }

        if (!empty($validationErrors)) {
            $errorMessage = 'System integration validation failed: ' . implode(', ', $validationErrors);
            $this->logger->error($errorMessage, ['errors' => $validationErrors]);
            throw new AuditException($errorMessage);
        }

        $this->logger->info('System integration validation passed');
    }

    /**
     * Get the initialized audit controller
     *
     * @return AuditController
     * @throws AuditException If system not initialized
     */
    public function getAuditController(): AuditController
    {
        if (!$this->isInitialized) {
            throw new AuditException('System not initialized. Call initialize() first.');
        }

        return $this->controller;
    }

    /**
     * Get a specific service
     *
     * @param string $serviceName
     * @return mixed
     * @throws AuditException If service not found
     */
    public function getService(string $serviceName)
    {
        if (!isset($this->services[$serviceName])) {
            throw new AuditException("Service not found: {$serviceName}");
        }

        return $this->services[$serviceName];
    }

    /**
     * Get all registered analyzers
     *
     * @return array
     */
    public function getAnalyzers(): array
    {
        return $this->analyzers;
    }

    /**
     * Get system status and health information
     *
     * @return array
     */
    public function getSystemStatus(): array
    {
        return [
            'initialized' => $this->isInitialized,
            'analyzers' => [
                'count' => count($this->analyzers),
                'types' => array_keys($this->analyzers)
            ],
            'services' => [
                'count' => count($this->services),
                'types' => array_keys($this->services)
            ],
            'configuration' => [
                'target_directory' => $this->config->get('audit.target_directory'),
                'progress_file' => $this->config->get('audit.progress_file'),
                'report_directory' => $this->config->get('audit.report_directory')
            ],
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true)
            ]
        ];
    }

    /**
     * Perform system health check
     *
     * @return array Health check results
     */
    public function performHealthCheck(): array
    {
        $this->logger->info('Performing system health check');

        $healthCheck = [
            'overall_status' => 'healthy',
            'checks' => []
        ];

        // Check initialization status
        $healthCheck['checks']['initialization'] = [
            'status' => $this->isInitialized ? 'pass' : 'fail',
            'message' => $this->isInitialized ? 'System initialized' : 'System not initialized'
        ];

        // Check analyzers
        $healthCheck['checks']['analyzers'] = [
            'status' => !empty($this->analyzers) ? 'pass' : 'fail',
            'message' => count($this->analyzers) . ' analyzers registered',
            'details' => array_keys($this->analyzers)
        ];

        // Check services
        $healthCheck['checks']['services'] = [
            'status' => !empty($this->services) ? 'pass' : 'fail',
            'message' => count($this->services) . ' services initialized',
            'details' => array_keys($this->services)
        ];

        // Check file system permissions
        $progressFile = $this->config->get('audit.progress_file');
        $progressDir = dirname($progressFile);
        $healthCheck['checks']['progress_directory'] = [
            'status' => is_writable($progressDir) ? 'pass' : 'fail',
            'message' => is_writable($progressDir) ? 'Progress directory writable' : 'Progress directory not writable',
            'path' => $progressDir
        ];

        $reportDir = $this->config->get('audit.report_directory');
        $healthCheck['checks']['report_directory'] = [
            'status' => is_writable($reportDir) ? 'pass' : 'fail',
            'message' => is_writable($reportDir) ? 'Report directory writable' : 'Report directory not writable',
            'path' => $reportDir
        ];

        // Check target directory
        $targetDir = $this->config->get('audit.target_directory');
        $healthCheck['checks']['target_directory'] = [
            'status' => is_readable($targetDir) ? 'pass' : 'fail',
            'message' => is_readable($targetDir) ? 'Target directory readable' : 'Target directory not readable',
            'path' => $targetDir
        ];

        // Check MCP integration
        if ($this->services['best_practices_checker'] ?? null) {
            try {
                // Try to get best practices to test MCP connectivity
                $testResult = $this->services['best_practices_checker']->getBestPractices('php', 'test');
                $mcpStatus = !empty($testResult);
                $healthCheck['checks']['mcp_integration'] = [
                    'status' => 'warning', // Always warning since we're using fallback
                    'message' => 'MCP server not accessible (fallback mode)'
                ];
            } catch (\Exception $e) {
                $healthCheck['checks']['mcp_integration'] = [
                    'status' => 'warning',
                    'message' => 'MCP server not accessible (fallback mode)'
                ];
            }
        }

        // Determine overall status
        $failedChecks = array_filter($healthCheck['checks'], function($check) {
            return $check['status'] === 'fail';
        });

        if (!empty($failedChecks)) {
            $healthCheck['overall_status'] = 'unhealthy';
        } else {
            $warningChecks = array_filter($healthCheck['checks'], function($check) {
                return $check['status'] === 'warning';
            });
            if (!empty($warningChecks)) {
                $healthCheck['overall_status'] = 'degraded';
            }
        }

        $this->logger->info('Health check completed', [
            'overall_status' => $healthCheck['overall_status'],
            'failed_checks' => count($failedChecks),
            'warning_checks' => count($warningChecks ?? [])
        ]);

        return $healthCheck;
    }

    /**
     * Cleanup system resources
     *
     * @return void
     */
    public function cleanup(): void
    {
        $this->logger->info('Cleaning up system resources');

        // Cleanup analyzers
        foreach ($this->analyzers as $analyzer) {
            if (method_exists($analyzer, 'cleanup')) {
                $analyzer->cleanup();
            }
        }

        // Cleanup services
        foreach ($this->services as $service) {
            if (method_exists($service, 'cleanup')) {
                $service->cleanup();
            }
        }

        $this->logger->info('System cleanup completed');
    }
}